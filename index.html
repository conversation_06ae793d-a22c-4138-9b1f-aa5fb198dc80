<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <meta http-equiv="pragram" content="no-cache">
  <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="expires" content="0">
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/<%= htmlWebpackPlugin.options.config%>"></script>
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/vue/vue.min.js"></script>
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/vue/vue-router.min.js"></script>
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/elementUI/element-ui.min.js"></script>
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/vue/axios.min.js"></script>
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/vue/vuex.min.js"></script>
  <!-- 微信二维码登录js -->
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/wx/wxLogin.js"></script>
  <!-- ECharts-js -->
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/eCharts/echarts.js"></script>
  <!-- 小程序JSSDK -->
  <script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/wx/jweixin-1.6.0.js"></script>
  <script>
    if (typeof Promise === 'undefined')
      document.write('<script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/vue/bluebird.core.min.js"><\/script>');
    if (typeof fetch === 'undefined')
      document.write('<script src="/<%= htmlWebpackPlugin.options.productName%>/static/libs/vue/fetch.umd.js"><\/script>');
  </script>
  <link rel="stylesheet" href="/<%= htmlWebpackPlugin.options.productName%>/static/libs/elementUI/index.css" />
  <link rel="stylesheet" href="/<%= htmlWebpackPlugin.options.productName%>/static/libs/designer/css/designer.css" />
  <link rel="stylesheet" href="/<%= htmlWebpackPlugin.options.productName%>/static/libs/flowable/css/designer.css" />
  <title></title>
</head>

<body>
  <div id="rootApp"></div>
  <!-- built files will be auto injected -->
</body>

</html>
