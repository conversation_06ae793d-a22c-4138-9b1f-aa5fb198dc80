server:
  port: 8039
spring:
  # profiles:
  #   active: nacos
  application:
    name: mftcc-flowable-server
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_ADDRESS:127.0.0.1:8848}
        namespace: 30a7f7f1-f414-4acc-8a4b-bbb5808727d5
        group: example
        file-extension: yml
        ext-config:
          - data-id: mftcc.yml
            group: example
            refresh: true
mybatis-plus:
  # xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  mapper-locations: classpath*:mapping/**/*Mapper.xml,classpath:/META-INF/modeler-mybatis-mappings/*.xml
  # 以下配置均有默认值,可以不设置
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  configuration-properties:
    prefix:
    blobType: BLOB
    boolValue: TRUE
mftcc:
  log_path: ../
  FeignClient:
    sys: mftcc-sys-server
  license-path: mftcc-license.lic
  sso:
    switch: false
    url: http://127.0.0.1:8099
    clientId: inf-flowable
    clientSecret: flowCxEUDn0qxym0SILF5DoKrokyL2Ud
