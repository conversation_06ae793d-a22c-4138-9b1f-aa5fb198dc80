## 版本说明

#### 4.3.0

流程引擎缓存调整为 redis 存储

#### 4.2.6

业务流程配置两个相邻子审批流程节点提交问题
子流程终止后业务流程图显示问题
审批人员弹层取数逻辑调整
审批历史展示优化，支持根据业务号和流程标识，展示所有审批历史，包含打回的记录

#### 4.2.5

修复服务节点后接子流程报错的问题

#### 4.2.4

修复主子流程业务标识不同时任务丢失的问题

#### 4.2.3

修复业务流程配置两个相邻子审批流程节点提交问题  
修复单人任务指定审批人时指定前面会签任务节点的审批人，提交时报错  
修复退回到发起人后 ，选择 提交到已执行过的审批岗位 类型，选择审批岗位后提交，未提交到选择审批岗位  
修复流程设计器 岗位设置“二次到当前节点，默认为第一次执行人”参数时，不生效  
修复审批人员弹层中 查询岗位编号 筛选条件无效

#### 4.2.2

支持流程流转方式为跳转时，可以选择下一节点审批人

#### 4.2.1

添加提交到已执行的指定节点流转方式  
支持流程选择审批人弹层添加业务角色列  
支持流程待办已办工作台列表显示 发起人和 上一节点审批人员 列
支持待办工作台中配置自定义业务列字段  
支持允许审批页面的意见类型、审批说明自定义  
添加会签节点岗位回避功能

#### 4.1.19

修复流程服务节点执行外部请求返回后获取不到 traceNo（业务主键号）的问题
支持流程服务节点通讯模式为异步模式
支持流程流转指定退回到指定节点后是正常提交到下一节点，还是提交到退回的节点
修复当子流程于主流程共用同一个 traceNo（业务主键号）时，流程审批历史时间轴不能从主流程中正确查看子流程审批历史时间轴的问题。
支持流程设计器模拟测试和流程审批工作台弹层选择“退回到主流程指定节点”的意见类型，并支持从主流程历史中选择节点
支持流程底层流转方式“退回到主流程指定节点”，支持流程中间层对子流程跳转主流程的逻辑处理
支持当子流程退回到主流程，子流程实例销毁后，再次提交到子流程时，查看审批历史时间轴仍然可以看到以前审批过的子流程历史记录
修复子流程退回主流程后，因子流程实例销毁导致审批历史工作台无法正常查看的问题
修复子流程退回主流程后，因子流程实例销毁导致查看流程图时，连线信息获取不到问题
优化审批意见类型定义方式，将字典项改为枚举类，减少开发人员额外配置

#### 4.1.8

流程模型复制接口返回数据格式添加模型相关信息  
添加业务流程回退到指定业务节点的 feign 接口  
添加流程设计器中选择处理人可选择“某节点处理人的上级机构”，并添加流程引擎对相对上级机构的处理人选择策略  
修复流程引擎注册中心版 feign 接口拦截器与单机版 token 认证拦截器冲突的问题  
修复流程引擎待办已办工作台在人大金仓数据库下的字段大小写转换问题  
支持新版 IDM（审批人员组织架构）模块在表单设计器中的选择审批用户弹层组件  
修复子流程审批和业务流审批不支持人大金仓数据的问题  
支持流程设计器中模拟测试功能可以模拟测试业务流程，优化模拟过程，业务流程无需选择处理人即可运行

#### 4.1.0

支持流程设计器中流程模拟测试业务流程  
优化审批历史时间轴业务流查看时的处理人名称

#### 3.1.1

修复流程审批后待办任务数量未刷新的问题  
支持 OA 流程领导的批量审批

#### 2.2.1

修复流程审批时自定义业务字段大小写问题
