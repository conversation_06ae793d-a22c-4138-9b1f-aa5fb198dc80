<?xml version="1.0" encoding="UTF-8"?>
<classes>
	<class name="com.dhcc.funclib.FunctionLibUtil" desc="规则引擎函数库">
		<method name="concat" return="String" multiple="true" desc="拼接字符串函数,用于拼接字符串" title="拼接字符串">
		</method>
		<method name="format" return="String" multiple="false" desc="数据格式化函数,用于格式化数据" title="数据格式化">
			<params>
				<param type="String" desc="需要格式化的数据类型"></param>
				<param type="Object" desc="需要格式化的数据"></param>
			</params>
		</method>
		<method name="arrayMinimum" return="Double" multiple="false" desc="字符串数组最小值(如'3,1,2'返回1)" title="字符串数组最小值(如'3,1,2'返回1)">
			<params>
				<param type="String" desc="字符串数组(如'3,1,2')"></param>
			</params>
		</method>
		<method name="subRule" return="Object" multiple="false" desc="子调用规则('xxx',ruleInfo)" title="子调用规则">
			<params>
				<param type="String" desc="规则编号"></param>
				<param type="Object" desc="固定参数ruleInfo"></param>
			</params>
		</method>
	</class>
	
	<class name="com.dhcc.funclib.RepayPlanUtil" desc="利息计算函数库">
	
	</class>
	
	<class name="com.dhcc.funclib.RepayPlanForMftcc" desc="还款计划函数库">
		<method name="calNextPayDate" return="String" multiple="false" desc="计算每期的到期日" title="计算每期的到期日">
			<params>
				<param type="String" desc="开始日期"></param>
				<param type="String" desc="结束日期"></param>
				<param type="Integer" desc="还款日"></param>
				<param type="String" desc="固定还款日还款方式"></param>
				<param type="String" desc="上期到期日"></param>
			</params>
		</method>
		<method name="calAdequacyFlg" return="Integer" multiple="false" desc="判断当期是否足期（足月）" title="判断当期是否足期（足月）">
			<params>
				<param type="String" desc="本期开始日期"></param>
				<param type="String" desc="本期结束日期"></param>
			</params>
		</method>
		<method name="getLoanTerm" return="Integer" multiple="false" desc="计算期供期数" title="计算期供期数">
			<params>
				<param type="String" desc="开始日期"></param>
				<param type="String" desc="结束日期"></param>
				<param type="String" desc="还款日方式"></param>
				<param type="String" desc="固定还款日还款方式"></param>
				<param type="Integer" desc="固定还款日"></param>
			</params>
		</method>
		<method name="getNormInstmAmt" return="Double" multiple="false" desc="计算等额本息期供" title="计算等额本息期供">
			<params>
				<param type="Double" desc="贷款金额"></param>
				<param type="Integer" desc="期供期数"></param>
				<param type="Double" desc="月利率"></param>
			</params>
		</method>
		<method name="ppmt" return="Double" multiple="false" desc="计算等额本息每期应还本金" title="计算等额本息每期应还本金">
			<params>
				<param type="Double" desc="每期利率"></param>
				<param type="Integer" desc="期数（第几期）"></param>
				<param type="Integer" desc="总期数"></param>
				<param type="Double" desc="本金"></param>
			</params>
		</method>
		<method name="ipmt" return="Double" multiple="false" desc="计算等额本息每期应还利息" title="计算等额本息每期应还利息">
			<params>
				<param type="Double" desc="每期利率"></param>
				<param type="Integer" desc="期数（第几期）"></param>
				<param type="Integer" desc="总期数"></param>
				<param type="Double" desc="本金"></param>
			</params>
		</method>
		<method name="getResultJson" return="String" multiple="false" desc="组装结果" title="组装结果">
			<params>
				<param type="String" desc="返回值"></param>
				<param type="String" desc="结束日期"></param>
				<param type="Integer" desc="每期期数"></param>
				<param type="String" desc="每期开始日期"></param>
				<param type="String" desc="每期结束日期"></param>
				<param type="Double" desc="每期应还本金"></param>
				<param type="Double" desc="每期应还利息"></param>
				<param type="Double" desc="每期还款总额"></param>
				<param type="Double" desc="每期本金余额"></param>
			</params>
		</method>
		<method name="shareBalanceToPerCapitalForKing" return="String" multiple="false" desc="将最后一期本金余额分摊到每一期的还款本金中" title="将最后一期本金余额分摊到每一期的还款本金中">
			<params>
				<param type="String" desc="返回结果"></param>
				<param type="Integer" desc="小数位数"></param>
			</params>
		</method>
		<method name="shareRetainageInterestToPerForHm" return="String" multiple="false" desc="将尾款利息分摊到除最后一期的每一期的还款利息中(海马财务)" title="将尾款利息分摊到除最后一期的每一期的还款利息中(海马财务)">
			<params>
				<param type="String" desc="返回结果"></param>
				<param type="Double" desc="尾款利息"></param>
				<param type="Integer" desc="小数位数"></param>
			</params>
		</method>
		<method name="sumInterestToFirstPeriod" return="String" multiple="false" desc="将每期利息合计到第一期的应还利息中" title="将每期利息合计到第一期的应还利息中">
			<params>
				<param type="String" desc="返回结果"></param>
				<param type="Integer" desc="小数位数"></param>
			</params>
		</method>
		<method name="getBetweenDays" return="Integer" multiple="false" desc="获取日期差（暂用）" title="获取日期差（暂用）">
			<params>
				<param type="String" desc="开始日期"></param>
				<param type="String" desc="结束日期"></param>
			</params>
		</method>
		<method name="getCurrentDay" return="Integer" multiple="false" desc="获取日期后两位" title="获取日期后两位">
			<params>
				<param type="String" desc="日期字符串"></param>
			</params>
		</method>
		<method name="setScale" return="Double" multiple="false" desc="四舍五入保留小数位数" title="四舍五入保留小数位数">
			<params>
				<param type="Integer" desc="小数位数"></param>
				<param type="Double" desc="原始数据"></param>
			</params>
		</method>
		<method name="setScaleUp" return="Double" multiple="false" desc="保留小数位数,远离零方向舍入" title="保留小数位数,远离零方向舍入">
			<params>
				<param type="Integer" desc="小数位数"></param>
				<param type="Double" desc="原始数据"></param>
			</params>
		</method>
		<method name="setScaleDown" return="Double" multiple="false" desc="保留小数位数,向零方向舍入" title="保留小数位数,向零方向舍入">
			<params>
				<param type="Integer" desc="小数位数"></param>
				<param type="Double" desc="原始数据"></param>
			</params>
		</method>
		<method name="isLoanDayEndOfMonth" return="Integer" multiple="false" desc="判断日期是否是月末" title="判断日期是否是月末">
			<params>
				<param type="String" desc="日期字符串"></param>
			</params>
		</method>
		<method name="getPeriods4CycleDaysRepay" return="Integer" multiple="false" desc="获取按周期天数还款期供期数" title="获取按周期天数还款期供期数">
			<params>
				<param type="String" desc="开始日期"></param>
				<param type="String" desc="结束日期"></param>
				<param type="Integer" desc="还款周期（天）"></param>
			</params>
		</method>
		<method name="getPeriodEndDate4CycleDaysRepay" return="String" multiple="false" desc="获取按周期天数还款每期到期日期" title="获取按周期天数还款每期到期日期">
			<params>
				<param type="String" desc="上期到期日期"></param>
				<param type="Integer" desc="还款周期天数"></param>
				<param type="String" desc="结束日期"></param>
			</params>
		</method>
		<method name="isSeasonMonth" return="Integer" multiple="false" desc="判断日期是否是自然季月（3、6、9、12）" title="判断日期是否是自然季月（3、6、9、12）">
			<params>
				<param type="String" desc="日期字符串"></param>
			</params>
		</method>
		<method name="getDateAfterMonths" return="String" multiple="false" desc="获取某个日期N个月后的日期（yyyyMMdd）" title="获取某个日期N个月后的日期（yyyyMMdd）">
			<params>
				<param type="String" desc="开始日期"></param>
				<param type="Integer" desc="月数"></param>
			</params>
		</method>
		<method name="getCurrentDate" return="String" multiple="false" desc="获取系统当前日期（yyyyMMdd）" title="获取系统当前日期（yyyyMMdd）">
			<params>
				<param type="String" desc="系统当前日期（yyyyMMdd）"></param>
			</params>
		</method>
		<method name="getPeriods4Sun" return="Integer" multiple="false" desc="按揭还款获取期供期数（阳光村镇银行）" title="按揭还款获取期供期数（阳光村镇银行）">
			<params>
				<param type="String" desc="开始日期"></param>
				<param type="String" desc="结束日期"></param>
				<param type="Integer" desc="固定还款日"></param>
			</params>
		</method>
		<method name="getPeriodEndDate4Sun" return="String" multiple="false" desc="按揭还款获取每期结束日期（阳光村镇银行）" title="按揭还款获取每期结束日期（阳光村镇银行）">
			<params>
				<param type="String" desc="上期到期日期"></param>
				<param type="Integer" desc="固定还款日"></param>
				<param type="String" desc="结束日期"></param>
			</params>
		</method>
	</class>
	
	<class name="com.dhcc.funclib.GuaranteeSystemCostUtil" desc="担保系统费用收取函数库">
		<method name="getPeriods" return="Integer" multiple="false" desc="获取期数" title="获取期数">
			<params>
				<param type="String" desc="担保合同开始日期"></param>
				<param type="String" desc="担保合同结束日期"></param>
				<param type="String" desc="收取方式"></param>
			</params>
		</method>
		<method name="getPeriodEndDate" return="String" multiple="false" desc="获取每期结束日期" title="获取每期结束日期">
			<params>
				<param type="String" desc="上期结束日期"></param>
				<param type="String" desc="收取方式"></param>
				<param type="String" desc="担保合同结束日期"></param>
			</params>
		</method>
		<method name="getResultJson" return="String" multiple="false" desc="组装结果" title="组装结果">
			<params>
				<param type="String" desc="上期结束日期"></param>
				<param type="String" desc="担保合同结束日期"></param>
				<param type="Integer" desc="编号"></param>
				<param type="String" desc="每期开始日期"></param>
				<param type="String" desc="每期结束日期"></param>
				<param type="String" desc="每期应收款日期"></param>
				<param type="Double" desc="费用"></param>
			</params>
		</method>
		<method name="getResultJsonByByRepayPeriods" return="String" multiple="false" desc="组装结果（按还款计划期数）" title="组装结果（按还款计划期数）">
			<params>
				<param type="String" desc="上期结束日期"></param>
				<param type="Integer" desc="还款计划期数"></param>
				<param type="Integer" desc="编号"></param>
				<param type="Double" desc="费用"></param>
			</params>
		</method>
		<method name="setScale" return="Double" multiple="false" desc="四舍五入保留小数位数" title="四舍五入保留小数位数">
			<params>
				<param type="Integer" desc="小数位数"></param>
				<param type="Double" desc="原始数据"></param>
			</params>
		</method>
		<method name="getPeriods4Month" return="Integer" multiple="false" desc="获取期数（月）" title="获取期数（月）">
			<params>
				<param type="String" desc="开始日期"></param>
				<param type="String" desc="结束日期"></param>
			</params>
		</method>
		<method name="getBetweenDays" return="Integer" multiple="false" desc="获取两日期之间天数" title="获取两日期之间天数">
			<params>
				<param type="String" desc="开始日期"></param>
				<param type="String" desc="结束日期"></param>
			</params>
		</method>
	</class>
	
	<class name="com.dhcc.funclib.Arith" desc="加减乘除，四舍五入等运算函数库">
		<method name="add" return="Double" multiple="false" desc="提供精确的加法运算" title="加法">
			<params>
				<param type="Double" desc="被加数"></param>
				<param type="Double" desc="加数"></param>
			</params>
		</method>
		<method name="sub" return="Double" multiple="false" desc="提供精确的减法运算" title="减法">
			<params>
				<param type="Double" desc="被减数"></param>
				<param type="Double" desc="减数"></param>
			</params>
		</method>
		<method name="mul" return="Double" multiple="false" desc="提供精确的乘法运算" title="乘法">
			<params>
				<param type="Double" desc="被乘数"></param>
				<param type="Double" desc="乘数"></param>
			</params>
		</method>
		<method name="div" return="Double" multiple="false" desc="提供（相对）精确的除法运算，当发生除不尽的情况时，精确到小数点以后10位，以后的数字四舍五入" title="除法（精确到小数点后10位）">
			<params>
				<param type="Double" desc="被除数"></param>
				<param type="Double" desc="除数"></param>
			</params>
		</method>
		<method name="divByScale" return="Double" multiple="false" desc="提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指定精度，以后的数字四舍五入" title="除法（由scale参数指定精度）">
			<params>
				<param type="Double" desc="被除数"></param>
				<param type="Double" desc="除数"></param>
				<param type="Integer" desc="表示需要精确到小数点以后几位"></param>
			</params>
		</method>
		<method name="round" return="Double" multiple="false" desc="提供精确的小数位四舍五入处理" title="四舍五入">
			<params>
				<param type="Double" desc="需要四舍五入的数字"></param>
				<param type="Integer" desc="小数点后保留几位"></param>
			</params>
		</method>
		<method name="roundUp" return="Double" multiple="false" desc="提供精确的小数位舍入处理，远离零方向舍入" title="远离零方向舍入">
			<params>
				<param type="Double" desc="需要舍入的数字"></param>
				<param type="Integer" desc="小数点后保留几位"></param>
			</params>
		</method>
		<method name="roundDown" return="Double" multiple="false" desc="提供精确的小数位舍入处理，向零方向舍入" title="向零方向舍入">
			<params>
				<param type="Double" desc="需要舍入的数字"></param>
				<param type="Integer" desc="小数点后保留几位"></param>
			</params>
		</method>
		<method name="average" return="Double" multiple="true" desc="提供（相对）精确的计算平均数运算。当发生除不尽的情况时，精确到小数点以后10位，以后的数字四舍五入。" title="计算平均数，参数用,号分割（如10.1, 20.2, 30.3）">
		</method>
	</class>
	
	<class name="com.dhcc.funclib.WarningMessageUtil" desc="供应链预警消息函数库">
		<method name="getNewDateByAddDays" return="Integer" multiple="false" desc="日期+N天后的新日期（贷后）" title="日期+N天后的新日期（贷后）">
			<params>
				<param type="Integer" desc="旧日期"></param>
				<param type="Integer" desc="天数"></param>
			</params>
		</method>
		
		<method name="getNewDateByDecreaseDays" return="Integer" multiple="false" desc="日期-N天后的新日期（贷后）" title="日期-N天后的新日期（贷后）">
			<params>
				<param type="Integer" desc="旧日期"></param>
				<param type="Integer" desc="天数"></param>
			</params>
		</method>
		
		<method name="compareDays" return="Integer" multiple="false" desc="比较日期与日是否相等（贷后）" title="比较日期与日是否相等（贷后）">
			<params>
				<param type="Integer" desc="日期"></param>
				<param type="Integer" desc="日"></param>
			</params>
		</method>
		
		<method name="cusExpireWarn" return="int" multiple="false" desc="到期预警判断（贷后）" title="到期预警判断（贷后）">
			<params>
				<param type="String" desc="当前日期"></param>
				<param type="String" desc="到期日期/应还款日期"></param>
				<param type="int" desc="提前预警天数"></param>
				<param type="int" desc="发送频率"></param>
				<param type="String" desc="频率单位"></param>
				<param type="int" desc="发送次数"></param>
			</params>
		</method>
		
		<method name="cusOverdueWarn" return="int" multiple="false" desc="逾期预警判断（贷后）" title="逾期预警判断（贷后）">
			<params>
				<param type="String" desc="当前日期"></param>
				<param type="String" desc="到期日期/应还款日期"></param>
				<param type="int" desc="提前预警天数"></param>
				<param type="int" desc="发送频率"></param>
				<param type="String" desc="频率单位"></param>
			</params>
		</method>
		
		<method name="getBeforeDaysUntilQuarter" return="int" multiple="false" desc="计算当前系统日期距离3、6、9、12四个季度末日期的天数（贷后）" title="距离季末日期天数（贷后）">
			<params>
				<param type="String" desc="当前日期"></param>
			</params>
		</method>
		<method name="getDateBeforeDaysQuarter" return="int" multiple="false" desc="计算当前系统日期距离3、6、9、12四个季度末日期N天数的日期" title="距离季末N天数日期（贷后）">
			<params>
				<param type="int" desc="当前日期"></param>
				<param type="int" desc="天数"></param>
			</params>
		</method>
		<method name="getFixDateByDay" return="int" multiple="false" desc="获取固定日期" title="固定日期">
			<params>
				<param type="int" desc="当前日期"></param>
				<param type="int" desc="日期（几号）"></param>
			</params>
		</method>
		
		<method name="determineExpireWarn" return="int" multiple="false" desc="到期预警判断（押品）" title="到期预警判断（押品）">
			<params>
				<param type="String" desc="当前日期"></param>
				<param type="String" desc="到期日期（失效日期）"></param>
				<param type="int" desc="提前预警天数"></param>
				<param type="int" desc="发送频率"></param>
				<param type="String" desc="频率单位"></param>
				<param type="int" desc="发送次数"></param>
			</params>
		</method>
		<method name="determineOverdueWarn" return="int" multiple="false" desc="逾期预警判断（押品）" title="逾期预警判断（押品）">
			<params>
				<param type="String" desc="当前日期"></param>
				<param type="String" desc="到期日期（失效日期）"></param>
				<param type="int" desc="逾期预警天数"></param>
				<param type="int" desc="发送频率"></param>
				<param type="String" desc="频率单位"></param>
				<param type="int" desc="发送次数"></param>
			</params>
		</method>
		<method name="determineValueWaveWarn" return="int" multiple="false" desc="押品价值波动预警（押品）" title="押品价值波动预警（押品）">
			<params>
				<param type="String" desc="波动类型"></param>
				<param type="double" desc="首次评估值"></param>
				<param type="double" desc="本次评估值"></param>
				<param type="double" desc="波动警戒值"></param>
				<param type="double" desc="波动警戒比"></param>
			</params>
		</method>
		<method name="determineEvalInfoWarn" return="int" multiple="false" desc="判断重估预警（押品）" title="判断重估预警（押品）">
			<params>
				<param type="String" desc="当前日期"></param>
				<param type="String" desc="到期日期（失效日期）"></param>
				<param type="int" desc="发送频率"></param>
				<param type="String" desc="频率单位"></param>
			</params>
		</method>
		
	</class>
	<class name="com.dhcc.funclib.NumberRulesUtil" desc="编号规则函数库">
		<method name="getValue" return="String" multiple="false" desc="获取流水号" title="获取流水号">
			<params>
				<param type="String" desc="key值"></param>
			</params>
		</method>
		<method name="getSerialNum" return="String" multiple="false" desc="获取补位流水号（不足位数前面补0）" title="获取补位流水号（不足位数前面补0）">
			<params>
				<param type="int" desc="流水号长度"></param>
				<param type="String" desc="流水号value"></param>
			</params>
		</method>
		<method name="getNumSubOne" return="String" multiple="false" desc="获取编号减1的值" title="获取编号减1的值">
			<params>
				<param type="String" desc="编号"></param>
			</params>
		</method>
	</class>
</classes>
