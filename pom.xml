<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright © 2020 北京微金时代科技有限公司
    <EMAIL>

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.mftcc</groupId>
        <artifactId>mftcc-sys-common</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <groupId>cn.mftcc</groupId>
    <artifactId>mftcc-doc-server</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>mftcc-doc-server</name>

    <dependencies>

        <!--feign调用api-->
        <dependency>
            <groupId>cn.mftcc</groupId>
            <artifactId>mftcc-feign-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>seata-spring-boot-starter</artifactId>
                    <groupId>io.seata</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--common引用-->
        <dependency>
            <groupId>cn.mftcc</groupId>
            <artifactId>common-base</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.mftcc</groupId>
            <artifactId>mftcc-biz-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>seata-spring-boot-starter</artifactId>
                    <groupId>io.seata</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--Spring Cloud Function functionRouter SPEL代码执行漏洞（CVE-2022-22963）-->
        <dependency>
            <groupId>cn.mftcc</groupId>
            <artifactId>mftcc-boot-starter-rabbitmq</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-function-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-function-core</artifactId>
            <version>3.1.7</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
            <scope>compile</scope>
        </dependency>
        <!--  pdf转换成图片-->
        <!--<dependency>-->
            <!--<groupId>org.icepdf.os</groupId>-->
            <!--<artifactId>icepdf-core</artifactId>-->
            <!--<version>6.1.2</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.icepdf.os</groupId>
            <artifactId>icepdf-core</artifactId>
            <version>6.1.2</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/main/resources/lib/icepdf-core-6.1.2.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>
        <!-- 访问html文件依赖begin -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- 访问html文件依赖end-->
        <!-- webOffic需要的依赖 begin -->

        <dependency>
            <groupId>com.corundumstudio.socketio</groupId>
            <artifactId>netty-socketio</artifactId>
            <version>1.7.18</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.5.7</version>
        </dependency>
        <!-- word 标签解析渲染 start -->
        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.9.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.3</version>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>

        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib-ext-spring</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>${micrometer.version}</version>
        </dependency>
        <!--<dependency>-->
            <!--<groupId>com.qiyuesuo.sdk</groupId>-->
            <!--<artifactId>sdk-java</artifactId>-->
            <!--<version>3.4.0</version>-->
            <!--<scope>compile</scope>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>2.2.10.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.15.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.3.6</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

	<build>
        <finalName>mftcc-doc-server</finalName>
        <plugins>
			<plugin>
				<!--打包时去除第三方依赖-->
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<layout>ZIP</layout>
					<includes>
						<include>
							<groupId>non-exists</groupId>
							<artifactId>non-exists</artifactId>
						</include>
						<include>
							<groupId>cn.mftcc</groupId>
							<artifactId>mftcc-feign-api</artifactId>
						</include>
						<include>
							<groupId>cn.mftcc</groupId>
							<artifactId>common-base</artifactId>
						</include>
						<include>
							<groupId>cn.mftcc</groupId>
							<artifactId>mftcc-biz-common</artifactId>
						</include>
					</includes>
				</configuration>
			</plugin>
			<!--拷贝第三方依赖文件到指定目录-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<!--target/lib是依赖jar包的输出目录，根据自己喜好配置-->
							<outputDirectory>target/lib</outputDirectory>
							<excludeTransitive>false</excludeTransitive>
							<stripVersion>false</stripVersion>
							<includeScope>runtime</includeScope>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
    </build>

</project>
