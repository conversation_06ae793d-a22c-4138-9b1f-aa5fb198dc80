#!/usr/bin/env groovy
def dockerName = "mftcc/mftcc-doc-server-prod"
node {
    stage('checkout') {
        checkout scm
    }

    stage('check java') {
        sh "java -version"
    }

    stage('clean') {
        sh "chmod +x mvnw"
        sh "./mvnw clean"
    }

    stage('packaging') {
        sh "./mvnw -pl . -am package -DskipTests"
        archiveArtifacts artifacts: '**/target/*.jar', fingerprint: true
    }


    def dockerImage
    stage('build docker') {
        sh "cp -R src/main/docker target/"
        sh "cp target/*.jar target/docker/"
        dockerImage = docker.build(dockerName, 'target/docker')
    }

    stage('publish docker') {
        docker.withRegistry('https://repo.ca-sinfusi.com:5000', 'docker-login') {
            dockerImage.push 'latest'
        }
    }

    stage('run kubernet') {
        sh "/volumes01/script/deploy/prod_deploy.sh '" + dockerName + "'"
    }
}
