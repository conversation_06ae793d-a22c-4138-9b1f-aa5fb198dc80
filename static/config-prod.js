const config = {
  host: {
    //访问后台流程IP地址
    gateway_path: "http://127.0.0.1:7019",
    /* 单点登录服务地址 */
    sso_server: "http://127.0.0.1:8099/oauth/login",
    /* 服务标识 */
    sso_client_id: "inf-flowable",
    /* 是否启用单点登录 */
    sso_sts: false
  },
  // 系统设置
  sys_setting:{
    //登录图片
    login_img: "/fh-web/static/images/login/login.png",
    //登录背景
    login_bg: "/fh-web/static/images/login/login_bg.png",
    //左上角logo
    logo_img: "/fh-web/static/images/header/logo_yellow_wj.png",
    //左上角描述
    logo_title: "/fh-web/static/images/header/logo_title.png",
    //系统名称
    system_name: "鸿云系统",
    //版权信息
    copyright_information: "版权所有 © 2015 XXXXXXXX有限公司 ©京lCP备******** 公司客服电话：400-007-9200",
    // 登录默认方式 账号/微信扫码 true-账号；false-微信扫码
    login_account_way: true,
    // 微信扫码appId
    wx_appId: "wx62b7631304a20db8",
    // 微信扫码重定向地址
    redirect_uri: "http://hongyun.fh-lease.com/fh-web/login",
    loginUri: "https://gateway-uat.fh-lease.com/fh-web/login",
  },
  //表单配置文件地址---页面展现时读取表单配置文件（无需关心）
  form_profile_path: "/fh-web/static/data",
  //路由白名单---不会验证token不会被拦截
  router_white_list: ["/login", "/getLoginData", "/dev/main", "/mobileLogin", "/order_singleLogin", "/appletSigned"],
  //api中的后台服务名称配置; 用法： $servers.xxx
  servers: {
    flowable: "mftcc-flowable-server", //流程后台
    sys: "mftcc-sys-server", //登录后台
    rule: "mftcc-rule-server", //规则引擎
    pledge: "mftcc-pledge-server", //押品
    doc: "mftcc-doc-server", //文件
    cus: "mftcc-cus-server", //客户
    config: "mftcc-config-server", //基础配置
    product: "mftcc-product-server", //产品
    lease: "mftcc-lease-server", //租赁
    calc: "mftcc-calc-server" , //测算
    group: "mftcc-group-server", //集团管控
    risk: "mftcc-risk-server", //风控
    credit: "mftcc-credit-server", //授信
    elink: "mftcc-elink-server", //通讯
    archive: "mftcc-archive-server", //归档
    loan: "mftcc-loan-server", //信贷
    factor: "mftcc-factor-server", //保理
    guarantee: "mftcc-guarantee-server", //担保
    investment: "mftcc-investment-server", //投资
    fund: "mftcc-fund-server", //基金
    msg: "mftcc-msg-server", //消息
    after: "mftcc-after-server", //贷后
  },
  //表单设计器所需要访问的后台项目（无需关心）
  designer_server: "mftcc-flowable-server",
  //首页路由地址
  index_router: "/home/<USER>",
  //待办路由地址
  approve_router: "/toDoflowable/index",
  //大屏路由地址
  report_router: "/home/<USER>",
  //首页是否显示菜单
  index_menu_show: true,
  //数据传输是否加密
  data_crypto: false,
  //数据传输是否验签
  data_verify_sign: false,
  //sessionStorage标识（默认为mftcc_vuex）
  session_storage_key: "flowable",
  //是否显示表单/列表的title(默认：true)
  show_title: true,
  //是否启用水印(默认：false)
  watermark: false,
  //开启登录页面滑块验证或图片验证码验证0-不开启；1-开启滑块验证；2-开启图片验证码验证
  loginCheckType:"0",
  //主题
  theme: "blue",
};

(function (window) {
  window.config = config;
})(this);
