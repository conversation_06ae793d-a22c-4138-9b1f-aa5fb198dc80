/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.common.multids;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.guarantee.feign.api.GuaranteeInterface;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.List;
import java.util.Map;

@Component
public class MultiDataSourceManage {

    @Autowired
    private MultiDataSourceBean multiDataSourceBean;
    /**
     * 获取指定数据库的Connection
     * @param dateBase
     * @return
     * @throws ServiceException
     */
    public  Connection getCusConnection(String dateBase)throws ServiceException {
        Connection conn = null ;
        Map<String, Map<String,String>> setting=multiDataSourceBean.getSetting();
        try {
            Map<String,String>connMap=setting.get(dateBase);
            conn=getConfigConn(connMap);
            return conn;

        } catch (Exception e) {
            MFLogger.error("获取数据库:"+dateBase+"的Connection",e);
            throw new ServiceException("获取指定数据库的Connection",dateBase,e);
        }

    }

    /**
     * ---------------------------------------------------------------------本地以及测试配置------------------------------------------------------
     */
    /**
     * 获得连接
     */
    public Connection getConfigConn(Map<String,String>connInfMap) throws Exception {
        Connection conn = null;
        try {

            Class.forName(connInfMap.get("driverClassName"));
            conn = DriverManager.getConnection(connInfMap.get("url"), connInfMap.get("username"), connInfMap.get("password"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return conn;
    }


    /**
     * 获取查询sql的结果集 带着connect
     * @param sql  查询sql
     * @param connection 数据库连接
     * @param paramAry 参数列表
     * @return
     * @throws ServiceException
     */
    public JSONArray getQueryResultWithConn(String sql,Connection connection,String paramAry[])throws  ServiceException{
        JSONArray result=new JSONArray();
        PreparedStatement ps=null;
        ResultSet resultSet=null;
        try{
            ps=connection.prepareStatement(sql);
            if(paramAry!=null){
                int len=paramAry.length;
                if(len>0){
                    for(int i=0;i<len;i++){
                        ps.setString(i+1,paramAry[i]);
                    }
                }
            }
            resultSet=ps.getResultSet();
            ResultSetMetaData rsmd=resultSet.getMetaData();
            int cn=rsmd.getColumnCount();
            //获取查询的列名
            String colNames[]=new String[cn];
            for(int ik=1;ik<=rsmd.getColumnCount();ik++)
            {
                colNames[ik-1]=rsmd.getColumnName(ik);
            }
            //开始封装数据
            while (resultSet.next()) {
                JSONObject jsonObj=new JSONObject();
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    jsonObj.put(colNames[i],resultSet.getString(i+1));
                }
                result.add(jsonObj);
            }

        }catch (Exception e){
            MFLogger.error("获取查询sql的结果集",e);
            throw new ServiceException("获取查询sql的结果集",sql,e);
        }finally {
            try{

                if(resultSet!=null){
                    resultSet.close();
                }
                if(ps!=null){
                    ps.close();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        return result;
    }


    /**
     * 获取查询sql的结果集
     * @param sql
     * @param dataBase
     * @param paramAry
     * @return
     * @throws ServiceException
     */
    public JSONArray getQueryResult(String sql,String dataBase,String paramAry[])throws  ServiceException{
        JSONArray result=new JSONArray();
        Connection connection=null;
        PreparedStatement ps=null;
        ResultSet resultSet=null;
        try{
            connection=getCusConnection(dataBase);
            ps=connection.prepareStatement(sql);
            if(paramAry!=null){
                int len=paramAry.length;
                if(len>0){
                    for(int i=0;i<len;i++){
                        ps.setString(i+1,paramAry[i]);
                    }
                }
            }
            resultSet=ps.executeQuery();
            ResultSetMetaData rsmd=resultSet.getMetaData();
            int cn=rsmd.getColumnCount();
            //获取查询的列名
            String colNames[]=new String[cn];
            for(int ik=1;ik<=rsmd.getColumnCount();ik++)
            {
                colNames[ik-1]=rsmd.getColumnName(ik);
            }
            //开始封装数据
            while (resultSet.next()) {
                JSONObject jsonObj=new JSONObject();
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    jsonObj.put(colNames[i],resultSet.getString(i+1));
                }
                result.add(jsonObj);
            }

        }catch (Exception e){
            MFLogger.error("获取查询sql的结果集",e);
            throw new ServiceException("获取查询sql的结果集",sql,e);
        }finally {
            try{

                if(resultSet!=null){
                    resultSet.close();
                }
                if(ps!=null){
                    ps.close();
                }
                if(connection!=null){
                    connection.close();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        return result;
    }
    /**
     * 获取查询sql的结果实体
     * @param sql
     * @param dataBase
     * @param paramAry
     * @return
     * @throws ServiceException
     */
    public JSONObject getQueryObject(String sql,String dataBase,String paramAry[])throws  ServiceException{
        JSONObject result=new JSONObject();
        Connection connection=null;
        PreparedStatement ps=null;
        ResultSet resultSet=null;
        try{
            MFLogger.info("执行sql:",sql);
            MFLogger.info("参数：", JSON.toJSONString(paramAry));
            connection=getCusConnection(dataBase);
            ps=connection.prepareStatement(sql);
            if(sql.contains("?")){

                if(paramAry!=null){
                    int len=paramAry.length;
                    if(len>0){
                        for(int i=0;i<len;i++){
                            ps.setString(i+1,paramAry[i]);
                        }
                    }
                }
            }
            resultSet=ps.executeQuery();
            ResultSetMetaData rsmd=resultSet.getMetaData();
            int cn=rsmd.getColumnCount();
            //获取查询的列名
            String colNames[]=new String[cn];
            for(int ik=1;ik<=rsmd.getColumnCount();ik++)
            {
                colNames[ik-1]=rsmd.getColumnLabel(ik);
            }
            //开始封装数据
            if (resultSet.next()) {

                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    result.put(colNames[i],resultSet.getString(i+1));
                }

            }

        }catch (Exception e){
            MFLogger.error("获取查询sql的结果集",e);
              throw new ServiceException("获取查询sql的结果集","sql="+sql +"参数："+ JSON.toJSONString(paramAry),e);
        }finally {
            try{

                if(resultSet!=null){
                    resultSet.close();
                }
                if(ps!=null){
                    ps.close();
                }
                if(connection!=null){
                    connection.close();
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        return result;
    }


}
