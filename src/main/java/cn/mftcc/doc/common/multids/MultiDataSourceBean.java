/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.common.multids;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
@Data
@Component
@ConfigurationProperties(prefix ="multids")
public class MultiDataSourceBean {

   private  Map<String,Map<String,String>> setting;
}
