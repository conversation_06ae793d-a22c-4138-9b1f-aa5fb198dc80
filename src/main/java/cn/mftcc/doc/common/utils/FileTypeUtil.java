/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.common.utils;

import cn.mftcc.common.exception.ServiceException;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;

/**
 * 方法描述：获取文件类型
 *
 */

public final class FileTypeUtil {
	private static Map<String,String> FILE_TYPE_MAP = new HashMap<String,String>();
	static{
		getAllFileType();  //初始化文件类型信息
	}

	//常见文件头信息
	private static void getAllFileType(){
		//images
		FILE_TYPE_MAP.put("jpeg", "FFD8FF"); //JPEG
		FILE_TYPE_MAP.put("jpg", "FFD8FF"); //JPEG (jpg)
		FILE_TYPE_MAP.put("png", "89504E47");  //PNG (png)
		FILE_TYPE_MAP.put("gif", "47494638");  //GIF (gif)
		FILE_TYPE_MAP.put("tif", "49492A00");  //TIFF (tif)
		FILE_TYPE_MAP.put("bmp", "424D"); //Windows Bitmap (bmp)
		FILE_TYPE_MAP.put("jpe", "FFD8FF"); //JPE

		FILE_TYPE_MAP.put("xls", "D0CF11E0");  //MS Word
		FILE_TYPE_MAP.put("doc", "D0CF11E0");  //MS Excel 注意：word 和 excel的文件头一样
		FILE_TYPE_MAP.put("wps", "D0CF11E0");  //MS Excel 注意：word 和 excel的文件头一样
		FILE_TYPE_MAP.put("ppt", "D0CF11E0");  //ppt
		FILE_TYPE_MAP.put("pptx", "D0CF11E0");  //pptx
		FILE_TYPE_MAP.put("xlsx", "504B0304"); //docx  or  xlsx
		FILE_TYPE_MAP.put("docx", "504B0304"); //docx  or  xlsx
		FILE_TYPE_MAP.put("pdf", "255044462D312E");  //Adobe Acrobat (pdf)
		FILE_TYPE_MAP.put("ppt", "D0CF11E0");  //ppt
		FILE_TYPE_MAP.put("txt", "61575750");  //txt
		//压缩包
		FILE_TYPE_MAP.put("zip", "504B0304");
		FILE_TYPE_MAP.put("rar", "52617221");
		//视频或音频类
		FILE_TYPE_MAP.put("wav", "57415645");  //Wave (wav)
		FILE_TYPE_MAP.put("avi", "41564920");
		FILE_TYPE_MAP.put("rm", "2E524D46");  //Real Media (rm)
		FILE_TYPE_MAP.put("mpg", "000001BA");  //
		FILE_TYPE_MAP.put("asf", "3026B2758E66CF11"); //Windows Media (asf)
		FILE_TYPE_MAP.put("wmv", "3026B2758E66CF11"); //wmv
		FILE_TYPE_MAP.put("mp3", "494433030000000008");
		FILE_TYPE_MAP.put("mp3", "494433030000000008");
//		FILE_TYPE_MAP.put("mp4", "00000020667479706");


//pbm,rmvb,divx,mpeg,mpe,mkv,vob
//		FILE_TYPE_MAP.put("dwg", "41433130"); //CAD (dwg)
//		FILE_TYPE_MAP.put("html", "68746D6C3E");  //HTML (html)
//		FILE_TYPE_MAP.put("rtf", "7B5C727466");  //Rich Text Format (rtf)
//		FILE_TYPE_MAP.put("xml", "3C3F786D6C");
//		FILE_TYPE_MAP.put("psd", "38425053");  //Photoshop (psd)
//		FILE_TYPE_MAP.put("eml", "44656C69766572792D646174653A");  //Email [thorough only] (eml)
//		FILE_TYPE_MAP.put("dbx", "CFAD12FEC5FD746F");  //Outlook Express (dbx)
//		FILE_TYPE_MAP.put("pst", "2142444E");  //Outlook (pst)
//		FILE_TYPE_MAP.put("mdb", "5374616E64617264204A");  //MS Access (mdb)
//		FILE_TYPE_MAP.put("wpd", "FF575043"); //WordPerfect (wpd)
//		FILE_TYPE_MAP.put("eps", "252150532D41646F6265");
//		FILE_TYPE_MAP.put("ps", "252150532D41646F6265");
//		FILE_TYPE_MAP.put("qdf", "AC9EBD8F");  //Quicken (qdf)
//		FILE_TYPE_MAP.put("pwl", "E3828596");  //Windows Password (pwl)
//		FILE_TYPE_MAP.put("ram", "2E7261FD");  //Real Audio (ram)
//		FILE_TYPE_MAP.put("mov", "6D6F6F76");  //Quicktime (mov)
//		FILE_TYPE_MAP.put("mid", "4D546864");  //MIDI (mid)
	}

	public static boolean isImage(String fileType){
		Set<String> imgSet = new HashSet<>();
		imgSet.add("jpeg");
		imgSet.add("jpg");
		imgSet.add("png");
		imgSet.add("gif");
		imgSet.add("tif");
		imgSet.add("bmp");
		imgSet.add("jpe");
		if(imgSet.contains(fileType)){
			return true;
		}else {
			return false;
		}

	}


	/**
	 * @Description long转文件大小KB单位方法,保留兩位小數點
	 * @param bytes
	 * @return
	 */
	public static Double bytes2kb(long bytes) {
		BigDecimal filesize = new BigDecimal(bytes);
		BigDecimal megabyte = new BigDecimal(1024);
		double returnValue = filesize.divide(megabyte, 2, BigDecimal.ROUND_UP).doubleValue();
		BigDecimal b = new BigDecimal(returnValue);
		returnValue = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		return returnValue;
	}


	/**
	 *  base64转换为文件
	 * @param base64
	 * @param fileName
	 * @param savePath
	 * @throws ServiceException
	 */
	public static void base64ToFile(String base64, String fileName, String savePath)throws ServiceException {
		File file = null;
		//创建文件目录
		String filePath = savePath;
		File dir = new File(filePath);
		if (!dir.exists() && !dir.isDirectory()) {
			dir.mkdirs();
		}
		BufferedOutputStream bos = null;
		java.io.FileOutputStream fos = null;
		try {
			byte[] bytes = Base64.getDecoder().decode(base64);
			file = new File(filePath + fileName);
			fos = new java.io.FileOutputStream(file);
			bos = new BufferedOutputStream(fos);
			bos.write(bytes);
		} catch (Exception e) {
			throw  new ServiceException(" base64转换为文件"," base64转换为文件",e);
		} finally {
			if (bos != null) {
				try {
					bos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}




	/**
	 * <p>Discription:[getFileTypeByFile,获取文件类型,包括图片,若格式不是已配置的,则返回null]</p>
	 * @param file
	 * @return fileType
	 */
	public final static String getFileTypeByFile(File file){
		String filetype = null;
		byte[] b = new byte[50];
		try{
			InputStream is = new FileInputStream(file);
			is.read(b);
			filetype = getFileTypeByStream(b);
			is.close();
		}catch (FileNotFoundException e){
			e.printStackTrace();
		}catch (IOException e){
			e.printStackTrace();
		}
		return filetype;
	}
	/**
	 * <p>Discription:[getFileTypeByFile,获取文件类型,包括图片,若格式不是已配置的,则返回null]</p>
	 * @param is
	 * @return fileType
	 */
	public final static String getFileTypeByFile(InputStream is){
		String filetype = null;
		byte[] b = new byte[50];
		try{
			is.read(b);
			filetype = getFileTypeByStream(b);
			is.close();
		}catch (FileNotFoundException e){
			e.printStackTrace();
		}catch (IOException e){
			e.printStackTrace();
		}
		return filetype;
	}

	/**
	 * <p>Discription:[getFileTypeByStream]</p>
	 * @param b
	 * @return fileType
	 */
	public final static String getFileTypeByStream(byte[] b){
		String filetypeHex = String.valueOf(getFileHexString(b));
		if(filetypeHex.startsWith("00000000")){
			return "blank";//处理空文档
		}
		Iterator<Map.Entry<String, String>> entryiterator = FILE_TYPE_MAP.entrySet().iterator();
		while (entryiterator.hasNext()) {
			Map.Entry<String,String> entry =  entryiterator.next();
			String fileTypeHexValue = entry.getValue();
			if (filetypeHex.toUpperCase().startsWith(fileTypeHexValue)) {
				return entry.getKey();
			}
		}
		return null;
	}

	/**
	 * <p>Discription:[getFileHexString]</p>
	 * @param b
	 * @return fileTypeHex
	 */
	public final static String getFileHexString(byte[] b){
		StringBuilder stringBuilder = new StringBuilder();
		if (b == null || b.length <= 0){
			return null;
		}
		for (int i = 0; i < b.length; i++){
			int v = b[i] & 0xFF;
			String hv = Integer.toHexString(v);
			if (hv.length() < 2){
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString();
	}




	/**
	 * <p>Discription:[isImage,判断文件是否为图片]</p>
	 * @param inputStream
	 * @return true 是 | false 否
	 */
	public static final boolean isImage(InputStream inputStream){
		boolean flag = false;
		try{
			BufferedImage bufreader = ImageIO.read(inputStream);
			int width = bufreader.getWidth();
			int height = bufreader.getHeight();
			if(width==0 || height==0){
				flag = false;
			}else {
				flag = true;
			}
		}catch (IOException e){
			flag = false;
		}catch (Exception e) {
			flag = false;
		}
		return flag;
	}
}