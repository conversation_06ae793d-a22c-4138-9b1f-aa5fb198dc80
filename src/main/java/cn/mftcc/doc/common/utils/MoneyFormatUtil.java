/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.common.utils;

import cn.mftcc.common.exception.ServiceException;

import java.rmi.ServerException;
import java.text.DecimalFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MoneyFormatUtil {
    private static final double MAXIMUM_NUMBER = 9999999999d;
    private static final String CN_ZERO = "零";
    private static final String CN_DOLLAR = "圆";
    private static final String CN_INTEGER = "整";
    private static final String[] digits = new String[] { "零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖" };
    private static final String[] radices = new String[] { "", "拾", "佰", "仟" };
    private static final String[] bigRadices = new String[] { "", "万", "亿", "万" };
    private static final String[] decimals = new String[] { "角", "分" };// 判断小数点后2位的数字的正则表达式
    public static final Pattern MONEY_PATTERN =Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");


    /**
     * 检查输入数字的合法性
     *
     * @param currencyDigits
     * @return
     */
    private static boolean check(final String currencyDigits) {
        if (currencyDigits == null || currencyDigits.trim().equals("")) {
            System.out.println("没有输入要转换的数字");
            return false;
        }

        Pattern pattern = Pattern.compile("/[^,.\\d]/", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(currencyDigits);

        if (matcher.find()) {
            System.out.println("数字中含有非法字符!");
            return false;
        }

        pattern = Pattern.compile("/^((\\d{1,3}(,\\d{3})*(.((\\d{3},)*\\d{1,3}))?)|(\\d+(.\\d+)?))$/", Pattern.CASE_INSENSITIVE);
        matcher = pattern.matcher(currencyDigits);
        if (matcher.find()) {
            System.out.println("错误的数字格式!");
            return false;
        }

        if (Double.parseDouble(currencyDigits) > MAXIMUM_NUMBER) {
            System.out.println("超出转换最大范围!");
            return false;
        }

        return true;
    }
    /**
     *@desc 金额转换为大写
     *<AUTHOR>
     *@date 2018/6/27 11:36
     *@parm [moneyVal]
     *@return java.lang.String
     **/
    public static String numberToChinese(double moneyVal)throws ServiceException {

        DecimalFormat decimalFormat = new DecimalFormat("0.00");// 格式化设置
        // String currencyDigits = String.valueOf(moneyVal);
        String currencyDigits = decimalFormat.format(moneyVal);// 科学计数法装换字符串

        if (check(currencyDigits)) {

            String integral = null; // 整数部分
            String decimal = null; // 小数部分
            String outputCharacters = null; // 最终转换输出结果

            String d = null;
            int zeroCount = 0, p = 0, quotient = 0, modulus = 0;

            // 删除数字中的逗号,
            currencyDigits = currencyDigits.replace("/,/g", "");
            // 删除数字左边的0
            currencyDigits = currencyDigits.replace("/^0+/", "");

            // 拆分数字中的整数与小数部分
            String[] parts = currencyDigits.split("\\.");
            if (parts.length > 1) {
                integral = parts[0];
                decimal = parts[1];

                // 如果小数部分长度大于2，四舍五入，保留两位小数
                if (decimal.length() > 2) {
                    long dd = Math.round(Double.parseDouble("" + decimal) * 100);
                    decimal = Long.toString(dd);
                }

            } else {
                integral = parts[0];
                decimal = "0";
            }

            // Start processing:
            outputCharacters = "";
            // Process integral part if it is larger than 0:
            if (Double.parseDouble(integral) > 0) {

                zeroCount = 0;

                for (int i = 0; i < integral.length(); i++) {

                    p = integral.length() - i - 1;
                    d = integral.substring(i, i + 1);

                    quotient = p / 4;
                    modulus = p % 4;
                    if (d.equals("0")) {
                        zeroCount++;
                    } else {
                        if (zeroCount > 0) {
                            outputCharacters += digits[0];
                        }
                        zeroCount = 0;
                        outputCharacters += digits[Integer.parseInt(d)] + radices[modulus];
                    }
                    if (modulus == 0 && zeroCount < 4) {
                        outputCharacters += bigRadices[quotient];
                    }
                }
                outputCharacters += CN_DOLLAR;
            }

            // Process decimal part if it is larger than 0:
            if (Double.parseDouble(decimal) > 0) {
                for (int i = 0; i < decimal.length(); i++) {
                    d = decimal.substring(i, i + 1);
                    if (!d.equals("0")) {
                        outputCharacters += digits[Integer.parseInt(d)] + decimals[i];
                    } else {
                        if (i == 0) {
                            outputCharacters += CN_ZERO;
                        }
                    }
                }
            }

            // Confirm and return the final output string:
            if (outputCharacters.equals("")) {
                outputCharacters = CN_ZERO + CN_DOLLAR;
            }
            if (decimal == null || decimal.equals("0") || decimal.equals("00")) {
                outputCharacters += CN_INTEGER;
            }

            return outputCharacters;
        } else {
            return null;
        }
    }
}
