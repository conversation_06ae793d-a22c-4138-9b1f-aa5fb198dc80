package cn.mftcc.doc.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyun.oss.model.VoidResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * <AUTHOR>
 */
@Component
public class OssConfig {
    @Value("${mftcc.aliyun.oss.endpoint}")
    private String ALIYUN_OSS_ENDPOINT ;
    @Value("${mftcc.aliyun.oss.accessKeyId}")
    private String ALIYUN_OSS_ACCESSKEYID;
    @Value("${mftcc.aliyun.oss.accessKeySecret}")
    private String ALIYUN_OSS_ACCESSKEYSECRET ;
    @Value("${mftcc.aliyun.oss.bucketName}")
    private String BUCKET_NAME ;



    public OSS OssClient(){
        OSS ossClient = new OSSClientBuilder().build(this.ALIYUN_OSS_ENDPOINT, this.ALIYUN_OSS_ACCESSKEYID, this.ALIYUN_OSS_ACCESSKEYSECRET);
        return ossClient;
    }

    public JSONObject writeOss(String objectName, byte[] bytes){
        JSONObject jsonObject = new JSONObject();
        OSS ossClient =  this.OssClient();
        try{
            PutObjectResult putObjectResult = ossClient.putObject(this.BUCKET_NAME, objectName, new ByteArrayInputStream(bytes));
            //返回路径
                String filePath=objectName;

                if(putObjectResult !=null) {
                    jsonObject.put("filePath", filePath);
                    jsonObject.put("code", "0000");
                    return jsonObject;
                }
                return jsonObject;
                }catch (Exception e){
                    throw new RuntimeException("上传失败！",e);
                }
        }

    public JSONObject writeOss(String objectName,InputStream inputStream){
        JSONObject jsonObject = new JSONObject();
        OSS ossClient = OssClient();
        try{
            PutObjectResult putObjectResult = ossClient.putObject(this.BUCKET_NAME, objectName, inputStream);
            //返回路径
            String filePath=objectName;
            if(putObjectResult !=null) {
                jsonObject.put("filePath", filePath);
                jsonObject.put("code", "0000");
                return jsonObject;
            }
            return jsonObject;
        }catch (Exception e){
            throw new RuntimeException("上传失败！",e);
        }
    }

    public InputStream readOss(String filePath){
        OSS ossClient = OssClient();
        try{
            OSSObject ossObject = ossClient.getObject(this.BUCKET_NAME,filePath);
            return ossObject.getObjectContent();
        }catch (Exception e){
            throw new RuntimeException("读取失败",e);
        }
    }

    public void deteleOss(String filePath){
        OSS ossClient = OssClient();
        try{
            VoidResult voidResult = ossClient.deleteObject(this.BUCKET_NAME,filePath);
            System.out.println("void"+voidResult.toString());
        }catch (Exception e){
            throw new RuntimeException("删除失败",e);
        }
    }

    public void clientClose(){
        OSS ossClient = OssClient();
        ossClient.shutdown();
    }
}