/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.common.utils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import net.sf.json.JSONObject;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;



public class HttpRequestUtil {

    private static Logger logger = LoggerFactory.getLogger(HttpRequestUtil.class);

    public static ObjectMapper mapper = new ObjectMapper();

    public static  String API_TOKEN = "mp_gateway-token";

    public static RedisTemplate<String, Object> redisTemplate;

    public static String doGet(String url, Map<String, String> param,Map<String, String> header) {
        logger.debug("### request url : {} , param : {} , header : {} !",url,param,header);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            URIBuilder builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, String.valueOf(param.get(key)));
                }
            }
            URI uri = builder.build();
            logger.info("get uri is ");
            HttpGet httpGet = new HttpGet(uri);
            if (header != null) {
                for (String key : header.keySet()) {
                    httpGet.setHeader(key, header.get(key));
                }
            }
            response = httpclient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            logger.error("### Get request error occured !",e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if(httpclient != null){
                    httpclient.close();
                }
            } catch (IOException e) {
                logger.error("### close http client error !",e);
            }
        }
        return resultString;
    }

    public static String doGet(String url) {
        return doGet(url, null,null);
    }

    /**
     * 发送带有body的Post请求
     * @param url
     * @param param
     * @param header
     * @param body
     * @return
     */
    public static String doPostWithBody(String url, Map<String, String> param,Map<String, String> header,String body) {
        logger.debug("### request url : {} , param : {} , header : {} !",url,param,header);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            URIBuilder builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, String.valueOf(param.get(key)));
                }
            }
            URI uri = builder.build();
            HttpPost httpPost = new HttpPost(uri);
            if (header != null) {
                for (String key : header.keySet()) {
                    httpPost.setHeader(key, header.get(key));
                }
            }

            StringEntity stringEntity = new StringEntity(body,ContentType.APPLICATION_JSON);
            httpPost.setEntity(stringEntity);
            response = httpclient.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            logger.error("### Post request error occured !",e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if(httpclient != null){
                    httpclient.close();
                }
            } catch (IOException e) {
                logger.error("### close http client error !",e);
            }
        }
        return resultString;
    }

    /**
     * 发送带有body的Post请求
     * @param url
     * @param param
     * @param header
     * @param body
     * @return
     */
    public static String doPostWithBodyBytes(String url, Map<String, String> param,Map<String, String> header,byte[] body) {
        logger.debug("### request url : {} , param : {} , header : {} !",url,param,header);
        CloseableHttpClient httpclient = HttpClients.createDefault();
        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            URIBuilder builder = new URIBuilder(url);
            Charset utf8=Charset.forName("UTF-8");
            builder.setCharset(utf8);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, String.valueOf(param.get(key)));
                }
            }
            URI uri = builder.build();
            logger.debug("post uri is  "+uri);
            HttpPost httpPost = new HttpPost(uri);
            if (header != null) {
                for (String key : header.keySet()) {
                    httpPost.setHeader(key, header.get(key));
                }
            }
            ByteArrayEntity byteArrayEntity=new ByteArrayEntity(body);
            httpPost.setEntity(byteArrayEntity);
            response = httpclient.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            logger.error("### Post request error occured !",e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if(httpclient != null){
                    httpclient.close();
                }
            } catch (IOException e) {
                logger.error("### close http client error !",e);
            }
        }
        return resultString;
    }

    /**
     * 想网关请求token
     * @param url
     * @param param
     * @param header
     * @return
     */
    public static String doPost(String url, Map<String, String> param,Map<String, String> header) {
        logger.debug("### request url : {} , param : {} , header : {} !",url,param,header);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            HttpPost httpPost = new HttpPost(url);
            //logger.info("url="+url);
            if (header != null) {
                for (String key : header.keySet()) {
                    httpPost.setHeader(key, header.get(key));
                }
            }
            if (param != null) {
                List<NameValuePair> paramList = new ArrayList<>();
                for (String key : param.keySet()) {
                    if(param.get(key) !=null){
                        paramList.add(new BasicNameValuePair(key, String.valueOf(param.get(key))));
                    }
                }
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList,"utf-8");
                //logger.info("paramList="+JsonUtil.obj2string(paramList));
                httpPost.setEntity(entity);
            }
            response = httpClient.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            }
        } catch (Exception e) {
            logger.error("### Post request error occured !",e);
        } finally {
            try {
                if(response != null){
                    response.close();
                }
                if(httpClient != null){
                    httpClient.close();
                }


            } catch (IOException e) {
                logger.error("### close http client error !",e);
            }
        }
        return resultString;
    }

    public static String doPost(String url) {
        return doPost(url, null, null);
    }

    public static String doPostJson(String url, String json) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            HttpPost httpPost = new HttpPost(url);
            StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            response = httpClient.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "utf-8");
            }

        } catch (Exception e) {
            logger.error("### PostJson  request error occured !",e);
        } finally {
            try {
                response.close();
            } catch (IOException e) {
                logger.error("### close http client error !",e);
            }
        }
        return resultString;
    }

    public static String getToken(){

        String token = getRedisToken();
        if(token != null){
            return token;
        }

        String clientId = getClientId();
        String secret = getSecret();
        String inputData = clientId+":"+secret;
        String url = getGatewayUrl()+getTokenUrl();
        Map<String, String> header = new HashMap<String, String>();
        String basic = "";
        try {
            basic = new String(Base64.encodeBase64(inputData.getBytes("UTF-8")), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("### Encode ["+inputData+"] fail .");
            basic = "cm9vdDpvcGVucGxhdGZvcm1AZ2F0ZXdheQ==";
        }
        header.put("Authorization", "Basic "+basic);
        header.put("X-Tyk-Authorization", getHeader());
        String resultString = doPost(url,null,header);
        logger.debug("### token result : "+resultString);
        if(StringUtils.isEmpty(resultString)) throw new IllegalStateException("### get token fail");
        //logger.info("resultString="+resultString);
        JSONObject jsStr = JSONObject.fromObject(resultString);
        if(!jsStr.containsKey("access_token")){
            throw new IllegalStateException("### Result ["+jsStr+"] not exits key [access_token] !");
        }
        token = jsStr.getString("access_token");
        String expiresTime = jsStr.getString("expires_in");
        long expiresIn = 0L;
        if(!StringUtils.isEmpty(expiresTime)) {
            try{
                expiresIn = Long.parseLong(expiresTime);
            }catch(Exception ex){}
        }
        setRedisToken(token,expiresIn);
        return token;
    }

    /**
     * 调用网关请求
     * @param apiUrl
     * @param requestMethod
     * @param param
     * @return
     */
    public static String doGatewayRequest(String apiUrl,String requestMethod,Map<String, String> param){
        if(StringUtils.isEmpty(apiUrl)) return null;
        if(param == null){
            param = new HashMap<String, String>();
        }
        String gatewayUrl = getGatewayUrl();
        if(!apiUrl.startsWith("/")){
            apiUrl = "/"+apiUrl;
        }
        if(apiUrl.endsWith("&") || apiUrl.endsWith("?")){
            apiUrl = apiUrl.substring(0,apiUrl.length()-1);
        }

        String url = gatewayUrl+apiUrl;
        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", "Bearer "+getToken());
//        header.put("Saas-Id", ContextHolder.getContext().getTenantId().toString());
        header.put("api-version", getApiVersion());
        String resultString  = null;
        if(StringUtils.isEmpty(requestMethod)) {
            resultString = doGet(url, param,header);
        }else if(requestMethod.equalsIgnoreCase("GET")){
            resultString = doGet(url, param,header);
        }else if(requestMethod.equalsIgnoreCase("POST")){
            //resultString = doPost(url, param,header);
            resultString = doPostWithBody(url, param, header, "{}");
        }
        return resultString;
    }


    /**
     * 调用网关请求 ，带有请求体
     * @param apiUrl
     * @param requestMethod
     * @param param
     * @return
     */
    public static String doGatewayRequestWithBody(String apiUrl,String requestMethod,Map<String, String> param,Map<String,String> requestHeader,String body){
        if(StringUtils.isEmpty(apiUrl)) return null;
        if(param == null){
            param = new HashMap<String, String>();
        }
        String gatewayUrl = getGatewayUrl();
        if(!apiUrl.startsWith("/")){
            apiUrl = "/"+apiUrl;
        }
        if(apiUrl.endsWith("&") || apiUrl.endsWith("?")){
            apiUrl = apiUrl.substring(0,apiUrl.length()-1);
        }

        String url = gatewayUrl+apiUrl;
        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", "Bearer "+getToken());
//        header.put("Saas-Id", ContextHolder.getContext().getTenantId().toString());
        header.put("api-version", getApiVersion());
        String resultString  = null;
        if(StringUtils.isEmpty(requestMethod)) {
            resultString = doGet(url, param,header);
        }else if(requestMethod.equalsIgnoreCase("GET")){
            resultString = doGet(url, param,header);
        }else if(requestMethod.equalsIgnoreCase("POST")){

			/*if(requestHeader != null && requestHeader.size() > 0){
				header.putAll(requestHeader);
			}
			url = attachMapParameterToUrl(param,url,true);
			logger.info("request url : {} ",url);
			RestUrl restUrl = new RestUrl(url);
			header.put("Content-Type", "application/json;charset=utf-8");
			if(StringUtils.isEmpty(body)) body = "{}";
			logger.info("### body content is {} ",body);
			ResponseResult<String> result = RestUtil.postRequest(restUrl, body, header, param);
			resultString = result.getBody();*/
            if(requestHeader != null && requestHeader.size() > 0){
                header.putAll(requestHeader);
            }
            resultString = doPostWithBody(url, param,header,body);
        }
        return resultString;
    }

    /**
     * 调用网关请求 ，带有请求体
     * @param apiUrl
     * @param requestMethod
     * @param param
     * @return
     */
    public static String doGatewayRequestWithBodyBytes(String tenantId,String apiUrl,String requestMethod,Map<String, String> param,Map<String,String> requestHeader,byte []bodyBytes){
        if(StringUtils.isEmpty(apiUrl)) return null;
        if(param == null){
            param = new HashMap<String, String>();
        }
        String gatewayUrl = getGatewayUrl();
        if(!apiUrl.startsWith("/")){
            apiUrl = "/"+apiUrl;
        }
        if(apiUrl.endsWith("&") || apiUrl.endsWith("?")){
            apiUrl = apiUrl.substring(0,apiUrl.length()-1);
        }

        String url = gatewayUrl+apiUrl;
        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", "Bearer "+getToken());
        header.put("Saas-Id", tenantId);
        header.put("api-version", getApiVersion());
        String resultString  = null;
        if(StringUtils.isEmpty(requestMethod)) {
            resultString = doGet(url, param,header);
        }else if(requestMethod.equalsIgnoreCase("GET")){
            resultString = doGet(url, param,header);
        }else if(requestMethod.equalsIgnoreCase("POST")){
            if(requestHeader != null && requestHeader.size() > 0){
                header.putAll(requestHeader);
            }
            resultString = doPostWithBodyBytes(url, param,header,bodyBytes);
        }
        return resultString;
    }



    /**
     * 调用网关请求 ，带有请求体
     * @param apiUrl
     * @param requestMethod
     * @param param
     * @return
     */
    public static String doGatewayRequestWithBody(String tenantId,String apiUrl,String requestMethod,Map<String, String> param,Map<String,String> requestHeader,String body){
        if(StringUtils.isEmpty(apiUrl)) return null;
        if(tenantId==null) return  doGatewayRequestWithBody(apiUrl,requestMethod,param,requestHeader,body);
        if(param == null){
            param = new HashMap<String, String>();
        }
        String gatewayUrl = getGatewayUrl();
        if(!apiUrl.startsWith("/")){
            apiUrl = "/"+apiUrl;
        }
        if(apiUrl.endsWith("&") || apiUrl.endsWith("?")){
            apiUrl = apiUrl.substring(0,apiUrl.length()-1);
        }

        String url = gatewayUrl+apiUrl;
        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", "Bearer "+getToken());
        header.put("Saas-Id", tenantId);
        header.put("api-version", getApiVersion());
        String resultString  = null;
        if(StringUtils.isEmpty(requestMethod)) {
            resultString = doGet(url, param,header);
        }else if(requestMethod.equalsIgnoreCase("GET")){
            resultString = doGet(url, param,header);
        }else if(requestMethod.equalsIgnoreCase("POST")){

			/*if(requestHeader != null && requestHeader.size() > 0){
				header.putAll(requestHeader);
			}
			url = attachMapParameterToUrl(param,url,true);
			logger.info("request url : {} ",url);
			RestUrl restUrl = new RestUrl(url);
			header.put("Content-Type", "application/json;charset=utf-8");
			if(StringUtils.isEmpty(body)) body = "{}";
			logger.info("### body content is {} ",body);
			ResponseResult<String> result = RestUtil.postRequest(restUrl, body, header, param);
			resultString = result.getBody();*/
            if(requestHeader != null && requestHeader.size() > 0){
                header.putAll(requestHeader);
            }
            resultString = doPostWithBody(url, param,header,body);
        }
        return resultString;
    }
    public static Map convert(String jsonStr) {
        try {
            if(jsonStr.startsWith("json(")){
                jsonStr = jsonStr.substring("json(".length(),jsonStr.length()-1);
            }else if(jsonStr.startsWith("json,json(")){
                jsonStr = jsonStr.substring("json,json(".length(),jsonStr.length()-1);
            }
            return mapper.readValue(jsonStr, Map.class);
        } catch (JsonParseException e) {
            // TODO Auto-generated catch block
            logger.error("Parse Json fail.",e);
        } catch (JsonMappingException e) {
            logger.error("Json mapping fail .",e);
        } catch (IOException e) {
            logger.error("IO exception ",e);
        }
        return new HashMap();
    }

    public static String getGatewayUrl(){
        return System.getProperty("openapi.gatewayUrl");
    }
    public static String getClientId(){
        return System.getProperty("openapi.clientId");
    }
    public static String getSecret(){
        return System.getProperty("openapi.secret");
    }
    public static String getHeader(){
        return System.getProperty("openapi.header");
    }
    public static String getTokenUrl(){
        return "/tyk/root/token";
    }

    public static String getApiVersion(){
        String version = System.getProperty("openapi.version");
        if(StringUtils.isEmpty(version)){
            return "v1";
        }
        return version;
    }

    public static String getRedisToken() {
        if(redisTemplate == null){
            try{
                redisTemplate = (RedisTemplate<String, Object>)ApplicationContextUtil.getBean("redisTemplate");
            }catch(Exception ex){
            }
            if(redisTemplate == null) {
                return null;
            }

        }
        Object tokenValue = null;
        try{
            tokenValue = redisTemplate.opsForValue().get(API_TOKEN);
        }catch(Exception ex){
            logger.error("### get {} from redis fail .",API_TOKEN);
        }
        return tokenValue == null ? null : String.valueOf(tokenValue);
    }

    //存储redis
    public static void setRedisToken(String value, long time) {
        if(redisTemplate != null){
            try{
                redisTemplate.opsForValue().set(API_TOKEN, value, time, TimeUnit.SECONDS);
            }catch(Exception ex){
                logger.error("### write {} to redis fail, token value is {} !",API_TOKEN,value);
            }

        }

    }

    public static String attachMapParameterToUrl(Map<String,String> toBeAddedParameters, String requestUrl, boolean isoverride){
        //if(StringUtils.isEmpty(requestUrl)) return null;
        String result = "";
        Map<String,String> originParameters = new LinkedHashMap<String,String>();
        if(StringUtils.isNotEmpty(requestUrl) && requestUrl.contains("?")){
            if(requestUrl.endsWith("&")) requestUrl = requestUrl.substring(0,requestUrl.length()-1);
            String sub = requestUrl.substring(requestUrl.indexOf("?")+1);
            String [] kvs = sub.split("&");
            if(kvs != null && kvs.length > 0){
                for(String kv : kvs){
                    originParameters.put(kv.split("=")[0],kv.split("=")[1]);
                }
            }
            if(toBeAddedParameters != null && toBeAddedParameters.size() > 0){
                for(String key : toBeAddedParameters.keySet()){
                    if(originParameters.containsKey(key)){
                        if(isoverride){
                            originParameters.put(key, toBeAddedParameters.get(key));
                        }
                    }else{
                        originParameters.put(key, toBeAddedParameters.get(key));
                    }
                }
            }
            if(originParameters != null && originParameters.size() >0){
                String prefix = requestUrl.substring(0,requestUrl.indexOf("?"))+"?";
                for(String key : originParameters.keySet()){
                    prefix = prefix + key+"="+originParameters.get(key)+"&";
                }
                prefix = prefix.substring(0,prefix.length()-1);
                result = prefix;
            }else{
                result = requestUrl;
            }
        }else{
            if(toBeAddedParameters != null && toBeAddedParameters.size() >0){
                String prefix = "";
                if(StringUtils.isEmpty(requestUrl)){
                    prefix = "";
                }else{
                    prefix = requestUrl+"?";
                }

                for(String key : toBeAddedParameters.keySet()){
                    prefix = prefix + key+"="+toBeAddedParameters.get(key)+"&";
                }
                prefix = prefix.substring(0,prefix.length()-1);
                result = prefix;
            }else{
                result = requestUrl;
            }
        }
        return result;
    }

    public static void main(String []args) throws Exception{
		/*String inputData = "root:openplatform@gateway";
		//String ip = "http://************:8000";
		String url = "http://************:8000/tyk/root/token";
		//String url = "http://**********:8888/tyk/root/token";
		//String url = "http://openapi.300.cn/tyk/root/token";
		Map<String, String> header = new HashMap<String, String>();
		header.put("Authorization", "Basic "+new String(Base64.encodeBase64(inputData.getBytes("UTF-8")), "UTF-8"));
		header.put("X-Tyk-Authorization", "352d20ee67be67f6340b4c0605b044b7");
		String resultString = doPost(url, null,header);
		System.out.println(resultString);
		Map<String,Object> reutrnObject = mapper.readValue(resultString, Map.class);
		System.out.println(reutrnObject);

		JSONObject jsStr = JSONObject.fromObject(resultString);
		String access_token = jsStr.getString("access_token");
		System.out.println(access_token);*/

        System.setProperty("openapi.gatewayUrl", "http://************:8000");
        System.setProperty("openapi.clientId", "root");
        System.setProperty("openapi.secret", "openplatform@gateway");
        System.setProperty("openapi.header", "352d20ee67be67f6340b4c0605b044b7");

//        ContextHolder.getContext().setTenantId(1600002912L);
        //appId=2&tempId=1
        Map<String,String> parameters = new HashMap<String,String>();
        parameters.put("appId", "2");
        //parameters.put("tempId", "1");
        parameters.put("tenantId", "1600002912");
        parameters.put("categoryId", "8");
        //parameters.put("callback", "json");
        //parameters.put("id", "2");

        //String result = getApi("/api/template/getBaseById","POST",parameters);
        //String result3 = doGatewayRequest("/api/product/category/getCategoryById","GET",parameters);
        //System.out.println("##### result 3 === "+result3);

        Map<String,String> param4 = new HashMap<String,String>();
        param4.put("tenantId", "1600003906");
        param4.put("appId", "2");
        /*String result4 = doGatewayRequestWithBody("/api/info/infoService/findByPagination","POST",param4,null,"{}");*/
        String result4 = doGatewayRequestWithBody("/webapi/batchGoods/keyword/findKeyword.do","POST",param4,null,"{}");
        System.out.println("result 4 : "+result4);

		/*String testUrl = "http://10.12.51.54/api/info/infoService/findByPagination?tenantId=1600003906&currentPage=1&pageSize=20&appId=2";
		testUrl = "http://10.12.51.54/api/info/infoService/findByPagination";
		Map<String,String> param = new HashMap<String,String>();
		param.put("tenantId", "1600003906");
		param.put("appId", "2");

		testUrl = attachMapParameterToUrl(param,testUrl,true);
		System.out.println("testurl:"+testUrl);
		RestUrl restUrl = new RestUrl(testUrl);
		Map<String,String> resHeader = new HashMap<String,String>();
		resHeader.put("Content-Type", "application/json;charset=utf-8");

		ResponseResult<String> result = RestUtil.postRequest(restUrl, "{}", resHeader, param);
		String resultString = result.getBody();
		System.out.println("result : "+resultString);*/


        //rest api get

        //String getUrl = "http://10.12.51.55/api/product/category/getCategoryById?appId=2&tenantId=1600002912&categoryId=8";
		/*String getUrl = "http://10.12.40.51:9000/gettbydomain";
		Map<String,String> params = new HashMap<String,String>();
		params.put("domain", "wjtest.20180041801.cn");
		params.put("tenantId", "1600002912");
		params.put("categoryId", "8");
		params.put("type", "common");
		String getResult = doGet(getUrl,params,null);
		System.out.println("Get result : "+getResult);
		String postResut=doPost(getUrl,params,null);
		System.out.println("post result :"+postResut);*/


		/*String url2 = "http://**********:8888/openapi/siteConfigService/publishPageList.do";
		Map<String, Object> param2 = new HashMap<String, Object>();
		param2.put("viewType", "1");
		Map<String, String> header2 = new HashMap<String, String>();
		header2.put("Authorization", "Bearer "+access_token);
		header2.put("Saas-Id", "40144");
		header2.put("api-version", "v1");
		String resultString2 = doPost(url2, param2,header2);
		System.out.println(resultString2);*/

/*		String url2 = "http://10.12.40.17/openapi/siteConfigService/getPageInfoByPageId.do";
		Map<String, Object> param2 = new HashMap<String, Object>();
		param2.put("viewType", "1");
		param2.put("pageId", "4abd215c-b19e-425f-af1a-1d1d05b9797b");
		param2.put("tenantId", "1600002971");
		Map<String, String> header2 = new HashMap<String, String>();
		header2.put("Authorization", "Bearer "+access_token);
		header2.put("Saas-Id", "1600002971");
		header2.put("api-version", "v1");
		String resultString2 = doPost(url2, param2,null);
		System.out.println(resultString2);
		JSONObject jsStr2 = JSONObject.fromObject(resultString2);
		PageProperty pages = JsonUtil.str2obj(jsStr2.getString("content"), PageProperty.class);
		pages.setName("123123");
		pages.setId("123123123");

		ArrayList<PageProperty> needUpdate=new ArrayList<>();
		needUpdate.add(pages);*/

/*		String url3 = "http://************:8000/openapi/siteConfigService/updatePageList.do";
		Map<String, Object> param3 = new HashMap<String, Object>();
		param3.put("viewType", "1");
		param3.put("pages", JsonUtil.obj2string(needUpdate));
		//param3.put("tenantId", "1600002971");
		Map<String, String> header3 = new HashMap<String, String>();
		header3.put("Authorization", "Bearer "+access_token);
		header3.put("Saas-Id", "1600002971");
		header3.put("api-version", "v1");
		String resultString3 = doPost(url3, param3,header3);
		System.out.println(resultString3);*/

/*		String url2 = "http://************:8000/openapi/siteConfigService/purgeCache.do";
		Map<String, Object> param2 = new HashMap<String, Object>();
		param2.put("viewType", "1");
		Map<String, String> header2 = new HashMap<String, String>();
		header2.put("Authorization", "Bearer "+access_token);
		header2.put("Saas-Id", "1600002971");
		header2.put("api-version", "v1");
		String resultString2 = doPost(url2, param2,header2);
		System.out.println(resultString2);*/

/*		String url2 = "http://************:8000/openapi/siteConfigService/getPageInfoByPhysicalName.do";
		Map<String, Object> param2 = new HashMap<String, Object>();
		param2.put("viewType", "1");
		param2.put("name", "cpzh3");
		Map<String, String> header2 = new HashMap<String, String>();
		header2.put("Authorization", "Bearer "+access_token);
		header2.put("Saas-Id", "1600002971");
		header2.put("api-version", "v1");
		String resultString2 = doPost(url2, param2,header2);
		System.out.println(resultString2);
		JSONObject jsStr2 = JSONObject.fromObject(resultString2);
		PageProperty pages = JsonUtil.str2obj(jsStr2.getString("content"), PageProperty.class);
		System.out.println(pages);*/

		/*String url2 = "http://************:8000/openapi/siteConfigService/findPages.do";
		//String url2 = "http://**********:8888/openapi/siteConfigService/findPages.do";
		//String url2 = "http://10.12.51.89/openapi/siteConfigService/findPages.do";
		//http://10.20.1.250/openapi/siteConfigService/findPages.do?viewType=1&tenantId=40853
		//String url2 = "http://openapi.300.cn/openapi/siteConfigService/findPages.do";
		Map<String, Object> param2 = new HashMap<String, Object>();
		param2.put("viewType", "1");
		//param2.put("tenantId", "1600002971");
		Map<String, String> header2 = new HashMap<String, String>();
		header2.put("Authorization", "Bearer "+access_token);
		header2.put("Saas-Id", "1600002971");
		header2.put("api-version", "v1");
		String resultString2 = doPost(url2, param2,header2);
		System.out.println(resultString2);*/
        //JSONObject jsStr2 = JSONObject.fromObject(resultString2);
        //List<PageProperty> pages = JsonUtil.str2list(jsStr2.getString("content"), PageProperty.class);


/*		String pagePath = "D:\\pages.json";
		String pages=FileUtil.readFileToString(pagePath);

		SiteConfig siteConfig = new SiteConfig();
		siteConfig.setKey("page");
		siteConfig.setContent(pages);
		String ppListJson = JsonUtil.obj2string(siteConfig);
		System.out.println(ppListJson);*/

        //String url4 = "http://************:8000/openapi/siteConfigService/deleteAllPage.do";
		/*String url4 = "http://**********:8888/openapi/siteConfigService/deleteAllPage.do";
		Map<String, Object> param4 = new HashMap<String, Object>();
		param4.put("viewType", "1");
		param4.put("operStatus", "0");
		Map<String, String> header4 = new HashMap<String, String>();
		header4.put("Authorization", "Bearer "+access_token);
		header4.put("Saas-Id", "33621");
		header4.put("api-version", "v1");
		String resultString4 = doPost(url4, param4,header4);
		System.out.println(resultString4);*/


        //String url3 = "http://************:8000/openapi/siteConfigService/updatePageContent.do";
/*		String url3 = "http://**********:8888/openapi/siteConfigService/updatePageContent.do";
		Map<String, Object> param3 = new HashMap<String, Object>();
		param3.put("viewType", "1");
		param3.put("siteConfig", ppListJson);
		Map<String, String> header3 = new HashMap<String, String>();
		header3.put("Authorization", "Bearer "+access_token);
		header3.put("Saas-Id", "40538");
		header3.put("api-version", "v1");
		String resultString3 = doPost(url3, param3,header3);
		System.out.println(resultString3);*/

		/*String url3 = "http://************:8000/openapi/siteConfigService/updatePagePerperty.do";
		Map<String, Object> param3 = new HashMap<String, Object>();
		param3.put("viewType", "1");
		param3.put("pp", JsonUtil.obj2string(pages));
		Map<String, String> header3 = new HashMap<String, String>();
		header3.put("Authorization", "Bearer "+access_token);
		header3.put("Saas-Id", "1600002971");
		header3.put("api-version", "v1");
		String resultString3 = doPost(url3, param3,header3);
		System.out.println(resultString3);*/

/*		String url2 = "http://**********:8888/openapi/siteConfigService/getPageInfoByPageId.do";
		Map<String, Object> param2 = new HashMap<String, Object>();
		param2.put("viewType", "1");
		param2.put("pageId", "68113279-7f15-492c-8f75-7abb6557c973");
		Map<String, String> header2 = new HashMap<String, String>();
		header2.put("Authorization", "Bearer "+access_token);
		header2.put("Saas-Id", "40144");
		header2.put("api-version", "v1");
		String resultString2 = doPost(url2, param2,header2);
		System.out.println(resultString2);
		JSONObject jsStr2 = JSONObject.fromObject(resultString2);
		PageProperty pageProperty = JsonUtil.str2obj(jsStr2.getString("content"), PageProperty.class);
		pageProperty.setName("123123");
		pageProperty.setId("123123123");

		String url3 = "http://120.133.9.175/openapi/siteConfigService/addPage.do";
		Map<String, Object> param3 = new HashMap<String, Object>();
		param3.put("viewType", "1");
		param3.put("pageProperty", JsonUtil.obj2string(pageProperty));
		param3.put("tenantId", "40144");
		Map<String, String> header3 = new HashMap<String, String>();
		header3.put("Authorization", "Bearer "+access_token);
		header3.put("Saas-Id", "40144");
		header3.put("api-version", "v1");
		String resultString3 = doPost(url3, param3,header3);
		System.out.println(resultString3);*/

		/*String url2 = "http://openapi.300.cn/openapi/siteConfigService/getCompData.do";
		Map<String, Object> param2 = new HashMap<String, Object>();
		param2.put("viewType", "1");
		param2.put("service", "cn.ce.ebiz.support.service.ProductSupportService");
		param2.put("method", "getProductDesignJson");
		param2.put("id", "c_product_list-15148705607238546");
		Map<String, String> header2 = new HashMap<String, String>();
		header2.put("Authorization", "Bearer "+access_token);
		header2.put("Saas-Id", "49");
		header2.put("api-version", "v1");
		String resultString2 = doPost(url2, param2,header2);
		System.out.println(resultString2);*/
    }

}
