/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.common.utils;


import org.apache.commons.lang3.StringUtils;
import org.apache.tools.ant.Project;
import org.apache.tools.ant.taskdefs.Expand;
import org.apache.tools.ant.taskdefs.Zip;
import org.apache.tools.ant.types.FileSet;
import org.apache.tools.tar.TarEntry;
import org.apache.tools.tar.TarInputStream;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.FileNotFoundException;
import java.io.BufferedOutputStream;
import java.io.BufferedInputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 文件解压缩工具类
 * <AUTHOR>
 *
 */
public class ZipUtil {
	
	private static final Project DEFAULT_PROJECT = new Project();
    
	/**
	 * 解压
	 * @param orgin 要解压的文件
	 * @param dest 解压后的文件夹
	 */
    public static void unZip(File orgin, File dest) {
        Expand expand = new Expand();
        expand.setProject(DEFAULT_PROJECT);
        expand.setSrc(orgin);
        expand.setDest(dest);
        expand.execute();
    }
      
    /**
     * 压缩
     * @param orgin 要压缩的文件夹
     * @param dest 压缩后的文件
     */
    public static void zip(File orgin, File dest) {
        Zip zip = new Zip();
        zip.setProject(DEFAULT_PROJECT);
        zip.setDestFile(dest);
          
        FileSet fs = new FileSet();
        fs.setProject(DEFAULT_PROJECT);
        fs.setDir(orgin);
          
        zip.addFileset(fs);
        zip.execute();
    }
    /**
     * 
     * 方法描述： 解压tar压缩文件到指定目录，目录不存在会自动创建
     * @param filename
     * @param directory
     * @return
     * boolean
     * <AUTHOR>
     * @date 2017-11-15 上午8:42:40
     */
    public static boolean extTarFileList(String filename, String directory) {  
        boolean flag = false;  
        OutputStream out = null;
        TarInputStream in =null;
        try {  
            in = new TarInputStream(new FileInputStream(new File(filename)));
            TarEntry entry = null;  
            while ((entry = in.getNextEntry()) != null) {  
                if (entry.isDirectory()) {  
                    continue;  
                }  
                System.out.println(entry.getName());  
                File outfile = new File(directory + entry.getName());  
                new File(outfile.getParent()).mkdirs();  
                out = new BufferedOutputStream(new FileOutputStream(outfile));  
                int x = 0;  
                while ((x = in.read()) != -1) {  
                    out.write(x);  
                }  
                //out.close();
            }  
            //in.close();
            flag = true;  
        } catch (IOException ioe) {  
            ioe.printStackTrace();  
            flag = false;  
        }  finally {
            if (out !=null){
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (in !=null){
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return flag;  
    }  
    public static void readZipFile(String file) throws Exception {
		InputStream in = new BufferedInputStream(new FileInputStream(file));
		ZipInputStream zin = new ZipInputStream(in);
		ZipEntry ze;
		while ((ze = zin.getNextEntry()) != null) {
			if (ze.isDirectory()) {
			} else {
				//System.err.println("file - " + ze.getName() + " : "+ ze.getSize() + " bytes");
			}
		}
		zin.close();
	}
	
	/**
	 * 
	 * 方法描述： 获取zip包里面文件的名称，压缩包文件不支持中文名称
	 * @param zipFilePath
	 * @return
	 * @throws Exception
	 * List<String>
	 * <AUTHOR>
	 * @date 2017-11-5 下午2:27:02
	 */
	public static List<String> readZipFileNames(String zipFilePath) throws Exception{
		InputStream in = new BufferedInputStream(new FileInputStream(zipFilePath));
		ZipInputStream zin = new ZipInputStream(in);
		ZipEntry ze;
		List<String> listFileName = new ArrayList<>();
		while ((ze = zin.getNextEntry()) != null) {
			if (ze.isDirectory()) {
			} else {
				//System.out.println(ze.getName());
				listFileName.add(ze.getName());
			}
		}
		zin.close();
		return listFileName;
	}

	
	 /** 
     * 将存放在sourceFilePath目录下的源文件，打包成fileName名称的zip文件，并存放到zipFilePath路径下 
     * @param sourceFilePath :待压缩的文件路径 
     * @param zipFilePath :压缩后存放路径 
     * @param fileName :压缩后文件的名称 
     * @return 
     */  
    public static boolean fileToZip(String sourceFilePath,String zipFilePath,String fileName){  
        boolean flag = false;  
        File sourceFile = new File(sourceFilePath);  
        FileInputStream fis = null;  
        BufferedInputStream bis = null;  
        FileOutputStream fos = null;  
        ZipOutputStream zos = null;  
          
        if(sourceFile.exists() == false){  
            System.out.println("待压缩的文件目录："+sourceFilePath+"不存在.");  
        }else{  
            try {  
                File zipFile = new File(zipFilePath + "/" + fileName +".zip");  
                if(zipFile.exists()){  
                    System.out.println(zipFilePath + "目录下存在名字为:" + fileName +".zip" +"打包文件.");  
                }else{  
                    File[] sourceFiles = sourceFile.listFiles();  
                    if(null == sourceFiles || sourceFiles.length<1){  
                        System.out.println("待压缩的文件目录：" + sourceFilePath + "里面不存在文件，无需压缩.");  
                    }else{  
                        fos = new FileOutputStream(zipFile);  
                        zos = new ZipOutputStream(new BufferedOutputStream(fos));  
                        byte[] bufs = new byte[1024*10];  
                        for(int i=0;i<sourceFiles.length;i++){  
                            //创建ZIP实体，并添加进压缩包  
                            ZipEntry zipEntry = new ZipEntry(sourceFiles[i].getName());  
                            zos.putNextEntry(zipEntry);  
                            //读取待压缩的文件并写进压缩包里  
                            fis = new FileInputStream(sourceFiles[i]);  
                            bis = new BufferedInputStream(fis, 1024*10);  
                            int read = 0;  
                            while((read=bis.read(bufs, 0, 1024*10)) != -1){  
                                zos.write(bufs,0,read);  
                            }  
                        }  
                        flag = true;  
                    }  
                }  
            } catch (FileNotFoundException e) {  
                e.printStackTrace();  
                throw new RuntimeException(e);  
            } catch (IOException e) {  
                e.printStackTrace();  
                throw new RuntimeException(e);  
            } finally{  
                //关闭流  
                if(null != bis) {
                    try {
                        bis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if(null != fis) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if(null != fos) {
                    try {
                        fos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if(null != zos) {
                    try {
                        zos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }  
        }  
        return flag;  
    } 
    
	public static void main(String[] args) {
		try {
		    System.out.println(StringUtils.equals("renjunqing","renjunqing"));
		    System.out.println(StringUtils.equals("renjunqing","renjun45qing"));
//			String sourceFilePath = "D:\\test";
//	        String zipFilePath = "D:\\test";
//	        String fileName = "12700153file";
//	        boolean flag = fileToZip(sourceFilePath, zipFilePath, fileName);
//	        if(flag){
//	            System.out.println("文件打包成功!");
//	        }else{
//	            System.out.println("文件打包失败!");
//	        }
//
//			//readZipFile("F:/qqfile/YGCB84-OD2171028000047785122017110.zip");
//			/*String tarFilePath ="D:/apache-tomcat-7.0/download/paph/dowloadfile/20171106/YGCBIMG8420171105.tar";
//			String destPath="D:/apache-tomcat-7.0/download/paph/dowloadfile/20171106/ssss/";
//			extTarFileList(tarFilePath, destPath);*/
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
