/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.common.utils;
import java.awt.Color;
import java.awt.Graphics;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Iterator;

import javax.imageio.ImageIO;
import javax.imageio.ImageReadParam;
import javax.imageio.ImageReader;
import javax.imageio.ImageTypeSpecifier;

public class ImgCompress {

    private static String DEFAULT_PREVFIX = "thumb_";
    private static Boolean DEFAULT_FORCE = false;//建议该值为false

    /**
     * <p>Title: thumbnailImage</p>
     * <p>Description: 根据图片路径生成缩略图 </p>
     * @param imagePath    原图片路径
     * @param w            缩略图宽
     * @param h            缩略图高
     * @param targetPath    生成缩略图的路劲
     * @param force      是否强制按照宽高生成缩略图(如果为false，则生成最佳比例缩略图)
     */
    public void thumbnailImage(String imagePath, int w, int h, String targetPath, boolean force){
        File imgFile = new File(imagePath);
        if(imgFile.exists()){
            try {
                // ImageIO 支持的图片类型 : [BMP, bmp, jpg, JPG, wbmp, jpeg, png, PNG, JPEG, WBMP, GIF, gif]
                String types = Arrays.toString(ImageIO.getReaderFormatNames());
                String suffix = null;
                // 获取图片后缀
                if(imgFile.getName().indexOf(".") > -1) {
                    suffix = imgFile.getName().substring(imgFile.getName().lastIndexOf(".") + 1);
                }
                // 类型和图片后缀全部小写，然后判断后缀是否合法
                if(suffix == null || types.toLowerCase().indexOf(suffix.toLowerCase()) < 0){
                    System.out.println("Sorry, the image suffix is illegal. the standard image suffix is {}." + types);
                    return ;
                }
                System.out.println("target image's size, width:{"+w+"}, height:{"+h+"}.");
                Image img = null ;

                try{
                    img =ImageIO.read(imgFile);
                }catch (Exception e){
                    try{

                        Iterator<ImageReader> iter = ImageIO.getImageReaders(imgFile);
                        Exception lastException = null;
                        while (iter.hasNext()) {
                            ImageReader reader = null;
                            try {
                                reader = (ImageReader)iter.next();
                                ImageReadParam param = reader.getDefaultReadParam();
                                reader.setInput(imgFile, true, true);
                                Iterator<ImageTypeSpecifier> imageTypes = reader.getImageTypes(0);
                                while (imageTypes.hasNext()) {
                                    ImageTypeSpecifier imageTypeSpecifier = imageTypes.next();
                                    int bufferedImageType = imageTypeSpecifier.getBufferedImageType();
                                    if (bufferedImageType == BufferedImage.TYPE_BYTE_GRAY) {
                                        param.setDestinationType(imageTypeSpecifier);
                                        break;
                                    }
                                }
                                img = reader.read(0, param);
                                if (null != img){
                                    break;
                                }

                            } catch (Exception ee) {
                                lastException = ee;
                            } finally {
                                if (null != reader) {
                                    reader.dispose();
                                }
                            }
                        }
                        // If you don't have an image at the end of all readers
                        if (null == img) {
                            if (null != lastException) {
                                throw lastException;
                            }
                        }
                    }catch(Exception ex){
                        ex.printStackTrace();
                    }
                }

                if(!force){
                    // 根据原图与要求的缩略图比例，找到最合适的缩略图比例
                    int width = img.getWidth(null);
                    int height = img.getHeight(null);
                    if((width*1.0)/w < (height*1.0)/h){
                        if(width > w){
                            h = Integer.parseInt(new java.text.DecimalFormat("0").format(height * w/(width*1.0)));
                            System.out.println("change image's height, width:{"+w+"}, height:{"+h+"}.");
                        }
                    } else {
                        if(height > h){
                            w = Integer.parseInt(new java.text.DecimalFormat("0").format(width * h/(height*1.0)));
                            System.out.println("change image's width,  width:{"+w+"}, height:{"+h+"}.");
                        }
                    }
                }
                BufferedImage bi = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
                Graphics g = bi.getGraphics();
                g.drawImage(img, 0, 0, w, h, Color.LIGHT_GRAY, null);
                g.dispose();
                String p = imgFile.getPath();
                // 将图片保存在原目录并加上前缀
                ImageIO.write(bi, suffix, new File(targetPath));
                System.out.println("缩略图在原路径下生成成功");
            } catch (IOException e) {
                System.out.println("generate thumbnail image failed."+e);
            }
        }else{
            System.out.println("the image is not exist.");
        }
    }

    /**
     * 	测试生成图片缩略图
     * @param args
     */
    public static void main(String[] args) {
        new ImgCompress().thumbnailImage("D:/ttt.jpg", 50, 75,"D:/ren11.jpg",true);
    }
}