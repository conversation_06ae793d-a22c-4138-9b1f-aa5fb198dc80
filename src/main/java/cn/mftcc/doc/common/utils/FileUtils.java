/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.common.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.crypto.digest.MD5;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.sysutils.MD5Util;
import cn.mftcc.common.utils.DateUtil;
import cn.mftcc.common.utils.UUIDUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import sun.misc.BASE64Encoder;

import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.imageio.ImageIO;

import org.icepdf.core.pobjects.Document;
import org.icepdf.core.util.GraphicsRenderingHints;


import javax.sql.rowset.serial.SerialException;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class FileUtils {


    public static MultipartFile base64ToMultipartFileForZip(String fileName, String savePath) throws Exception {
        try {
            File file = new File(savePath + fileName);
            DiskFileItem fileItem = (DiskFileItem) new DiskFileItemFactory().createItem("file",
                    MediaType.ALL_VALUE, true, file.getName());

            try (InputStream input = new FileInputStream(file); OutputStream os = fileItem.getOutputStream()) {
                IOUtils.copy(input, os);
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid file: " + e, e);
            }

            MultipartFile multipartFile = new CommonsMultipartFile(fileItem);
            return multipartFile;
        } catch (Exception e) {
            MFLogger.info("base转base64ToMultipartFile 失败！");
            throw new Exception("base转base64ToMultipartFile 失败");
        }
    }

    /**
     * 拷贝文件到一个路径下
     * @param oldPath 源文件路径
     * @param newPath 目标路径
     * @throws ServiceException
     */
    public void copyFile(String oldPath, String newPath)throws ServiceException {
        InputStream inStream =null; //读入原文件
        FileOutputStream fs=null;//写入文件
        try {
            int bytesum = 0;
            int byteread = 0;
            File oldfile = new File(oldPath);

            if (oldfile.exists()) { //文件存在时
                 inStream = new FileInputStream(oldPath); //读入原文件
                 fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                int length;
                while ((byteread = inStream.read(buffer)) != -1) {
                    bytesum += byteread; //字节数 文件大小
//                    System.out.println(bytesum);
                    fs.write(buffer, 0, byteread);
                }


            }
        } catch (Exception e) {
            MFLogger.error("拷贝文件失败",e);
            throw new ServiceException("拷贝文件失败", "文件是" + oldPath, e);
        }finally {
            try{

                if(fs!=null){
                    fs.close();
                }
                if(inStream!=null){
                    inStream.close();
                }
            }catch (Exception e){
                MFLogger.error("拷贝文件失败 关闭流" ,e);
            }
        }
    }


    /**
     * 把base64串转化为文件，保存到指定目录
     *
     * @param base64 串
     * @param fileName
     * @param savePath
     */
    public static void base64ToFile(String base64, String fileName, String savePath) throws IOException {
        File file = null;
        //创建文件目录
        String filePath = savePath;
        File dir = new File(filePath);
        if (!dir.exists() && !dir.isDirectory()) {
            dir.mkdirs();
        }
        BufferedOutputStream bos = null;
        java.io.FileOutputStream fos = null;
        try {
            byte[] bytes = Base64.decodeBase64(base64);
            file = new File(filePath + fileName);
            fos = new java.io.FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            MFLogger.info("把base64串转化为文件，保存到指定目录失败！");
            throw e;
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    /**
     * todo base转base64ToMultipartFile
     * @参数
     * @返回
     * @作者 仇招
     * @日期 2022/8/29 10:47
     **/
    public static MultipartFile base64ToMultipartFile(String fileName, String savePath) throws Exception {
        try {
            MFLogger.info("base转base64ToMultipartFile：" + savePath + fileName);
            File file = new File(savePath + fileName);
            InputStream inputStream = new FileInputStream(file);

            // MultipartFile
            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "text/plain", inputStream);
            return multipartFile;
        } catch (Exception e) {
            MFLogger.info("base转base64ToMultipartFile 失败！");
            throw new Exception("base转base64ToMultipartFile 失败");
        }
    }

    public static MultipartFile base64ToMultipartFileFh(String fileName, String savePath) throws Exception {
        try {
            MFLogger.info("base转base64ToMultipartFile：" + savePath + fileName);
            File file = new File(savePath + fileName);
            InputStream inputStream = new FileInputStream(file);

            // MultipartFile
            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, "application/pdf", inputStream);
            return multipartFile;
        } catch (Exception e) {
            MFLogger.info("base转base64ToMultipartFile 失败！");
            throw new Exception("base转base64ToMultipartFile 失败");
        }
    }


    /**
     * pdf转换成图片
     * @param basePath  基础存储路径
     * @param path  相对路径
     * @param docFileName 文件名称
     * @return
     */
    public static List<String> getPdfToImage(String  basePath,String path,String docFileName) {
        String filePath=basePath+path+docFileName;
        String fileName = filePath.substring(0, filePath.lastIndexOf("."));//获取去除后缀的文件路径
        List<String> list = new ArrayList<>();
        String imagePath;
        Document document = new Document();
        try {
            document.setFile(filePath);
        } catch (Exception e) {
            e.printStackTrace();
        }
        float scale = 1.5f;// 缩放比例
        float rotation = 0f;// 旋转角度

        for (int i = 0; i < document.getNumberOfPages(); i++) {
            BufferedImage image = (BufferedImage) document.getPageImage(i,
                    GraphicsRenderingHints.SCREEN,
                    org.icepdf.core.pobjects.Page.BOUNDARY_CROPBOX, rotation,
                    scale);
            RenderedImage rendImage = image;
            try {
                int n = i + 1;
                File f = new File(fileName);
                if (!f.exists()) {
                    f.mkdir();
                }
                imagePath = fileName + "_" + n + ".jpg";//生成图片的路径
                int k=docFileName.lastIndexOf(".");
                String fileNamePrefix=docFileName.substring(0,k);
                list.add(fileNamePrefix+ "_" + n + ".jpg");
                File file = new File(imagePath);
                ImageIO.write(rendImage, "jpg", file);
                // 这里png作用是：格式是jpg但有png清晰度
                // ImageIO.write(rendImage, "png", file);

            } catch (IOException e) {
                e.printStackTrace();
            }
            image.flush();
        }
        document.dispose();
        return list;
    }



    /**
     * 读取resource下面的动态资源
     * @param path resource下的相对路径
     * @return
     * @throws ServiceException
     */
    public InputStream getResourceStream(String path)throws ServiceException{
        InputStream inputStream =this.getClass().getClassLoader().getResourceAsStream(path);
        return inputStream;

    }
    /**
     * 拷贝文件到一个路径下
     * @param inStream 源文件的流
     * @param newPath 目标路径
     * @throws ServiceException
     */
    public void copyFileByInputStream(InputStream inStream , String newPath)throws ServiceException {
//        InputStream inStream =null; //读入原文件
        FileOutputStream fs=null;//写入文件
        try {
            int bytesum = 0;
            int byteread = 0;
//            File oldfile = new File(oldPath);

            if (inStream !=null) { //文件存在时
//                 inStream = new FileInputStream(oldPath); //读入原文件
                 fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                int length;
                while ((byteread = inStream.read(buffer)) != -1) {
                    bytesum += byteread; //字节数 文件大小
//                    System.out.println(bytesum);
                    fs.write(buffer, 0, byteread);
                }


            }
        } catch (Exception e) {
            MFLogger.error("拷贝文件失败",e);
            throw new ServiceException("拷贝文件失败", "文件是" + newPath, e);
        }finally {
            try{

                if(fs!=null){
                    fs.close();
                }
                if(inStream!=null){
                    inStream.close();
                }
            }catch (Exception e){
                MFLogger.error("拷贝文件失败 关闭流" ,e);
            }
        }
    }

    /**
     * 把文件转换成base64
     * @param path
     * @return
     * @throws Exception
     */
    public static String encodeBase64File(String path) throws Exception {
        File file = new File(path);
        FileInputStream inputFile = new FileInputStream(file);
        byte[] buffer = new byte[(int)file.length()];
        inputFile.read(buffer);
        inputFile.close();
        return new String(Base64.encodeBase64String(buffer));
    }

    /**
     * 将网络图片编码为base64
     * @param path
     * @return
     */
    public static String encodeImageToBase64(String path) throws Exception {
        URL url = new URL(path);
        //将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        System.out.println("图片的路径为:" + url.toString());

        //通过输入流获取图片数据
        InputStream inStream = url.openStream();
        //得到图片的二进制数据，以二进制封装得到数据，具有通用性
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        //创建一个Buffer字符串
        byte[] buffer = new byte[1024];
        //每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len = 0;
        //使用一个输入流从buffer里把数据读取出来
        while ((len = inStream.read(buffer)) != -1) {
            //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
        }
        //关闭输入流
        inStream.close();
        byte[] data = outStream.toByteArray();
        //对字节数组Base64编码
        BASE64Encoder encoder = new BASE64Encoder();
        String base64 = encoder.encode(data);

        return base64;

    }



    /**
     * 读取文件内容
     * @param fileName
     * @return
     */
    public  String readFileContent(String fileName) {
        File file = new File(fileName);
        BufferedReader reader = null;
        StringBuffer sbf = new StringBuffer();
        try {
            reader = new BufferedReader(new FileReader(file));
            String tempStr;
            while ((tempStr = reader.readLine()) != null) {
                sbf.append(tempStr);
            }
            reader.close();
            return sbf.toString();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return sbf.toString();
    }

    /**
     * 读取 tagShow.html内容
     * @return
     */
    public  String generateFileContent(String  templateId,String plugsDataBaseUrl,String serverPath,String serverName) {

        String  sourcePath=plugsDataBaseUrl;

        StringBuilder sb=new StringBuilder();
        sb.append("<!DOCTYPE html>                                                                                                                                                                                                    ");
        sb.append("<html lang=\"en\" xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:th=\"http://www.thymeleaf.org\">                                                                                                                    ");
        sb.append("<head th:replace=\"header :: common_header(~{::title},~{::link},~{::script},~{::style})\">                                                                                                                         ");
        sb.append("    <meta charset=\"UTF-8\">                                                                                                                                                                                       ");
        sb.append("    <title>模板标签</title>                                                                                                                                                                                        ");
        sb.append("    <!-- <link rel=\"stylesheet\" type=\"text/css\" th:href=\"@{/scripts/plugins.css}\"/> -->                                                                                                                      ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("    <link rel=\"stylesheet\" href=\""+sourcePath+"index.css\" type=\"text/css\" />                                                                                                                                               ");
        sb.append("    <script type=\"text/javascript\" src=\""+sourcePath+"plugins.js\"></script>                                                                                                                                                  ");
        sb.append("    <script type=\"text/javascript\" src='"+sourcePath+"vue.min.js'></script>                                                                                                                                                    ");
        sb.append("    <script type=\"text/javascript\" src='"+sourcePath+"element-ui.min.js'></script>                                                                                                                                             ");
        sb.append("    <script type=\"text/javascript\" src='"+sourcePath+"axios.min.js'></script>                                                                                                                                                  ");
//        sb.append("    <script type=\"text/javascript\" src='"+sourcePath+"tagShow.js'></script>                                                                                                                                                    ");
        sb.append("    <script>Vue.config.productionTip=false </script></script>                                                                                                                                                      ");
        sb.append("</head>                                                                                                                                                                                                            ");
        sb.append("<body style=\"background-color: #FFFFFF;\">                                                                                                                                                                        ");
        sb.append("<div id=\"app\" >                                                                                                                                                                                                  ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("    <div class=\"box-card\" v-for=\"(tagType,i) in tagList\" >                                                                                                                                                     ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("            <div class=\"tagTitle\">{{tagType.desc}}</div>                                                                                                                                                         ");
        sb.append("            <div style=\"margin: top 4px;\"  >                                                                                                                                                                     ");
        sb.append("            <span  v-for=\"tag in tagType.tags\">                                                                                                                                                                  ");
        sb.append("            <!-- <div> -->                                                                                                                                                                                         ");
        sb.append("                <!-- 加载文字标签-->                                                                                                                                                                               ");
        sb.append("                <el-button  icon=\"el-icon-warning-outline\" class=\"el-button-margin-top\"   size=\"mini\"  v-if=\"'text'==tag.type\"   :key=\"tag.key\" @click=\"addTextTag(tag)\">{{tag.tag}}</el-button>       ");
        sb.append("                <!-- 加载图片标签-->                                                                                                                                                                               ");
        sb.append("                <el-button  icon=\"el-icon-picture-outline\" class=\"el-button-margin-top\"   size=\"mini\"   v-if=\"'img'==tag.type\"   :key=\"tag.key\" @click=\"addImgTag(tag)\">{{tag.tag}}</el-button>        ");
        sb.append("                <!-- 加载表格标签-->                                                                                                                                                                               ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                <!-- <div   v-if=\"'table'==tag.type\" >                                                                                                                                                           ");
        sb.append("                    <el-button  icon=\"el-icon-postcard\"  class=\"el-button-margin-top\" size=\"mini\"   :key=\"tag.key\" @click=\"addTextTag(tag)\">{{tag.tag}}</el-button>                                      ");
        sb.append("                    <el-button  type=\"text\"   size=\"mini\" v-for=\"(subtag,index) in tag.value\" :key=\"index\"     @click=\"addTableTag(tag,subtag)\">列_{{subtag.tag}}</el-button>                                            ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                </div> -->                                                                                                                                                                                         ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                <el-dropdown  v-if=\"'table'==tag.type\"  @click=\"addTextTag(tag)\"  @command=\"addTableTag\" split-button   size=\"mini\"  @click=\"handleClick\">                                               ");
        sb.append("                    {{tag.tag}}                                                                                                                                                                                    ");
        sb.append("                    <el-dropdown-menu slot=\"dropdown\" >                                                                                                                                                          ");
        sb.append("                      <el-dropdown-item v-for=\"(subtag,index) in tag.value\"   :command=\"subtag\"  @click=\"addTableTag(tag,subtag)\"  :key=\"index\"  >{{subtag.tag}}</el-dropdown-item>                                        ");
        sb.append("                    </el-dropdown-menu>                                                                                                                                                                            ");
        sb.append("                  </el-dropdown>                                                                                                                                                                                   ");
        sb.append("                <!-- 加载循环标签-->                                                                                                                                                                               ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                <el-dropdown  v-if=\"'loop'==tag.type\"  @click=\"addLoopTag(tag)\" @command=\"addTextTag\"  split-button   size=\"mini\"  @click=\"handleClick\">                                                 ");
        sb.append("                    {{tag.tag}}                                                                                                                                                                                    ");
        sb.append("                    <el-dropdown-menu slot=\"dropdown\" >                                                                                                                                                          ");
        sb.append("                      <el-dropdown-item v-for=\"(subtag,index) in tag.value\"   :command=\"subtag\"  :key=\"index\"  >{{subtag.tag}}</el-dropdown-item>                                                                           ");
        sb.append("                    </el-dropdown-menu>                                                                                                                                                                            ");
        sb.append("                  </el-dropdown>                                                                                                                                                                                   ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                <!-- <div  v-if=\"'loop'==tag.type\" >                                                                                                                                                             ");
        sb.append("                    <el-button  size=\"mini\"   :key=\"tag.key\" @click=\"addLoopTag(tag)\">{{tag.tag}}</el-button>                                                                                                ");
        sb.append("                    <el-button  type=\"text\"   size=\"mini\" v-for=\"(subtag,index) in tag.value\"    @click=\"addTextTag(subtag)\">值_{{subtag.tag}}</el-button>                                                 ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                </div> -->                                                                                                                                                                                         ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                <!-- 加载勾选项标签-->                                                                                                                                                                             ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                <el-button   icon=\"el-icon-circle-check\"  size=\"mini\"   v-if=\"'checkbox'==tag.type\"   :key=\"tag.key\" @click=\"addCheckBoxTag(tag)\">{{tag.tag}}</el-button>                                ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                <!-- <el-button  type=\"text\"   size=\"mini\" v-for=\"(subtag,index) in tag.value\"    @click=\"addTableTag(tag,subtag)\">{{subtag.tag}}</el-button> -->                                          ");
        sb.append("            <!-- </div>  -->                                                                                                                                                                                       ");
        sb.append("            </span>                                                                                                                                                                                                ");
        sb.append("            </div>                                                                                                                                                                                                 ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("    </div>                                                                                                                                                                                                         ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("                                                                                                                                                                                                                   ");
        sb.append("</div>                                                                                                                                                                                                             ");
        sb.append("<style>                                                                                                                                                                                                            ");
//        sb.append("                                                                                                                                                                                                                   ");
//        sb.append("    .box-card {                                                                                                                                                                                                    ");
//        sb.append("        margin-bottom: 10px;                                                                                                                                                                                       ");
//        sb.append("    }                                                                                                                                                                                                              ");
//        sb.append("    .box-card .tagTitle{                                                                                                                                                                                           ");
//        sb.append("        font-family: \"PingFang SC\", \"Hiragino Sans GB\",\"Helvetica Neue\",Helvetica,\"Microsoft YaHei\",\"微软雅黑\",Arial,sans-serif;                                                                         ");
//        sb.append("        font-size:16px;                                                                                                                                                                                            ");
//        sb.append("        height:24px;                                                                                                                                                                                               ");
//        sb.append("        font-weight: 800;                                                                                                                                                                                          ");
//        sb.append("        line-height: 24px;                                                                                                                                                                                         ");
//        sb.append("    }                                                                                                                                                                                                              ");
//        sb.append("    .box-card .el-button-margin-top{                                                                                                                                                                               ");
//        sb.append("        margin-top:12px;                                                                                                                                                                                           ");
//        sb.append("    }                                                                                                                                                                                                              ");
//        sb.append("    .box-card .el-dropdown__caret-button{                                                                                                                                                                          ");
//        sb.append("        margin-top:12px;                                                                                                                                                                                           ");
//        sb.append("    }                                                                                                                                                                                                              ");
//        sb.append("                                                                                                                                                                                                                   ");
        sb.append("</style>                                                                                                                                                                                                           ");
        sb.append("<script>  ");
        sb.append("    let serverPrefix='"+serverPath+serverName+"';  ");
        sb.append("    let templateId= '"+templateId+"';");
        sb.append("    var AscThis;    ");
        sb.append("    new Vue({       ");
        sb.append("        el: '#app', ");
        sb.append("        data: function () {         ");
        sb.append("            return {                ");
        sb.append("                tagShowFlag:false,  ");
        sb.append("                tagList:{}          ");
        sb.append("            }                       ");
        sb.append("        },                          ");
        sb.append("        created(){                  ");
        sb.append("            var vthis = this;       ");
        sb.append("            window.Asc.plugin.init = function () { ");
        sb.append("                AscThis = this;                    ");
        sb.append("                vthis.getTags();                   ");
        sb.append("            };                                     ");
        sb.append("            window.Asc.plugin.button = function (id) {        ");
        sb.append("                if (-1 === id) {                              ");
        sb.append("                       this.executeCommand(\"close\", \"\");  ");
        sb.append("                }          ");
        sb.append("            };             ");
        sb.append("        },                 ");
        sb.append("        mounted() {        ");
        sb.append("        },                 ");
        sb.append("        methods: {         ");
        sb.append("            getTags() {    ");
        sb.append("                var that = this;                              ");
        sb.append("                let tagUrl=serverPrefix+\"/mould/docTemplateModel/getTagsConfig/\"+templateId; ");
        sb.append("                axios.get(tagUrl, {}, {}).then(function (response) {                           ");
        sb.append("                    debugger;                             ");
        sb.append("                    that.tagList = response.data.data;    ");
        sb.append("                    console.log(that.tagList);            ");
        sb.append("                    that.tagShowFlag=true;  ");
        sb.append("                }).catch(function (error) { ");
        sb.append("                    console.log(error);     ");
        sb.append("                });                         ");
        sb.append("            },                              ");
        sb.append("            addTextTag(tag) {               ");
        sb.append("                console.log(tag) ;           ");
//        sb.append("                // alert(tag);              ");
        sb.append("                Asc.scope.text = \"{{\"+tag.tag+ \"}}\";  ");
        sb.append("                this.insertTag();    ");
        sb.append("            },                       ");
//        sb.append("            //插入循环标签           ");
        sb.append("            addLoopTag(tag) {        ");
        sb.append("                console.log(tag) ;    ");
//        sb.append("                // alert(tag);       ");
        sb.append("                Asc.scope.text = \"{{?\"+tag.tag+ \"}}\"; ");
        sb.append("                this.insertTag();                         ");
        sb.append("                Asc.scope.text = \"{{/\"+tag.tag+ \"}}\"; ");
        sb.append("                this.insertTag();  ");
        sb.append("            },                     ");
        sb.append("            addImgTag(tag) {       ");
        sb.append("                console.log(tag);   ");
        sb.append("                Asc.scope.text = \"{{@\"+tag.tag+ \"}}\"; ");
        sb.append("                this.insertTag();    ");
        sb.append("            },                       ");
        sb.append("            addCheckBoxTag(tag) {    ");
        sb.append("                console.log(tag);     ");
        sb.append("                Asc.scope.text = \"{{?\"+tag.tag+\"}} {{@checkBoxKey}}{{checkBoxValue}} {{/\"+tag.tag+\"}}\"; ");
        sb.append("                this.insertTag();  ");
        sb.append("            },                     ");
        sb.append("            addTableTag(tag) {     ");
//        sb.append("                // console.log(table, tag)                                                                                                                                                                         ");
//        sb.append("                // Asc.scope.text = \"[\"+tag.tag+ \"]\";            ");
        sb.append("                if (tag.type==\"text\"){                             ");
        sb.append("                    Asc.scope.text = \"[\"+tag.tag+ \"]\";           ");
        sb.append("                }else if(tag.type==\"img\"){                         ");
        sb.append("                    Asc.scope.text = \"[@\"+tag.tag+ \"]\";          ");
        sb.append("                }else{             ");
        sb.append("                    return false;  ");
        sb.append("                }                  ");
        sb.append("                this.insertTag();  ");
        sb.append("            },                     ");
        sb.append("            addTable(table) {      ");
        sb.append("                Asc.scope.text = \"{{\"+table.tag+ \"}}\";           ");
        sb.append("                this.insertTag();    ");
        sb.append("            },                       ");
        sb.append("            insertTag(){             ");
        sb.append("                if (AscThis.info.editorType==\"word\"){              ");
        sb.append("                    AscThis.callCommand(function () {                ");
        sb.append("                        var oDocument = Api.GetDocument();           ");
        sb.append("                        var oParagraph = Api.CreateParagraph();      ");
        sb.append("                        oParagraph.AddText(Asc.scope.text);          ");
        sb.append("                        oDocument.InsertContent([oParagraph], true); ");
        sb.append("                    }, false);                                       ");
        sb.append("                }else if(AscThis.info.editorType==\"cell\"){         ");
        sb.append("                    AscThis.callCommand(function () {                ");
        sb.append("                        var oWorksheet = Api.GetActiveSheet();       ");
        sb.append("                        Api.GetSelection().SetValue(Asc.scope.text); ");
        sb.append("                    }, false);                                       ");
        sb.append("                } ");
        sb.append("            }     ");
        sb.append("        }         ");
        sb.append("    })            ");
        sb.append("</script>         ");

        sb.append("</body>                                                                                                                                                                                                            ");
        sb.append("</html>                                                                                                                                                                                                            ");


       return sb.toString();
    }

    /**
     * 获取字符串的长度
     * @param s
     * @return
     */
    public  int length(String s) {
        if (s == null) {
            return 0;
        }
        char[] c = s.toCharArray();
        int len = 0;
        for (int i = 0; i < c.length; i++) {
            len++;
            if (!isLetter(c[i])) {
                len++;
            }
        }
        return len;
    }

    public BigDecimal getFileSize(String filename) {
        File file = new File(filename);

        if (!file.exists() || !file.isFile()) {
            System.out.println("文件不存在");
            return null;
        }
        long fileSize=file.length();
        double fileSizeKb=fileSize/1000;
        BigDecimal result=new BigDecimal(fileSizeKb);
        return result;
    }
    boolean isLetter(char c) {
        int k = 0x80;
        return c / k == 0 ? true : false;
    }


    /**
     *  向文件中写入内容
     * @param filePath
     * @param content
     * @throws ServiceException
     */
    public  void  writeFileInfo(String filePath,String content)throws  ServiceException{
        File file = new File(filePath);
        byte[] b = content.getBytes();	//将字符串转换成字节数
        OutputStream out = null;
        try {
            out = new FileOutputStream(file);	//实例化OutpurStream
            out.write(b);		//写入

        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }finally {
            if(out!=null){
                try {
                 out.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }


    }

    public static void main(String[]df){
        try{

            DateTime t= DateTime.now();
            String timestamp= t.toString("yyyMMddhhmmss");
//                   DateUtil.DateToStr(new Date(),"yyyyMMddhhmmss");
            String AppToken = "Gr97uLK9mc";
            String AppSecret = "16lIaGmueEqVL5h8uqeirIiHTxatcE";
            String concatStr=AppToken+AppSecret+timestamp;

            String signature= MD5Util.md5(concatStr);
            System.out.println("timestamp="+timestamp);
            System.out.println("signature="+signature);


//            http://10.2.156.28:9182
//            AppToken = "Gr97uLK9mc"
//            AppSecret = "16lIaGmueEqVL5h8uqeirIiHTxatcE"


//           List<String> imgPath=  getPdfToImage("D://","","fp.pdf");
//            FileUtils fileUtils=new FileUtils();
//            String  originalFile="D:\\work\\microservices\\prdnew\\web\\mftcc-doc-web\\static\\template\\newmould"+File.separator+"originalWord.docx";
//            String  itemFile="D:\\work\\microservices\\prdnew\\web\\mftcc-doc-web\\static\\template\\rjq.docx";
//            fileUtils.copyFile(originalFile,itemFile);
//            String path=fileUtils.getOrignalFilePath();
//            System.out.println(path);

//            String ary[]={"ren","jun","qing"};
//            String  string= JSON.toJSONString(ary);
//
////            BigDecimal fileSize=fileUtils.getFileSize("D:/mftcc-doc-server基础库.sql");
//            System.out.println(string);
//
////            fileUtils.checkCredatePath("d:/ren/jun/qing/renjunqing.txt");
//            String fileName="adsfafd.asdfadsf. asdfadsf .xlxs";
//            System.out.println(fileUtils.getFileSuffixName(fileName));
        }catch (Exception e){

            e.printStackTrace();
        }
    }

    /**
     * 对日期进行格式化
     * @param date 日期
     * @param patten  格式
     * @return
     */
    public  String  getPattenDateStr(Date date,String  patten){
        SimpleDateFormat bartDateFormat =new SimpleDateFormat(patten);
        return  bartDateFormat.format(date);


    }

    /**
     * 获取文件类型标识
     * @param filePathName 文件名称（带路径）
     * @return
     * @throws ServiceException
     */
    public  String   getFileType(String filePathName)throws  ServiceException{
        String  result="10";
        String  suffix=getFileSuffixName(filePathName);//后缀名
        //1-目录 2-word 3-excel 4-pdf 5-图片 6-压缩包 7-txt 8-视频 9-音频 10-其他。
        if("doc".equals(suffix) || "docx".equals(suffix) || "dot".equals(suffix) || "dotx".equals(suffix)
                || "docm".equals(suffix) || "dotm".equals(suffix)){
            result="2";
        }else{
            if("xls".equals(suffix) || "xlsx".equals(suffix)){
                result="3";
            }else {
                if("pdf".equals(suffix)){
                    result="4";
                }else{
                    if("bmp".equals(suffix) || "gif".equals(suffix) || "jpeg".equals(suffix) || "jpg".equals(suffix)
                            || "png".equals(suffix) || "tiff".equals(suffix) || "tga".equals(suffix) || "svg".equals(suffix)
                            || "psd".equals(suffix) || "eps".equals(suffix) || "ufo".equals(suffix)){
                        result="5";
                    }else
                    if("rar".equals(suffix) || "zip".equals(suffix) || "7z".equals(suffix) || "tar".equals(suffix)
                            || "jar".equals(suffix)){
                        result="6";
                    }else
                    if("txt".equals(suffix)){
                        result="7";
                    }
                    else
                    {
                        if("mp4".equals(suffix) || "mov".equals(suffix) || "rmvb".equals(suffix) || "rm".equals(suffix)
                                || "avi".equals(suffix) || "mp4".equals(suffix) || "flv".equals(suffix) || "3gp".equals(suffix)){
                            result="8";
                        }else{
                            if("mp3".equals(suffix) || "cda".equals(suffix) || "wav".equals(suffix)){
                                result="9";
                            }
                        }
                    }
                }
            }
        }
        return result;

    }

    /**
     * 获取模板文件的基础路径
     * @return
     * @throws ServiceException
     */
    public  String getOrignalFilePath()throws ServiceException{

        try{

            String  result="";

            String courseFile = FileUtils.class.getClassLoader().getResource("").getPath();

            result  =courseFile+"static/template/";
            return  result;

        }catch (Exception e){
            throw  new ServiceException("获取模板文件的基础路径","getOrignalFilePath",e);
        }

    }


    /**
     *  检查文件目录是否存在 没有就创建
     * @param filepath
     * @return
     * @throws Exception
     */
    public  File checkCredatePath(String filepath) throws Exception{

        File file=new File(filepath);



        if (file.exists()) {//判断文件目录的存在

            System.out.println("文件夹存在！");

            if(file.isDirectory()){//判断文件的存在性

                System.out.println("文件存在！");

            }

        }else {



            File file2=new File(file.getParent());

            file2.mkdirs();




        }

        return file;

    }

    public  String getFileSuffixName(String fileName)throws ServiceException{
        String suffixName="";
        if(fileName!=null){

            String ary[]=fileName.split("\\.");
            if(ary.length>1){
                int ind_=ary.length-1;
                suffixName=ary[ind_] ;
            }

        }
        return suffixName;
    }




    /**
     * 重命名文件的名称
     * @param path 文件路径
     * @param newname 文件新名称
     * @return
     * @throws ServiceException
     */
    public boolean ReName(String path,String newname)throws  ServiceException {//文件重命名
        //Scanner scanner=new Scanner(System.in);
        File file=new File(path);
        if(file.exists()) {
            File newfile=new File(file.getParent()+File.separator+newname);//创建新名字的抽象文件
            if(file.renameTo(newfile)) {
                return true;
            }
            else {

                return false;
            }
        }
        else {

            return false;
        }

    }



}
