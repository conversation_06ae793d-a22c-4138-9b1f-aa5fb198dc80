/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.handler.tag;

import cn.mftcc.doc.components.docmanage.model.entity.*;
import cn.mftcc.doc.components.docmanage.model.enums.ImgSourceType;
import cn.mftcc.doc.components.docmanage.model.enums.TagType;
import cn.mftcc.doc.components.docmanage.model.enums.ValueEnum;
import cn.mftcc.doc.components.docmanage.model.params.TableTagParam;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.style.Style;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Slf4j
@Component("wordTagConvert")
public class WordTagConvertHandler implements TagConvertHandler {

    @Value("${mftcc.template.tagRedTipWhenNull:}")
    private String tagRedTipWhenNull;
    @Override
    public Map<String, BaseTagEntity> toBusTag(JSONObject tagData, JSONObject busData) {
        return this.getTag(tagData, busData);
    }

    Map<String, BaseTagEntity> getTag(JSONObject tagData, JSONObject busData) {

        Set<String> tagKeys = tagData.keySet();
        Map<String, BaseTagEntity> tagMap = new HashMap<>();


        tagKeys.forEach(tagKey -> {
            JSONObject originalTagJson = tagData.getJSONObject(tagKey);
            Object bus = busData.getOrDefault(tagKey, null);
            BaseTagEntity tag = combineTagAndBus(originalTagJson, bus);
            tagMap.put(tagKey, tag);
        });

        return tagMap;
    }

    /**
     * 合并标签及业务数据
     * @param originalTagJson 原始标签数据（JSON 格式）
     * @param bus 对应的业务数据，可为空
     * @return 合并成功之后的业务数据
     */
    private BaseTagEntity combineTagAndBus(JSONObject originalTagJson, @Nullable Object bus) {
        TagType tagType = ValueEnum.valueToEnum(TagType.class, originalTagJson.getString("type"));
        BaseTagEntity tag;
        TagConvert<? extends BaseTagEntity> tagConvert;
        switch (tagType) {
            case TEXT:
                tagConvert = new TextTagConvert();
                break;
            case IMG:
                tagConvert = new ImageTagConvert();
                break;
            case TABLE:
                tagConvert = new TableTagConvert();
                break;
            case LOOP:
                tagConvert = new TableTagConvert();
                break;
            case CHECKBOX:
                tagConvert = new CheckboxTagonvert();
                break;

            default:
                throw new IllegalStateException("不正确的标签类型: " + tagType);
        }

        tag = tagConvert.convert(originalTagJson, bus);
        return tag;
    }

    interface TagConvert<T extends BaseTagEntity> {
        /**
         * 标签数据转换
         * @param originalTagJson JSON 格式的标签数据
         * @param bus 业务数据
         * @return
         */
        T convert(JSONObject originalTagJson, @Nullable Object bus);
    }

    class TextTagConvert implements TagConvert<TextTag> {
        @Override
        public TextTag convert(JSONObject originalTagJson, @Nullable Object bus) {
            TextTag textTag = originalTagJson.toJavaObject(TextTag.class);
            if(null == bus) {
                log.warn("标签：{} 的业务数据为空", textTag.getTag());

                String warnText = "";
                if("1".equals(tagRedTipWhenNull)){
                    warnText = "数据不存在";
                }

                textTag.setValue(warnText);
                textTag.setStyle(Style
                        .builder()
                        .buildColor("FF2121")
                        .build());

                return textTag;
            }

            textTag.setValue(bus.toString());
            return textTag;
        }
    }

    class ImageTagConvert implements TagConvert<ImageTag> {

        @Override
        public ImageTag convert(JSONObject originalTagJson, @Nullable Object bus) {
            ImageTag imageTag = originalTagJson.toJavaObject(ImageTag.class);
            if(null == bus) {
                log.warn("标签：{} 的业务数据为空", imageTag.getTag());

                imageTag.setAlt("图片数据不存在");
                return imageTag;
            }
            JSONObject tagData=(JSONObject) JSONObject.toJSON(bus);
            imageTag.setUrl(tagData.getString("url"));
            imageTag.setSource((ImgSourceType)tagData.get("source"));
            imageTag.setImgType((PictureType) tagData.get("impType"));

            return imageTag;
        }
    }



    class TableTagConvert implements TagConvert<TableTag> {
        @Override
        public TableTag convert(JSONObject originalTagJson, @Nullable Object bus) {
            TableTagParam tableTagParam = originalTagJson.toJavaObject(TableTagParam.class);
            TableTag tableTag = new TableTag();
            tableTag.setTag(tableTagParam.getTag());
            tableTag.setType(tableTagParam.getType());

            if(null == bus) {
                log.warn("标签：{} 的业务数据为空", tableTagParam.getTag());
                return tableTag;
            }

            List<Map<String, ? extends BaseTagEntity>> value = new ArrayList<>();
            List<Map<String, Object>> busMap = (List<Map<String, Object>>) bus;
            JSONObject tagObject = JSONObject.parseObject(
                    JSONObject.toJSONString(
                            tableTagParam.getValue()
                    ));
            // 对每一行进行处理
            busMap.forEach(busRow -> {
                JSONObject busObject = JSONObject.parseObject(
                        JSONObject.toJSONString(
                                busRow
                        ));
                Map<String, BaseTagEntity> itemTag =  getTag(tagObject, busObject);
                // 表格数据的特殊处理
                itemTag.keySet().forEach(key -> {
                    BaseTagEntity item =  itemTag.get(key);
                    if(item instanceof TextTag) {
                        if(StringUtils.equals(((TextTag) item).getValue(), "数据不存在")) {
                            ((TextTag) item).setValue("");
                            ((TextTag) item).setStyle(null);
                            itemTag.put(key, item);
                        }
                    }
                });

                value.add(itemTag);
            });
            tableTag.setValue(value);
            tableTag.setListLoopPartten(originalTagJson.getString("listLoopPartten"));
            return tableTag;
        }
    }
    class CheckboxTagonvert implements TagConvert<CheckboxTag> {
        @Override
        public CheckboxTag convert(JSONObject originalTagJson, @Nullable Object bus) {
            CheckboxTag checkboxTag=new CheckboxTag();
            TableTagParam tableTagParam = originalTagJson.toJavaObject(TableTagParam.class);

            checkboxTag.setTag(tableTagParam.getTag());
            checkboxTag.setType(tableTagParam.getType());

            if(null == bus) {
                log.warn("标签：{} 的业务数据为空", tableTagParam.getTag());
                return checkboxTag;
            }else{
                checkboxTag.setValue((JSONArray) bus);
            }

            return checkboxTag;
        }
    }
}