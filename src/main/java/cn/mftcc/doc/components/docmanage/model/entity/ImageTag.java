/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.entity;

import cn.mftcc.doc.components.docmanage.model.enums.ImgSourceType;
import com.deepoove.poi.data.PictureType;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图片标签
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImageTag extends BaseTagEntity {
    /**
     * 图片地址
     */
    private String url;
    /**
     * 图片存储类型
     */
    private ImgSourceType source;
    /**
     * 图片渲染宽度
     */
    private int width;
    /**
     * 图片渲染高度
     */
    private int height;
    /**
     * 图片渲染类型
     */
    private PictureType imgType;
    /**
     * 图片渲染失败提示
     */
    private String alt;

    public void size(int w, int h) {
        this.width = w;
        this.height = h;
    }
}
