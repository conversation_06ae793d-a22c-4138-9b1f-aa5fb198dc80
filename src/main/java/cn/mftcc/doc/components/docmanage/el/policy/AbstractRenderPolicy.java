/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.policy;

import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;
import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.data.MergeRenderData;
import cn.mftcc.doc.components.docmanage.el.render.RenderContext;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;
import com.deepoove.poi.data.TextRenderData;
import com.deepoove.poi.exception.RenderException;


import org.apache.commons.lang3.ClassUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 数据验证，渲染，清除模板标签和异常处理的通用逻辑
 * 
 * <AUTHOR>
 */
public abstract class AbstractRenderPolicy<T> implements RenderPolicy {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @SuppressWarnings("unchecked")
    @Override
    public void render(ElementTemplate eleTemplate, Object data, XSSFSheetTemplate template) {
        // type safe
        T model = null;
        try {
            model = (T) data;
        } catch (ClassCastException e) {
            throw new RenderException("Error Render Data format for template: " + eleTemplate.getSource(), e);
        }

        RenderContext<T> context = new RenderContext<T>(eleTemplate, model, template);
        try {
            // validate
            if (!validate(model)) {
                postValidError(context);
                return;
            }

            // merge group
            if (mergeGroup(eleTemplate, template.getGroupData())) {
                XSSFCell cell = eleTemplate.getCell();
                String text = "";
                if(model instanceof TextRenderData) {
                    text = ((TextRenderData) model).getText();
                }
                MergeRenderData mergeRenderData = computeMergeRender(cell, template.getMergeRender(text));
                template.setMergeRender(text, mergeRenderData);
            }

            // do render
            beforeRender(context);
            doRender(context, eleTemplate);
            afterRender(context);
        } catch (Exception e) {
            reThrowException(context, e);
        }

    }

    private MergeRenderData computeMergeRender(XSSFCell cell, MergeRenderData mergeRender) {
        if(mergeRender == null) {
            return new MergeRenderData(cell.getRowIndex(), cell.getRowIndex(), cell.getColumnIndex(), cell.getColumnIndex());
        }
        return mergeRender.compare(cell);
    }

    private boolean mergeGroup(ElementTemplate eleTemplate, List<String> groupData) {
        if(groupData != null && groupData.size() > 0) {
            return groupData.contains(eleTemplate.getTagName());
        }
        return false;
    }

    public abstract void doRender(RenderContext<T> context, ElementTemplate eleTemplate) throws Exception;

    protected boolean validate(T data) {
        return true;
    }

    protected void beforeRender(RenderContext<T> context) {
    }

    protected void afterRender(RenderContext<T> context) {
    }

    protected void reThrowException(RenderContext<T> context, Exception e) {
        throw new RenderException("Render template " + context.getEleTemplate() + " failed!", e);
    }

    protected void postValidError(RenderContext<T> context) {
        Configure.ValidErrorHandler errorHandler = context.getConfig().getValidErrorHandler();
        logger.info("The data [{}] of the template {} is illegal, will apply error handler [{}]", context.getData(),
                context.getTagSource(), ClassUtils.getSimpleName(errorHandler.getClass()));
        errorHandler.handler(context);
    }

    /**
     * For operations that are not in the current tag position, the tag needs to be
     * cleared
     *
     * @param context
     *
     */
    protected void clearPlaceholder(RenderContext<?> context) {
        XSSFCell cell = context.getCell();

        cell.setCellValue("");
    }
}
