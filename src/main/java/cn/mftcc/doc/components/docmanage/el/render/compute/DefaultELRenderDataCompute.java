/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render.compute;

import com.deepoove.poi.exception.ExpressionEvalException;
import com.deepoove.poi.expression.DefaultEL;


/**
 * 默认表达式计算
 * 
 * <AUTHOR>
 */
public class DefaultELRenderDataCompute implements RenderDataCompute {

    private DefaultEL elObject;
    private DefaultEL envObject;
    private boolean isStrict;

    public DefaultELRenderDataCompute(EnvModel model, boolean isStrict) {
        this.elObject = DefaultEL.create(model.getRoot());
        if (null != model.getEnv() && !model.getEnv().isEmpty()) {
            this.envObject = DefaultEL.create(model.getEnv());
        }
        this.isStrict = isStrict;
    }

    @Override
    public Object compute(String el) {
        try {
            if (null != envObject && !el.contains("#this")) {
                try {
                    Object val = envObject.eval(el);
                    if (null != val) {
                        return val;
                    }
                } catch (Exception e) {
                    // ignore
                }
            }
            return elObject.eval(el);
        } catch (ExpressionEvalException e) {
            if (isStrict) {
                throw e;
            }
            // 无法计算表达式，默认返回null
            return null;
        }
    }

}
