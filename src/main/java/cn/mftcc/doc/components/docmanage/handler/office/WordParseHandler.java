/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.handler.office;

import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.components.docmanage.model.entity.*;
import cn.mftcc.doc.components.docmanage.model.enums.TagType;
import cn.mftcc.doc.components.docmanage.web.utils.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.*;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;

import com.deepoove.poi.policy.LoopColumnTableRenderPolicy;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Slf4j
@Component("wordParse")
public class WordParseHandler implements DocParseHandler {
    private final HackLoopTableRenderPolicy hackLoopTableRenderPolicy = new HackLoopTableRenderPolicy(false);
    private final LoopColumnTableRenderPolicy loopColumnTableRenderPolicy = new LoopColumnTableRenderPolicy();

    @Override
    @SuppressWarnings("unchecked")
    public <T> T parseOfficeByTag(String docPath, Map<String, BaseTagEntity> tagMap) {
        ConfigureBuilder configureBuilder = Configure.builder();

        Map<String, Object> templateData = convertTagToData(tagMap,
                (key) -> configureBuilder.bind(key, hackLoopTableRenderPolicy));

        XWPFTemplate xwpfTemplate =null;
        try{

            Configure configure=configureBuilder.build();
            LoopColumnTableRenderPolicy policy = new LoopColumnTableRenderPolicy();
            //处理列表循环方向
            for(String key:tagMap.keySet()){
                JSONObject obj=JSONObject.parseObject(JSONObject.toJSONString(tagMap.get(key)));

                if(obj.containsKey("listLoopPartten")){
                    if("v".equals(obj.getString("listLoopPartten"))){
                        String  tagName=obj.getString("tag");
                        configure = configureBuilder.bind(tagName, policy).build();
                    }
                }
            }
            xwpfTemplate= XWPFTemplate
                    .compile(docPath, configure)
                    .render(templateData);
        }catch (Exception e){
            e.printStackTrace();
        }
        return (T)xwpfTemplate;

    }

    /**
     * 转换 tag 为模板数据
     * @param tagMap 需要转换的 tag 数据
     * @return 模板所需要的数据
     */
    public Map<String, Object> convertTagToData(Map<String, ? extends BaseTagEntity> tagMap,
                                                               Consumer<String> consumer) {
        Map<String, Object> templateData = new HashMap<>();

        Set<String> tagKeys = tagMap.keySet();
        tagKeys.forEach(tagKey -> {
            BaseTagEntity tagEntity = tagMap.get(tagKey);


                RenderData data = poiTagParse(tagEntity, consumer);

                if(data == null) {
                    return;
                }
                // 如果是 table 则取出其内的 rows 参数
                if(data instanceof TableRenderData) {
                    templateData.put(tagEntity.getTag(), ((TableRenderData) data).getRows());
                } else if(data instanceof LoopRenderData){
                    templateData.put(tagEntity.getTag(), ((LoopRenderData) data).getRows());
                }else if(data instanceof  PictureRenderData){

                    templateData.put(tagEntity.getTag(),data);
                }else if(data instanceof  CheckboxRenderData){
                    templateData.put(tagEntity.getTag(),((CheckboxRenderData) data).getOptions());
                }
                else{
                    templateData.put(tagEntity.getTag(), data);
                }

        });

        return templateData;
    }

    /**
     * 标签 tag 解析
     * @param tag
     * @return
     */
    private RenderData poiTagParse(BaseTagEntity tag, Consumer<String> consumer) {
        RenderData renderData = null;
        if (tag.getType() == null) {
            return null;
        }

        switch (tag.getType()) {
            case TEXT:
                renderData =  new TextTagParse().tagToTemplateData((TextTag) tag);
                break;
            case IMG:
                renderData = new ImageTagParse().tagToTemplateData((ImageTag) tag);
                break;
            case TABLE:
                consumer.accept(tag.getTag());
                renderData = new TableTagParse().tagToTemplateData((TableTag) tag);
                break;
            case LOOP:
                consumer.accept(tag.getTag());
                renderData = new LoopTagParse().tagToTemplateData((TableTag) tag);
                break;
            case CHECKBOX:
                consumer.accept(tag.getTag());
                renderData = new CheckboxTagParse().tagToTemplateData((CheckboxTag) tag);
                break;
            default:
                throw new IllegalStateException("不正确的标签类型: " + tag.getType());
        }

        return renderData;
    }

    interface TagParse<T extends BaseTagEntity, V> {
        V tagToTemplateData(T tag);
    }

    class TextTagParse implements TagParse<TextTag, TextRenderData> {
        @Override
        public TextRenderData tagToTemplateData(TextTag tag) {
            Texts.TextBuilder textBuilder = Texts.of(tag.getValue());
            if(StringUtils.isNotEmpty(tag.getUrl())) {
                textBuilder.link(tag.getUrl());
            }

            return textBuilder
                    .style(tag.getStyle())
                    .create();
        }
    }

    class ImageTagParse implements TagParse<ImageTag, PictureRenderData> {
        @Override
        public PictureRenderData tagToTemplateData(ImageTag tag) {
            Pictures.PictureBuilder pictureBuilder = null;
            if(tag.getUrl() == null) {
                return null;
            }
            switch (tag.getSource()) {
                case LOCAL:
                    pictureBuilder = Pictures
                            .ofLocal(tag.getUrl());
                    break;
                case URL:
                    PictureType pictureType = StringUtil.getUrlType(tag.getUrl());
                    pictureBuilder = Pictures
                            .ofUrl(tag.getUrl(), pictureType);
                    break;
                default:
                    throw new IllegalStateException("不支持的图片资源: " + tag.getImgType());
            }

            return pictureBuilder
                    .size(tag.getWidth(), tag.getHeight())
                    .altMeta(tag.getAlt())
                    .create();
        }
    }

    class TableTagParse implements TagParse<TableTag, TableRenderData> {
        @Override
        public TableRenderData tagToTemplateData(TableTag tag) {
            TableRenderData tableRenderData = new TableRenderData();
            List<Map<String, ? extends BaseTagEntity>> rows = tag.getValue();
            List<Map<String, Object>> rowDataList = new ArrayList<>();
            rows.forEach(row -> {
                Map<String, Object> rowTemplateData = convertTagToData(row, null);
                rowDataList.add(rowTemplateData);
            });

            tableRenderData.setRows(rowDataList);
            return tableRenderData;
        }
    }
    class LoopTagParse implements TagParse<TableTag, LoopRenderData> {
        @Override
        public LoopRenderData tagToTemplateData(TableTag tag) {
            LoopRenderData tableRenderData = new LoopRenderData();
            List<Map<String, ? extends BaseTagEntity>> rows = tag.getValue();
            List<Map<String, Object>> rowDataList = new ArrayList<>();
            rows.forEach(row -> {
                Map<String, Object> rowTemplateData = convertTagToData(row, null);
                Map<String, Object> loopTemplateData = new HashedMap();
                for(String key:rowTemplateData.keySet()){
                    JSONObject tmpTag=(JSONObject) JSONObject.toJSON(rowTemplateData.get(key));
                    loopTemplateData.put(key,tmpTag.getString("text"));
                }
                rowDataList.add(loopTemplateData);
            });

            tableRenderData.setRows(rowDataList);
            return tableRenderData;
        }
    }

    class CheckboxTagParse implements TagParse<CheckboxTag, CheckboxRenderData> {
        @Override
        public CheckboxRenderData tagToTemplateData(CheckboxTag tag) {
            CheckboxRenderData checkboxRenderData = new CheckboxRenderData();


            checkboxRenderData.setOptions(tag.getValue());
            return checkboxRenderData;
        }
    }
    @Data
    class LoopRenderData implements RenderData {
        List<Map<String, Object>>rows;
    }
    @Data
    class CheckboxRenderData implements RenderData {
        JSONArray options;
    }

    @Data
    class TableRenderData implements RenderData {
        List<Map<String, Object>> rows;
    }
}
