/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.web.utils;

import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.common.utils.FileUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;

public class DocUtil {


    public static void downloadToFile(String url, File file) throws Exception {

        MFLogger.info("第一步："+url);
        if (url == null || url.isEmpty()) {
            throw new Exception("argument url");
        }
        if (file == null) {
            throw new Exception("argument path");
        }

        MFLogger.info("第二步：获取流");
        URL uri = new URL(url);
        java.net.HttpURLConnection connection = (java.net.HttpURLConnection) uri.openConnection();
        InputStream stream = connection.getInputStream();
        MFLogger.info("获取流:"+stream.toString());
        if (stream == null) {
            throw new Exception("Stream is null");
        }

        MFLogger.info("第三步：开始写入文件:"+file.getAbsolutePath());
        new FileUtils().checkCredatePath(file.getAbsolutePath());
        try (FileOutputStream out = new FileOutputStream(file)) {
            int read;
            final byte[] bytes = new byte[1024];
            while ((read = stream.read(bytes)) != -1) {
                out.write(bytes, 0, read);
            }

            out.flush();
        }

        connection.disconnect();
    }
    public  static void main(String[]d)throws  Exception{
        String officeServerPath="http://192.168.2.192:5080";
        String downloadUri="http://sit-office.ahgytz.com.cn/cache/files/oi2tdl98db4e22lqlp0z_21/output.docx/output.docx?md5=EZ6FGCozCzP33Kwqzvc4lQ&expires=1649920727&disposition=attachment&filename=output.docx";
        File file=new File("d:/rjq.docx");
        downloadToFile(downloadUri,file);
        int dex_=officeServerPath.lastIndexOf("/");
        if(dex_>7){
            String fromStr=officeServerPath.substring(0,dex_);
            downloadUri=downloadUri.replaceAll(fromStr,officeServerPath);
        }
        System.out.println(downloadUri);
    }



}
