/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.data;

import com.deepoove.poi.data.RenderData;
import org.apache.poi.xssf.usermodel.XSSFCell;

/**
 * 合并数据模板
 * 
 * <AUTHOR>
 *
 */
public class MergeRenderData implements RenderData {

    private static final long serialVersionUID = 1L;

    protected int firstRow;
    protected int lastRow;

    protected int firstCol;
    protected int lastCol;


    public MergeRenderData(int firstRow, int lastRow, int firstCol, int lastCol) {
        this.firstRow = firstRow;
        this.lastRow = lastRow;
        this.firstCol = firstCol;
        this.lastCol = lastCol;
    }

    public MergeRenderData compare(XSSFCell cell) {
        this.firstRow = Math.min(cell.getRowIndex(), this.firstRow);
        this.lastRow = Math.max(cell.getRowIndex(), this.lastRow);

        this.firstCol = Math.min(cell.getColumnIndex(), this.firstCol);
        this.lastCol = Math.max(cell.getColumnIndex(), this.lastCol);

        return this;
    }

    public int getFirstRow() {
        return firstRow;
    }

    public int getLastRow() {
        return lastRow;
    }

    public int getFirstCol() {
        return firstCol;
    }

    public int getLastCol() {
        return lastCol;
    }
}
