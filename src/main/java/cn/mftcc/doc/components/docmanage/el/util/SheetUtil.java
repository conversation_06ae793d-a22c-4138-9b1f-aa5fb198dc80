/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package com.mftcc.doc.poi.el.util;

import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.usermodel.*;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * sheet 页工具类
 *
 * <AUTHOR>
 * @date 2021/1/25
 */
public class SheetUtil {
    /**
     * 在 excel 中创建一个新行
     * @param sheet
     * @param rowIndex
     * @return
     */
    public static XSSFRow createRow(XSSFSheet sheet, Integer rowIndex) {
        XSSFRow row = null;
        XSSFCellStyle xssfCellStyle = null;
        short height = -1;
        if (sheet.getRow(rowIndex) != null) {
            int lastRowNo = sheet.getLastRowNum();
            XSSFRow lastRow = sheet.getRow(lastRowNo);

            sheet.shiftRows(rowIndex, lastRowNo, 1);
            // 最后一行则不做处理
            if(rowIndex != lastRowNo) {
                xssfCellStyle = sheet.getRow(lastRowNo).getRowStyle();
                height = lastRow.getHeight();
            }
        }

        row = sheet.createRow(rowIndex);
        row.setRowStyle(xssfCellStyle);
        row.setHeight(height);

        return row;
    }

    /**
     * 从 excel 中删除一行（有数据则不删除）
     * @param sheet
     * @param rowIndex
     */
    public static void removeRow(XSSFSheet sheet, Integer rowIndex) {
        XSSFRow row = sheet.getRow(rowIndex);
        // 先删除此行中的合并行
        removeRowMergedRegions(sheet, rowIndex);
        // 删除此行
        sheet.removeRow(row);

        int lastRowNo = sheet.getLastRowNum();
        if(++rowIndex <= lastRowNo) {
            sheet.shiftRows(rowIndex, lastRowNo, -1);
        }
    }

    /**
     * 删除本 sheet 页中，处于 rowIndex 之间的合并行,即将其释放为普通单元格
     * @param sheet
     * @param rowIndex
     */
    public static void removeRowMergedRegions(XSSFSheet sheet, Integer rowIndex) {
        List<CellRangeAddress> cellRangeAddressList = sheet.getMergedRegions();
        for(int index = 0; index < cellRangeAddressList.size(); index++) {
            CellRangeAddress ca = cellRangeAddressList.get(index);
            int firstRow = ca.getFirstRow();
            int lastRow = ca.getLastRow();

            if(rowIndex >= firstRow && rowIndex <= lastRow) {
                sheet.removeMergedRegion(index);
            }
        }
    }

    /**
     * 设置单元格的宽度与高度
     * 实际上就是设置列宽与行高
     * @param cell 单元格
     * @param width 宽度
     * @param height 高度
     */
    public static void setCellSize(XSSFCell cell, short width, short height) {
        if(width != -1) {
            cell.getSheet().setColumnWidth(cell.getColumnIndex(), width);
        }

        if(height != -1) {
            cell.getRow().setHeight(height);
        }
    }

    /**
     * 为某个单元格设置图片
     * @param cell 单元格
     * @param image 图片
     * @param pictureType 图片类型
     * @param width 宽度（单位/点）
     * @param height 高度（单位/字符）
     * @throws IOException
     */
    public static void addPicture(XSSFCell cell, InputStream image, int pictureType, short width, short height) throws IOException {
        SheetUtil.setCellSize(cell, width, height);
        XSSFSheet sheet = cell.getSheet();
        XSSFWorkbook workbook = sheet.getWorkbook();
        XSSFDrawing patriarch = sheet.createDrawingPatriarch();

        XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex() + 1, cell.getRowIndex() + 1);
        anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);

        patriarch.createPicture(anchor, workbook.addPicture(image, pictureType));
    }

    /**
     * 为单元格设置
     * @param cell
     * @param url
     */
    public static void setLink(XSSFCell cell, String url) {
        if(url == null) {
            return;
        }
        CreationHelper createHelper = cell.getSheet().getWorkbook().getCreationHelper();
        XSSFHyperlink  hyperlink = (XSSFHyperlink) createHelper.createHyperlink(HyperlinkType.URL);
        hyperlink.setAddress(url);

        cell.setHyperlink(hyperlink);

        // 设置为超链接的样式
//        XSSFCellStyle linkStyle = cell.getCellStyle();
//        XSSFFont font = cell.getSheet().getWorkbook().createFont();
//        font.setUnderline((byte)1);
    }

    /**
     * 像素转换为字符长度
     * @param width
     * @return
     */
    public static double pixelToWidth(int width) {
       return (width * 256.0 / Units.DEFAULT_CHARACTER_WIDTH);
    }

    /**
     * 像素转换为点为单位的高度
     * @return
     */
    public static double pixelToHeight(int height) {
        return Units.pixelToPoints(height) * 20;
    }
}
