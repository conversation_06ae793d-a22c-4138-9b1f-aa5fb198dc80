/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.handler.office;



import cn.mftcc.doc.components.docmanage.model.entity.BaseTagEntity;

import java.util.Map;

/**
 * office 解析处理
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
public interface DocParseHandler {
    /**
     * 根据 tag 标签数据 {@link } 解析渲染 docPath 下的文档模板
     * 渲染逻辑可参考 https://www.yuque.com/docs/share/fd41e412-f504-4d41-b830-9dce68d09bf6?#%20%E3%80%8ADOC%20%E6%B8%B2%E6%9F%93%E6%A0%BC%E5%BC%8F%E8%AE%B0%E5%BD%95%EF%BC%88%E4%B8%80%EF%BC%89%E3%80%8B
     *
     * @param docPath 文档地址
     * @param tagMap 文档内的标签属性
     * @return
     */
    <T> T parseOfficeByTag(String docPath, Map<String, BaseTagEntity> tagMap);
}
