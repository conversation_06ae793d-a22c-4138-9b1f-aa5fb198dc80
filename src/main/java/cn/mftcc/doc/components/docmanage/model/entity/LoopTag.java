/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class LoopTag extends BaseTagEntity {
    /**
     * Map 数据格式，格式大致为
     * [
     *    {
     *       "name": {
     *         "type": "text",
     *         "tag": "名称",
     *          value": "春季服装"
     *       }
     *    }
     * ]
     */
    private List<Map<String, String>> value;
}
