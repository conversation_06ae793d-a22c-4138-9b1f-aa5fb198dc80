/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.policy;

import cn.mftcc.doc.components.docmanage.el.render.RenderContext;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;
import com.deepoove.poi.data.HyperlinkTextRenderData;
import com.deepoove.poi.data.TextRenderData;

import com.mftcc.doc.poi.el.util.SheetUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;

/**
 * 渲染文本
 * 
 * <AUTHOR>
 *
 */
public class TextRenderPolicy extends AbstractRenderPolicy<Object> {

    @Override
    protected boolean validate(Object data) {
        return null != data;
    }

    @Override
    public void doRender(RenderContext<Object> context, ElementTemplate eleTemplate) throws Exception {
        Helper.renderTextRun(context.getCell(), context.getData(), eleTemplate);
    }

    public static class Helper {

        public static void renderTextRun(XSSFCell cell, Object data, ElementTemplate eleTemplate) {
            XSSFCell textCell = cell;
            // 设置超链接
            if (data instanceof HyperlinkTextRenderData) {
                textCell = createHyperlink(textCell, ((HyperlinkTextRenderData) data).getUrl());
            }

            TextRenderData wrapper = wrapper(data);
            // 设置样式
            // wrapper.getStyle();

            String text = wrapper.getText();
            switch (textCell.getCellType()) {
                case BOOLEAN:
                    break;
                case NUMERIC:
                    break;
                case BLANK:
                    break;
                default:
                    textCell.setCellValue(textCell.getStringCellValue().replace(eleTemplate.getSource(), text));
                    break;
            }
        }

        private static TextRenderData wrapper(Object obj) {
            TextRenderData text = obj instanceof TextRenderData ? (TextRenderData) obj
                    : new TextRenderData(obj.toString());
            return null == text.getText() ? new TextRenderData("") : text;
        }

        private static XSSFCell createHyperlink(XSSFCell cell, String url) {
            SheetUtil.setLink(cell, url);
            return cell;
        }
    }
}
