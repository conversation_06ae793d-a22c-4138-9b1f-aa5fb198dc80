/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.resolver;

import cn.mftcc.doc.components.docmanage.el.template.MetaTemplate;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import java.util.List;

/**
 * 解析所有的标签
 *
 * <AUTHOR>
 * @date 2021/1/21
 */
public interface Resolver {
    /**
     * 获取目标 sheet 页中的所有标签信息
     *
     * @param sheet
     * @return
     */
    List<MetaTemplate> resolveSheet(XSSFSheet sheet);
    /**
     * 获取目标 sheet 页下某一行的标签信息
     */
    List<MetaTemplate> resolveSheetRow(XSSFSheet sheet, XSSFRow row);
}
