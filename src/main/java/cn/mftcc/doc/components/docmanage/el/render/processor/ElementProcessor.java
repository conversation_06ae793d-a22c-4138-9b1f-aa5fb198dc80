/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render.processor;


import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;
import cn.mftcc.doc.components.docmanage.el.policy.RenderPolicy;
import cn.mftcc.doc.components.docmanage.el.render.compute.RenderDataCompute;

import cn.mftcc.doc.components.docmanage.el.resolver.Resolver;
import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;
import cn.mftcc.doc.components.docmanage.el.template.PictureTemplate;
import org.apache.commons.lang3.ClassUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

public class ElementProcessor extends DefaultTemplateProcessor {

    private static final Logger logger = LoggerFactory.getLogger(ElementProcessor.class);

    public ElementProcessor(XSSFSheetTemplate template, Resolver resolver,
                            RenderDataCompute renderDataCompute) {
        super(template, resolver, renderDataCompute);
    }

    @Override
    public void visit(PictureTemplate pictureTemplate) {
        visit((ElementTemplate) pictureTemplate);
    }

    @Override
    public void visit(CellTemplate cellTemplate) {
        visit((ElementTemplate) cellTemplate);
    }

    void visit(ElementTemplate eleTemplate) {
        RenderPolicy policy = eleTemplate.findPolicy(template.getConfig());

        Objects.requireNonNull(policy, "Cannot find render policy: [" + eleTemplate.getTagName() + "]");

        logger.info("Start render Template {}, Sign:{}, policy:{}", eleTemplate, eleTemplate.getSign(),
                ClassUtils.getShortClassName(policy.getClass()));
        policy.render(eleTemplate, renderDataCompute.compute(eleTemplate.getTagName()), template);
    }

}
