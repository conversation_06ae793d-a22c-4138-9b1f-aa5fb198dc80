/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.resolver;

import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.PictureTemplate;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFPicture;

/**
 * 创建元素模板工厂
 * 
 * <AUTHOR>
 *
 */
public interface ElementTemplateFactory {

    CellTemplate createCellTemplate(Configure config, String tag, XSSFCell run);

    PictureTemplate createPictureTemplate(Configure config, String tag, XSSFPicture pic);
}
