/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MergeTag extends BaseTagEntity {
    private List<BaseTagEntity> values = new ArrayList<>();
}
