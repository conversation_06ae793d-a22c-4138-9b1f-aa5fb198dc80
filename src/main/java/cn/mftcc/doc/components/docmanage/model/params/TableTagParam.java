/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.params;

import cn.mftcc.doc.components.docmanage.model.entity.BaseTagEntity;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TableTagParam extends BaseTagEntity {
    private Map<String, JSONObject> value;
}
