/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import cn.mftcc.doc.common.utils.R;
import cn.mftcc.doc.components.docmanage.entity.DocManageEntity;
import cn.mftcc.doc.components.docmanage.model.entity.Parse;
import cn.mftcc.doc.components.docmanage.service.ParseRenderService;
import cn.mftcc.doc.components.docmanage.web.model.DocModel;
import cn.mftcc.doc.components.docmanage.web.utils.DocConstant;
import cn.mftcc.doc.components.docmanage.web.utils.DocumentManager;
import cn.mftcc.doc.components.docmanage.web.utils.FileUtility;
import cn.mftcc.doc.components.docmanage.web.utils.MockUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping(value = "/demo")
//@CrossOrigin(origins = "*", maxAge = 3600)
public class DemoController {
    private static final Log log = LogFactory.get();
    @Value("${storage-folder:app_data}")
    private String folder;


    @Autowired
    private ParseRenderService parseRenderService;

    @RequestMapping(value = {"/getTags/{docNo}"})
    @ResponseBody
    public JSONObject getTags(@PathVariable("docNo") Integer docNo) {
        log.debug("标签模版接收的参数:{}", docNo);
        ClassPathResource resource = new ClassPathResource("demodata/tags.json");
        File file;
        try {
            file = resource.getFile();
        } catch (IOException e) {
            String userDir = System.getProperty("user.dir");
            String newFilePath = userDir + File.separator + "config/tags.json";
            file = FileUtil.file(newFilePath);
        }

        FileReader fileReader = new FileReader(file);
        return JSON.parseObject(fileReader.readString());
    }

    @RequestMapping(value = {"/renderDocTest/{docNo}"})
    @ResponseBody
    public R renderDocTest(@PathVariable("docNo") Integer docNo) {
        ClassPathResource resource = new ClassPathResource("demodata/tags.json");
        File file;
        try {
            file = new File("D:/json/tag.json");
        } catch (Exception e) {

            String userDir = System.getProperty("user.dir");
            String newFilePath = userDir + File.separator + "config/tags.json";
            file = FileUtil.file(newFilePath);
        }

        FileReader fileReader = new FileReader(file);
        JSONObject tagData = JSON.parseObject(fileReader.readString());
        ClassPathResource dataresource = new ClassPathResource("demodata/data.json");

        File datafile;
        try {
            datafile = new File("D:/json/data.json");
        } catch (Exception e) {
            String userDir = System.getProperty("user.dir");
            String newFilePath = userDir + File.separator + "config/data.json";
            datafile = FileUtil.file(newFilePath);
        }

        FileReader datafileReader = new FileReader(datafile);

        String busStr = datafileReader.readString();
        JSONObject busData = JSON.parseObject(busStr);
        DocManageEntity docManageEntity = new DocManageEntity();
        String docVersion = DateTime.now().toString("yyyyMMddHHmmss");
        String newFilePath = "D:\\work\\microservices\\prdnew\\web\\mftcc-doc-web\\static\\template\\new.docx";
        Map map = new HashMap();
        map.put("docVersion", docVersion);
        map.put("docNo", docNo);
        Parse parse = new Parse();
        parse.setDocPath(newFilePath);
        parse.setBusData(busData);
        parse.setTagData(tagData);

        try {
//            String type = FileUtility.getFileType(docManageEntity.getDocName()).toString().toLowerCase();
            String type ="text";
            FileUtil.mkParentDirs(newFilePath);
            if ("text".equals(type)){
                parseRenderService.renderWordTemplate(parse, newFilePath);
            }else if("spreadsheet".equals(type)){
                parseRenderService.renderExcelTemplate(parse, newFilePath);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.ok(map);
    }



    @RequestMapping(value = {"/renderDoc/{docNo}"})
    @ResponseBody
    public R renderDoc(@PathVariable("docNo") Integer docNo) {
        ClassPathResource resource = new ClassPathResource("demodata/tags.json");
        File file;
        try {
            file = resource.getFile();
        } catch (IOException e) {
            String userDir = System.getProperty("user.dir");
            String newFilePath = userDir + File.separator + "config/tags.json";
            file = FileUtil.file(newFilePath);
        }

        FileReader fileReader = new FileReader(file);
        JSONObject tagData = JSON.parseObject(fileReader.readString());
        ClassPathResource dataresource = new ClassPathResource("demodata/data.json");

        File datafile;
        try {
            datafile = dataresource.getFile();
        } catch (IOException e) {
            String userDir = System.getProperty("user.dir");
            String newFilePath = userDir + File.separator + "config/data.json";
            datafile = FileUtil.file(newFilePath);
        }

        FileReader datafileReader = new FileReader(datafile);

        String busStr = MockUtil.renderJsonData(datafileReader.readString());
        JSONObject busData = JSON.parseObject(busStr);
        DocManageEntity docManageEntity = new DocManageEntity();
        String docVersion = DateTime.now().toString("yyyyMMddHHmmss");
        String newFilePath = folder + docManageEntity.getDocDir() + File.separator + File.separator + DocConstant.RENDER_PATH + File.separator + docVersion + "." + docManageEntity.getDocType();
        Map map = new HashMap();
        map.put("docVersion", docVersion);
        map.put("docNo", docNo);
        Parse parse = new Parse();
        parse.setDocPath(docManageEntity.getDocPath());
        parse.setBusData(busData);
        parse.setTagData(tagData);

        try {
            String type = FileUtility.getFileType(docManageEntity.getDocName()).toString().toLowerCase();
            FileUtil.mkParentDirs(newFilePath);
            if ("text".equals(type)){
                parseRenderService.renderWordTemplate(parse, newFilePath);
            }else if("spreadsheet".equals(type)){
                parseRenderService.renderExcelTemplate(parse, newFilePath);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.ok(map);
    }

    @RequestMapping(value = {"/renderDocView"})
    public String renderDoc(@RequestParam Map map, Model model) {
        Integer docNo = Integer.parseInt((String) map.get("docNo"));
        String docVersion = (String) map.get("docVersion");
        DocManageEntity docManage = new DocManageEntity();
        DocModel docModel = new DocModel(JSONObject.parseObject(JSONUtil.toJsonStr(docManage)));

        docModel.document.url = DocumentManager.getRenderFileUri(JSONUtil.toJsonStr(docManage), docVersion);
        docModel.changeType("view", null);
        model.addAttribute("file", docModel);
        model.addAttribute("docNo", docNo);
        model.addAttribute("json", JSON.toJSONString(docModel));
        model.addAttribute("docserviceApiUrl", SpringUtil.getProperty("files.docservice.url.api"));
        return "editor";
    }

    @RequestMapping(value = {"/renderDocConvertPdf"})
    @ResponseBody
    public R renderDocConvertPdf(@RequestParam Map map) {
        Integer docNo = Integer.parseInt((String) map.get("docNo"));
        String docVersion = (String) map.get("docVersion");
        DocManageEntity docManage = new DocManageEntity();

        String docUrl = DocumentManager.getRenderFileUri(JSONUtil.toJsonStr(docManage), docVersion);
        String newFilePath = folder + docManage.getDocDir() + File.separator + File.separator + DocConstant.RENDER_PATH + File.separator + docVersion + "." + docManage.getDocType();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("async", false);
        jsonObject.put("filetype", docManage.getDocType());
        jsonObject.put("outputtype", "pdf");
        jsonObject.put("key", RandomUtil.randomString(20));
        jsonObject.put("url", docUrl);
        String result = HttpRequest.post(SpringUtil.getProperty("files.docservice.url.converter")).body(jsonObject.toJSONString()).execute().body();
        JSONObject resultJson = JSONObject.parseObject(result);
        System.out.println(result);
        String pdfPath = newFilePath.replace(docManage.getDocType(), "pdf");
        HttpUtil.downloadFile(resultJson.getString("fileUrl"), pdfPath);
        Map rmap = new HashMap();
        rmap.put("docVersion", docVersion);
        rmap.put("docNo", docNo);
        rmap.put("url", "/demo/downloadPDF?docNo=" + docNo + "&docVersion=" + docVersion);
        return R.ok(rmap);
    }

    @RequestMapping("/downloadPDF")
    public void downloadPdf(@RequestParam Map map, HttpServletResponse response) throws IOException {
        Integer docNo = Integer.parseInt((String) map.get("docNo"));
        String docVersion = (String) map.get("docVersion");
        DocManageEntity docManage =new DocManageEntity();
        String newFilePath = folder + docManage.getDocDir() + File.separator + File.separator + DocConstant.RENDER_PATH + File.separator + docVersion + ".pdf";
        File file = FileUtil.file(newFilePath);
        if (file.exists()) {
            String pdfName = docManage.getDocName().replace(docManage.getDocType(), "pdf");
            MediaType mediaType = MediaTypeFactory.getMediaType(docManage.getDocName()).get();
            // 编码格式，防止乱码
            response.setCharacterEncoding("UTF-8");
            // 设置头部 content-type 类型，不同文件不同类型
            response.setHeader("content-type", mediaType.toString());
            // 设置文件在浏览器中打开还是下载，由前端去控制
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(pdfName, "UTF-8"));
            // 从response 获取输出流
            ServletOutputStream os = response.getOutputStream();
            // 利用hutool工具包将文件内容写入到输出流
            FileUtil.writeToStream(file, os);
            // 关闭输出流
            os.close();
        }
    }

}
