/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.resolver;


import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;
import cn.mftcc.doc.components.docmanage.el.template.MetaTemplate;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFPicture;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;

/**
 * <AUTHOR>
 * @date 2021/1/21
 */
public class SheetTemplateResolver extends AbstractResolver {
    private static Logger logger = LoggerFactory.getLogger(SheetTemplateResolver.class);

    private ElementTemplateFactory elementTemplateFactory;

    public SheetTemplateResolver(Configure config) {
        this(config, config.getElementTemplateFactory());
    }

    private SheetTemplateResolver(Configure config, ElementTemplateFactory elementTemplateFactory) {
        super(config);
        this.elementTemplateFactory = elementTemplateFactory;
    }

    @Override
    public List<MetaTemplate> resolveSheet(XSSFSheet sheet) {
        return resolveSheetRow(sheet, null);
    }

    @Override
    public List<MetaTemplate> resolveSheetRow(XSSFSheet sheet, XSSFRow row) {
        List<MetaTemplate> metaTemplates = new ArrayList<>();
        if (null == sheet) {
            return metaTemplates;
        }
        logger.info("Resolve the document start...");
        if( row == null ) {
            metaTemplates.addAll(resolveExcelRows(sheet));
        } else {
            metaTemplates.addAll(resolveExcelRow(row));
        }

        logger.info("Resolve the document end, resolve and create {} MetaTemplates.", metaTemplates.size());
        return metaTemplates;
    }

    private List<MetaTemplate> resolveExcelRows(XSSFSheet sheet) {
        List<MetaTemplate> metaTemplates = new ArrayList<>();
        int rowCount = sheet.getLastRowNum() + 1;
        if (rowCount == 0) {
            return metaTemplates;
        }

        for (int r = 0; r < rowCount; ++r) {
            XSSFRow row = sheet.getRow(r);
            if(row == null) {
                continue;
            }
            metaTemplates.addAll(resolveExcelRow(row));
        }

        return metaTemplates;
    }

    private List<MetaTemplate> resolveExcelRow(XSSFRow row) {
        List<MetaTemplate> metaTemplates = new ArrayList<>();
        int cellCount = row.getPhysicalNumberOfCells();

        for (int c = 0; c < cellCount; ++c) {
            XSSFCell xssfCell = row.getCell(c);
            if(xssfCell == null) {
                continue;
            }
            if(xssfCell.getCellType() != CellType.STRING) {
                continue;
            }
            String text = xssfCell.getStringCellValue();
            List<ElementTemplate> cellTemplate = parseTemplateFactorys(text, xssfCell, xssfCell);

            if(cellTemplate != null) {
                addNewMeta(metaTemplates, cellTemplate);
            }
        }
        return metaTemplates;
    }

    private void addNewMeta(final List<MetaTemplate> metaTemplates,
                            List<? extends MetaTemplate> newMeta) {
        metaTemplates.addAll(newMeta);
    }

    private <T extends MetaTemplate> void addNewMeta(final List<MetaTemplate> metaTemplates,
                                                     T newMeta) {
        addNewMeta(metaTemplates, Collections.singletonList(newMeta));
    }

    List<ElementTemplate> parseTemplateFactorys(String text, Object obj, XSSFCell cell) {
        List<ElementTemplate> elementTemplates = new LinkedList<>();
        if (null == text) {
            return null;
        }
        if (cellTemplatePattern.matcher(text).matches()) {
            Matcher matcher = templatePattern.matcher(text);
            while (matcher.find()) {
                ElementTemplate elementTemplate = parseTemplateFactory(matcher.group(), obj, cell);
                if(elementTemplate != null) {
                    elementTemplates.add(elementTemplate);
                }
            }
        }
        return elementTemplates;
    }

    ElementTemplate parseTemplateFactory(String text, Object obj, XSSFCell cell) {
        logger.debug("Resolve where text: {}, and create ElementTemplate for {}", text, obj.getClass());
        // 对于 excel 来说，一个 单元格内可能存在多个标签

        String tag = gramerPattern.matcher(text).replaceAll("").trim();
        if (obj.getClass() == XSSFCell.class) {
            return elementTemplateFactory.createCellTemplate(config, tag, (XSSFCell) obj);
        } else if (obj.getClass() == XSSFPicture.class) {
            return elementTemplateFactory.createPictureTemplate(config, tag, (XSSFPicture) obj);
        }

        return null;
    }
}
