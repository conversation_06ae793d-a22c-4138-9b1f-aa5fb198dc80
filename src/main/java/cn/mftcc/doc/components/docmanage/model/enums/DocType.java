/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.enums;

/**
 * 文档类型
 * <AUTHOR>
 * @date 2021/1/26
 */
public enum DocType implements ValueEnum<String> {
    /**
     * word
     */
    WORD("word"),
    /**
     * excel
     */
    EXCEL("excel");

    private final String value;
    DocType(String value) {
        this.value = value;
    }

    @Override
    public String getValue() {
        return value;
    }
}
