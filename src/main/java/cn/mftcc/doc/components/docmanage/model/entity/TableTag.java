/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 表格标签
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TableTag extends BaseTagEntity {
    /**
     * Map 数据格式，格式大致为
     * [
     *    {
     *       "name": {
     *         "type": "text",
     *         "tag": "名称",
     *          value": "春季服装"
     *       }
     *    }
     * ]
     */
    private List<Map<String, ? extends BaseTagEntity>> value;
    private String listLoopPartten;//列表循环方式
}
