/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.handler.tag;

import cn.mftcc.doc.components.docmanage.model.entity.BaseTagEntity;
import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * 标签转换处理
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
public interface TagConvertHandler {
    /**
     * 将业务数据与原始标签数据整合为标签属性
     * 数据的具体样式，可参考 https://www.yuque.com/docs/share/fd41e412-f504-4d41-b830-9dce68d09bf6?#%20%E3%80%8ADOC%20%E6%B8%B2%E6%9F%93%E6%A0%BC%E5%BC%8F%E8%AE%B0%E5%BD%95%EF%BC%88%E4%B8%80%EF%BC%89%E3%80%8B
     *
     * @param tagData 标签数据
     * @param busData 业务数据
     * @return
     */
    Map<String, BaseTagEntity> toBusTag(JSONObject tagData, JSONObject busData);
}
