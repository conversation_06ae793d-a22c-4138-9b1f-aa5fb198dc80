/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.web.model;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.components.docmanage.entity.DocManageEntity;
import cn.mftcc.doc.components.docmanage.web.utils.DocumentManager;
import cn.mftcc.doc.components.docmanage.web.utils.FileUtility;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;


import javax.xml.ws.Service;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DocModel {
    //文档配置属性

    /**
     * onlineOffice文件服务器
     */
    public  String url;



    /**
     * 文档类型
     */
    public String documentType;
    /**
     * 文档显示高度
     */
    public String height = "100%";
    /**
     * 文档显示宽度
     */
    public String width = "100%";
    /**
     * token认证码，默认为空
     */
    public String token;
    /**
     * 访问设备类型“desktop”，“mobile”
     */
    public String type = "desktop";

    //文档配置子属性
    public Document document;
    public EditorConfig editorConfig;

    //其他附加属性
    public String mode = "edit";
    public String lang = "zh";
    private String uid = "admin";
    private String uname = "admin";

    /**
     * 创建文档配置对象
     *
     * @param json
     */
    public DocModel(JSONObject json) {

        try{

            url=json.getString("officeServer");
            documentType = FileUtility.getFileType(json.getString("docName")).toString().toLowerCase();

            document = new Document();
            document.title = json.getString("docShowName");
            document.url = json.getString("docUrl");
            document.fileType = json.getString("docType");
            document.key = RandomUtil.randomString(20);


            editorConfig = new EditorConfig(null);
            editorConfig.callbackUrl = json.getString("callbackUrl");
            editorConfig.lang = lang;
            editorConfig.user.id = uid;
           Object object =json.get("pluginsUrlAry");
           String[]pluginsUrlAry =(String[])json.get("pluginsUrlAry");

            editorConfig.plugins.pluginsData=pluginsUrlAry;
            editorConfig.user.name = uname;
            editorConfig.customization.goback.url =json.getString("goBackUrl");

            changeType(json.getString("openType"), json.getString("clientType"));
        }catch (Exception e){
            e.printStackTrace();
            MFLogger.error("创建文档配置对象错误",e);
            throw new ServiceException("创建文档配置对象错误","",e);

        }
    }




    public void changeType(String currMode, String currType) {
        if (currMode != null) {
            mode = currMode;
        }
        if (currType != null) {
            type = currType;
        }

//        Boolean canEdit = DocumentManager.getEditedExts().contains(FileUtility.getFileExtension(document.title));
        Boolean canEdit ="edit".equals(currMode)?true:false;
//
        editorConfig.mode = canEdit && !"view".equals(mode) ? "edit" : "view";

        document.permissions = new Permissions(mode, type, canEdit);

        if ("embedded".equals(type)) {
            initDesktop();
        }
    }

    public void initDesktop() {
        editorConfig.initDesktop(document.url);
    }

    public class Document {
        /**
         * 文档类型，必须小写，例：docx
         */
        public String fileType;
        /**
         * 文档唯一标识，每次保存和编辑都必须重新生成密钥，长度128字符
         */
        public String key;
        /**
         * 文档编辑器显示名称，长度128字符
         */
        public String title;
        /**
         * 文档访问路径
         */
        public String url;
        /**
         * 权限配置
         */
        public Permissions permissions;
        /**
         * 文档信息配置
         */
        public Info info;
    }

    public class Info {
        /**
         * 定义文档所有者/创建者的名称
         */
        public String owner;
        /**
         * 定义文档上传日期
         */
        public String uploaded;
    }

    public class Permissions {
        /**
         * 当前版本已弃用，使用onRequestRestore属性
         */
        public Boolean changeHistory;
        /**
         * 是否可以注释
         */
        public Boolean comment;
        /**
         * 是否可以复制
         */
        public Boolean copy;
        /**
         * 是否可以下载
         */
        public Boolean download;
        /**
         * 是否可以编辑
         */
        public Boolean edit;
        /**
         * 是否可以打印
         */
        public Boolean print;
        /**
         * 是否可以填写报表，默认和mode的值一致
         */
        public Boolean fillForms;
        /**
         * 是否可以修改内容，当edit为true时有效
         */
        public Boolean modifyContentControl;
        /**
         * 是否可是使用筛选器，当mode为edit时有效，Excel文档有效
         */
        public Boolean modifyFilter;
        /**
         * 重命名，弃用，改为onRequestRename
         */
        public Boolean rename;
        /**
         * 是否可以审阅
         */
        public Boolean review;


        public Permissions(String mode, String type, Boolean canEdit) {
            comment = !"view".equals(mode) && !"fillForms".equals(mode) && !"embedded".equals(mode) && !"blockcontent".equals(mode);
            download = true;
            edit = canEdit && ("edit".equals(mode) || "filter".equals(mode) || "blockcontent".equals(mode));
            fillForms = !"view".equals(mode) && !"comment".equals(mode) && !"embedded".equals(mode) && !"blockcontent".equals(mode);
            modifyFilter = !"filter".equals(mode);
            modifyContentControl = !"blockcontent".equals(mode);
            review = "edit".equals(mode) || "review".equals(mode);
        }
    }


    public class EditorConfig {
        public HashMap<String, Object> actionLink = null;
        public String callbackUrl;

        public  Plugins plugins;
        /**
         * 定义创建文档url ，可以使用onRequestCreateNew代替
         */
        public String createUrl;
        /**
         * 语言
         */
        public String lang = "zh";
        /**
         * 指定度量单位us或ca，默认为空
         */
        public String location;
        /**
         * 编辑模式edit或view
         */
        public String mode = "edit";


        public User user;
        public Customization customization;
        public Embedded embedded;

        public EditorConfig(String actionData) {
            if (actionData != null) {
                actionLink = JSON.parseObject(actionData, new TypeReference<HashMap<String, Object>>() {
                });
            }
            user = new User();
            plugins=new Plugins();
            customization = new Customization();
        }

        public void initDesktop(String url) {
            embedded = new Embedded();
            embedded.saveUrl = url;
            embedded.embedUrl = url;
            embedded.shareUrl = url;
            embedded.toolbarDocked = "top";
        }

        public class User {
            /**
             * 用户ID
             */
            public String id = "admin";
            /**
             * 用户姓名
             */
            public String name = "admin";
        }
        public class Plugins {
            String []pluginsData;
        }





        public class Embedded {
            public String saveUrl;
            public String embedUrl;
            public String shareUrl;
            public String toolbarDocked;
        }
    }

}
