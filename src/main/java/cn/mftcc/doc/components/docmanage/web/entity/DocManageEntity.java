/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.web.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-13 09:29:48
 */
@Data
public class DocManageEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 文档编号
     */
    private Integer docNo;
    /**
     * 文档名称
     */
    private String docName;
    /**
     * 文档状态
     */
    private String docSts;
    /**
     * 文档类型
     */
    private String docType;

    /**
     * 文档版本编号
     */
    private String docVerNo;
    /**
     * 文档文件夹名称
     */
    private String docDir;
    /**
     * 文档编辑状态
     */
    private String docEditSts;
    /**
     * 文档编辑KEY
     */
    private String docEditKey;

    /**
     * 文档路径
     */
    private String docPath;
    /**
     * 创建日期
     */
    private Date createDate;
    /**
     * 创建用户
     */
    private String createUser;
    /**
     * 版本启用状态
     */
    private String docVerSts;

    @Override
    public String toString() {
        return "DocManageEntity{" +
                "docNo=" + docNo +
                ", docName='" + docName + '\'' +
                ", docSts='" + docSts + '\'' +
                ", docType='" + docType + '\'' +
                ", docVerNo='" + docVerNo + '\'' +
                ", docDir='" + docDir + '\'' +
                ", docPath='" + docPath + '\'' +
                ", createDate=" + createDate +
                ", createUser='" + createUser + '\'' +
                '}';
    }
}
