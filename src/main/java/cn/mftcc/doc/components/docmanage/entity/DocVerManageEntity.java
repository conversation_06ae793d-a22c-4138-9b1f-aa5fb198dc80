/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件版本表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-13 09:29:48
 */
@Data
@TableName("doc_ver_manage")
public class DocVerManageEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 文档编号
	 */
	@TableId
	private Integer docNo;
	/**
	 * 文档版本编号
	 */
	private String docVerNo;
	/**
	 * 文档路径
	 */
	private String docPath;
	/**
	 * 创建日期
	 */
	private Date createDate;
	/**
	 * 创建用户
	 */
	private String createUser;
	/**
	 * 版本启用状态
	 */
	private String docVerSts;

}
