/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.handler.office;


import cn.mftcc.doc.components.docmanage.el.XSSFTemplate;
import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.config.ConfigureBuilder;
import cn.mftcc.doc.components.docmanage.el.policy.HackLoopTableRenderPolicy;
import cn.mftcc.doc.components.docmanage.model.entity.BaseTagEntity;
import cn.mftcc.doc.components.docmanage.model.entity.MergeTag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Slf4j
@Component("excelParse")
public class ExcelParseHandler implements DocParseHandler {
    private final HackLoopTableRenderPolicy hackLoopTableRenderPolicy = new HackLoopTableRenderPolicy();
    private final WordParseHandler docParseHandler = new WordParseHandler();

    /**
     * 解析 Excel 标签
     * 实际上本方法只是一个壳子，实际解析方法在 {@link WordParseHandler#convertTagToData(Map, Consumer)}
     * @param excelPath excel 路径
     * @param tagMap 标签 map 数据
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T> T parseOfficeByTag(String excelPath, Map<String, BaseTagEntity> tagMap) {
        ConfigureBuilder configureBuilder = Configure.builder();

        MergeTag mergeTag = (MergeTag) tagMap.get("MERGE_GROUP");
        List<String> tagNames = mergeTag.getValues().stream().map(BaseTagEntity::getTag).collect(Collectors.toList());
        tagMap.remove("MERGE_GROUP");

        Map<String, Object> templateData = docParseHandler.convertTagToData(tagMap,
                (key) -> configureBuilder.bind(key, hackLoopTableRenderPolicy));

        XSSFTemplate xssfTemplate = XSSFTemplate
                .compile(excelPath, configureBuilder.build())
                .group(tagNames)
                .render(templateData);

        return (T)xssfTemplate;
    }
}
