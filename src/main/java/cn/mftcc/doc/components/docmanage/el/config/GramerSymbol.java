/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.config;

/**
 * 内置模板语法
 * 
 * <AUTHOR>
 */
public enum GramerSymbol {

    /**
     * 模板中的图片
     */
    IMAGE('@'),

    /**
     * 模板中的文本
     */
    TEXT('\0'),

    /**
     *
     * 模板中的文本，文本别名，与#this兼容，并且与现有的表编写不冲突：{{=＃this}}
     */
    TEXT_ALIAS('='),

    /**
     * 模板中的表格
     */
    TABLE('#'),

    /**
     * 模板中的编号
     */
    NUMBERING('*'),

    /**
     * 模板中的嵌套/合并/包含/引用
     */
    DOCX_TEMPLATE('+'),

    /**
     * 块(if & for each) 开始
     */
    ITERABLE_START('?'),

    /**
     * 块结束
     */
    BLOCK_END('/');

    private char symbol;

    private GramerSymbol(char symbol) {
        this.symbol = symbol;
    }

    public char getSymbol() {
        return this.symbol;
    }

    @Override
    public String toString() {
        return String.valueOf(this.symbol);
    }

}
