/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.entity;


import cn.mftcc.doc.components.docmanage.model.enums.TagType;
import lombok.Data;

/**
 * 标签基础实体
 *
 * <AUTHOR>
 * @date 2021/1/18
 */
@Data
public class BaseTagEntity {
     /**
      * 标签类型
      */
     private TagType type;
     /**
      * 标签对应的模板内的标签名
      */
     private String tag;
     /**
      * 是否为分组字段
      */
     private boolean group = false;
}
