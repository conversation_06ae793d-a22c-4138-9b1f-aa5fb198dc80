/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.resolver;



import cn.mftcc.doc.components.docmanage.el.config.Configure;
import com.deepoove.poi.util.RegexUtils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 初始模式
 */
public abstract class AbstractResolver implements Resolver {

    protected final Configure config;
    protected Pattern cellTemplatePattern;
    protected Pattern templatePattern;
    protected Pattern gramerPattern;

    private static final String CELL_FORMAT_TEMPLATE = "{0}{1}{2}{3}{4}{5}";
    private static final String FORMAT_TEMPLATE = "{0}{1}{2}{3}";
    private static final String FORMAT_GRAMER = "({0})|({1})";

    public AbstractResolver(Configure config) {
        this.config = config;
        patternCreated();
    }

    void patternCreated() {
        String sign = getGramarRegex(config);
        String prefix = RegexUtils.escapeExprSpecialWord(config.getGramerPrefix());
        String suffix = RegexUtils.escapeExprSpecialWord(config.getGramerSuffix());

        cellTemplatePattern = Pattern
                .compile(MessageFormat.format(CELL_FORMAT_TEMPLATE, "[\\s\\S]*", prefix, sign, config.getGrammerRegex(), suffix, "[\\s\\S]*"));
        templatePattern = Pattern
                .compile(MessageFormat.format(FORMAT_TEMPLATE, prefix, sign, config.getGrammerRegex(), suffix));
        gramerPattern = Pattern.compile(MessageFormat.format(FORMAT_GRAMER, prefix, suffix));
    }

    String getGramarRegex(Configure config) {
        List<Character> gramerChar = new ArrayList<Character>(config.getGramerChars());
        StringBuilder reg = new StringBuilder("(");
        for (int i = 0;; i++) {
            Character chara = gramerChar.get(i);
            String word = RegexUtils.escapeExprSpecialWord(chara.toString());
            if (i == gramerChar.size() - 1) {
                reg.append(word).append(")?");
                break;
            } else {
                reg.append(word).append("|");
            }
        }
        return reg.toString();
    }

    public Pattern getTemplatePattern() {
        return templatePattern;
    }

    public Pattern getGramerPattern() {
        return gramerPattern;
    }

    public Pattern getCellTemplatePattern() {
        return cellTemplatePattern;
    }
}
