/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.enums;

import org.springframework.util.Assert;

import java.util.stream.Stream;

/**
 * 枚举类接口
 *
 * @param <T> 值类型，例如 {@link String}
 * <AUTHOR>
 */
public interface ValueEnum<T> {

    /**
     * 将值转换为相应的枚举
     *
     * @param enumType 枚举类型
     * @param value    数据值
     * @param <V>      通用值
     * @param <E>      通用枚举
     * @return 对应的枚举
     */
     static <V, E extends ValueEnum<V>> E valueToEnum(Class<E> enumType, V value) {
        Assert.notNull(enumType, "枚举类型不能为空");
        Assert.notNull(value, "值不能为空");
        Assert.isTrue(enumType.isEnum(), "类型必须是枚举类型");

        return Stream.of(enumType.getEnumConstants())
                .filter(item -> item.getValue().equals(value))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到对应的标签类型: " + value));
    }

    /**
     * 获取枚举的值
     *
     * @return 枚举的值
     */
    T getValue();

}
