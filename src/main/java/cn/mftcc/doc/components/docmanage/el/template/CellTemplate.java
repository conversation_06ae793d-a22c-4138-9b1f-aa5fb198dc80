/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.template;


import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.policy.RenderPolicy;


import cn.mftcc.doc.components.docmanage.el.render.processor.Visitor;
import org.apache.poi.xssf.usermodel.XSSFCell;

/**
 * 基本 excel 模板元素: XSSFCell
 * 
 * <AUTHOR>
 */
public class CellTemplate extends ElementTemplate {

    protected XSSFCell cell;

    public CellTemplate() {
    }

    public CellTemplate(String tagName, XSSFCell run) {
        this.tagName = tagName;
        this.cell = run;
    }

    /**
     * @return the run
     */
    @Override
    public XSSFCell getCell() {
        return cell;
    }

    /**
     * @param cell the run to set
     */
    @Override
    public void setCell(XSSFCell cell) {
        this.cell = cell;
    }

    @Override
    public void accept(Visitor visitor) {
        visitor.visit(this);
    }

    @Override
    public RenderPolicy findPolicy(Configure config) {
        RenderPolicy policy = config.getCustomPolicy(tagName);
        if (null == policy) {
            policy = config.getDefaultPolicy(sign);
        }
        return null == policy ? config.getTemplatePolicy(this.getClass()) : policy;
    }

}
