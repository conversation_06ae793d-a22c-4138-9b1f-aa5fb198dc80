/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.entity;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/2/3
 */
@Data
public class Parse {
    /**
     * 标签组编号
     */
    private JSONObject tagData;
    /**
     * 业务数据（JSON 格式）
     */
    private JSONObject busData;
    /**
     * 模板文档路径
     */
    private String docPath;
}
