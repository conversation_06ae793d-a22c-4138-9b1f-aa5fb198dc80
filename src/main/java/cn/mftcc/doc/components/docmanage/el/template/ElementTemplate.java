/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.template;



import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.policy.RenderPolicy;
import org.apache.poi.xssf.usermodel.XSSFCell;

/**
 * sign + tagName == source
 * 
 * <AUTHOR>
 */
public abstract class ElementTemplate implements MetaTemplate {
    protected Character sign;
    protected String tagName;
    protected String source;
    protected XSSFCell cell;
    protected int row;
    protected int col;

    public ElementTemplate() {
    }

    /**
     * @return the tagName
     */
    public String getTagName() {
        return tagName;
    }

    /**
     * @param tagName the tagName to set
     */
    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Character getSign() {
        return sign;
    }

    public void setSign(Character sign) {
        this.sign = sign;
    }

    @Override
    public String toString() {
        return source;
    }

    @Override
    public String variable() {
        return source;
    }

    public XSSFCell getCell() {
        return cell;
    }

    public void setCell(XSSFCell cell) {
        this.cell = cell;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public int getCol() {
        return col;
    }

    public void setCol(int col) {
        this.col = col;
    }

    public abstract RenderPolicy findPolicy(Configure config);

}
