/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.policy;

import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;
import cn.mftcc.doc.components.docmanage.el.render.compute.RenderDataCompute;
import cn.mftcc.doc.components.docmanage.el.render.processor.DocumentProcessor;
import cn.mftcc.doc.components.docmanage.el.resolver.SheetTemplateResolver;
import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;
import cn.mftcc.doc.components.docmanage.el.template.MetaTemplate;
import com.deepoove.poi.exception.RenderException;


import com.mftcc.doc.poi.el.util.SheetUtil;
import org.apache.poi.ss.usermodel.CellCopyPolicy;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import java.util.Iterator;
import java.util.List;

/**
 * 循环表行
 * 
 * <AUTHOR>
 *
 */
public class HackLoopTableRenderPolicy implements RenderPolicy {

    private String prefix;
    private String suffix;

    public HackLoopTableRenderPolicy() {
        this("[", "]");
    }

    public HackLoopTableRenderPolicy(String prefix, String suffix) {
        this.prefix = prefix;
        this.suffix = suffix;
    }

    /**
     * 渲染 excel 表格数据
     * 对于表格数据来说，需要规定 table tag 必须在数据行的最左边的上一个单元格内
     *
     * @param eleTemplate template tag
     * @param data        render data
     * @param template    XWPFTemplate instance
     */
    @Override
    public void render(ElementTemplate eleTemplate, Object data, XSSFSheetTemplate template) {
        CellTemplate cellTemplate = (CellTemplate) eleTemplate;
        XSSFCell cell = cellTemplate.getCell();
        XSSFSheet sheet = cell.getSheet();
        try {
            int templateRowIndex = cell.getRowIndex();
            if(templateRowIndex > sheet.getLastRowNum()) {
                return;
            }

            SheetTemplateResolver resolver = new SheetTemplateResolver(template.getConfig().copy(prefix, suffix));
            int nextRowIndex = 0;
            if (data instanceof Iterable) {
                Iterator<?> iterator = ((Iterable<?>) data).iterator();
                while (iterator.hasNext()) {
                    nextRowIndex = ++templateRowIndex;
                    XSSFRow insertRow = sheet.getRow(nextRowIndex);

                    List<MetaTemplate> templates = resolver.resolveSheetRow(sheet, insertRow);
                    // 遍历删除所有不属于当前表格的数据
                    Iterator<MetaTemplate> templateIterator = templates.iterator();
                    while (templateIterator.hasNext()) {
                        MetaTemplate mt = templateIterator.next();
                        if(mt instanceof CellTemplate) {
                            if(!((CellTemplate) mt).getTagName().startsWith(cellTemplate.getTagName())) {
                                templateIterator.remove();
                            }
                        }
                    }
                    createNextTableRow(sheet, templates, nextRowIndex);

                    RenderDataCompute dataCompute = template.getConfig().getRenderDataComputeFactory()
                            .newCompute(iterator.next());

                    new DocumentProcessor(template, resolver, dataCompute).process(templates);
                    templateRowIndex = nextRowIndex;
                }
            }
            removeNextTableRow(sheet, ++templateRowIndex);
            cell.setCellValue(cell.getStringCellValue().replace(cellTemplate.getSource(), ""));

            template.setSheet(sheet);
            // 重新渲染本 sheet 页
            template.getRenderer().reload(template);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RenderException("HackLoopTable for " + eleTemplate + "error: " + e.getMessage(), e);
        }
    }

    /**
     * 删除一行
     * @param sheet
     * @param nextRowIndex
     */
    private void removeNextTableRow(XSSFSheet sheet, int nextRowIndex) {
        SheetUtil.removeRow(sheet, nextRowIndex);
    }

    /**
     * 使用原有标签，创建一个新的表格行
     * @param sheet
     * @param templates
     * @param nextRowIndex
     */
    private void createNextTableRow(XSSFSheet sheet, List<MetaTemplate> templates, int nextRowIndex) {
        int newInsertPosition = nextRowIndex + 1;
        SheetUtil.createRow(sheet, newInsertPosition);
        CellCopyPolicy policy = new CellCopyPolicy();
        // 复制上一行的数据，到本行
        sheet.copyRows(nextRowIndex, nextRowIndex, newInsertPosition, policy);
    }

}
