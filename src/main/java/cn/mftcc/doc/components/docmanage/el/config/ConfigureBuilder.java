/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.config;


import cn.mftcc.doc.components.docmanage.el.policy.RenderPolicy;

import com.deepoove.poi.util.RegexUtils;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 配置文件构造 {@link Configure}
 * 
 * <AUTHOR>
 *
 */
public class ConfigureBuilder {
    private Configure config;
    private boolean changeRegex;

    ConfigureBuilder() {
        config = new Configure();
    }

    public ConfigureBuilder buildGramer(String prefix, String suffix) {
        config.gramerPrefix = prefix;
        config.gramerSuffix = suffix;
        return this;
    }

    public ConfigureBuilder buidIterableLeft(char c) {
        config.iterable = Pair.of(c, config.iterable.getRight());
        return this;
    }

    public ConfigureBuilder buildGrammerRegex(String reg) {
        changeRegex = true;
        config.grammerRegex = reg;
        return this;
    }

    public ConfigureBuilder bind(String tagName, RenderPolicy policy) {
        config.customPolicy(tagName, policy);
        return this;
    }

    public Configure build() {
        if (!changeRegex) {
            config.grammerRegex = RegexUtils.createGeneral(config.gramerPrefix, config.gramerSuffix);
        }
        return config;
    }
}