/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.web.utils;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.mftcc.doc.components.docmanage.entity.DocManageEntity;

import java.util.Arrays;
import java.util.List;

public class DocumentManager {

    /**
     * 获取文档在线url地址
     *
     * @param json
     * @return
     */
    public static String getFileUri(String json) {
        DocManageEntity docManage = JSONUtil.toBean(json,DocManageEntity.class);
        String serverPath = getServerUrl();
        return serverPath + "/" + DocConstant.DOC_PATH + "/" + docManage.getDocDir() + "/" + docManage.getDocVerNo() + "." + docManage.getDocType();
    }

    /**
     * 获取文档转换后的URL地址
     *
     * @param json
     * @param docVersion
     * @return
     */
    public static String getRenderFileUri(String json, String docVersion) {
        DocManageEntity docManage = JSONUtil.toBean(json,DocManageEntity.class);

        String serverPath = getServerUrl();
        return serverPath + "/" + DocConstant.DOC_PATH + "/" + docManage.getDocDir() + "/" + DocConstant.RENDER_PATH + "/" + docVersion + "." + docManage.getDocType();
    }

    /**
     * 获取当前服务器地址
     *
     * @return
     */
    public static String getServerUrl() {
        return SpringUtil.getProperty("files.callback.server");
    }

    /**
     * 获取文档引擎回调地址
     *
     * @param docManage
     * @return
     */
    public static String getCallback(DocManageEntity docManage) {
        String serverPath = getServerUrl();
        return serverPath + "/doc/docmanage/track/" + docManage.getDocNo();
    }


    /**
     * 获取支持查看的文档类型
     *
     * @return
     */
    public static List<String> getViewedExts() {
        String exts = SpringUtil.getProperty("files.docservice.viewed-docs");
        return Arrays.asList(exts.split("\\|"));
    }

    /**
     * 获取支持编辑的文档类型
     *
     * @return
     */
    public static List<String> getEditedExts() {
        String exts = SpringUtil.getProperty("files.docservice.edited-docs");
        return Arrays.asList(exts.split("\\|"));
    }

    /**
     * 获取支持转换的文档类型
     *
     * @return
     */
    public static List<String> getConvertExts() {
        String exts = SpringUtil.getProperty("files.docservice.convert-docs");
        return Arrays.asList(exts.split("\\|"));
    }

}
