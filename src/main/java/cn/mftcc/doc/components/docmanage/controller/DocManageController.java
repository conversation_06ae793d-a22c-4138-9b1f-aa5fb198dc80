/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.components.docmanage.entity.DocVerManageEntity;
import cn.mftcc.doc.components.docmanage.entity.PushMessage;
import cn.mftcc.doc.components.docmanage.web.model.ConvertBody;
import cn.mftcc.doc.components.docmanage.web.utils.DocConstant;
import cn.mftcc.doc.components.docmanage.web.utils.DocUtil;
import cn.mftcc.doc.components.docmanage.web.utils.DocumentManager;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateModelService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import cn.mftcc.doc.common.utils.PageUtils;
import cn.mftcc.doc.common.utils.R;
import cn.mftcc.doc.components.docmanage.entity.DocManageEntity;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.rmi.ServerException;
import java.util.*;


/**
 * 文件管理表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-13 09:29:48
 */
@RestController
@RequestMapping("doc/docmanage")
public class DocManageController {
    private static final Log log = LogFactory.get();

    @Value("${storage-folder:app_data}")
    private String folder;
    @Value("${mftcc.template.mould-path:}")
    private String docMouldPath;
    @Autowired
    private DocTemplateModelService docTemplateModelService;



    @Value("${mftcc.template.officeServer-path-inner:}")
    private String  officeServerPathInner;
    @Value("${mftcc.template.officeServer-path:}")
    private String  officeServerPath;
    @Value("${mftcc.template.plugsData-baseUrl:}")
    private String  plugsDataBaseUrl;

    @Value("${mftcc.template.server:}")
    private String   serverPath;
    @Value("${spring.application.name:}")
    private String serverName;







    /**
     * 下载
     *
     * @param docNo
     * @return
     */
    @RequestMapping("/download/{docNo}")
    public void download(@PathVariable("docNo") Integer docNo, HttpServletResponse response) throws IOException {
        DocManageEntity docManage = new DocManageEntity();
//        DocManageEntity docManage = docManageService.getById(docNo);

        String newFilename = folder + docManage.getDocDir() + File.separator + docManage.getDocVerNo() + "." + docManage.getDocType();
        File file = FileUtil.file(newFilename);
        if (file.exists()) {
            MediaType mediaType = MediaTypeFactory.getMediaType(docManage.getDocName()).get();
            // 编码格式，防止乱码
            response.setCharacterEncoding("UTF-8");
            // 设置头部 content-type 类型，不同文件不同类型
            response.setHeader("content-type", mediaType.toString());
            // 设置文件在浏览器中打开还是下载，由前端去控制
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(docManage.getDocName(), "UTF-8"));
            // 从response 获取输出流
            ServletOutputStream os = response.getOutputStream();
            // 利用hutool工具包将文件内容写入到输出流
            FileUtil.writeToStream(file, os);
            // 关闭输出流
            os.close();
        }
    }

    @RequestMapping(value = "/tagsShow/{templateId}")
    public String tagsShow(@PathVariable("templateId") String templateId,HttpServletResponse response, HttpServletRequest request, RedirectAttributes attr ) throws IOException {
//        String templateId=request.getParameter("templateId");
//        if(templateId.contains("?")){
//            String[]ary=templateId.split("?");
//            templateId=ary[0];
//            attr.addAttribute("templateId",templateId);
//        }
        attr.addAttribute("templateId",templateId);

        return "tagsShow";
    }
    @RequestMapping(value = "/config.json")
    public void download(HttpServletResponse response) throws IOException {
        DocManageEntity docManage = new DocManageEntity();
//        DocManageEntity docManage = docManageService.getById(docNo);

        String templateId="84013c4e1bf94f47a2a7422a1fa2c31c";
        String json="{\"baseUrl\":\"http://*************:7011/mftcc-doc-web/static/tagPlugin/\",\"variations\":[{\"initData\":\"\",\"buttons\":[],\"initDataType\":\"\",\"description\":\"标签管理\",\"isInsideMode\":true,\"isUpdateOleOnResize\":false,\"icons\":[\"icon.png\",\"<EMAIL>\",\"icon2.png\",\"<EMAIL>\"],\"isViewer\":false,\"isVisual\":true,\"url\":\"tagPlugins/tagsShow.html?templateId="+templateId+",\"EditorsSupport\":[\"word\",\"slide\",\"cell\"],\"isModal\":false}],\"name\":\"标签管理\",\"guid\":\"asc.{97e0c731-71c0-4ea3-0145-a5f79351ad21}\"}";

        response.setContentType(ContentType.TEXT_HTML.getValue());
        PrintWriter printWriter=response.getWriter();
        printWriter.println(json);
//        String newFilename = folder + docManage.getDocDir() + File.separator + docManage.getDocVerNo() + "." + docManage.getDocType();
//        File file = FileUtil.file(newFilename);
//        if (file.exists()) {
//
//
//            MediaType mediaType = MediaTypeFactory.getMediaType(docManage.getDocName()).get();
//            // 编码格式，防止乱码
//            response.setCharacterEncoding("UTF-8");
//            // 设置头部 content-type 类型，不同文件不同类型
//            response.setHeader("content-type", mediaType.toString());
//            // 设置文件在浏览器中打开还是下载，由前端去控制
//            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(docManage.getDocName(), "UTF-8"));
//            // 从response 获取输出流
//            ServletOutputStream os = response.getOutputStream();
//            // 利用hutool工具包将文件内容写入到输出流
//            FileUtil.writeToStream(file, os);
//            // 关闭输出流
//            os.close();
//        }
    }

    /**
     * 下载转换的PDF
     *
     * @param docNo
     * @param response
     * @throws IOException
     */
    @RequestMapping("/downloadPDF/{docNo}")
    public void downloadPdf(@PathVariable("docNo") Integer docNo, HttpServletResponse response) throws IOException {
//        DocManageEntity docManage = docManageService.getById(docNo);
        DocManageEntity docManage = new DocManageEntity();

        String newFilename = folder + docManage.getDocDir() + File.separator + docManage.getDocVerNo() + ".pdf";
        File file = FileUtil.file(newFilename);
        if (file.exists()) {
            String pdfName = docManage.getDocName().replace(docManage.getDocType(), "pdf");
            MediaType mediaType = MediaTypeFactory.getMediaType(docManage.getDocName()).get();
            // 编码格式，防止乱码
            response.setCharacterEncoding("UTF-8");
            // 设置头部 content-type 类型，不同文件不同类型
            response.setHeader("content-type", mediaType.toString());
            // 设置文件在浏览器中打开还是下载，由前端去控制
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(pdfName, "UTF-8"));
            // 从response 获取输出流
            ServletOutputStream os = response.getOutputStream();
            // 利用hutool工具包将文件内容写入到输出流
            FileUtil.writeToStream(file, os);
            // 关闭输出流
            os.close();
        }
    }

    /**
     * 新建文件
     */
    @RequestMapping("/newFile")
    public R newFile(@RequestBody DocManageEntity docManage) {

        ClassPathResource resource = new ClassPathResource("new/new." + docManage.getDocType());
        File file = null;
        try {
            file = resource.getFile();
        } catch (IOException e) {
            String userDir = System.getProperty("user.dir");
            String newFilePath = userDir + File.separator + "config/new." + docManage.getDocType();
            file = FileUtil.file(newFilePath);
        }
//        if (file == null || !file.exists()) {
//
//        }

        String simpleUUID = IdUtil.simpleUUID();
        String docVersion = DateTime.now().toString("yyyyMMddHHmmss");
        String newFilename = folder + simpleUUID + File.separator + docVersion + "." + docManage.getDocType();
        docManage.setDocSts("0");
        docManage.setDocName(docManage.getDocName() + "." + docManage.getDocType());
        docManage.setDocVerNo(docVersion);
        docManage.setDocDir(simpleUUID);
        docManage.setDocPath(newFilename);
        docManage.setCreateDate(DateTime.now());
        docManage.setDocEditSts("0");
        FileUtil.mkParentDirs(newFilename);
        FileUtil.copyFile(file, new File(newFilename));
//        docManageService.uploadDoc(docManage);
        return R.ok();
    }


    /**
     * 单文件上传
     *
     * @param file
     * @return
     */
    @PostMapping("/upload")
    public R upload(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.error("上传失败，请选择文件");
        }
        String fileName = file.getOriginalFilename();
        String extName = FileNameUtil.extName(fileName);
        byte[] byteArr = new byte[0];
        try {
            byteArr = file.getBytes();
        } catch (IOException e) {
            return R.error("上传失败，请重新上传文件");
        }
        InputStream inputStream = new ByteArrayInputStream(byteArr);
        String type = FileTypeUtil.getType(inputStream);
        List<String> fileTypeList = Arrays.asList(DocConstant.FILE_TYPE);
        List<String> docTypeList = Arrays.asList(DocConstant.DOC_TYPE);
        if (!fileTypeList.contains(type) || !docTypeList.contains(extName)) {
            return R.error("文件类型错误！");
        }
        String name = FileNameUtil.getName(fileName);
        String simpleUUID = IdUtil.simpleUUID();
        String docVersion = DateTime.now().toString("yyyyMMddHHmmss");
        String newFilename = folder + simpleUUID + File.separator + docVersion + "." + extName;
        DocManageEntity docManageEntity = new DocManageEntity();
        docManageEntity.setDocName(name);
        docManageEntity.setDocSts("0");
        docManageEntity.setDocType(extName);
        docManageEntity.setDocVerNo(docVersion);
        docManageEntity.setDocDir(simpleUUID);
        docManageEntity.setDocPath(newFilename);
        docManageEntity.setCreateDate(DateTime.now());
        docManageEntity.setDocEditSts("0");

        try {
            File dest = new File(newFilename);
            FileUtil.mkParentDirs(newFilename);
            file.transferTo(dest);
//            docManageService.uploadDoc(docManageEntity);
            log.info(name + "上传成功");
            return R.ok("上传成功");
        } catch (IOException e) {
            log.error(e.toString(), e);
        }
        return R.error();
    }

    /**
     * 多文件上传
     *
     * @param request
     * @return
     */
    @PostMapping("/multiUpload")
    public String multiUpload(HttpServletRequest request) {
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("file");
        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            if (file.isEmpty()) {
                return "上传第" + (i++) + "个文件失败";
            }
            String fileName = file.getOriginalFilename();

            File dest = new File(folder + fileName);
            try {
                file.transferTo(dest);
                log.info("第" + (i + 1) + "个文件上传成功");
            } catch (IOException e) {
                log.error(e.toString(), e);
                return "上传第" + (i++) + "个文件失败";
            }
        }

        return "上传成功";

    }

    /**
     * 文件服务回调方法
     *
     * @param templateId
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/track/{templateId}/{bizFile}")
    public R track(@PathVariable("templateId") String templateId,@PathVariable("bizFile") String bizFile, HttpServletRequest request, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");//设置编码
//        DocManageEntity docManage = docManageService.getDocVerInfo(docNo);
        //获取文档返回值
        String body = "";
        Scanner scanner = null;
        try {
            scanner = new Scanner(request.getInputStream());
            scanner.useDelimiter("\\A");
            body = scanner.hasNext() ? scanner.next() : "";
            scanner.close();
        } catch (Exception ex) {
            return R.error("get request.getInputStream error:" + ex.getMessage());
        }finally {
            if (scanner!=null){
                scanner.close();
            }
        }

        if (body.isEmpty()) {
            return R.error("empty request.getInputStream");
        }

        JSONObject jsonObj;

        try {
            jsonObj = JSON.parseObject(body);
            MFLogger.info("******body-json："+jsonObj);
        } catch (Exception ex) {
            return R.error("JSONParser.parse error:" + ex.getMessage());
        }
        //获取返回值 END
        int status = 99;
        String downloadUri;
        String changesUri;
        String key;
        //获取当前文档状态
        /**
         * 1-正在编辑文档，
         *
         * 2-文档已准备好保存，
         *
         * 3-发生文档保存错误，
         *
         * 4-文档已关闭，没有更改，
         *
         * 6-正在编辑文档，但当前文档状态已保存，
         *
         * 7-强制保存文档时出错
         */
        status = Math.toIntExact(Long.parseLong(jsonObj.getString("status")));
        downloadUri = (String) jsonObj.get("url");

        int saved = 0;
        DocTemplateModelEntity docTemplateModelEntity=docTemplateModelService.findById(templateId);
        docTemplateModelEntity.getUpdateTime();

        String newfileName = docTemplateModelService.getDocMouldPath()+docTemplateModelEntity.getTemplateFileName();
        boolean fileExistFlag=false;
        File tmpfile=new File(newfileName);
        if(tmpfile.exists()){
            fileExistFlag=true;
        }
        if (status == DocConstant.DOC_STATUS_6 || !fileExistFlag) {
            try {

                //替换本地文档

                if(StringUtils.isNotEmpty(bizFile)){
                    if(!"no".equals(bizFile)) {
                        newfileName = docTemplateModelService.getDocMouldPath()  + "saveMould" + File.separator + bizFile;
                    }
                }
                MFLogger.info("******保存的文件路径名称："+newfileName);
                File toSave = new File(newfileName);

                //判断是否有二级域名
                int dex_=officeServerPath.lastIndexOf("/");
                if(dex_>7){
                    String fromStr=officeServerPath.substring(0,dex_);
                    downloadUri=downloadUri.replaceAll(fromStr,officeServerPath);
                }
                downloadUri=downloadUri.replaceAll(officeServerPath,officeServerPathInner);
                //从webOffice服务器下载最新保存文档
                DocUtil.downloadToFile(downloadUri, toSave);
                //更新文档的编辑状态
                if("no".equals(bizFile)) {

                    DocTemplateModelEntity updEntity=new DocTemplateModelEntity();
                    updEntity.setTemplateId(templateId);
                    updEntity.setDocEditSts("0");
                    updEntity.setUpdateTime(new Date());
                    docTemplateModelService.update(updEntity);
                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("type", "save");
                jsonObject.put("msg", "保存成功");
                //通过socket推送消息
                PushMessage pushMessage = new PushMessage();
//                pushMessage.setDocKey(docManage.getDocEditKey());
                pushMessage.setContent(jsonObject.toJSONString());
//                socketIOService.pushMessageToUser(pushMessage);
            } catch (Exception ex) {
                ex.printStackTrace();
                MFLogger.error(templateId,"文件服务回调方法",ex);
                saved = 1;
            }
        }
        //更新文档状态
        if (status == DocConstant.DOC_STATUS_2 || status == DocConstant.DOC_STATUS_4) {
//            docManage.setDocEditSts("0");
//            docManageService.updateById(docManage);
        }

        log.debug("当前文档状态：" + status);

        return R.ok().put("error", saved);
    }

    /**
     * 文件转换
     *
     * @param docNo
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/convert/{docNo}")
    public R convert(@PathVariable("docNo") Integer docNo, HttpServletRequest request, HttpServletResponse response) {
        //创建转换对象
        DocManageEntity docManage = new DocManageEntity();
//        DocManageEntity docManage = docManageService.getDocVerInfo(docNo);
        ConvertBody convertBody = new ConvertBody();
        convertBody.setAsync(false);
        convertBody.setFiletype(docManage.getDocType());
        convertBody.setKey(RandomUtil.randomString(20));
        convertBody.setOutputtype("pdf");
        convertBody.setUrl(DocumentManager.getFileUri(JSONUtil.toJsonStr(docManage)));
        //调用weboffice转换接口
        String result = HttpRequest.post(SpringUtil.getProperty("files.docservice.url.converter")).body(JSON.toJSONString(convertBody)).execute().body();
        JSONObject resultJSON = JSONObject.parseObject(result);
        String pdfPath = docManage.getDocPath().replace(docManage.getDocType(), "pdf");

        //判断二级域名
        //判断是否有二级域名
        int dex_=officeServerPathInner.lastIndexOf("/");
        if(dex_>7){
            String fromStr=officeServerPathInner.substring(0,dex_);
            pdfPath=pdfPath.replaceAll(fromStr,officeServerPathInner);
        }

        MFLogger.info("****  最终pdf路径："+pdfPath);
//        pdfPath=pdfPath.replaceAll(officeServerPath,officeServerPathInner);
        //下载转换好的PDF到本地
        HttpUtil.downloadFile(resultJSON.getString("fileUrl"), pdfPath);
        //返回下载链接
        return R.ok().put("url", "/doc/docmanage/downloadPDF/" + docManage.getDocNo());
    }





    public  void convertDocToTxt()throws ServerException{
        ConvertBody convertBody = new ConvertBody();
        convertBody.setAsync(false);
        convertBody.setFiletype("pdf");
        convertBody.setKey(RandomUtil.randomString(20));
        convertBody.setOutputtype("png");
        convertBody.setCodePage(950);
//        convertBody.setTitle("一个测试.docx");
        convertBody.setUrl("http://192.168.2.196:7019/mftcc-doc-server-dev-rjq/file/docFileInf/getFileStream/89a9af20c1554f14be9adf1f9c1a31fa");
        //调用weboffice转换接口
        String result = HttpRequest.post("http://192.168.2.192:5080/ConvertService.ashx").body(JSON.toJSONString(convertBody)).execute().body();
        JSONObject resultJSON = JSONObject.parseObject(result);
        String pdfPath = "D:/fapiao.png";
        //下载转换好的PDF到本地
        HttpUtil.downloadFile(resultJSON.getString("fileUrl"), pdfPath);

    }

    public static void  main(String[]df){
        DocManageController test=new DocManageController();
        try{

            test.convertDocToTxt();
            System.out.println("转换完毕");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}
