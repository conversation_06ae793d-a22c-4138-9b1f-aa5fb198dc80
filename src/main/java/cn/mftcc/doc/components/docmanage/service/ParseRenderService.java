/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.service;

import cn.mftcc.doc.components.docmanage.el.XSSFTemplate;
import cn.mftcc.doc.components.docmanage.model.entity.Parse;
import com.deepoove.poi.XWPFTemplate;

import org.springframework.lang.NonNull;

import java.io.IOException;
import java.io.OutputStream;

/**
 * Parse Render interface.
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
public interface ParseRenderService {
    /**
     * 解析 word 文档并输出到文件下
     * @param parse 需要解析的数据
     * @param filePath word 文档路径
     * @throws IOException 读取文档可能会产生 IO 异常
     */
    void renderWordTemplate(@NonNull Parse parse, @NonNull String filePath) throws IOException;

    /**
     * 解析 word 文档，并保存至输出流
     * @param parse 需要解析的数据
     */
    void renderWordTemplate(@NonNull Parse parse, OutputStream outputStream) throws IOException;

    /**
     * 解析 word 文档，并返回解析后的 word 文档对象
     * @param parse 需要解析的数据
     */
    XWPFTemplate renderWordTemplate(@NonNull Parse parse);
    /**
     * 解析 excel 文档并输出到文件下
     * @param parse 需要解析的数据
     */
    void renderExcelTemplate(@NonNull Parse parse, @NonNull String filePath) throws IOException;

    /**
     * 解析 excel 文档，并保存至输出流
     * @param parse 需要解析的数据
     */
    void renderExcelTemplate(@NonNull Parse parse, OutputStream outputStream) throws IOException;

    /**
     * 解析 excel 文档，并返回解析后的 excel 文档对象
     * @param parse 需要解析的数据
     */
    XSSFTemplate renderExcelTemplate(@NonNull Parse parse);
}
