/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render.processor;


import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.MetaTemplate;
import cn.mftcc.doc.components.docmanage.el.template.PictureTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class LogProcessor implements Visitor {

    private static Logger log = LoggerFactory.getLogger(LogProcessor.class);

    private String indentState;

    public LogProcessor() {
        this.indentState = "";
    }

    public LogProcessor(String indent) {
        this.indentState = indent;
    }

    public void process(List<MetaTemplate> templates) {
        templates.forEach(template -> template.accept(this));
    }

    @Override
    public void visit(CellTemplate cellTemplate) {
        log.info("{}{}", indentState, cellTemplate);
    }

    @Override
    public void visit(PictureTemplate pictureTemplate) {
        log.info("{}{}", indentState, pictureTemplate);
    }
}
