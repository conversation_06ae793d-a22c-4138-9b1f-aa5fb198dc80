/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.enums;

/**
 * 标签类型枚举
 *
 * <AUTHOR>
 * @date 2021/1/18
 */
public enum TagType implements ValueEnum<String> {
    /**
     * 普通文本标签
     */
    TEXT("text"),
    /**
     * 图片标签
     */
    IMG("img"),
    /**
     * 表格标签
     */
    TABLE("table"),
    /**
     * 勾选项
     */
    CHECKBOX("checkbox"),
    /**
     * 表格标签
     */
    LOOP("loop");

    private final String value;
    TagType(String value) {
        this.value = value;
    }

    @Override
    public String getValue() {
        return value;
    }
}
