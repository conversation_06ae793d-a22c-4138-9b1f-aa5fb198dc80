/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render;


import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;

/**
 * Excel 模板渲染接口
 *
 * <AUTHOR>
 * @date 2021/1/21
 */
public interface Render {
    /**
     * 根据模板和数据进行渲染
     * @param template
     * @param root
     */
    void render(XSSFSheetTemplate template, Object root);

    /**
     * 对模板重新进行渲染，此方法可以在执行渲染时调用
     * 会终止当前正在解析的标签并重新获取模板内的标签用于重新渲染
     */
    void reload(XSSFSheetTemplate template);
}
