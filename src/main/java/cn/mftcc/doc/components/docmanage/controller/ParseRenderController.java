/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.controller;

import cn.mftcc.doc.components.docmanage.model.entity.Parse;
import cn.mftcc.doc.components.docmanage.service.ParseRenderService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.mftcc.doc.components.docmanage.model.entity.Parse;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * parse render 控制层
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
@RestController
@RequestMapping("/template")
public class ParseRenderController {
    @Autowired
    private ParseRenderService parseRenderService;

    @PostMapping("renderWord")
    public void renderDocTemplate(@RequestBody Parse parse, HttpServletResponse response) {
        OutputStream out = null;

        BufferedOutputStream bos = null;
        try {
            out = response.getOutputStream();
            bos = new BufferedOutputStream(out);
            parseRenderService.renderWordTemplate(parse, bos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if(bos != null) {
                    bos.flush();
                }
                if(out != null) {
                    out.flush();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
