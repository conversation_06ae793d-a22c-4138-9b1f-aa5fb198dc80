/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.web.utils;

import com.deepoove.poi.data.PictureType;

/**
 * 文档解析工具包
 *
 * <AUTHOR>
 * @date 2021/1/20
 */
public class StringUtil {
    public static PictureType getUrlType(String url) {
        String suffix = url.substring(url.lastIndexOf("."));

        return PictureType.suggestFileType(suffix);
    }
}
