/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.policy;

import cn.mftcc.doc.components.docmanage.el.render.RenderContext;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;
import com.deepoove.poi.data.NumberingRenderData;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;

/**
 * 编号渲染
 * 
 * <AUTHOR>
 */
public class NumberingRenderPolicy extends AbstractRenderPolicy<NumberingRenderData> {

    @Override
    protected boolean validate(NumberingRenderData data) {
        return (null != data && CollectionUtils.isNotEmpty(data.getItems()));
    }

    @Override
    public void doRender(RenderContext<NumberingRenderData> context, ElementTemplate eleTemplate) throws Exception {
        Helper.renderNumbering(context.getCell(), context.getData());
    }

    @Override
    protected void afterRender(RenderContext<NumberingRenderData> context) {
        clearPlaceholder(context);
    }

    public static class Helper {

        public static void renderNumbering(XSSFCell cell, NumberingRenderData data) throws Exception {

        }

    }
}
