/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.controller;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import cn.mftcc.doc.components.docmanage.entity.DocManageEntity;
import cn.mftcc.doc.components.docmanage.web.model.DocModel;
import com.alibaba.fastjson.JSON;

import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Controller
public class IndexController {


    @RequestMapping(value = {"/"})
    public String index() {
        return "index";
    }

    @RequestMapping(value = {"/chat"})
    public String chat() {
        return "chat";
    }

    /**
     * 打开文档编辑
     *
     * @param docNo
     * @param request
     * @param response
     * @param model
     * @return
     */
    @RequestMapping(value = {"/editor/{docNo}"})
    public String editor(@PathVariable("docNo") Integer docNo, HttpServletRequest request, HttpServletResponse response, Model model) {

//        DocManageEntity docManage = docManageService.getDocVerInfo(docNo);
        DocManageEntity docManage = new DocManageEntity();

        DocModel docFileModel = new DocModel(JSONObject.parseObject(JSONUtil.toJsonStr(docManage)));
        //判断当前是否已经在编辑
        if ("0".equals(docManage.getDocEditSts())) {
            //edit为编辑状态
            docFileModel.changeType("edit", null);
            docManage.setDocEditSts("1");
            docManage.setDocEditKey(docFileModel.document.key);
//            docManageService.updateById(docManage);
        } else {
            //view为查看状态
            docFileModel.changeType("view", null);
        }


//        model.addAttribute("file", docFileModel);
        model.addAttribute("docNo", docNo);
        model.addAttribute("json", JSON.toJSONString(docFileModel));
        model.addAttribute("docserviceApiUrl", SpringUtil.getProperty("files.docservice.url.api"));
        model.addAttribute("serverUrl", SpringUtil.getProperty("files.callback.server"));
        model.addAttribute("wsUrl", SpringUtil.getProperty("socketio.host") + ":" + SpringUtil.getProperty("socketio.port"));
        return "editor";
    }

    /**
     * 查看文档
     *
     * @param docNo
     * @param map
     * @param model
     * @return
     */
    @RequestMapping(value = {"/view/{docNo}"})
    public String view(@PathVariable("docNo") Integer docNo, @RequestParam Map map, Model model) {

        DocManageEntity docManage = new DocManageEntity();
//        DocManageEntity docManage = docManageService.getDocVerInfo(docNo);

        DocModel docFileModel = new DocModel(JSONObject.parseObject(JSONUtil.toJsonStr(docManage)));
        //type 为空则为web查看，type=mobile为移动端查看
        String type = (String) map.get("type");
        docFileModel.changeType("view", type);

        model.addAttribute("file", docFileModel);
        model.addAttribute("docNo", docNo);
        model.addAttribute("json", JSON.toJSONString(docFileModel));
        model.addAttribute("docserviceApiUrl", SpringUtil.getProperty("files.docservice.url.api"));
        model.addAttribute("serverUrl", SpringUtil.getProperty("files.callback.server"));
        model.addAttribute("wsUrl", SpringUtil.getProperty("socketio.host") + ":" + SpringUtil.getProperty("socketio.port"));
        return "editor";
    }
}
