/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.entity;

import com.deepoove.poi.data.style.Style;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文本标签
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TextTag extends BaseTagEntity {
    /**
     * 业务数据值
     */
    private String value;
    /**
     * 需要跳转的链接地址
     */
    private String url;
    /**
     * 样式
     */
    private Style style;

}
