/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.policy;


import cn.mftcc.doc.components.docmanage.el.render.RenderContext;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.exception.RenderException;


import com.mftcc.doc.poi.el.util.SheetUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * 图片渲染
 * 
 * <AUTHOR>
 *
 */
public class PictureRenderPolicy extends AbstractRenderPolicy<PictureRenderData> {

    @Override
    protected boolean validate(PictureRenderData data) {
        if (null == data) {
            return false;
        }
        if (null == data.getPictureType()) {
            throw new RenderException("PictureRenderData must set picture type!");
        }
        return true;
    }

    @Override
    public void doRender(RenderContext<PictureRenderData> context, ElementTemplate eleTemplate) throws Exception {
        Helper.renderPicture(context.getCell(), context.getData());
    }

    @Override
    protected void afterRender(RenderContext<PictureRenderData> context) {
        clearPlaceholder(context);
    }

    @Override
    protected void reThrowException(RenderContext<PictureRenderData> context, Exception e) {
        logger.info("Render picture " + context.getEleTemplate() + " error: {}", e.getMessage());
        context.getCell().setCellValue(context.getData().getAltMeta());
    }

    public static class Helper {
        public static void renderPicture(XSSFCell cell, PictureRenderData picture) throws Exception {
            if (null == picture.getImage()) {
                throw new IllegalStateException("Can't get input data from picture!");
            }
            try (InputStream stream = new ByteArrayInputStream(picture.getImage())) {
                SheetUtil.addPicture(cell, stream, picture.getPictureType().type(),
                        (short)SheetUtil.pixelToWidth(picture.getWidth()), (short) SheetUtil.pixelToHeight(picture.getHeight()));
            }
        }
    }
}
