/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render;

import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;
import cn.mftcc.doc.components.docmanage.el.data.MergeRenderData;
import cn.mftcc.doc.components.docmanage.el.render.compute.RenderDataCompute;
import cn.mftcc.doc.components.docmanage.el.render.processor.DocumentProcessor;
import cn.mftcc.doc.components.docmanage.el.render.processor.LogProcessor;
import cn.mftcc.doc.components.docmanage.el.render.processor.Visitor;
import com.deepoove.poi.exception.RenderException;

import org.apache.commons.lang3.time.StopWatch;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 默认渲染
 */
public class DefaultRender implements Render {
    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultRender.class);

    private Object root = null;
    private List<Visitor> visitors = new ArrayList<>();

    public DefaultRender() {
    }

    @Override
    public void render(XSSFSheetTemplate template, Object root) {
        Objects.requireNonNull(template, "Template must not be null.");
        Objects.requireNonNull(root, "Data root must not be null");
        this.root = root;
        LOGGER.info("Render template start...");

        RenderDataCompute renderDataCompute = template.getConfig().getRenderDataComputeFactory().newCompute(root);
        StopWatch watch = new StopWatch();
        try {

            watch.start();
            renderTemplate(template, renderDataCompute);
            renderMerge(template);
        } catch (Exception e) {
            if (e instanceof RenderException) {
                throw (RenderException) e;
            }
            throw new RenderException("Cannot render docx template, please check the Exception", e);
        } finally {
            watch.stop();
        }
        LOGGER.info("Successfully Render template in {} millis", TimeUnit.NANOSECONDS.toMillis(watch.getNanoTime()));
    }

    private void renderMerge(XSSFSheetTemplate template) {
        Collection<MergeRenderData> mergeRenderDataList = template.getMergeRenderDataMap().values();

        try {
            mergeRenderDataList.forEach(mergeRenderData -> {
                CellRangeAddress cellAddresses = new CellRangeAddress(
                        mergeRenderData.getFirstRow(),
                        mergeRenderData.getLastRow(),
                        mergeRenderData.getFirstCol(),
                        mergeRenderData.getLastCol());
                template.getSheet().addMergedRegionUnsafe(cellAddresses);
            });
        }catch (IllegalStateException e) {
            e.printStackTrace();
        }
    }

    private void renderTemplate(XSSFSheetTemplate template, RenderDataCompute renderDataCompute) {
        // log
        new LogProcessor().process(template.getEleTemplates());

        // render
        DocumentProcessor documentRender = new DocumentProcessor(template, template.getResolver(), renderDataCompute);
        visitors.add(documentRender);
        documentRender.process(template.getEleTemplates());
    }

    @Override
    public void reload(XSSFSheetTemplate template) {
        visitors.forEach(Visitor::reload);
        template.reload();
        this.render(template, root);
    }
}
