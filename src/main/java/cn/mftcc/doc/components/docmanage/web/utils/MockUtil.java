/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.web.utils;

import cn.hutool.core.io.resource.ResourceUtil;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

public class MockUtil {

    public static String renderJsonData(String jsonStr) {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("javascript");
        try {
            String str = ResourceUtil.readUtf8Str("static/scripts/mock-min.js");
            engine.eval(str);
            Object result = engine.eval("JSON.stringify(Mock.mock(" + jsonStr + "));");
            return result.toString();
        } catch (ScriptException e) {
            e.printStackTrace();
            return null;
        }
    }
}
