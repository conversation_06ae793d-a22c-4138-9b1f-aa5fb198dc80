/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.template;


import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.policy.RenderPolicy;

import cn.mftcc.doc.components.docmanage.el.render.processor.Visitor;
import org.apache.poi.xssf.usermodel.XSSFPicture;

/**
 * excel 图片模板元素: XSSFPicture
 * 
 * <AUTHOR>
 */
public class PictureTemplate extends ElementTemplate {

    protected XSSFPicture picture;

    public PictureTemplate() {}

    public PictureTemplate(String tagName, XSSFPicture picture) {
        this.tagName = tagName;
        this.picture = picture;
    }

    public XSSFPicture getPicture() {
        return picture;
    }

    public void setPicture(XSSFPicture picture) {
        this.picture = picture;
    }

    @Override
    public void accept(Visitor visitor) {
        visitor.visit(this);
    }

    @Override
    public RenderPolicy findPolicy(Configure config) {
        RenderPolicy renderPolicy = config.getCustomPolicy(tagName);
        return null == renderPolicy ? config.getTemplatePolicy(this.getClass()) : renderPolicy;
    }

    @Override
    public String toString() {
        return "Pic::" + source;
    }

}
