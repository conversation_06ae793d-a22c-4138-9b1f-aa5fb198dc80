/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.mftcc.doc.components.docmanage.el.XSSFTemplate;
import cn.mftcc.doc.components.docmanage.handler.office.DocParseHandler;
import cn.mftcc.doc.components.docmanage.handler.tag.TagConvertHandler;
import cn.mftcc.doc.components.docmanage.model.entity.BaseTagEntity;
import cn.mftcc.doc.components.docmanage.model.entity.Parse;
import cn.mftcc.doc.components.docmanage.model.enums.DocType;
import cn.mftcc.doc.components.docmanage.service.ParseRenderService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.XWPFTemplate;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;

/**
 * Parse Render implementation.
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
@Service
public class ParseRenderServiceImpl implements ParseRenderService {
    private final static String TAG_CONVERT_SUFFIX = "TagConvert";
    private final static String DOC_PARSE_SUFFIX = "Parse";

    @Autowired
    private Map<String, TagConvertHandler> tagConvertHandlerMap;
    @Autowired
    private Map<String, DocParseHandler> docParseHandlerMap;

    @Override
    public void renderWordTemplate(@NonNull Parse parse,
                                   @NonNull String filePath) throws IOException {

        XWPFTemplate xwpfTemplate = renderWordTemplate(parse);
        FileUtil.touch(filePath);
        xwpfTemplate.writeToFile(filePath);
    }

    @Override
    public void renderWordTemplate(@NonNull Parse parse,
                                   OutputStream outputStream) throws IOException {
        XWPFTemplate xwpfTemplate = renderWordTemplate(parse);

        xwpfTemplate.write(outputStream);
    }

    @Override
    public XWPFTemplate renderWordTemplate(@NonNull Parse parse) {
        DocType docType = DocType.WORD;
        return renderWordTemplate(parse, docType);
    }

    @Override
    public void renderExcelTemplate(@NonNull Parse parse,
                                    @NonNull String filePath) throws IOException {
        XSSFTemplate xssfTemplate = renderExcelTemplate(parse);
        FileUtil.touch(filePath);
        xssfTemplate.writeToFile(filePath);
    }

    @Override
    public void renderExcelTemplate(@NonNull Parse parse,
                                    OutputStream outputStream) throws IOException {
        XSSFTemplate xssfTemplate = renderExcelTemplate(parse);

        xssfTemplate.write(outputStream);
    }

    @Override
    public XSSFTemplate renderExcelTemplate(@NonNull Parse parse) {
        DocType docType = DocType.EXCEL;
        return renderWordTemplate(parse, docType);
    }

    private <T> T renderWordTemplate(@NonNull Parse parse,
                                     DocType docType) {
        TagConvertHandler tagConvertHandler = tagConvertHandlerMap.get(docType.getValue() + TAG_CONVERT_SUFFIX);
        DocParseHandler docParseHandler = docParseHandlerMap.get(docType.getValue() + DOC_PARSE_SUFFIX);

        //先排序一下数据，用于分组合并测试时使用，其他情况务必删除
        //******************* start ******************//
//        JSONArray table = busData.getJSONArray("table");
//        busData.put("table", table.stream().sorted(
//                Comparator.comparing(item -> ((JSONObject) item).getString("name"))
//                        .reversed()
//        ).collect(Collectors.toList()));
        //*******************  end  ******************//




        // 拼接转换业务数据与标签数据
        Map<String, BaseTagEntity> parseTab = tagConvertHandler.toBusTag(parse.getTagData(), parse.getBusData());

        return docParseHandler.parseOfficeByTag(
                parse.getDocPath(),
                parseTab);
    }
}
