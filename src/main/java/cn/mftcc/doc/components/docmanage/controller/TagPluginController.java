/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.controller;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.ContentType;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import cn.mftcc.common.utils.DateUtil;
import cn.mftcc.doc.components.docmanage.entity.DocManageEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateModelService;
import cn.mftcc.doc.feign.client.SysFeignClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

@Controller
public class TagPluginController {
    private static final Log log = LogFactory.get();

    @Value("${storage-folder:app_data}")
    private String folder;
    @Value("${mftcc.template.mould-path:}")
    private String docMouldPath;
    @Autowired
    private DocTemplateModelService docTemplateModelService;

    @Value("${mftcc.template.plugsData-baseUrl:}")
    private String  plugsDataBaseUrl;
    @Value("${mftcc.template.server:}")
    private String   serverPath;
    @Value("${mftcc.template.tagPluginSever:}")
    private String   tagPluginSever;
    @Value("${spring.application.name:}")
    private String serverName;
    @Autowired
    private SysFeignClient sysFeignClient;

    @RequestMapping(value = "/tagsShow/{templateId}")
    public String tagsShow(@PathVariable("templateId") String templateId, HttpServletResponse response, HttpServletRequest request, Model model) throws IOException {

        model.addAttribute("templateId", templateId);

        model.addAttribute("serverPath", tagPluginSever);
        model.addAttribute("serverName", serverName);
        model.addAttribute("plugsDataBaseUrl", plugsDataBaseUrl);
        JSONObject configInfo = docTemplateModelService.getTagsConfig(templateId);
        model.addAttribute("tagsJson", JSONObject.toJSONString(configInfo));

        return "tagsShow";
    }
    @RequestMapping(value = "/watermark/{templateId}")
    public String watermark(@PathVariable("templateId") String templateId, HttpServletResponse response, HttpServletRequest request, Model model) throws IOException {

        model.addAttribute("templateId", templateId);
        String opNo="";
        String waterMarkContent="";
        if(templateId.contains("-")){
            opNo=templateId.split("-")[1];
            String userInf= sysFeignClient.findUserByOpNo(opNo);
            JSONObject userJson=JSONObject.parseObject(userInf);
            String curTime= DateUtil.getTime();
            String curDate=DateUtil.parseDateToTenStr(new Date());
            waterMarkContent=userJson.getString("opName")+" "+curDate+" "+curTime;


        }
        model.addAttribute("serverPath", serverPath);
        model.addAttribute("waterMarkContent", waterMarkContent);
        model.addAttribute("serverName", serverName);
        model.addAttribute("plugsDataBaseUrl", plugsDataBaseUrl);
        JSONObject configInfo = new JSONObject();
        model.addAttribute("tagsJson", JSONObject.toJSONString(configInfo));

        return "watermark";
    }





    @RequestMapping(value = "config.json/{templateId}")
    public void download(@PathVariable("templateId") String templateId,HttpServletResponse response) throws IOException {
        DocManageEntity docManage = new DocManageEntity();
//        DocManageEntity docManage = docManageService.getById(docNo);
        response.setCharacterEncoding("utf-8");
//        String templateId="84013c4e1bf94f47a2a7422a1fa2c31c";
        String json="{\"baseUrl\":\"\",\"variations\":[{\"initData\":\"\",\"buttons\":[],\"initDataType\":\"\",\"description\":\"标签管理\",\"isInsideMode\":true,\"isUpdateOleOnResize\":false,\"icons\":[\"tag/icon.png\",\"tag/<EMAIL>\",\"tag/icon2.png\",\"tag/<EMAIL>\"],\"isViewer\":false,\"isVisual\":true,\"url\":\"tagsShow/"+templateId+"\",\"isSystem\": false,\"EditorsSupport\":[\"word\",\"slide\",\"cell\"],\"isModal\":false}],\"name\":\"标签管理\",\"guid\":\"asc.{97e0c731-71c0-4ea3-0145-a5f79351ad21}\"}";

        if(templateId.startsWith("watermark")){
            json="{\"baseUrl\":\"\",\"variations\":[{\"initData\":\"\",\"buttons\":[],\"initDataType\":\"\",\"description\":\"水印\",\"isInsideMode\":true,\"isUpdateOleOnResize\":false,\"icons\":[\"tag/icon.png\",\"tag/<EMAIL>\",\"tag/icon2.png\",\"tag/<EMAIL>\"],\"isViewer\":false,\"isVisual\":false,\"url\":\"watermark/"+templateId+"\", \"isSystem\": true,\"EditorsSupport\":[\"word\",\"slide\",\"cell\"],\"isModal\":false}],\"name\":\"水印\",\"guid\":\"asc.{FFE1F462-1EA2-4391-990D-4CC84940B754}\"}";
        }
        response.setContentType(ContentType.TEXT_HTML.getValue());
        PrintWriter printWriter=response.getWriter();
        printWriter.println(json);
    }
    @RequestMapping(value = "watermarkConfig.json/{templateId}")
    public void watermarkConfig(@PathVariable("templateId") String templateId,HttpServletResponse response) throws IOException {
            DocManageEntity docManage = new DocManageEntity();
//        DocManageEntity docManage = docManageService.getById(docNo);
            response.setCharacterEncoding("utf-8");
//        String templateId="84013c4e1bf94f47a2a7422a1fa2c31c";
            String json="{\"baseUrl\":\"\",\"variations\":[{\"initData\":\"\",\"buttons\":[],\"initDataType\":\"\",\"description\":\"水印\",\"isInsideMode\":true,\"isUpdateOleOnResize\":false,\"icons\":[\"tag/icon.png\",\"tag/<EMAIL>\",\"tag/icon2.png\",\"tag/<EMAIL>\"],\"isViewer\":false,\"isVisual\":true,\"url\":\"watermark/"+templateId+"\", \"isSystem\": false,\"EditorsSupport\":[\"word\",\"slide\",\"cell\"],\"isModal\":false}],\"name\":\"水印\",\"guid\":\"asc.{FFE1F462-1EA2-4391-990D-4CC84940B754}\"}";

            response.setContentType(ContentType.TEXT_HTML.getValue());
            PrintWriter printWriter=response.getWriter();
            printWriter.println(json);
        }


}
