/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.handler.tag;

import cn.mftcc.doc.components.docmanage.model.entity.BaseTagEntity;
import cn.mftcc.doc.components.docmanage.model.entity.MergeTag;
import cn.mftcc.doc.components.docmanage.model.params.TableTagParam;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import cn.mftcc.doc.components.docmanage.handler.tag.TagConvertHandler;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/19
 */
@Slf4j
@Component("excelTagConvert")
public class ExcelTagConvertHandler implements TagConvertHandler {
    private final WordTagConvertHandler docTagConvertHandler = new WordTagConvertHandler();

    /**
     * 解析标签数据
     * @param tagData 标签数据
     * @param busData 业务数据
     * @return
     */
    @Override
    public Map<String, BaseTagEntity> toBusTag(JSONObject tagData, JSONObject busData) {
        MergeTag mergeTag = new MergeTag();

        tagData.values().forEach(item -> {
            if (item instanceof JSONObject && "table".equals(((JSONObject) item).getString("type"))) {
                ((JSONObject) item).toJavaObject(TableTagParam.class).getValue().values().forEach(tdValues -> {
                    BaseTagEntity td = tdValues.toJavaObject(BaseTagEntity.class);
                    if(td.isGroup()) {
                        mergeTag.getValues().add(td);
                    }
                });
            }
        });

        Map<String, BaseTagEntity> tagMap = docTagConvertHandler.getTag(tagData, busData);
        tagMap.put("MERGE_GROUP", mergeTag);

        return tagMap;
    }
}
