/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render.processor;


import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.PictureTemplate;

public interface Visitor {

    /**
     * 访问单元格模板
     * 
     * @param cellTemplate
     */
    void visit(CellTemplate cellTemplate);

    /**
     * 访问图片模板
     * 
     * @param pictureTemplate
     */
    void visit(PictureTemplate pictureTemplate);

    /**
     * 重新执行
     */
    default void reload(){};
}
