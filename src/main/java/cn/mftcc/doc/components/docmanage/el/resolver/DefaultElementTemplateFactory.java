/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.resolver;


import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.PictureTemplate;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFPicture;

import java.util.Set;

public class DefaultElementTemplateFactory implements ElementTemplateFactory {

    public static final char EMPTY_CHAR = '\0';

    public DefaultElementTemplateFactory() {
    }

    @Override
    public CellTemplate createCellTemplate(Configure config, String tag, XSSFCell cell) {
        CellTemplate template = new CellTemplate();
        Set<Character> gramerChars = config.getGramerChars();
        Character symbol = EMPTY_CHAR;
        if (!"".equals(tag)) {
            char fisrtChar = tag.charAt(0);
            for (Character chara : gramerChars) {
                if (chara.equals(fisrtChar)) {
                    symbol = fisrtChar;
                    break;
                }
            }
        }
        template.setSource(config.getGramerPrefix() + tag + config.getGramerSuffix());
        template.setTagName(symbol.equals(EMPTY_CHAR) ? tag : tag.substring(1));
        template.setSign(symbol);
        template.setCell(cell);
        return template;
    }

    @Override
    public PictureTemplate createPictureTemplate(Configure config, String tag, XSSFPicture pic) {
        PictureTemplate template = new PictureTemplate();
        template.setSource(config.getGramerPrefix() + tag + config.getGramerSuffix());
        template.setTagName(tag);
        template.setSign(EMPTY_CHAR);
        template.setPicture(pic);
        return template;
    }

}
