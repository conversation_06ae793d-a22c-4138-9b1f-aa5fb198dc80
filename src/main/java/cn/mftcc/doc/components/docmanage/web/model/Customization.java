/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.web.model;

public class Customization {
    /**
     * 自动保存
     */
    public Boolean autosave = false;
    /**
     * 聊天
     */
    public Boolean chat = false;

    /**
     * 定义用户是否只能编辑和删除自己的注释，默认为true
     */
    public Boolean commentAuthorOnly;
    /**
     * 注释菜单按钮显示还是隐藏
     */
    public Boolean comments = false;
    /**
     * true-将工具栏和头部合到一起，false分开，默认false
     */
    public Boolean compactHeader;
    /**
     * 工具栏是否隐藏，true隐藏，false展示全部
     */
    public Boolean compactToolbar;
    /**
     * 强制保存
     */
    public Boolean forcesave = true;

    /**
     * 是否开启帮助页面
     */
    public Boolean help = false;
    /**
     * 首次加载是否隐藏右侧工具栏
     */
    public Boolean hideRightMenu;
    /**
     * 定义是否运行文档宏以及可用宏设置。默认值为true。
     */
    public Boolean macros;
    /**
     * 定义是否运行文档宏。可以采用以下值：disable-不运行；enable-自动运行所有宏；warn-警告宏并请求运行权限。默认值为“原始”。
     */
//            public Boolean macrosMode;

    /**
     * 定义插件是否启动和可用，默认为true
     */
    public Boolean plugins;
    /**
     * 定义在加载编辑器时，审核更改面板是自动显示还是隐藏。默认值为false
     */
    public Boolean showReviewChanges;
    /**
     * 定义在加载编辑器时是否自动打开或关闭拼写检查器。拼写检查器仅适用于文档编辑器和演示文稿编辑器。默认值为true。
     */
    public Boolean spellcheck;
    /**
     * 菜单Tab页是否高亮显示，默认false
     */
    public Boolean toolbarNoTabs;
    /**
     * 定义标尺和对话框中使用的测量单位。可以取以下值：cm-厘米，pt-点，英寸-英寸。默认值为厘米（cm）。
     */
    public String unit;


    public Goback goback;

    public Customization() {
        goback = new Goback();
    }

    public class Goback {
        /**
         * true-在新页面打开，false-当前页面打开
         */
        public Boolean blank;
        /**
         * 定义如果单击“打开文件位置”按钮，触发onRequestClose事件，而不是打开浏览器选项卡或窗口。默认值为false，
         */
        public Boolean requestClose ;
        /**
         * 定义按钮显示文字
         */
        public String text;
        /**
         * 定义按钮返回URL
         */
        public String url;
    }
}
