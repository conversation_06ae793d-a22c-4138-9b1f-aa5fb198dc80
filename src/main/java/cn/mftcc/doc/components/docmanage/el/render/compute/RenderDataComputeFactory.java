/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render.compute;

/**
 * <AUTHOR>
 */
@FunctionalInterface
public interface RenderDataComputeFactory {

    RenderDataCompute newCompute(EnvModel model);

    default RenderDataCompute newCompute(Object model) {
        return newCompute(model instanceof EnvModel ? (EnvModel)model : EnvModel.ofModel(model));
    }

}
