/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.policy;


import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;


/**
 * 
 * <AUTHOR>
 */
@FunctionalInterface
public interface RenderPolicy {

    /**
     * @param eleTemplate 模板标签
     * @param data        渲染数据
     * @param template    模板实例
     */
    void render(ElementTemplate eleTemplate, Object data, XSSFSheetTemplate template);

}
