/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render.compute;

import java.util.Collections;
import java.util.Map;

public class EnvModel {

    private Object root;
    private Map<String, Object> env;

    public static EnvModel ofModel(Object root) {
        return of(root, Collections.emptyMap());
    }

    public static EnvModel of(Object root, Map<String, Object> env) {
        EnvModel envModel = new EnvModel();
        envModel.root = root;
        envModel.env = env;
        return envModel;
    }

    public Object getRoot() {
        return root;
    }

    public void setRoot(Object root) {
        this.root = root;
    }

    public Map<String, Object> getEnv() {
        return env;
    }

    public void setEnv(Map<String, Object> env) {
        this.env = env;
    }

}
