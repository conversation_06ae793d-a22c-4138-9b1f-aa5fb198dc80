/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render.processor;



import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;
import cn.mftcc.doc.components.docmanage.el.render.compute.RenderDataCompute;
import cn.mftcc.doc.components.docmanage.el.resolver.Resolver;
import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.MetaTemplate;
import cn.mftcc.doc.components.docmanage.el.template.PictureTemplate;


import java.util.List;

/**
 * 处理文档的所有模板
 * 
 * <AUTHOR>
 *
 */
public class DocumentProcessor implements Visitor {

    private final ElementProcessor elementProcessor;
    private Boolean isStop = false;
    public DocumentProcessor(XSSFSheetTemplate template, final Resolver resolver,
                             final RenderDataCompute renderDataCompute) {
        elementProcessor = new ElementProcessor(template, resolver, renderDataCompute);
    }

    public void process(List<MetaTemplate> templates) {
        for (MetaTemplate template: templates) {
            if(isStop) {
                return;
            }
            template.accept(this);
        }
    }

    @Override
    public void visit(CellTemplate cellTemplate) {
        cellTemplate.accept(elementProcessor);
    }

    @Override
    public void visit(PictureTemplate pictureTemplate) {
        pictureTemplate.accept(elementProcessor);
    }

    @Override
    public void reload() {
        this.isStop = true;
    }
}
