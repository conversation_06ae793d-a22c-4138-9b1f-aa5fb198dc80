/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el;


import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.data.MergeRenderData;
import cn.mftcc.doc.components.docmanage.el.render.DefaultRender;
import cn.mftcc.doc.components.docmanage.el.render.Render;
import cn.mftcc.doc.components.docmanage.el.resolver.Resolver;
import cn.mftcc.doc.components.docmanage.el.resolver.SheetTemplateResolver;
import cn.mftcc.doc.components.docmanage.el.template.MetaTemplate;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/21
 */
public class XSSFSheetTemplate {
    private static Logger logger = LoggerFactory.getLogger(XSSFSheetTemplate.class);

    private XSSFSheet sheet;
    private Configure config;
    private Render renderer;
    private Resolver resolver;
    private List<String> groupData;
    private Map<String, MergeRenderData> mergeRenderDataMap;
    private List<MetaTemplate> eleTemplates;

    private XSSFSheetTemplate() {
    }

    public XSSFSheetTemplate(Configure config, XSSFSheet sheet) {
        this.sheet = sheet;
        this.config = config;
        this.resolver = new SheetTemplateResolver(config);
        this.renderer = new DefaultRender();
        this.mergeRenderDataMap = new HashMap<>();
        this.eleTemplates = this.resolver.resolveSheet(sheet);
    }

    public XSSFSheet getSheet() {
        return sheet;
    }

    public Configure getConfig() {
        return config;
    }

    public Render getRenderer() {
        return renderer;
    }

    public Resolver getResolver() {
        return resolver;
    }

    public List<MetaTemplate> getEleTemplates() {
        return eleTemplates;
    }

    public void setSheet(XSSFSheet sheet) {
        this.sheet = sheet;
    }

    public void reload() {
        this.eleTemplates = this.resolver.resolveSheet(sheet);
    }

    public void setGroup(List<String> groupData) {
        this.groupData = groupData;
    }

    public List<String> getGroupData() {
        return groupData;
    }

    public void setGroupData(List<String> groupData) {
        this.groupData = groupData;
    }

    public Map<String, MergeRenderData> getMergeRenderDataMap() {
        return mergeRenderDataMap;
    }

    public MergeRenderData getMergeRender(String key) {
        return this.mergeRenderDataMap.getOrDefault(key, null);
    }

    public void setMergeRender(String key, MergeRenderData mergeRenderData) {
        this.mergeRenderDataMap.put(key, mergeRenderData);
    }
}
