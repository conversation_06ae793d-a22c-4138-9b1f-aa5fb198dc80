/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render.processor;


import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;
import cn.mftcc.doc.components.docmanage.el.render.compute.RenderDataCompute;
import cn.mftcc.doc.components.docmanage.el.resolver.Resolver;
import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.MetaTemplate;
import cn.mftcc.doc.components.docmanage.el.template.PictureTemplate;


public abstract class DefaultTemplateProcessor implements Visitor {

    protected XSSFSheetTemplate template;
    protected final RenderDataCompute renderDataCompute;
    protected final Resolver resolver;

    public DefaultTemplateProcessor(XSSFSheetTemplate template, final Resolver resolver,
                                    final RenderDataCompute renderDataCompute) {
        this.template = template;
        this.resolver = resolver;
        this.renderDataCompute = renderDataCompute;
    }

    @Override
    public void visit(CellTemplate cellTemplate) {
        visitOther(cellTemplate);
    }

    @Override
    public void visit(PictureTemplate pictureTemplate) {
        visitOther(pictureTemplate);
    }

    protected void visitOther(MetaTemplate template) {
        // no-op
    }

}
