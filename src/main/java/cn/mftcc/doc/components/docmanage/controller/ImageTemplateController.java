/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.controller;

import cn.mftcc.common.logger.MFLogger;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;

@RestController
@RequestMapping("/imageTemplate")
public class ImageTemplateController {

    /**
     * 读取指定配置文件下的图片
     * @param fileName  yes.png   no.png
     * @param response
     */
    @RequestMapping(value = "/getFileStream/{fileName}")
    public void getFileStream(@PathVariable("fileName") String fileName, HttpServletResponse response) {
        try {
            ClassPathResource resource = new ClassPathResource("/static/template/img/"+fileName);
            InputStream inStream = resource.getInputStream();

            String tmpAry[]=fileName.split("\\.");

            String  suffix=tmpAry[tmpAry.length-1];//后缀名
            response.reset();
            response.setContentType("application/x-download");
            response.addHeader("Content-Disposition", "attachment;filename=");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            byte[] b=new byte[1024];
            BufferedInputStream buf=new BufferedInputStream(inStream);
            ServletOutputStream out=response.getOutputStream();
            BufferedOutputStream bot=new BufferedOutputStream(out);

            int readLength;
            while ((readLength = buf.read(b)) != -1) {
                bot.write(b, 0, readLength);
            }
            bot.flush();
        } catch (Exception  e) {
            MFLogger.error("",e);

        }
    }

}
