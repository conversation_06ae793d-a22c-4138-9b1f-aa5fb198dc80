/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.policy;

import cn.mftcc.doc.components.docmanage.el.render.RenderContext;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.TableRenderData;
import com.deepoove.poi.data.style.TableStyle;

import org.apache.poi.xssf.usermodel.XSSFCell;

import java.util.List;

/**
 * 渲染表格
 * 
 * <AUTHOR>
 */
public class TableRenderPolicy extends AbstractRenderPolicy<TableRenderData> {

    @Override
    protected boolean validate(TableRenderData data) {
        if (null == data || 0 == data.obtainColSize()) {
            return false;
        }
        List<RowRenderData> rows = data.getRows();
        final int col = data.obtainColSize();
        for (RowRenderData row : rows) {
            if (col != row.obtainColSize()) {
                throw new IllegalArgumentException("Number of cells in each row should be the same!");
            }
        }
        TableStyle tableStyle = data.getTableStyle();
        if (null != tableStyle) {
            int[] colWidths = tableStyle.getColWidths();
            if (null != colWidths && colWidths.length != col) {
                throw new IllegalArgumentException(
                        "The length of Colwidth array and number of columns must be the same!");
            }
        }
        return true;
    }

    @Override
    public void doRender(RenderContext<TableRenderData> context, ElementTemplate eleTemplate) throws Exception {
        Helper.renderTable(context.getCell(), context.getData());
    }

    @Override
    protected void afterRender(RenderContext<TableRenderData> context) {
        clearPlaceholder(context);
    }

    public static class Helper {

        public static void renderTable(XSSFCell run, TableRenderData data) throws Exception {
            System.out.println(data);
        }

    }

}
