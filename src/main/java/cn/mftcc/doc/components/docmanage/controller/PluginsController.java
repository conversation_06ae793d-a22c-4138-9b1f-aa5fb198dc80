/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileReader;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.compress.utils.InputStreamStatistics;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.io.IOException;

@Controller
@RequestMapping(value = "/plugins")
@CrossOrigin(origins = "*", maxAge = 3600)
public class PluginsController {
    private static final Log log = LogFactory.get();


    @RequestMapping(value = {"/templatetags/index"})
    public String templatetagPage() {
        return "plugins/templatetags/index";
    }

    @RequestMapping(value = {"/templatetags/config.json"})
    @ResponseBody
    public JSONObject config() {
        ClassPathResource resource = new ClassPathResource("static/plugins/templatetags/templatetag.json");
        File file = null;
        try {

//            InputStreamStatistics d;
            file = resource.getFile();
        } catch (IOException e) {
            String userDir = System.getProperty("user.dir");
            String newFilePath = userDir + File.separator + "config/templatetag.json";
            file = FileUtil.file(newFilePath);
        }
        FileReader fileReader = new FileReader(file);
        return JSON.parseObject(fileReader.readString());
    }


}
