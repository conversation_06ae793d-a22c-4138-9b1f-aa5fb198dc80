/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el.render;


import cn.mftcc.doc.components.docmanage.el.XSSFSheetTemplate;
import cn.mftcc.doc.components.docmanage.el.config.Configure;
import cn.mftcc.doc.components.docmanage.el.template.CellTemplate;
import cn.mftcc.doc.components.docmanage.el.template.ElementTemplate;

import org.apache.poi.xssf.usermodel.XSSFCell;

/**
 * 模板上下文
 * 
 * <AUTHOR>
 */
public class RenderContext<T> {

    private final ElementTemplate eleTemplate;
    private final T data;
    private final XSSFSheetTemplate template;
    private final WhereDelegate where;

    public RenderContext(ElementTemplate eleTemplate, T data, XSSFSheetTemplate template) {
        this.eleTemplate = eleTemplate;
        this.data = data;
        this.template = template;
        where = new WhereDelegate((this.eleTemplate).getCell());
    }

    public ElementTemplate getEleTemplate() {
        return eleTemplate;
    }

    public T getThing() {
        return data;
    }

    public T getData() {
        return data;
    }

    public XSSFSheetTemplate getTemplate() {
        return template;
    }

    public WhereDelegate getWhereDelegate() {
        return where;
    }

    public XSSFCell getWhere() {
        return getCell();
    }

    public XSSFCell getCell() {
        return ((CellTemplate) eleTemplate).getCell();
    }

//    public IBody getContainer() {
//        // XWPFTableCell、XWPFDocument、XWPFHeaderFooter、XWPFAbstractFootnoteEndnote
//        return ((XWPFParagraph) getRun().getParent()).getBody();
//    }

    public Configure getConfig() {
        return getTemplate().getConfig();
    }

    public Object getTagSource() {
        return getEleTemplate().getSource();
    }

}
