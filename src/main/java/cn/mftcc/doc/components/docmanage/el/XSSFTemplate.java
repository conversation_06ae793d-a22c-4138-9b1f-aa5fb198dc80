/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.el;

import cn.mftcc.doc.components.docmanage.el.config.Configure;
import com.deepoove.poi.exception.ResolverException;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.util.Preconditions;
import org.apache.commons.collections4.list.TreeList;
import org.apache.poi.Version;
import org.apache.poi.openxml4j.exceptions.OLE2NotOfficeXmlFileException;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2021/1/21
 */
public class XSSFTemplate implements Closeable {
    private static Logger logger = LoggerFactory.getLogger(XSSFTemplate.class);
    private static final String SUPPORT_MINIMUM_VERSION = "4.1.2";
    private Configure config;
    private XSSFWorkbook workbook;
    private List<XSSFSheetTemplate> sheetTemplates;


    static {
        try {
            Class.forName("org.apache.poi.Version");
            Preconditions.checkMinimumVersion(Version.getVersion(), SUPPORT_MINIMUM_VERSION,
                    (cur, min) -> "Require Apache POI version at least " + min + ", but now is " + cur
                            + ", please check the dependency of project.");
        } catch (ClassNotFoundException e) {
            // no-op
        }
    }

    private XSSFTemplate() {
    }

    public static XSSFTemplate compile(String path) {
        return compile(new File(path));
    }

    public static XSSFTemplate compile(File file) {
        return compile(file, Configure.createDefault());
    }

    public static XSSFTemplate compile(InputStream inputStream) {
        return compile(inputStream, Configure.createDefault());
    }

    public static XSSFTemplate compile(String path, Configure config) {
        return compile(new File(path), config);
    }

    public static XSSFTemplate compile(File file, Configure config) {
        try {
            return compile(new FileInputStream(file), config);
        } catch (FileNotFoundException e) {
            throw new ResolverException("Cannot find the file [" + file.getPath() + "]", e);
        }
    }

    public static XSSFTemplate compile(InputStream inputStream, Configure config) {
        try {
            XSSFTemplate template = new XSSFTemplate();
            template.config = config;
            template.workbook = new XSSFWorkbook(inputStream);
            template.sheetTemplates = getSheetTemplateList(template.workbook, template.config);
            return template;
        } catch (OLE2NotOfficeXmlFileException e) {
            logger.error("Poi-tl currently only supports .docx format");
            throw new ResolverException("Compile template failed", e);
        } catch (IOException e) {
            throw new ResolverException("Compile template failed", e);
        }
    }

    /**
     * 通过数据模型渲染模板
     *
     * @param model 渲染数据
     * @return
     */
    public XSSFTemplate render(Object model) {
        this.sheetTemplates.forEach(xssfSheetTemplate -> {
            xssfSheetTemplate.getRenderer().render(xssfSheetTemplate, model);
        });
        return this;
    }

    /**
     * 存储需要分组的信息
     * @param groupData
     * @return
     */
    public XSSFTemplate group(List<String> groupData) {
        this.sheetTemplates.forEach(xssfSheetTemplate -> {
            xssfSheetTemplate.setGroup(groupData);
        });
        return this;
    }

    /**
     *
     * 通过数据模型渲染模板并写入OutputStream，不要忘记调用 {@link XSSFTemplate#close()}, {@link OutputStream#close()}
     *
     * @param model render data
     * @param out   output
     * @return
     * @throws IOException
     */
    public XSSFTemplate render(Object model, OutputStream out) throws IOException {
        this.render(model);
        this.write(out);
        return this;
    }

    /**
     * w写入输出流，不要忘了最后调用 {@link XSSFTemplate#close()},
     * {@link OutputStream#close()} finally
     *
     * @param out eg.ServletOutputStream
     * @throws IOException
     */
    public void write(OutputStream out) throws IOException {
        this.workbook.write(out);
    }

    /**
     * 写入并关闭输出流
     *
     * @param out eg.ServletOutputStream
     * @throws IOException
     */
    public void writeAndClose(OutputStream out) throws IOException {
        try {
            this.write(out);
            out.flush();
        } finally {
            PoitlIOUtils.closeQuietlyMulti(this.workbook, out);
        }
    }

    /**
     * 写入文件，此方法将关闭所有流
     *
     * @param path output path
     * @throws IOException
     */
    public void writeToFile(String path) throws IOException {
        this.writeAndClose(new FileOutputStream(path));
    }

    /**
     * 重新加载模板
     *
     * @param workbook load new template document
     */
    public void reload(XSSFWorkbook workbook) {
        PoitlIOUtils.closeLoggerQuietly(this.workbook);
        this.workbook = workbook;
        this.sheetTemplates = getSheetTemplateList(this.workbook, this.config);
    }

    /**
     * 关闭文件
     *
     * @throws IOException
     */
    @Override
    public void close() throws IOException {
        this.workbook.close();
    }


    private static List<XSSFSheetTemplate> getSheetTemplateList(XSSFWorkbook workbook, Configure config) {
        List<XSSFSheetTemplate> sheetList = new TreeList<>();
        Iterator<Sheet> iterator =  workbook.sheetIterator();
        while(iterator.hasNext()) {
            Sheet sheet = iterator.next();

            if(sheet instanceof XSSFSheet) {
                XSSFSheetTemplate xssfSheetTemplate = new XSSFSheetTemplate(config, (XSSFSheet) sheet);
                sheetList.add(xssfSheetTemplate);
            }
        }
        return sheetList;
    }

    public XSSFWorkbook getWorkbook() {
        return workbook;
    }

    public Configure getConfig() {
        return config;
    }

    public List<XSSFSheetTemplate> getSheetTemplates() {
        return sheetTemplates;
    }
}
