/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */

package cn.mftcc.doc.components.docmanage.web.utils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class FileUtility
{
    /**
     * 获取文件类型
     * @param fileName
     * @return
     */
    public static FileType getFileType(String fileName)
    {
        String ext = Objects.requireNonNull(getFileExtension(fileName)).toLowerCase();

        if (ExtsDocument.contains(ext)) {
            return FileType.Text;
        }

        if (ExtsSpreadsheet.contains(ext)) {
            return FileType.Spreadsheet;
        }

        if (ExtsPresentation.contains(ext)) {
            return FileType.Presentation;
        }

        return FileType.Text;
    }

    /**
     * 文档支持的类型
     */
    public static List<String> ExtsDocument = Arrays.asList
            (
                    ".doc", ".docx", ".docm",
                    ".dot", ".dotx", ".dotm",
                    ".odt", ".fodt", ".ott", ".rtf", ".txt",
                    ".html", ".htm", ".mht",
                    ".pdf", ".djvu", ".fb2", ".epub", ".xps"
            );
    /**
     * 表格支持的类型
     */
    public static List<String> ExtsSpreadsheet = Arrays.asList
            (
                    ".xls", ".xlsx", ".xlsm",
                    ".xlt", ".xltx", ".xltm",
                    ".ods", ".fods", ".ots", ".csv"
            );

    /**
     * 幻灯片支持的类型
     */
    public static List<String> ExtsPresentation = Arrays.asList
            (
                    ".pps", ".ppsx", ".ppsm",
                    ".ppt", ".pptx", ".pptm",
                    ".pot", ".potx", ".potm",
                    ".odp", ".fodp", ".otp"
            );

    /**
     * 获取路径中的文件名称
     * @param url
     * @return
     */
    public static String getFileName(String url)
    {
        if (url == null) {
            return null;
        }
        return url.substring(url.lastIndexOf('/') + 1, url.length());
    }

    /**
     * 获取路径中的文件后缀
     * @param url
     * @return
     */
    public static String getFileExtension(String url)
    {
        String fileName = getFileName(url);
        if (fileName == null) {
            return null;
        }
        String fileExt = fileName.substring(fileName.lastIndexOf("."));
        return fileExt.toLowerCase();
    }


}
