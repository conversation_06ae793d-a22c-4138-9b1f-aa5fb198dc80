/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.docmanage.model.enums;

/**
 * 图片存储类型
 *
 * <AUTHOR>
 * @date 2021/1/19
 */
public enum ImgSourceType implements ValueEnum<String> {
    /**
     * 本地图片
     */
    LOCAL("local"),
    /**
     * 互联网图片
     */
    URL("url");

    ImgSourceType(String value) {
        this.value = value;
    }

    private final String value;
    @Override
    public String getValue() {
        return value;
    }
}
