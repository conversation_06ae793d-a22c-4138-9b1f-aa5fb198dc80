/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.controller;

import cn.mftcc.common.exception.ServiceException;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.file.entity.DocScFiletypeConfigEntity;
import cn.mftcc.doc.components.file.service.DocScFiletypeConfigService;

import java.util.List;

/**
 * 场景的要件类型配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 11:32:22
 */
@RestController
@RequestMapping("file/docScFiletypeConfig")
public class DocScFiletypeConfigController {

    @Autowired
    private DocScFiletypeConfigService docScFiletypeConfigService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocScFiletypeConfigEntity docScFiletypeConfigEntity) {
        IPage<DocScFiletypeConfigEntity> list = this.docScFiletypeConfigService.findByPage(docScFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocScFiletypeConfigEntity docScFiletypeConfigEntity){
        this.docScFiletypeConfigService.insert(docScFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/insertList")
    public R insertList(@RequestBody List<DocScFiletypeConfigEntity> docScFiletypeConfigEntityList){
        this.docScFiletypeConfigService.insertList(docScFiletypeConfigEntityList);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocScFiletypeConfigEntity docScFiletypeConfigEntity){
        this.docScFiletypeConfigService.update(docScFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }
    @RequestMapping("/findList")
    public R findList(@RequestBody DocScFiletypeConfigEntity docScFiletypeConfigEntity){
        List<DocScFiletypeConfigEntity>configList = this.docScFiletypeConfigService.findList(docScFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", configList);
    }

    @RequestMapping("/getRootListByTypeBean")
    public R getRootListByTypeBean(@RequestBody DocScFiletypeConfigEntity docScFiletypeConfigEntity){
        JSONArray rootList = this.docScFiletypeConfigService.getRootListByTypeBean(docScFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", rootList);
    }



    @RequestMapping("/findById/{scId}")
    public R findById(@PathVariable("scId") String scId){
        DocScFiletypeConfigEntity docScFiletypeConfigEntity = this.docScFiletypeConfigService.findById(scId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docScFiletypeConfigEntity);
    }

    @RequestMapping("/deleteById/{scId}")
    public R deleteById(@PathVariable("scId") String scId){
        this.docScFiletypeConfigService.deleteById(scId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }


}