/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.mapper;

import cn.mftcc.doc.components.file.entity.DocScFiletypeConfigEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 场景的要件类型配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 11:32:22
 */
@Mapper
public interface DocScFiletypeConfigMapper extends BaseMapper<DocScFiletypeConfigEntity> {
	
}
