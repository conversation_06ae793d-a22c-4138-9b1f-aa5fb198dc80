/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service.impl;

import cn.mftcc.bizcommon.flow.FlowDataUtil;
import cn.mftcc.common.utils.UUIDUtil;
import cn.mftcc.doc.components.docmanage.web.utils.StringUtil;
import cn.mftcc.doc.components.mould.entity.DocScTemplateConfigEntity;
import cn.mftcc.doc.components.mould.mapper.DocScTemplateConfigMapper;
import cn.mftcc.doc.feign.client.WkfFeignClient;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.file.entity.DocScFiletypeConfigEntity;
import cn.mftcc.doc.components.file.mapper.DocScFiletypeConfigMapper;
import cn.mftcc.doc.components.file.service.DocScFiletypeConfigService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 场景的要件类型配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 11:32:22
 */
@Service("docScFiletypeConfigService")
@Transactional(rollbackFor = Exception.class)
public class DocScFiletypeConfigServiceImpl implements DocScFiletypeConfigService {

    @Autowired
    private DocScFiletypeConfigMapper docScFiletypeConfigMapper;
    @Autowired
    private DocScTemplateConfigMapper docScTemplateConfigMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private WkfFeignClient wkfFeignClient;

    @Override
    public IPage<DocScFiletypeConfigEntity> findByPage(DocScFiletypeConfigEntity docScFiletypeConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocScFiletypeConfigEntity> page = new Page<>();
            page.setCurrent(docScFiletypeConfigEntity.getPageNo());
            page.setSize(docScFiletypeConfigEntity.getPageSize());
            QueryWrapper<DocScFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docScFiletypeConfigEntity);
            return docScFiletypeConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docScFiletypeConfigEntity.getScId(),e);
        }
    }

    /**
     * 获取列表
     * @param docScFiletypeConfigEntity
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocScFiletypeConfigEntity> findList(DocScFiletypeConfigEntity docScFiletypeConfigEntity) throws ServiceException {
        try{


            QueryWrapper<DocScFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
//            mapperUtil.tableQuery(queryWrapper,docScFiletypeConfigEntity);
            queryWrapper.eq(StringUtils.isNotBlank(docScFiletypeConfigEntity.getPrdUniqueVal()),"prd_unique_val",docScFiletypeConfigEntity.getPrdUniqueVal());
            queryWrapper.eq(StringUtils.isNotBlank(docScFiletypeConfigEntity.getFlowNo()),"flow_no",docScFiletypeConfigEntity.getFlowNo());
            queryWrapper.eq(StringUtils.isNotBlank(docScFiletypeConfigEntity.getNodeNo()),"node_no",docScFiletypeConfigEntity.getNodeNo());
            if(StringUtils.isEmpty(docScFiletypeConfigEntity.getParentTypeNo())){

                queryWrapper.isNull("parent_type_no");
            }else if(!"bizConfig".equals(docScFiletypeConfigEntity.getParentTypeNo())){
                queryWrapper.eq(StringUtils.isNotBlank(docScFiletypeConfigEntity.getParentTypeNo()),"parent_type_no",docScFiletypeConfigEntity.getParentTypeNo());
            }

            return docScFiletypeConfigMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docScFiletypeConfigEntity.getScId(),e);
        }
    }

    /**
     *  拷贝一个产品的要件配置到另外一个产品中
     * {
     *   newPrdId  新的产品唯一标识
     *   motherPrdId 母体产品唯一标识
     * }
     * @return
     *   {
     *       code:"0000" --成功  "1111" -- 报错
     *       msg：""  --错误消息
     *   }
     * @throws Exception
     */
    @Override
    public JSONObject copyFilesConfigOfPrd( String newPrdId,String motherPrdId )throws  ServiceException{
        JSONObject result=new JSONObject();


        if(StringUtils.isEmpty(newPrdId)){
            result.put("code","1111");
            result.put("msg","参数[newPrdId]不能为空");
            return result;

        }

        if(StringUtils.isEmpty(motherPrdId)){
            result.put("code","1111");
            result.put("msg","参数[motherPrdId]不能为空");
            return result;

        }

        //获取母体产品所有的模板配置列表
        DocScFiletypeConfigEntity docScFiletypeConfigEntity=new DocScFiletypeConfigEntity();
        docScFiletypeConfigEntity.setPrdUniqueVal(motherPrdId);
        QueryWrapper<DocScFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(docScFiletypeConfigEntity.getPrdUniqueVal()),"prd_unique_val",docScFiletypeConfigEntity.getPrdUniqueVal());
        List<DocScFiletypeConfigEntity>motherConfigList =docScFiletypeConfigMapper.selectList(queryWrapper);

        if(motherConfigList!=null){
            if(motherConfigList.size()>0){
                for(DocScFiletypeConfigEntity newBean : motherConfigList){
                    newBean.setPrdUniqueVal(newPrdId);
                    newBean.setScId(UUIDUtil.getUUID());
                    Date tmpDate=new Date();
                    newBean.setCreateTime(tmpDate);
                    newBean.setUpdateTime(tmpDate);
                    docScFiletypeConfigMapper.insert(newBean);
                }
            }
        }

        result.put("code","0000");
        result.put("msg","拷贝成功");
        return  result;
    }


    /**
     * 删除指定的流程节点要件配置
     * @param delNode{
     *           prdUniqueVal：产品唯一标识
     *      *     flowNo：所属流程
     *      *     nodeNo：节点编号
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject delFileNodeSetting(List<Map<String,String>>delNode)throws Exception{
        JSONObject reuslt=new JSONObject();
        reuslt.put("code","0000");
        reuslt.put("msg","success");
        try{

            //获取母体产品所有的模板配置列表
            DocScFiletypeConfigEntity docScFiletypeConfigEntity=new DocScFiletypeConfigEntity();
            if(delNode!=null){
                for(Map<String ,String> node:delNode){
                    QueryWrapper<DocScFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
                    if(StringUtils.isNotEmpty(node.get("prdUniqueVal"))){
                        queryWrapper.eq("prd_unique_val",node.get("prdUniqueVal"));
                    }
                    if(StringUtils.isNotEmpty(node.get("flowNo"))){
                        queryWrapper.eq("flow_no",node.get("flowNo"));
                    }
                    if(StringUtils.isNotEmpty(node.get("nodeNo"))){
                        queryWrapper.eq("node_no",node.get("nodeNo"));
                    }
                    docScFiletypeConfigMapper.delete(queryWrapper);

                }
            }
        }catch (Exception e){
            reuslt.put("code","1111");
            reuslt.put("msg","删除失败");
            throw  e;

        }
        return reuslt;
    }

    /**
     * 删除要件配置信息
     * @param delNode
     * @throws Exception
     */
    @Override
    public  void delOneFileNodeSetting(Map<String,String>delNode)throws Exception{
        JSONObject reuslt=new JSONObject();
        reuslt.put("code","0000");
        reuslt.put("msg","success");
        try{

                if(delNode.size()>0){

                    QueryWrapper<DocScFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
                    if(StringUtils.isNotEmpty(delNode.get("prdUniqueVal"))){
                        queryWrapper.eq("prd_unique_val",delNode.get("prdUniqueVal"));
                    }
                    if(StringUtils.isNotEmpty(delNode.get("flowNo"))){
                        queryWrapper.eq("flow_no",delNode.get("flowNo"));
                    }
                    if(StringUtils.isNotEmpty(delNode.get("nodeNo"))){
                        queryWrapper.eq("node_no",delNode.get("nodeNo"));
                    }
                    docScFiletypeConfigMapper.delete(queryWrapper);
                }



        }catch (Exception e){

            throw  e;

        }

    }


    /**
     * 删除指定的流程节点模板配置
     * @param delNode{
     *           prdUniqueVal：产品唯一标识
     *      *     flowNo：所属流程
     *      *     nodeNo：节点编号
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject delTemplateNodeSetting(List<Map<String,String>>delNode)throws Exception{
        JSONObject reuslt=new JSONObject();
        reuslt.put("code","0000");
        reuslt.put("msg","success");
        try{

            //获取母体产品所有的模板配置列表
            DocScTemplateConfigEntity scTemplateConfigEntity=new DocScTemplateConfigEntity();
            if(delNode!=null){
                for(Map<String ,String> node:delNode){
                    QueryWrapper<DocScTemplateConfigEntity> queryWrapper = new QueryWrapper<>();
                    if(StringUtils.isNotEmpty(node.get("prdUniqueVal"))){
                        queryWrapper.eq("prd_unique_val",node.get("prdUniqueVal"));
                    }
                    if(StringUtils.isNotEmpty(node.get("flowNo"))){
                        queryWrapper.eq("flow_no",node.get("flowNo"));
                    }
                    if(StringUtils.isNotEmpty(node.get("nodeNo"))){
                        queryWrapper.eq("node_no",node.get("nodeNo"));
                    }
                    docScTemplateConfigMapper.delete(queryWrapper);

                }
            }
        }catch (Exception e){
            reuslt.put("code","1111");
            reuslt.put("msg","删除失败");
            throw  e;

        }
        return reuslt;
    }





    /**
     * 获取一个实体
     * @param typeBean
     * @return
     * @throws ServiceException
     */
    public  DocScFiletypeConfigEntity getOneObj(DocScFiletypeConfigEntity typeBean)throws  ServiceException{
        QueryWrapper<DocScFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
//        mapperUtil.tableQuery(queryWrapper,typeBean);
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getPrdUniqueVal()),"prd_unique_val",typeBean.getPrdUniqueVal());
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getFlowNo()),"flow_no",typeBean.getFlowNo());
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getNodeNo()),"node_no",typeBean.getNodeNo());
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getTypeNo()),"type_no",typeBean.getTypeNo());
        return docScFiletypeConfigMapper.selectOne(queryWrapper);

    }


    /**
     * 逐层获取父亲目录
     * @param typeBean
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray getRootListByTypeBean(DocScFiletypeConfigEntity typeBean)throws  ServiceException{


        JSONArray result=new JSONArray();

        if(StringUtils.isEmpty(typeBean.getTypeNo())){
            DocScFiletypeConfigEntity root=new DocScFiletypeConfigEntity();
            root.setTypeNo("");
            root.setTypeName("根目录");
            result.add(root);
            return result;
        }
        List<DocScFiletypeConfigEntity> list=new ArrayList<>();
        DocScFiletypeConfigEntity obj=getOneObj(typeBean);
        list.add(obj);
        while(StringUtils.isNotEmpty(obj.getTypeNo())){

            if(StringUtils.isEmpty(obj.getParentTypeNo())){
                DocScFiletypeConfigEntity root=new DocScFiletypeConfigEntity();
                root.setTypeNo("");
                root.setTypeName("根目录");
                result.add(root);
                break;
            }
            obj.setTypeNo(obj.getParentTypeNo());
            DocScFiletypeConfigEntity pBean=getOneObj(obj);
            list.add(pBean);
            obj.setParentTypeNo(pBean.getParentTypeNo());

        }


        if(list.size()>0){
            int len=list.size()-1;
            for(int i=len;i>=0;i-- ){
                result.add(list.get(i));
            }
        }


        return result;
    }



    @Override
    public void insert(DocScFiletypeConfigEntity docScFiletypeConfigEntity) throws ServiceException {
        try{


            docScFiletypeConfigEntity.setScId(UUIDUtil.getUUID());
            docScFiletypeConfigEntity.setIsEncrypt("0");
            docScFiletypeConfigEntity.setIfMustRead("0");
            if(StringUtils.isEmpty(docScFiletypeConfigEntity.getIfUpload())){
                docScFiletypeConfigEntity.setIfUpload("0");
            }
            if(StringUtils.isNotEmpty(docScFiletypeConfigEntity.getParentTypeNo())){

                String newTypeNo=getNewSubTypeNo(docScFiletypeConfigEntity);
                docScFiletypeConfigEntity.setTypeNo(newTypeNo);
            }

            Date tmpDate=new Date();
            docScFiletypeConfigEntity.setCreateTime(tmpDate);
            docScFiletypeConfigEntity.setUpdateTime(tmpDate);
            if(StringUtils.isEmpty(docScFiletypeConfigEntity.getParentTypeNo())){
                docScFiletypeConfigEntity.setParentTypeNo(null);
            }
            docScFiletypeConfigMapper.insert(docScFiletypeConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docScFiletypeConfigEntity.getScId(),e);
        }
    }

    @Override
    public void insertList(List<DocScFiletypeConfigEntity> docScFiletypeConfigEntityList) throws ServiceException {
        try{
            if (!docScFiletypeConfigEntityList.isEmpty()){
                DocScFiletypeConfigEntity query = new DocScFiletypeConfigEntity();
                query.setFlowNo(docScFiletypeConfigEntityList.get(0).getFlowNo());
                query.setPrdUniqueVal(docScFiletypeConfigEntityList.get(0).getPrdUniqueVal());
                query.setNodeNo(docScFiletypeConfigEntityList.get(0).getNodeNo());
                List<DocScFiletypeConfigEntity> scFiletypeConfigEntities = findList(query);
                if (!scFiletypeConfigEntities.isEmpty()){
                    for (DocScFiletypeConfigEntity filetypeConfigEntity:scFiletypeConfigEntities){
                        if("1".equals(filetypeConfigEntity.getIfUpload())){
                            for (DocScFiletypeConfigEntity docScFiletypeConfigEntity:docScFiletypeConfigEntityList){
                                // 判断typeNo是否相同，相同将保留的是否必填状态
                                if(filetypeConfigEntity.getTypeNo().equals(docScFiletypeConfigEntity.getTypeNo())){
                                    docScFiletypeConfigEntity.setIfUpload(filetypeConfigEntity.getIfUpload());
                                }
                            }
                        }
                        deleteById(filetypeConfigEntity.getScId());
                    }
                }

                for (DocScFiletypeConfigEntity docScFiletypeConfigEntity:docScFiletypeConfigEntityList){
                    this.insert(docScFiletypeConfigEntity);
                }
            }
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,"",e);
        }
    }

    private String getNewSubTypeNo(DocScFiletypeConfigEntity docScFiletypeConfigEntity)throws ServiceException{
        String pNo="";
        if(docScFiletypeConfigEntity.getParentTypeNo()!=null){
            pNo=docScFiletypeConfigEntity.getParentTypeNo();
        }
        String result=pNo+"01";
        List<DocScFiletypeConfigEntity> tmpList= findList(docScFiletypeConfigEntity);
        if(tmpList!=null){
            if(tmpList.size()>0){
                int num=0;
                int begInd_=pNo.length();
                for(DocScFiletypeConfigEntity bean:tmpList){
                    String tmpNum=bean.getTypeNo().substring(begInd_);
                    int numVal=Integer.parseInt(tmpNum);
                    if(num<numVal){
                        num=numVal;
                    }
                }
                num=num+1;
                if(num<10){
                    result=pNo+"0"+num;
                }else{
                    result=pNo+num;

                }

            }
        }
        return result;
    }



    @Override
    public void update(DocScFiletypeConfigEntity docScFiletypeConfigEntity) throws ServiceException {
        try{
            docScFiletypeConfigMapper.updateById(docScFiletypeConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docScFiletypeConfigEntity.getScId(),e);
        }
    }

    @Override
    public DocScFiletypeConfigEntity findById(String scId) throws ServiceException {
        try{
            return docScFiletypeConfigMapper.selectById(scId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,scId,e);
        }
    }

    @Override
    public void deleteById(String scId) throws ServiceException {
        try{
            docScFiletypeConfigMapper.deleteById(scId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,scId,e);
        }
    }

    /**
     * 获取一个业务配置的所有要件类型
     * @param parmJson{
     *                {
     *
     * 	cus:{
     * 		defaultBizNo:55555,
     * 		parmList:[
     * 			{prdUniqual:"",flow_no:"",nodeNo:""}
     * 		],
     * 		specalBizNo:{
     * 			nodeNo:main_sign
     * 			bizNo:55555
     * 		}
     * 	},
     *   bus:{
     * 		defaultBizNo:55555,
     * 		parmList:[
     * 			{prdUniqual:"",flow_no:"",nodeNo:""}
     * 		],
     * 		specalBizNo:{
     * 			nodeNo:main_sign
     * 			bizNo:55555
     * 		}
     * 	}
     * }
     * }
     * @return{
     * cus:
     *
     * 		[
     * 			{
     *                		nodeNo:
     *
     * 				nodeName:
     * 				docType:[
     * 					{prdUniqual:"",flow_no:"",nodeNo:"",bizNo:"",typeNo,typeName},{}
     * 				]
     * 			}
     * 	       ]
     *
     * 	,
     * 	bus;[
     * 			{
     *             nodeNo:
     *
     * 				nodeName:
     * 				docType:[
     * 					{prdUniqual:"",flow_no:"",nodeNo:"",bizNo:"",typeNo,typeName},{}
     * 				]
     * 			}
     * 	       ]
     * }
     * @throws ServiceException
     */
    @Override
    public  JSONObject getAllFileTypeOfOneBuseness(JSONObject parmJson)throws ServiceException{
        JSONObject result =new JSONObject();
        JSONObject cusParmJson=parmJson.getJSONObject("cus");
        JSONArray cusFileType=getTypeCusList(cusParmJson);
        result.put("cus",cusFileType);

        JSONObject busParmJson=parmJson.getJSONObject("bus");
        JSONArray busFileType=getTypeList(busParmJson);
        result.put("bus",busFileType);



        return result;


    }


    /**
     * 根据参数获取客户下所有要件类型
     * @param queryJson{
     *      defaultBizNo:55555,
     * 		parmList:[
     * 			{prdUniqual:"",flowNo:"",nodeNo:""}
     * 		],
     * 		specalBizNo:[{
     * 			nodeNo:main_sign
     * 			bizNo:55555
     * 		},{
     * 		     nodeNo:main_sign
     *           bizNo:55555
     * 		}
     * 	   ]
     * }
     * @return
     * @throws ServiceException
     */
    public JSONArray getTypeCusList(JSONObject queryJson)throws ServiceException {
        JSONArray result = new JSONArray();
        String defaultBizNo = queryJson.getString("defaultBizNo");
        String prdUniqualVal = queryJson.getString("defaultBizNo");
        JSONArray parmList = queryJson.getJSONArray("parmList");
        if (parmList != null) {
            if (parmList.size() > 0) {

                JSONArray specalBizNo = queryJson.getJSONArray("specalBizNo");
                //封装特殊节点的bizNo
                Map<String, String> specalBizNoMap = new HashedMap();
                if (specalBizNo != null) {
                    for (int i = 0; i < specalBizNo.size(); i++) {
                        JSONObject json = specalBizNo.getJSONObject(i);
                        specalBizNoMap.put(json.getString("nodeNo"), json.getString("bizNo"));
                    }
                }

                for (int i = 0; i < parmList.size(); i++) {
                    JSONObject qObj = parmList.getJSONObject(i);
                    String prdUniqual = qObj.getString("prdUniqual");
                    String flowNo = qObj.getString("flowNo");
                    String nodeNo = qObj.getString("nodeNo");
                    DocScFiletypeConfigEntity docScFiletypeConfigEntity = new DocScFiletypeConfigEntity();
                    docScFiletypeConfigEntity.setPrdUniqueVal(prdUniqual);
                    docScFiletypeConfigEntity.setFlowNo(flowNo);
                    docScFiletypeConfigEntity.setNodeNo(nodeNo);


                    List<DocScFiletypeConfigEntity> filetypeList = findList(docScFiletypeConfigEntity);
                    JSONArray fileTypeArray = new JSONArray();
                    if (filetypeList != null) {
                        String bizNo = defaultBizNo;
                        if (specalBizNoMap.containsKey(nodeNo)) {
                            bizNo = specalBizNoMap.get(nodeNo);
                        }
                        for (DocScFiletypeConfigEntity fileType : filetypeList) {
                            JSONObject type = new JSONObject();
                            type.put("prdUniqual", prdUniqual);
                            type.put("flowNo", flowNo);
                            type.put("nodeNo", nodeNo);
                            type.put("bizNo", bizNo);
                            type.put("typeNo", fileType.getTypeNo());
                            type.put("typeName", fileType.getTypeName());
                            fileTypeArray.add(type);

                        }

                    }
                    JSONObject nodeJson = new JSONObject();
                    {
                        nodeJson.put("nodeNo", nodeNo);
                        nodeJson.put("nodeName", "客户资料");
                        nodeJson.put("docType", fileTypeArray);
                        result.add(nodeJson);
                    }
                }
            }
        }
        return result;

    }





    /**
     * 根据参数获取所有要件类型
     * @param queryJson{
     *      defaultBizNo:55555,
     * 		parmList:[
     * 			{prdUniqual:"",flowNo:"",nodeNo:"",nodeName:""}
     * 		],
     * 		specalBizNo:[{
     * 			nodeNo:main_sign
     * 			bizNo:55555
     * 		},{
     * 		     nodeNo:main_sign
     *           bizNo:55555
     * 		}
     * 	   ]
     * }
     * @return
     * @throws ServiceException
     */
    private  JSONArray getTypeList(JSONObject queryJson)throws ServiceException {
        JSONArray result = new JSONArray();
        String defaultBizNo = queryJson.getString("defaultBizNo");
        JSONArray parmList = queryJson.getJSONArray("parmList");

        if (parmList != null) {
            if (parmList.size() > 0) {

                JSONArray specalBizNo = queryJson.getJSONArray("specalBizNo");
                //封装特殊节点的bizNo
                Map<String, String> specalBizNoMap = new HashedMap();
                if (specalBizNo != null) {
                    for (int i = 0; i < specalBizNo.size(); i++) {
                        JSONObject json = specalBizNo.getJSONObject(i);
                        specalBizNoMap.put(json.getString("nodeNo"), json.getString("bizNo"));
                    }
                }

                for (int i = 0; i < parmList.size(); i++) {
                    JSONObject qObj = parmList.getJSONObject(i);
                    String prdUniqual = qObj.getString("prdUniqual");





                    String flowNo = qObj.getString("flowNo");
                    String nodeNo = qObj.getString("nodeNo");
                    String nodeName = qObj.getString("nodeName");
                    DocScFiletypeConfigEntity docScFiletypeConfigEntity = new DocScFiletypeConfigEntity();
                    docScFiletypeConfigEntity.setPrdUniqueVal(prdUniqual);
                    docScFiletypeConfigEntity.setFlowNo(flowNo);
                    docScFiletypeConfigEntity.setNodeNo(nodeNo);


                    List<DocScFiletypeConfigEntity> filetypeList = findList(docScFiletypeConfigEntity);
                    JSONArray fileTypeArray = new JSONArray();
                    if (filetypeList != null) {
                        String bizNo = defaultBizNo;
                        if (specalBizNoMap.containsKey(nodeNo)) {
                            bizNo = specalBizNoMap.get(nodeNo);
                        }
                        for (DocScFiletypeConfigEntity fileType : filetypeList) {



                            JSONObject type = new JSONObject();
                            type.put("prdUniqual", prdUniqual);
                            if(StringUtils.isNotEmpty(flowNo)){

                                type.put("flowNo", flowNo);
                            }else{
                                type.put("flowNo", fileType.getFlowNo());
                            }


                            if(StringUtils.isNotEmpty(nodeNo)){

                                type.put("nodeNo", nodeNo);
                            }else{
                                type.put("nodeNo", fileType.getNodeNo());
                            }

                            type.put("bizNo", bizNo);
                            type.put("typeNo", fileType.getTypeNo());
                            type.put("typeName", fileType.getTypeName());
                            fileTypeArray.add(type);

                        }

                    }

                    JSONObject nodeJson = new JSONObject();
                    {
//     *             nodeNo:
//     *
//     * 				nodeName:
//     * 				docType:[
//     * 					{prdUniqual:"",flow_no:"",nodeNo:"",bizNo:"",typeNo,typeName},{}
//     * 				]
                        nodeJson.put("nodeNo", nodeNo);
                        nodeJson.put("nodeName", nodeName);
                        nodeJson.put("docType", fileTypeArray);

                        result.add(nodeJson);
                    }
                }
            }




        }
        return result;


    }

    /**
     * 获取一个流程的所有节点信息
     * @param flowMark
     * @return
     * @throws ServiceException
     */
    private Map<String ,String> getNodeMap(String flowMark)throws  ServiceException{

        String flowNodes = wkfFeignClient.getTaskDefByProcessDefinitionKey(flowMark);
        JSONObject flowNodeJson = FlowDataUtil.getTaskDefListResult(flowNodes);
        Map<String ,String> nodeMap = new HashedMap();
        if (flowNodeJson.getBoolean("flag")){
            JSONArray nodeList = flowNodeJson.getJSONArray("nodeList");
            for (int i = 0; i < nodeList.size(); i++) {
                JSONObject flowNode = new JSONObject();
                flowNode = (JSONObject) nodeList.get(i);
                if (flowNode.containsKey("name")){
                    JSONObject node = new JSONObject();
                    //排除子流程审批节点
            /*if (SetEnum.FLOW_CATEGORY_1.equals(productNodeEntity.getCategory())){
                if (flowNode.getString("id").lastIndexOf("_approve") == -1){
                    node.put("val",flowNode.getString("id"));
                    node.put("name",flowNode.getString("name"));
                    flowNodeArr.add(node);
                }
            }else {
            }*/
                    node.put("val",flowNode.getString("id"));
                    node.put("name",flowNode.getString("name"));
                    nodeMap.put(flowNode.getString("id"),flowNode.getString("name"));
                }
            }
        }
        return nodeMap;
    }
}




