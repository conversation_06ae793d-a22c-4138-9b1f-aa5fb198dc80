/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import cn.mftcc.common.MftccEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 业务数据的要件类型配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-05-08 14:31:02
 */
@Data
@TableName("doc_file_biz_filetype_config")
public class DocFileBizFiletypeConfigEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String scId;
	/**
	 * 业务流水号
	 */
	private String bizNo;
	/**
	 * 业务流水号
	 */
	@TableField(exist = false)
	private Set bizNoSet;
	/**
	 * 产品唯一标识
	 */
	private String prdUniqueVal;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;
	/**
	 * 要件类型编号
	 */
	private String typeNo;
	/**
	 * 要件类型编号
	 */
	@TableField(exist = false)
	private Set typeNoSet;
	/**
	 * 类型描述
	 */
	@TableField(exist = false)
	private String typeDesc;
	/**
	 * 要件类型名称
	 */
	private String typeName;
	/**
	 * 要件类型父亲编号
	 */
	private String parentTypeNo;
	/**
	 * 要件类型父亲名称
	 */
	@TableField(exist=false)
	private String parentTypeName;
	/**
	 * 是否加密 1-是 0-否
	 */
	private String isEncrypt;

	/**
	 * 是否使用高拍仪:1-是 0-否
	 */
	@TableField(exist=false)
	private String useSdkFlag;
	/**
	 * 是否必读  1-是 0-否
	 */
	private String ifMustRead;
	/**
	 * 是否必须上传1-是0-否
	 */
	private String ifUpload;

	/**
	 * 允许上传要件的个数
	 */
	private int uploadFileLimit;
	/**
	 * 产品版本号
	 */
	private Integer prdVerNo;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")

	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 法人机构号
	 */
	private  String  corpId;

	/**
	 * 主要产品唯一标识
	 */
	private String mainPrdUniqueVal;

	@TableField(exist=false)
	private List<DocFileInfEntity> fileList;

	/**
	 * 关联业务编号
	 */
	@TableField(exist=false)
	private String busRelatedId;
	/**
	 * 关联客户号
	 */
	@TableField(exist=false)
	private String cusRelatedId;
	/**
	 * 关联申请编号
	 */
	@TableField(exist=false)
	private String applyRelatedId;
	/**
	 * 关联合同编号
	 */
	@TableField(exist=false)
	private String constractRelatedId;
	/**
	 * 关联借据编号
	 */
	@TableField(exist=false)
	private String finRelatedId;
	/**
	 * 是否允许编辑
	 */
	@TableField(exist=false)
	private Boolean isEditFlag;





}
