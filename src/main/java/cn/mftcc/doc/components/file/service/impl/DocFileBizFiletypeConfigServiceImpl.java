/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.mftcc.archive.feign.dto.ArchivePledgeInfoDTO;
import cn.mftcc.archive.feign.dto.ArchiveRefundFileDTO;
import cn.mftcc.bizcommon.utils.BeanCopyUtil;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.*;
import cn.mftcc.doc.common.constant.DocConstant;
import cn.mftcc.doc.common.constant.MessageEnum;
import cn.mftcc.doc.common.utils.*;
import cn.mftcc.doc.components.docmanage.web.utils.DocUtil;
import cn.mftcc.doc.components.file.entity.*;
import cn.mftcc.doc.components.file.mapper.DocFileBizFiletypeConfigMapper;
import cn.mftcc.doc.components.file.mapper.DocFileInfMapper;
import cn.mftcc.doc.components.file.service.*;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import cn.mftcc.doc.components.mould.mapper.DocBizTemplateMapper;
import cn.mftcc.doc.components.mould.service.DocBizTemplateService;
import cn.mftcc.doc.components.mould.service.DocTemplateModelService;
import cn.mftcc.doc.components.rbmq.constant.CloudConstant;
import cn.mftcc.doc.feign.api.DocInterface;
import cn.mftcc.doc.feign.client.*;
import cn.mftcc.lease.feign.dto.LeaseMainEntityDTO;
import cn.mftcc.doc.feign.client.CreditFeignClient;
import cn.mftcc.doc.feign.client.CustomerFeignClient;
import cn.mftcc.doc.feign.client.ElinkApiFeignClient;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;
import cn.mftcc.pledge.feign.dto.PledgeBusRelInfoDTO;
import cn.mftcc.pledge.feign.dto.PledgeLeaseInfoDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.JsonObject;
import io.lettuce.core.dynamic.support.ReflectionUtils;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.hyperic.sigar.FileInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 业务数据的要件类型配置表
 *
 * <AUTHOR>
 * @email
 * @date 2021-05-08 14:31:02
 */
@Service("docFileBizFiletypeConfigService")
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DocFileBizFiletypeConfigServiceImpl implements DocFileBizFiletypeConfigService {

    @Autowired
    private DocFileTypeService  docFileTypeService;
    @Autowired
    private CustomerFeignClient customerFeignClient;
    @Autowired
    private DocFileBizFiletypeConfigMapper docFileBizFiletypeConfigMapper;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private DocBizTemplateService docBizTemplateService;
    @Autowired
    private DocScFiletypeConfigService docScFiletypeConfigService;
    @Autowired
    private DocFileInfService docFileInfService;
    @Autowired
    private DocTypeClassService docTypeClassService;
    @Autowired
    private ElinkApiFeignClient elinkFeignClient;
    @Autowired
    private LeaseFeiginClient leaseFeiginClient;
    @Autowired
    private RequestUtil requestUtil;
    @Value("${mftcc.template.mould-path:}")
    private String  mouldPath;
    @Autowired
    private OssConfig ossConfig;
    @Autowired CreditFeignClient creditFeignClient;
    @Autowired
    private DocTemplateModelService docTemplateModelService;
    @Autowired
    private ArchiveInfoFeignClient archiveInfoFeignClient;
    @Autowired
    private PledgeFeignClient pledgeFeignClient;
    //文档服务名称
    @Value("${spring.application.name:}")
    private String applicationName;
    @Value("${mftcc.template.server:}")
    private String   serverPath;

//    上传文件类型白名单
    @Value("${mftcc.file.upload-filetype-limit:}")
    private  String uploadFiletypeLimit;

//    上传单个文件大小限制单位（KB）
    @Value("${mftcc.file.upload-file-size:}")
    private  String uploadFileSize;
//    上传单个文件大小限制单位（KB）
    @Value("${mftcc.file.upload-file-name-length:}")
    private  String uploadFileNameLength;
//    传到外部服务器方式 obs-obs上传 ftp-ftp上传 oss-oss存储
    @Value("${mftcc.file.upload-outter-function:}")
    private  String uploadOutterFunction;


    @Value("${mftcc.fileTmpDown.downloadPath:}")
    private String downloadPath;



    @Autowired
    private MapperUtil mapperUtil;
    private static final String CONTENT_TYPE_DEF = "application/x-download";
    private static final String HEADER_VALUE_DEFAULT = "attchement;filename=";
    public static final String HEADER_NAME_DEFAULT = "Content-Disposition";
    private static final String CONTENT_TYPE_DEF1 = "application/octet-stream";
    private static final String CHARSET_NAME_DEF = "iso-8859-1";

//    @Value("${mftcc.file.doc-file-show-url:}")
//    private  String docFileShowUrl;

    @Value("${mftcc.file.doc-file-upload-path:}")
    private  String docFileUploadPath;

//    @Value("${mftcc.file.doc-file-thumb-url:}")
//    private  String docFileThumbUrl;
    private static final String IMAGE = "image";

    @Autowired
    private DocFileInfMapper docFileInfMapper;
    @Autowired
    private DocBizTemplateMapper docBizTemplateMapper;

    @Override
    public IPage<DocFileBizFiletypeConfigEntity> findByPage(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocFileBizFiletypeConfigEntity> page = new Page<>();
            page.setCurrent(docFileBizFiletypeConfigEntity.getPageNo());
            page.setSize(docFileBizFiletypeConfigEntity.getPageSize());
            QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docFileBizFiletypeConfigEntity);
            return docFileBizFiletypeConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docFileBizFiletypeConfigEntity.getScId(),e);
        }
    }

    @Override
    public JSONObject getUploadLimitParam()throws ServiceException{
        JSONObject result=new JSONObject();
        result.put("uploadFiletypeLimit",uploadFiletypeLimit);
        result.put("uploadFileSize",uploadFileSize);
        result.put("uploadFileNameLength",uploadFileNameLength);
        return  result;
    }
    @Override
    public List<DocFileBizFiletypeConfigEntity> findList(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException {
        try{
            QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getFlowNo()),"flow_no",docFileBizFiletypeConfigEntity.getFlowNo());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getNodeNo()),"node_no",docFileBizFiletypeConfigEntity.getNodeNo());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getBizNo()),"biz_no",docFileBizFiletypeConfigEntity.getBizNo());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getTypeNo()),"type_no",docFileBizFiletypeConfigEntity.getTypeNo());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getPrdUniqueVal()),"prd_unique_val",docFileBizFiletypeConfigEntity.getPrdUniqueVal());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getIfUpload()),"if_upload",docFileBizFiletypeConfigEntity.getIfUpload());
            if(StringUtils.isEmpty(docFileBizFiletypeConfigEntity.getParentTypeNo())){
            }else if(!"bizConfig".equals(docFileBizFiletypeConfigEntity.getParentTypeNo())){
                queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getParentTypeNo()),"parent_type_no",docFileBizFiletypeConfigEntity.getParentTypeNo());
            }
            queryWrapper.orderByAsc("create_time");
            return docFileBizFiletypeConfigMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docFileBizFiletypeConfigEntity.getScId(),e);
        }
    }


    @Override
    public List<DocFileBizFiletypeConfigEntity> findTypeAndList(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException {
        try{
            // 判断是否是归档，参数初始化
            // 归档参数处理
            String flowNo = docFileBizFiletypeConfigEntity.getFlowNo();
            if(StringUtils.isNotBlank(flowNo) && Constant.ARCHIVE_SHOW_FILE.equals(flowNo)){
                getArchiveFileParam(docFileBizFiletypeConfigEntity);
            }


            QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
            if(StringUtils.isNotBlank(flowNo) && Constant.ARCHIVE_SHOW_FILE.equals(flowNo)){
                queryWrapper.in("biz_no",docFileBizFiletypeConfigEntity.getBizNoSet());
                queryWrapper.in("type_no",docFileBizFiletypeConfigEntity.getTypeNoSet());
                queryWrapper.groupBy("type_no");
            }else{
                queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getPrdUniqueVal()),"prd_unique_val",docFileBizFiletypeConfigEntity.getPrdUniqueVal());
                queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getFlowNo()),"flow_no",docFileBizFiletypeConfigEntity.getFlowNo());
                queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getNodeNo()),"node_no",docFileBizFiletypeConfigEntity.getNodeNo());
                queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getBizNo()),"biz_no",docFileBizFiletypeConfigEntity.getBizNo());
                queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getTypeNo()),"type_no",docFileBizFiletypeConfigEntity.getTypeNo());
            }
            if(StringUtils.isEmpty(docFileBizFiletypeConfigEntity.getParentTypeNo())){
                queryWrapper.isNull("parent_type_no");
            }else if(!"bizConfig".equals(docFileBizFiletypeConfigEntity.getParentTypeNo())){
                queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getParentTypeNo()),"parent_type_no",docFileBizFiletypeConfigEntity.getParentTypeNo());
            }
            queryWrapper.orderByAsc("type_no");

            DocFileTypeEntity qBean=new DocFileTypeEntity();

            List<DocFileTypeEntity>typeList=docFileTypeService.findList(qBean);
            Map<String ,String>typeDescMap=new HashedMap();
            Map<String ,String>sdkUseFlagMap=new HashedMap();
            if(typeList!=null){
                for(DocFileTypeEntity docFileTypeEntity:typeList){
                    typeDescMap.put(docFileTypeEntity.getTypeNo(),docFileTypeEntity.getTypeDesc());
                    sdkUseFlagMap.put(docFileTypeEntity.getTypeNo(),docFileTypeEntity.getUseSdkFlag());
                }
            }


            List<DocFileBizFiletypeConfigEntity> _listAll = docFileBizFiletypeConfigMapper.selectList(queryWrapper);
            List<DocFileBizFiletypeConfigEntity> _list = new ArrayList<>();
            // 维度过滤
            if(CollectionUtils.isNotEmpty(_listAll)){
                for (DocFileBizFiletypeConfigEntity fileBizFiletypeConfigEntity: _listAll) {
                    if(getFilterFile(fileBizFiletypeConfigEntity,docFileBizFiletypeConfigEntity)){
                        _list.add(fileBizFiletypeConfigEntity);
                    }
                }
            }
            //封装要件类型的map
            DocFileTypeEntity qDocTypeBean=new DocFileTypeEntity();
            List<DocFileTypeEntity>fileTypeEntities=docFileTypeService.findList(qDocTypeBean);
            Map<String,String>fileTypeMap=new HashedMap();
            for(DocFileTypeEntity tmpBean:fileTypeEntities){
                fileTypeMap.put(tmpBean.getTypeNo(),tmpBean.getTypeClass());
            }
            DocTypeClassEntity qtypeClassBean=new DocTypeClassEntity();
            //封装要一级件类型的map
            List<DocTypeClassEntity> typeClassEntities=docTypeClassService.getList(qtypeClassBean);
            Map<String ,String >docTypeClassMap=new HashedMap();
            for(DocTypeClassEntity tmpbean:typeClassEntities){
                docTypeClassMap.put(tmpbean.getTypeNo(),tmpbean.getTypeName());
            }

            // 根据文件类型获取已经上传的要件信息

            for(DocFileBizFiletypeConfigEntity _docFileBizFiletypeConfigEntity: _list){
                DocFileInfEntity  docFileInfEntity = new  DocFileInfEntity();
                if(StringUtils.isNotBlank(flowNo) && Constant.ARCHIVE_SHOW_FILE.equals(flowNo)){
                    docFileInfEntity.setBizNoSet(docFileBizFiletypeConfigEntity.getBizNoSet());
                    docFileInfEntity.setFlowNo(flowNo);
                }else{
                    if (StringUtils.isNotBlank(_docFileBizFiletypeConfigEntity.getMainPrdUniqueVal())){
                        docFileInfEntity.setPrdUniqueVal(_docFileBizFiletypeConfigEntity.getMainPrdUniqueVal());
                    }else{
                        docFileInfEntity.setPrdUniqueVal(_docFileBizFiletypeConfigEntity.getPrdUniqueVal());
                    }
                    docFileInfEntity.setBizNo(_docFileBizFiletypeConfigEntity.getBizNo());
                    docFileInfEntity.setFlowNo(_docFileBizFiletypeConfigEntity.getFlowNo());
                    docFileInfEntity.setNodeNo(_docFileBizFiletypeConfigEntity.getNodeNo());
                }
                String typeNo=_docFileBizFiletypeConfigEntity.getTypeNo();
                docFileInfEntity.setTypeNo(typeNo);
                if(fileTypeMap.containsKey(typeNo)){
                    String pNo=fileTypeMap.get(typeNo);//获取上级类别编号
                    String pName=docTypeClassMap.get(pNo);//获取上级类别的名称
                    _docFileBizFiletypeConfigEntity.setParentTypeNo(pNo);
                    _docFileBizFiletypeConfigEntity.setParentTypeName(pName);
                }

                List<DocFileInfEntity> fileList =  docFileInfService.findFileInfList(docFileInfEntity);
                _docFileBizFiletypeConfigEntity.setFileList(fileList);
                _docFileBizFiletypeConfigEntity.setUseSdkFlag(sdkUseFlagMap.get(_docFileBizFiletypeConfigEntity.getTypeNo()));
                _docFileBizFiletypeConfigEntity.setTypeDesc(typeDescMap.get(_docFileBizFiletypeConfigEntity.getTypeNo()));//添加上要件类型描述

                if(StringUtils.isEmpty(_docFileBizFiletypeConfigEntity.getCusRelatedId())){
                    _docFileBizFiletypeConfigEntity.setCusRelatedId(docFileBizFiletypeConfigEntity.getCusRelatedId());
                }
                if(StringUtils.isEmpty(_docFileBizFiletypeConfigEntity.getApplyRelatedId())){
                    _docFileBizFiletypeConfigEntity.setApplyRelatedId(docFileBizFiletypeConfigEntity.getApplyRelatedId());
                }
                if(StringUtils.isEmpty(_docFileBizFiletypeConfigEntity.getConstractRelatedId())){
                    _docFileBizFiletypeConfigEntity.setConstractRelatedId(docFileBizFiletypeConfigEntity.getConstractRelatedId());
                }
                if(StringUtils.isEmpty(_docFileBizFiletypeConfigEntity.getFinRelatedId())){
                    _docFileBizFiletypeConfigEntity.setFinRelatedId(docFileBizFiletypeConfigEntity.getFinRelatedId());
                }
                if(StringUtils.isEmpty(_docFileBizFiletypeConfigEntity.getBusRelatedId())){
                    _docFileBizFiletypeConfigEntity.setBusRelatedId(docFileBizFiletypeConfigEntity.getBusRelatedId());
                }

                //是否允许编辑
                if(docFileBizFiletypeConfigEntity.getIsEditFlag() != null){
                    _docFileBizFiletypeConfigEntity.setIsEditFlag(docFileBizFiletypeConfigEntity.getIsEditFlag());
                }

            }

            return _list;
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docFileBizFiletypeConfigEntity.getScId(),e);
        }


    }

    /**
     * 归档参数处理
     * @param docFileBizFiletypeConfigEntity
     */
    private void getArchiveFileParam(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) {
        String bizNos [] = docFileBizFiletypeConfigEntity.getBizNo().split(",");
        String pactId = bizNos[1];
        String archRefundId = bizNos[0];
        String nodeNo = docFileBizFiletypeConfigEntity.getNodeNo();
        ArchiveRefundFileDTO archiveRefundFileDTO = new ArchiveRefundFileDTO();
        archiveRefundFileDTO.setArchRefundId(archRefundId);
        List<ArchiveRefundFileDTO> archiveRefundFileDTOList = archiveInfoFeignClient.findArchiveRefundFileList(archiveRefundFileDTO);
        Set typeNoSet = new HashSet();
        Set bizNoSet = new HashSet();
        if(CollectionUtils.isEmpty(archiveRefundFileDTOList)){
            docFileBizFiletypeConfigEntity.setNodeNo(Constant.ARCHIVE_SHOW_FILE);
            typeNoSet.add(Constant.ARCHIVE_SHOW_FILE);
            bizNoSet.add(Constant.ARCHIVE_SHOW_FILE);
            docFileBizFiletypeConfigEntity.setBizNoSet(bizNoSet);
            docFileBizFiletypeConfigEntity.setTypeNoSet(typeNoSet);
            return;
        }
        for (ArchiveRefundFileDTO refundFileDTO: archiveRefundFileDTOList) {
            typeNoSet.add(refundFileDTO.getTypeNo());
        }
        docFileBizFiletypeConfigEntity.setTypeNoSet(typeNoSet);

        if(Constant.ARCHIVE_RETURN_TYPE_PAGE_PACT_RETURN.equals(nodeNo)){
            bizNoSet.add(pactId);
        }else if(Constant.ARCHIVE_RETURN_TYPE_ALL_END_WARRANT_RETURN.equals(nodeNo)){
            bizNoSet.add(pactId);
            PledgeBusRelInfoDTO pledgeBusRelInfoDTO = new PledgeBusRelInfoDTO();
            pledgeBusRelInfoDTO.setRelPactId(pactId);
            List<PledgeBusRelInfoDTO> pledgeBusRelInfoDTOList = pledgeFeignClient.getPledgeBusRelInfoList(pledgeBusRelInfoDTO);
            for (PledgeBusRelInfoDTO busRelInfoDTO: pledgeBusRelInfoDTOList) {
                bizNoSet.add(busRelInfoDTO.getPledgeId());
            }
        }else if(Constant.ARCHIVE_RETURN_TYPE_PART_END_WARRANT_RETURN.equals(nodeNo)){
            bizNoSet.add(pactId);
            ArchivePledgeInfoDTO archivePledgeInfoDTO = new ArchivePledgeInfoDTO();
            archivePledgeInfoDTO.setArchAppId(archRefundId);
            List<ArchivePledgeInfoDTO> archivePledgeInfoDTOList = archiveInfoFeignClient.findArchivePledgeInfoList(archivePledgeInfoDTO);
            if(CollectionUtils.isNotEmpty(archivePledgeInfoDTOList)){
                for (ArchivePledgeInfoDTO pledgeInfoDTO: archivePledgeInfoDTOList) {
                    bizNoSet.add(pledgeInfoDTO.getLeaseId());
                }
            }
        }else{
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, "", new Exception("查询类型不存在"));
        }
        docFileBizFiletypeConfigEntity.setBizNoSet(bizNoSet);
    }

    /**
     * todo 根据维度过滤影像资料
     * @参数
     * @返回
     * @作者 仇招
     * @日期 2022/8/17 15:09
     **/
    private boolean getFilterFile(DocFileBizFiletypeConfigEntity fileBizFiletypeConfigEntity, DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) {
        try {
            DocFileTypeEntity fileTypeEntity = docFileTypeService.findById(fileBizFiletypeConfigEntity.getTypeNo());
            if (fileTypeEntity == null) {
                return true;
            }
            String serviceName = fileTypeEntity.getMultiFeign();
            String methodName = fileTypeEntity.getMultiFeignMethod();
            String parmList = fileTypeEntity.getParmList();
            if (StringUtils.isBlank(serviceName) || StringUtils.isBlank(methodName)) {
                MFLogger.info("服务名称:" + serviceName + ",方法名称:" + methodName + ",参数:" + JSONObject.toJSONString(docFileBizFiletypeConfigEntity));
                return true;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("templateId", fileTypeEntity.getTypeNo());
            String cusRelatedId = docFileBizFiletypeConfigEntity.getCusRelatedId();
            if (StringUtils.isNoneBlank(cusRelatedId) && parmList.contains("cusId")) {
                jsonObject.put("cusId", cusRelatedId);
            }
            String applyId = docFileBizFiletypeConfigEntity.getApplyRelatedId();
            if (StringUtils.isNoneBlank(applyId) && parmList.contains("applyId")) {
                jsonObject.put("applyId", applyId);
            }
            String pactId = docFileBizFiletypeConfigEntity.getConstractRelatedId();
            if (StringUtils.isNoneBlank(pactId) && parmList.contains("pactId")) {
                jsonObject.put("pactId", pactId);
            }
            String mainId = docFileBizFiletypeConfigEntity.getBusRelatedId();
            if (StringUtils.isNoneBlank(mainId)) {
                jsonObject.put("mainId", mainId);
                if (StringUtils.isBlank(applyId)) {
                    JSONObject leaseMainEntityDTO = leaseFeiginClient.getLeaseMainById(mainId);
                    if (leaseMainEntityDTO != null) {
                        jsonObject.put("applyId", leaseMainEntityDTO.getString("applyId"));
                        jsonObject.put("cusId", leaseMainEntityDTO.getString("cusId"));
                    }
                }
            } else {
                if (StringUtils.isNoneBlank(parmList) && parmList.contains("mainId")) {
                    mainId = docFileBizFiletypeConfigEntity.getBizNo();
                    JSONObject leaseMainEntityDTO = leaseFeiginClient.getLeaseMainById(mainId);
                    if (leaseMainEntityDTO != null) {
                        jsonObject.put("mainId", mainId);
                        jsonObject.put("applyId", leaseMainEntityDTO.getString("applyId"));
                        jsonObject.put("cusId", leaseMainEntityDTO.getString("cusId"));
                    }
                }
            }
            MFLogger.info("服务名称:" + serviceName + ",方法名称:" + methodName + ",参数:" + JSONObject.toJSONString(jsonObject));
            Method method = ReflectionUtils.findMethod(SpringUtil.getBean(serviceName).getClass(), methodName, JSONObject.class);
            return (Boolean) ReflectionUtils.invokeMethod(method, SpringUtil.getBean(serviceName), jsonObject);
        }catch(Exception e){
            MFLogger.error("维度过滤失败！",e);
            throw e;
        }
    }

    /**
     * todo 影像资料维度过滤
     * @参数
     * @返回
     * @作者 仇招
     * @日期 2022/8/17 15:22
     **/
    public Boolean getTemplteDetail(JSONObject jsonObject) throws ServiceException {
        String templateId = jsonObject.getString("templateId");
        if(StringUtils.isBlank(templateId)){
            MFLogger.info("类型编号为空");
            return true;
        }
        try{
            return  docTemplateModelService.getIfShowTemplate(jsonObject);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_ERROR,templateId,e);
        }
    }

    @Override
    public List<DocFileBizFiletypeConfigEntity> findTypeAndListMix(JSONObject jsonObject) throws ServiceException {

        JSONArray jsonArray = jsonObject.getJSONArray("fileParameterArray");

        List<DocFileBizFiletypeConfigEntity> docFileBizFiletypeConfigEntityList = new ArrayList<DocFileBizFiletypeConfigEntity>();

        if(jsonArray != null &&jsonArray.size() > 0){
            for(int a = 0; a<jsonArray.size();a++){
                DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = JSONObject.parseObject(jsonArray.getJSONObject(a).toJSONString(),DocFileBizFiletypeConfigEntity.class);
                List<DocFileBizFiletypeConfigEntity> list_for = this.findTypeAndList(docFileBizFiletypeConfigEntity);
                if(CollectionUtils.isNotEmpty(list_for)){
                    docFileBizFiletypeConfigEntityList.addAll(list_for);
                }

            }
        }

        return docFileBizFiletypeConfigEntityList;
    }

    @Override
    public JSONArray findTypeAndList(JSONObject jsonObject)throws  ServiceException{
        JSONArray result=new JSONArray();
        if(jsonObject!=null){
            if(jsonObject.size()>0){
                DocFileBizFiletypeConfigEntity parm=JSONObject.toJavaObject(jsonObject,DocFileBizFiletypeConfigEntity.class);
                List<DocFileBizFiletypeConfigEntity> tmpList=findList(parm);
                if(tmpList!=null){
                    Map<String,String >keyMap=new HashedMap();

                    for(DocFileBizFiletypeConfigEntity configEntity:tmpList){
                        String  key=configEntity.getFlowNo()+"_"+configEntity.getNodeNo();
                        if(!keyMap.containsKey(key)){

                            result.add(configEntity);
                            keyMap.put(key,key);
                        }
                    }
                }
            }
        }

        return result;
    }
    @Override
    public JSONArray findTypeScList(JSONObject jsonObject)throws  ServiceException{
        JSONArray result=new JSONArray();
        if(jsonObject!=null){
            if(jsonObject.size()>0){
                DocScFiletypeConfigEntity parm=JSONObject.toJavaObject(jsonObject,DocScFiletypeConfigEntity.class);
                List<DocScFiletypeConfigEntity> tmpList=docScFiletypeConfigService.findList(parm);
                if(tmpList!=null){
                    Map<String,String >keyMap=new HashedMap();

                    for(DocScFiletypeConfigEntity configEntity:tmpList){
                        String  key=configEntity.getFlowNo()+"_"+configEntity.getNodeNo();
                        if(!keyMap.containsKey(key)){

                            result.add(configEntity);
                            keyMap.put(key,key);
                        }
                    }
                }
            }
        }

        return result;
    }


    /**
     * 获取一个实体
     * @param docFileBizFiletypeConfigEntity
     * @return
     * @throws ServiceException
     */
    private  DocFileBizFiletypeConfigEntity findOneEntity(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException {
        try{

            QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();

//            mapperUtil.tableQuery(queryWrapper,docFileBizFiletypeConfigEntity);
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getPrdUniqueVal()),"prd_unique_val",docFileBizFiletypeConfigEntity.getPrdUniqueVal());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getFlowNo()),"flow_no",docFileBizFiletypeConfigEntity.getFlowNo());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getNodeNo()),"node_no",docFileBizFiletypeConfigEntity.getNodeNo());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getBizNo()),"biz_no",docFileBizFiletypeConfigEntity.getBizNo());
            queryWrapper.eq(StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getTypeNo()),"type_no",docFileBizFiletypeConfigEntity.getTypeNo());
            return docFileBizFiletypeConfigMapper.selectOne(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docFileBizFiletypeConfigEntity.getScId(),e);
        }
    }


    /**
     * 封装目录下所有的的 子层目录和 文件
     * @param docFileBizFiletypeConfigEntity
     * @return
     * @throws ServiceException
     */
    @Override
    public  List<FileShowObj> getFileShowList(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException {
        try{
            List<FileShowObj> result=new ArrayList<FileShowObj>();
            //获取该目录下所有子层目录
            List<DocFileBizFiletypeConfigEntity> subFolderList=findList(docFileBizFiletypeConfigEntity);
           //获取该目录下所有的要件
            DocFileInfEntity fileQbean=new DocFileInfEntity();
            fileQbean.setPrdUniqueVal(docFileBizFiletypeConfigEntity.getPrdUniqueVal());
            fileQbean.setFlowNo(docFileBizFiletypeConfigEntity.getFlowNo());
            fileQbean.setNodeNo(docFileBizFiletypeConfigEntity.getNodeNo());
            fileQbean.setBizNo(docFileBizFiletypeConfigEntity.getBizNo());
            fileQbean.setTypeNo(docFileBizFiletypeConfigEntity.getParentTypeNo());


            List<DocFileInfEntity> fileList=new ArrayList<>();
            if(StringUtils.isNotEmpty(fileQbean.getTypeNo())){

                fileList=docFileInfService.findFileInfList(fileQbean);
            }


            //封装文件
            packageFolderAndFiles(subFolderList,fileList,result);

            return result;

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docFileBizFiletypeConfigEntity.getScId(),e);
        }

    }




    /**
     *  根据要件id 获取目录下所有的的 子层目录和 文件
     * @param parmJson{
     *         fileIdAry
     *         typeNo
     * }
     * @param
     * @return
     * @throws ServiceException
     */
    @Override
    public  List<FileShowObj> findListByFileIdAry(JSONObject parmJson) throws ServiceException {
        try{



            JSONArray parmArray=parmJson.getJSONArray("fileIdAry");

            String[]fileIdAry=new String[parmArray.size()];
            for(int i=0;i<parmArray.size();i++){
                fileIdAry[i]=parmArray.getString(i);
            }

            String typeNo=(String) parmJson.get("typeNo");
            List<FileShowObj> result=new ArrayList<FileShowObj>();
            //获取该目录下所有子层目录

//            List<DocFileBizFiletypeConfigEntity> subFolderList=docFileInfService.getFloderListByFileIds(fileIdAry);
            List<DocFileBizFiletypeConfigEntity> subFolderList=new ArrayList<>();
            if(StringUtils.isEmpty(typeNo)){
                subFolderList=docFileInfService.getFloderListByFileIds(fileIdAry);
            }
           //获取该目录下所有的要件

            List<DocFileInfEntity> fileList=new ArrayList<>();

            fileList=docFileInfService.getFileListByFileIds(fileIdAry,typeNo);

            //封装文件
            packageFolderAndFiles(subFolderList,fileList,result);

            return result;

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,parmJson.toJSONString(),e);
        }

    }






    @Override
    public void downloadAllFile(HttpServletResponse response, JSONObject ajaxData)throws   ServiceException {
        OutputStream outputStream = null;
        String  folderId=ajaxData.getString("folderId");
        DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity=findById(folderId);
        String downloadName = docFileBizFiletypeConfigEntity.getTypeName()+".zip";

        try {
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(downloadName, "UTF-8"));

            outputStream = response.getOutputStream();

            // 将文件流写入zip中
            downloadTolocal(docFileBizFiletypeConfigEntity,downloadName,response);

        } catch (Exception e) {
            MFLogger.error("downloadAllFile-xxx下载全部附件失败，processInstanceId=[{}],错误信息=[{}]",folderId,e);
        }finally {

            if(outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e2) {
                    MFLogger.error("关闭输入流时出现错误",e2);
                }
            }

        }

    }

    @Override
    public void downloadAllSltFileZip(HttpServletResponse response, JSONObject ajaxData)throws   ServiceException {
        OutputStream outputStream = null;
        String  allFlag=ajaxData.getString("allFlag");
        JSONObject typeJsonAry=ajaxData.getJSONObject("fileTypeList");
        FileShowVo fileShowVo=JSONObject.parseObject(typeJsonAry.toJSONString(),FileShowVo.class);
        String downloadName = "要件下载.zip";

        try {
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(downloadName, "UTF-8"));

            outputStream = response.getOutputStream();

            // 将文件流写入zip中
            downloadSltFileZip(fileShowVo,downloadName,allFlag,response);

        } catch (Exception e) {
            MFLogger.error("downloadAllFile-xxx下载全部附件失败，processInstanceId=[{}],错误信息=[{}]","",e);
        }finally {

            if(outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e2) {
                    MFLogger.error("关闭输入流时出现错误",e2);
                }
            }

        }

    }

    @Override
    public JSONObject deleteSelectFile(JSONObject ajaxData) throws ServiceException {
        JSONObject result = new JSONObject();
        String  allFlag=ajaxData.getString("allFlag");
        JSONObject typeJsonAry=ajaxData.getJSONObject("fileTypeList");
        FileShowVo fileShowVo=JSONObject.parseObject(typeJsonAry.toJSONString(),FileShowVo.class);
        if(fileShowVo!=null){

            for(DocFileBizFiletypeConfigEntity typeObj:fileShowVo.getFileList()){
                if("1".equals(allFlag)){
                    //删除所有要件
                }else{
                    //删除选中要件
                    for(DocFileInfEntity fileObj:typeObj.getFileList()){
                        if(fileObj.getDownLoadFlag()){
                            //判断是否允许删除
                            DocFileInfEntity docFileInfEntity = docFileInfService.findById(fileObj.getFileId());
                            if(docFileInfEntity.getDeleteFlag().equals("1")){
                                docFileInfService.deleteById(fileObj.getFileId());
                            }else{
                                result.put("code","1111");
                                result.put("msg",fileObj.getFileName()+"放款前上传的材料不允许删除！");
                                return result;
                            }
                        }
                    }
                    result.put("code","0000");
                    result.put("msg","删除成功！");
                }
            }

        }
        return result;
    }


    @Override
    public void downloadAllFilesZip(HttpServletResponse response, JSONObject ajaxData)throws   ServiceException {
        OutputStream outputStream = null;
        ArrayList<String> fileIds = (ArrayList<String>) ajaxData.get("fileIds");
        if(CollectionUtils.isEmpty(fileIds)){
            return;
        }

        String downloadName = DateUtil.getCurrentDate("yyyyMMddHHmmssSSS") + "发票下载.zip";

        try {
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(downloadName, "UTF-8"));

            outputStream = response.getOutputStream();
            //获取文件信息（此处为业务代码，可根据自己的需要替换）
            QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("file_id",fileIds);
            List<DocFileInfEntity> fileInfoList=docFileInfMapper.selectList(queryWrapper);
            // 将文件流写入zip中
            downloadAllFilesZip(fileInfoList,downloadName,response);

        } catch (Exception e) {
            MFLogger.error("downloadAllFilesZip-发票下载失败，processInstanceId=[{}],错误信息=[{}]","",e);
        }finally {
            if(outputStream != null) {
                try {
                    outputStream.close();
                } catch (Exception e2) {
                    MFLogger.error("关闭输入流时出现错误",e2);
                }
            }

        }

    }


    @Override
    public void downloadTmpZip(HttpServletResponse response, String fileName) throws   ServiceException {
        InputStream fis =null;
        try {
            // 1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            OutputStream toClient =null;
            // 2.设置文件头：最后一个参数是设置下载文件名
            String fileUrlPath=downloadPath+File.separator+fileName;
            log.info("fileUrlPath文件下载目录:"+fileUrlPath);
            File file=new File(fileUrlPath);
            boolean exists = file.exists();
            log.info("fileUrlPath文件下载目录:"+exists+"===>"+fileName);
            if (file.exists()&&StringUtil.isNotBlank(fileName)) {
                fis = new BufferedInputStream(new FileInputStream(file));
                response.reset();
                response.setContentType(CONTENT_TYPE_DEF);
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                response.addHeader("Content-Length", "" + file.length());
                response.addHeader("file-name*", URLEncoder.encode(fileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                toClient.flush();
                toClient.close();
            } else {
                PrintWriter out = response.getWriter();
                out.print("<script>");
                out.print("alert(\"not find the file\")");
                out.print("</script>");
            }
        } catch (Exception e) {
            MFLogger.error("downloadAllFilesZip-发票下载失败，processInstanceId=[{}],错误信息=[{}]","",e);
        }finally {
            if(fis != null) {
                try {
                    fis.close();
                } catch (Exception e2) {
                    MFLogger.error("关闭输入流时出现错误",e2);
                }
            }

        }

    }

    @Override
    public Map<String,Object> downloadFilesZip(JSONObject ajaxData)throws   ServiceException {
        Map<String, Object> map =new HashMap<>();
         ArrayList<String> fileIds = (ArrayList<String>) ajaxData.get("fileIds");
        String downloadName = ajaxData.getString("fileName");
        String resultFileName = ajaxData.getString("resultFileName");
        if(CollectionUtils.isEmpty(fileIds)||StringUtils.isBlank(downloadName)){
            map.put("msg","必传参数为空");
            return map;
        }

        try {
            //获取文件信息（此处为业务代码，可根据自己的需要替换）
            QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("file_id",fileIds);
            List<DocFileInfEntity> fileInfoList=docFileInfMapper.selectList(queryWrapper);
            // 将文件流写入zip中
            downloadFilesZip(fileInfoList,downloadPath,downloadName,resultFileName);
            map.put("msg","success");

        } catch (Exception e) {
            e.printStackTrace();
            map.put("msg",e.getMessage());
            MFLogger.error("downloadFilesZip-发票下载失败，processInstanceId=[{}],错误信息=[{}]","",e);
        }

        return map;

    }

    @Override
    public void downloadTemplateAllFileZip(HttpServletResponse response, JSONObject ajaxData) {
        OutputStream toClient = null;
        InputStream fis = null;
        InputStream ossInputStream = null;
        ZipTool zipTool = new ZipTool();
        try{
            //获取文件信息（此处为业务代码，可根据自己的需要替换）
            log.info("下载请求参数：{}",ajaxData);
            List<DocBizTemplateEntity> list = new ArrayList<>();
            String flowNo = ajaxData.getString("flowNo");
            String nodeNo = ajaxData.getString("nodeNo");
            DocBizTemplateEntity docBizTemplateEntity = new DocBizTemplateEntity();
            JSONArray multiMould = ajaxData.getJSONArray("multiMould");
            JSONObject parmList = ajaxData.getJSONObject("parmList");
            for (int i = 0; i < multiMould.size(); i++) {
                JSONObject jsonObject=multiMould.getJSONObject(i);
                String bizNo = jsonObject.getString("bizNo");
                docBizTemplateEntity.setBizNo(bizNo);
                docBizTemplateEntity.setFlowNo(flowNo);
                docBizTemplateEntity.setNodeNo(nodeNo);
                List<DocBizTemplateEntity> docBizTemplateList = docBizTemplateService.findList(docBizTemplateEntity);
                if(docBizTemplateList!=null){
                    list.addAll(docBizTemplateList);
                    log.info("查询所有下载数据{}",JSONObject.parseArray(JSONObject.toJSONString(docBizTemplateList)));
                }
            }
            if (list == null){
                return;
            }
            log.info("查询所有需要下载的未初始化文件数据：{}",JSONObject.parseArray(JSONObject.toJSONString(list)));
            for (DocBizTemplateEntity bizTemplateEntity : list) {
                if (StringUtils.isEmpty(bizTemplateEntity.getSaveFileName())||StringUtils.isEmpty(bizTemplateEntity.getSavePath())){
                    //初始化模板数据吧模板路径存到库中
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("templateId",bizTemplateEntity.getTemplateId());
                    jsonObject.put("btId",bizTemplateEntity.getBtId());
                    jsonObject.put("wid",bizTemplateEntity.getBizNo());
                    jsonObject.put("parmList",parmList);
                    jsonObject.put("loadFlag","1");
                    docTemplateModelService.previewMould(jsonObject);
                }
            }
            DocBizTemplateEntity docBizTemplateEntity1 = new DocBizTemplateEntity();
            docBizTemplateEntity1.setBizNo(list.get(0).getBizNo());
            docBizTemplateEntity1.setFlowNo(flowNo);
            docBizTemplateEntity1.setNodeNo(nodeNo);
            List<DocBizTemplateEntity> docBizTemplateList = docBizTemplateService.findList(docBizTemplateEntity1);
            log.info("查询所有需要下载的文件数据：{}",JSONObject.parseArray(JSONObject.toJSONString(docBizTemplateList)));
            for (DocBizTemplateEntity bizTemplateEntity : docBizTemplateList) {
                if (StringUtils.isEmpty(bizTemplateEntity.getSaveFileName())||StringUtils.isEmpty(bizTemplateEntity.getSavePath())){
                    return;
                }else {
                    //获取文件路径
                    String filePath=bizTemplateEntity.getSavePath();
                    String saveFileName = bizTemplateEntity.getSaveFileName();
                    String suffix=new FileUtils().getFileSuffixName(saveFileName);
                    String  fileRename=bizTemplateEntity.getTemplateName()+"."+suffix;
                    String  realPath= filePath+saveFileName;
                    if("1".equals(bizTemplateEntity.getOssRemoveStatus())){
                        ossInputStream = ossConfig.readOss(realPath);
                        zipTool.addOssFile(fileRename,ossInputStream);
                    }else{
                        File tmpFile=new File(realPath);{
                            if(tmpFile.exists()){
                                zipTool.addFileReName(realPath,fileRename);
                            }
                        }
                    }
                }
            }
            fis = new BufferedInputStream(new ByteArrayInputStream(zipTool.getOutputStream()));
            response.reset();
            response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
//        response.addHeader("Content-Length", "" + file.length());

            response.addHeader("file-name*", URLEncoder.encode("融资租赁合同.zip", "UTF-8"));
            toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType(CONTENT_TYPE_DEF1);
            response.setContentType(CONTENT_TYPE_DEF1);
            byte[] buffer = new byte[1024 * 1024 * 4];
            int i = -1;
            while ((i = fis.read(buffer)) != -1) {
                toClient.write(buffer, 0, i);
            }
            toClient.flush();

        }catch (Exception e){
            throw new ServiceException("打包下载模板","打包下载档案信息",e);
        }finally {
            try{
                fis.close();
                ossInputStream.close();
                toClient.close();
            }catch (Exception e){
                throw new ServiceException("打包下载模板","关闭文件流失败！",e);
            }
            zipTool.close();
        }
    }

    /**
     * 递归封装指定目录下所有的要件
     * @param pBean
     * @param finalList
     * @throws Exception
     */
    private void getAllFiles(DocFileBizFiletypeConfigEntity pBean,List<DocFileInfEntity>finalList)throws  Exception{


        if(StringUtils.isNotEmpty(pBean.getTypeNo())){

            //获取该目录下所有的要件
            DocFileInfEntity fileQbean=new DocFileInfEntity();
            fileQbean.setPrdUniqueVal(pBean.getPrdUniqueVal());
            fileQbean.setFlowNo(pBean.getFlowNo());
            fileQbean.setNodeNo(pBean.getNodeNo());
            fileQbean.setBizNo(pBean.getBizNo());
            fileQbean.setTypeNo(pBean.getTypeNo());

            if(StringUtils.isNotEmpty(fileQbean.getTypeNo())){

                List<DocFileInfEntity> fileList=docFileInfService.findFileInfList(fileQbean);
                if(fileList!=null){
                    if(fileList.size()>0){
                        finalList.addAll(fileList);
                    }
                }
            }
        }
        //获取该目录下所有子层目录
        DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity=new DocFileBizFiletypeConfigEntity();
        docFileBizFiletypeConfigEntity.setBizNo(pBean.getBizNo());
        docFileBizFiletypeConfigEntity.setPrdUniqueVal(pBean.getPrdUniqueVal());
        docFileBizFiletypeConfigEntity.setFlowNo(pBean.getFlowNo());
        docFileBizFiletypeConfigEntity.setNodeNo(pBean.getNodeNo());
        docFileBizFiletypeConfigEntity.setParentTypeNo(pBean.getTypeNo());
        List<DocFileBizFiletypeConfigEntity> subFolderList=findList(docFileBizFiletypeConfigEntity);
        if(subFolderList!=null){
            for(DocFileBizFiletypeConfigEntity tmpBean:subFolderList){
                getAllFiles(tmpBean,finalList);
            }
        }


    }




    private void downloadTolocal(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity,String newFileName,HttpServletResponse response) throws Exception {

        //获取文件信息（此处为业务代码，可根据自己的需要替换）
        List<DocFileInfEntity> fileInfoList=new ArrayList<DocFileInfEntity>();

        OutputStream toClient = null;
        InputStream fis = null;
        //递归获取该目录下所有的文件
        getAllFiles(docFileBizFiletypeConfigEntity,fileInfoList);
        ZipTool zt = new ZipTool();
        if("obs".equals(uploadOutterFunction)){
            for (DocFileInfEntity fileBean : fileInfoList) {
                String obsId = fileBean.getObsId();
                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                String suffix=new FileUtils().getFileSuffixName(obsId);
                String  fileRename=fileBean.getFileName()+"."+suffix;
                zt.addObsFile(filePath,fileRename);
            }
        }else{
            for (DocFileInfEntity fileBean : fileInfoList) {

                String filePath=fileBean.getFilePath();
                String suffix=new FileUtils().getFileSuffixName(filePath);
                String  fileRename=fileBean.getFileName()+"."+suffix;
                String  realPath=docFileUploadPath+filePath;
                zt.addFile(realPath);
            }
        }

        fis = new BufferedInputStream(new ByteArrayInputStream(zt.getOutputStream()));
        response.reset();
        response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
//        response.addHeader("Content-Length", "" + file.length());

        response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
        toClient = new BufferedOutputStream(response.getOutputStream());
        response.setContentType(CONTENT_TYPE_DEF1);
        response.setContentType(CONTENT_TYPE_DEF1);
        byte[] buffer = new byte[1024 * 1024 * 4];
        int i = -1;
        while ((i = fis.read(buffer)) != -1) {
            toClient.write(buffer, 0, i);
        }
        toClient.flush();
    }


    /**
     * 封装上所有需要下载的要件
     * @param fileList 要件集合
     * @param allFlag  是否全部下载 1-是， 0-否
     * @param fileInfoList  封装的需要下载的列表
     */
    private void packageAllsltFile(FileShowVo fileList,String allFlag,List<DocFileInfEntity> fileInfoList){

        if(fileList!=null){
          FileShowVo fileShowVo=fileList;

                for(DocFileBizFiletypeConfigEntity typeObj:fileShowVo.getFileList()){
                    if("1".equals(allFlag)){
                        fileInfoList.addAll(typeObj.getFileList());
                    }else{

                        for(DocFileInfEntity fileObj:typeObj.getFileList()){
                            if(fileObj.getDownLoadFlag()){
                                fileInfoList.add(fileObj);
                            }
                        }
                    }
                }

        }
    }

    /**
     * 打包下载所有选中要件
     * @param fileList  要件列表
     * @param newFileName 打包的名称
     * @param allFlag 是否全部下载 1-是， 0-否
     * @param response
     * @throws Exception
     */
    private void downloadSltFileZip(FileShowVo fileList,String newFileName,String allFlag,HttpServletResponse response) throws Exception {

        //获取文件信息（此处为业务代码，可根据自己的需要替换）
        List<DocFileInfEntity> fileInfoList=new ArrayList<DocFileInfEntity>();

        OutputStream toClient = null;
        InputStream fis = null;
        //递归获取该目录下所有的文件
        packageAllsltFile(fileList,allFlag,fileInfoList);
        ZipTool zt = new ZipTool();
        if("obs".equals(uploadOutterFunction)){
            for (DocFileInfEntity fileBean : fileInfoList) {
                String obsId = fileBean.getObsId();
                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                String suffix=new FileUtils().getFileSuffixName(obsId);
                String  fileRename=fileBean.getFileName()+"."+suffix;
                zt.addObsFile(filePath,fileRename);
            }
        }else if("oss".equals(uploadOutterFunction)){
            for (DocFileInfEntity fileBean : fileInfoList) {
                String filePath=fileBean.getFilePath();
                String suffix=new FileUtils().getFileSuffixName(filePath);
                String  fileRename=fileBean.getFileName()+"."+suffix;
                InputStream inputStream = ossConfig.readOss(filePath);
                zt.addOssFile(fileRename,inputStream);
            }
        }else{
            for (DocFileInfEntity fileBean : fileInfoList) {

                String filePath=fileBean.getFilePath();
                String suffix=new FileUtils().getFileSuffixName(filePath);
                String  fileRename=fileBean.getFileName()+"."+suffix;
                String  realPath=docFileUploadPath+filePath;
                File tmpFile=new File(realPath);{
                    if(tmpFile.exists()){
                        zt.addFileReName(realPath,fileRename);

                    }
                }
            }
        }

        fis = new BufferedInputStream(new ByteArrayInputStream(zt.getOutputStream()));
        response.reset();
        response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
//        response.addHeader("Content-Length", "" + file.length());

        response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
        toClient = new BufferedOutputStream(response.getOutputStream());
        response.setContentType(CONTENT_TYPE_DEF1);
        response.setContentType(CONTENT_TYPE_DEF1);
        byte[] buffer = new byte[1024 * 1024 * 4];
        int i = -1;
        while ((i = fis.read(buffer)) != -1) {
            toClient.write(buffer, 0, i);
        }
        toClient.flush();
    }


    /**
     * 打包下载所有选中要件
     * @param fileList  要件列表
     * @param newFileName 打包的名称
     * @param response
     * @throws Exception
     */
    private void downloadAllFilesZip(List<DocFileInfEntity> fileList,String newFileName,HttpServletResponse response) throws Exception {

        OutputStream toClient = null;
        InputStream fis = null;
        //递归获取该目录下所有的文件
        ZipTool zt = new ZipTool();
        if("obs".equals(uploadOutterFunction)){
            for (DocFileInfEntity fileBean : fileList) {
                String obsId = fileBean.getObsId();
                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                String suffix=new FileUtils().getFileSuffixName(obsId);
                String  fileRename=fileBean.getFileName()+"."+suffix;
                zt.addObsFile(filePath,fileRename);
            }
        }else if("oss".equals(uploadOutterFunction)){
            for (DocFileInfEntity fileBean : fileList) {
                String filePath=fileBean.getFilePath();
                String suffix=new FileUtils().getFileSuffixName(filePath);
                String  fileRename=fileBean.getFileName()+"."+suffix;
                InputStream inputStream = ossConfig.readOss(filePath);
                zt.addOssFile(fileRename,inputStream);
            }
        }else{
            for (DocFileInfEntity fileBean : fileList) {
                String filePath=fileBean.getFilePath();
                String suffix=new FileUtils().getFileSuffixName(filePath);
                String  fileRename=fileBean.getFileName()+"."+suffix;
                String  realPath=docFileUploadPath+filePath;
                File tmpFile=new File(realPath);
                if(tmpFile.exists()){
                    zt.addFileReName(realPath,fileRename);
                }
            }
        }

        fis = new BufferedInputStream(new ByteArrayInputStream(zt.getOutputStream()));
        response.reset();
        response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
        response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
        toClient = new BufferedOutputStream(response.getOutputStream());
        response.setContentType(CONTENT_TYPE_DEF1);
        response.setContentType(CONTENT_TYPE_DEF1);
        byte[] buffer = new byte[1024 * 1024 * 4];
        int i = -1;
        while ((i = fis.read(buffer)) != -1) {
            toClient.write(buffer, 0, i);
        }
        toClient.flush();
    }


    /**
     * 打包下载的文件存至本地
     * @param fileList
     * @param newFileName
     * @throws Exception
     */
    private void downloadFilesZip(List<DocFileInfEntity> fileList,String downPath,String newFileName,String resultFileName) throws Exception {

         InputStream fis = null;
        FileOutputStream fos = null;
        OutputStream zos = null;
        try {

            //递归获取该目录下所有的文件
            ZipTool zt = new ZipTool();
            if ("obs".equals(uploadOutterFunction)) {
                for (DocFileInfEntity fileBean : fileList) {
                    String obsId = fileBean.getObsId();
                    String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                    String suffix = new FileUtils().getFileSuffixName(obsId);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    zt.addObsFile(filePath, fileRename);
                }
            } else if ("oss".equals(uploadOutterFunction)) {
                for (DocFileInfEntity fileBean : fileList) {
                    String filePath = fileBean.getFilePath();
                    String suffix = new FileUtils().getFileSuffixName(filePath);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    InputStream inputStream = ossConfig.readOss(filePath);
                    zt.addOssFile(fileRename, inputStream);
                }
            } else {
                for (DocFileInfEntity fileBean : fileList) {
                    String filePath = fileBean.getFilePath();
                    String suffix = new FileUtils().getFileSuffixName(filePath);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    String realPath = docFileUploadPath + filePath;
                    File tmpFile = new File(realPath);
                    if (tmpFile.exists()) {
                        zt.addFileReName(realPath, fileRename);
                    }
                }
            }
            if(StringUtils.isNotBlank(resultFileName)){
                File  resultFile =new File(downPath+File.separator+resultFileName);
                if(resultFile.exists()){
                    zt.addFile(downPath+File.separator+resultFileName,resultFileName);
                }
            }

            fis = new BufferedInputStream(new ByteArrayInputStream(zt.getOutputStream()));

            fos = new FileOutputStream(new File(downPath+File.separator+newFileName));
            zos = new BufferedOutputStream(fos);
            byte[] buffer = new byte[1024 * 1024 * 4];
            int i = -1;
            while ((i = fis.read(buffer)) != -1) {
                zos.write(buffer, 0, i);
            }
            zos.flush();
        }catch (Exception e){
            log.error("创建"+newFileName+"ZIP文件失败",e);
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,newFileName,e);
        }finally {
            try {
                if(fis!=null){
                    fis.close();
                }
            }catch(IOException e){
                log.error("创建ZIP文件时释放资源失败",e);
            }
            try {
                if (zos != null) {
                    zos.close();
                }
            } catch (IOException e) {
                log.error("创建ZIP文件时释放资源失败2",e);
            }
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                log.error("创建ZIP文件时释放资源失败3",e);
            }
        }
    }

    /**
     * 打包下载档案信息
     * @param fileObjs 文档属性
     * @param zipName 压缩包名称
     * @param response
     * @throws Exception
     */
    @Override
    public  void downloadArchiveFileZip(JSONArray fileObjs,String zipName,HttpServletResponse response) throws ServiceException {

        try{
            //获取文件信息（此处为业务代码，可根据自己的需要替换）
            List<DocFileInfEntity> fileInfoList=new ArrayList<DocFileInfEntity>();

            OutputStream toClient = null;
            InputStream fis = null;
            ZipTool zt = new ZipTool();
            for(int i=0;i<fileObjs.size();i++){
                String docClass=fileObjs.getJSONObject(i).getString("docClass");//文档类型 1-附件 2-文档
                String fileId=fileObjs.getJSONObject(i).getString("fileId");//文件Id
                if("1".equalsIgnoreCase(docClass)){
                    //如果是附件
                    DocFileInfEntity docFileInfEntity=docFileInfService.findById(fileId);
                    if(docFileInfEntity==null){
                        continue;
                    }
                    String filePath=docFileInfEntity.getFilePath();
                    String suffix=new FileUtils().getFileSuffixName(filePath);
                    String  fileRename=docFileInfEntity.getFileName()+"."+suffix;
                    String  realPath=docFileUploadPath+filePath;
                    File tmpFile=new File(realPath);{
                        if(tmpFile.exists()){
                            zt.addFileReName(realPath,fileRename);

                        }
                    }
                }else{
                    if("2".equalsIgnoreCase(docClass)){
                        // 如果是文档
                        DocBizTemplateEntity docBizTemplateEntity=docBizTemplateService.findById(fileId);
                        if(docBizTemplateEntity==null){
                            continue;
                        }
                        DocTemplateModelEntity tmpTemplate=docTemplateModelService.findById(docBizTemplateEntity.getTemplateId());
                        String fileName=tmpTemplate.getTemplateFileName();
                        String  suffixName=new FileUtils().getFileSuffixName(fileName);
                        String  saveFileName=docBizTemplateEntity.getTemplateId()+"_"+docBizTemplateEntity.getBizNo()+"_"+tmpTemplate.getTemplateId()+"_"+tmpTemplate.getTemplateNo()+"."+suffixName;


                        if(StringUtils.isNotEmpty(docBizTemplateEntity.getSubBizNo())){
                            String  subBizNo=docBizTemplateEntity.getSubBizNo();
                            saveFileName=docBizTemplateEntity.getTemplateId()+"_"+docBizTemplateEntity.getTemplateId()+"_"+subBizNo+"_"+tmpTemplate.getTemplateNo()+"."+suffixName;
                        }

                        String filePath=mouldPath+"saveMould/"+saveFileName;
//                    String filePath=docFileInfEntity.getFilePath();
                        String suffix=new FileUtils().getFileSuffixName(filePath);
                        String  fileRename=docBizTemplateEntity.getTemplateName()+"."+suffix;
                        String  realPath=docFileUploadPath+filePath;
                        File tmpFile=new File(realPath);{
                            if(tmpFile.exists()){
                                zt.addFileReName(realPath,fileRename);

                            }
                        }

                    }
                }


            }





            fis = new BufferedInputStream(new ByteArrayInputStream(zt.getOutputStream()));
            response.reset();
            response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
//        response.addHeader("Content-Length", "" + file.length());

            response.addHeader("file-name*", URLEncoder.encode(zipName+".zip", "UTF-8"));
            toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType(CONTENT_TYPE_DEF1);
            response.setContentType(CONTENT_TYPE_DEF1);
            byte[] buffer = new byte[1024 * 1024 * 4];
            int i = -1;
            while ((i = fis.read(buffer)) != -1) {
                toClient.write(buffer, 0, i);
            }
            toClient.flush();
        }catch (Exception e){
            throw new ServiceException("打包下载档案信息","打包下载档案信息",e);
        }

    }





   /**
     * 封装所有的目录和要件
     * @param folderList 子层目录列表
     * @param fileList
     * @param result
     * @throws ServiceException
     */
    private  void packageFolderAndFiles(List<DocFileBizFiletypeConfigEntity> folderList,List<DocFileInfEntity> fileList,List<FileShowObj>result)throws ServiceException{
        //封装子层目录
        if(folderList!=null){
            for(DocFileBizFiletypeConfigEntity folder : folderList){
                FileShowObj newFile=new FileShowObj();
                //1-目录 2-word 3-excel 4-pdf 5-图片 6-压缩包 7-txt 8-视频 9-音频 10-其他。
                newFile.setFileType("1");
                newFile.setScId(folder.getScId());
                newFile.setTypeNo(folder.getTypeNo());
                newFile.setTypeName(folder.getTypeName());
                if("1".equals(folder.getIfUpload())){
                    newFile.setIfMustUpload("1");
                }else{
                    newFile.setIfMustUpload("0");
                }
                newFile.setClassName("folder");
                newFile.setFileShortName(getShortName(newFile.getTypeName()));
                if(folder.getCreateTime()!=null){

                    String  createTime=new FileUtils().getPattenDateStr(folder.getCreateTime(),"yyyy-MM-dd HH:mm:ss");
                    newFile.setCreateTime(createTime);
                }

//                DateUtil.
//                newFile.setCreateTime(folder.getCreateTime());
                result.add(newFile);


            }
        }
        //封装目录下所有要件
        if(fileList!=null){
            for(DocFileInfEntity file:fileList){
                FileShowObj newFile=new FileShowObj();
                //1-目录 2-word 3-excel 4-pdf 5-图片 6-压缩包 7-txt 8-视频 9-音频 10-其他。
                String fileType="";
                if("obs".equals(uploadOutterFunction)){
                    String filePath = file.getFilePath();
                    String obsId = file.getObsId();
                    String fileName = StringUtils.isEmpty(filePath) ? obsId : filePath;
                    fileType=new FileUtils().getFileType(fileName);
                    if("5".equals(fileType) || "8".equals(fileType)
                            || "2".equals(fileType) || "3".equals(fileType)|| "4".equals(fileType)){
                        String imgUrl = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                        newFile.setFileShowUrl(imgUrl);
                    }
                }else{
                    fileType=new FileUtils().getFileType(file.getFilePath());
                }

                newFile.setFileType(fileType);
                newFile.setScId(file.getFileId());
                newFile.setTypeName(file.getFileName());
                newFile.setFileShortName(getShortName(file.getFileName()));
                String clsNme="";
                switch(fileType){
                    case "1" :
                        //语句
                        clsNme="folder"; //可选
                        break;
                    case "2" :
                        //语句
                        clsNme="word"; //可选
                        break;
                    case "3" :
                        //语句
                        clsNme="exl";//可选
                        break;
                    case "4" :
                        //语句
                        clsNme="pdf"; //可选
                        break;
                    case "5" :
                        //语句
                        clsNme="img";//可选
                        break;
                    case "6" :
                        //语句
                        clsNme="rar"; //可选
                        break;
                    case "7" :
                        //语句
                        clsNme="txt"; //可选
                        break;
                    case "8" :
                        //语句
                        clsNme="video"; //可选
                        break;
                    case "9" :
                        //语句
                        clsNme="audio"; //可选
                        break;

                    default : //可选
                        clsNme="other"; //可选
                        break;
                }
                newFile.setClassName(clsNme);
                newFile.setImgPath("@/doc/assets/imgs/file/"+clsNme+".png");
                String  createTime=new FileUtils().getPattenDateStr(file.getCreateTime(),"yyyy-MM-dd HH:mm:ss");
                newFile.setCreateTime(createTime);
                newFile.setFileSize(String.valueOf(file.getFileSize()));
                newFile.setCreateUserName(file.getCreateUserName());

//                String url=docFileShowUrl+file.getFileId();
                if("5".equals(fileType)){
                    //封装图片的缩略图路劲和图片路径
//                    String thumbUrl=docFileThumbUrl+file.getFileId();
//                    newFile.setImgThumbUrl(url);
//                    String  imgUrlAry[]={url};
//                    newFile.setImgUrlAry(imgUrlAry);
                }
//                newFile.setFileShowUrl(url);
                result.add(newFile);

            }
        }
    }

    /**
     * 超过五个字的 加省略号
     * @param fileName
     * @return
     * @throws ServiceException
     */
    private String  getShortName(String fileName)throws ServiceException{
        String tmpStr=fileName;
        if(fileName.length()>5){
            tmpStr=fileName.substring(0,5)+"...";
        }
        return  tmpStr;
    }




    @Override
    public void insert(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException {
        try{
            docFileBizFiletypeConfigEntity.setScId(UUIDUtil.getUUID());
            docFileBizFiletypeConfigMapper.insert(docFileBizFiletypeConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docFileBizFiletypeConfigEntity.getScId(),e);
        }
    }

    @Override
    public void insertBatch(List<DocFileBizFiletypeConfigEntity> docFileBizFiletypeConfigEntitys) throws ServiceException {
        docFileBizFiletypeConfigMapper.insertBatch(docFileBizFiletypeConfigEntitys);
    }

    @Override
    public void update(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException {
        try{
            docFileBizFiletypeConfigMapper.updateById(docFileBizFiletypeConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docFileBizFiletypeConfigEntity.getScId(),e);
        }
    }

    /**
     *  前端添加一个目录
     * @param docFileBizFiletypeConfigEntity
     * @throws ServiceException
     */
    @Override
    public  void   addOneFloder(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity)throws ServiceException{

        DocFileBizFiletypeConfigEntity qBean=new DocFileBizFiletypeConfigEntity();
        qBean.setBizNo(docFileBizFiletypeConfigEntity.getBizNo());
        qBean.setPrdUniqueVal(docFileBizFiletypeConfigEntity.getPrdUniqueVal());
        qBean.setFlowNo(docFileBizFiletypeConfigEntity.getFlowNo());
        qBean.setNodeNo(docFileBizFiletypeConfigEntity.getNodeNo());
        qBean.setTypeNo(docFileBizFiletypeConfigEntity.getTypeNo());

        DocFileBizFiletypeConfigEntity newObj=findOneEntity(docFileBizFiletypeConfigEntity);{
            DocFileBizFiletypeConfigEntity addBean=new DocFileBizFiletypeConfigEntity();
            BeanUtils.copyProperties(newObj,addBean);
            addBean.setScId(UuidUtils.generateUuid());
            addBean.setParentTypeNo(newObj.getTypeNo());
            //生成新的typeNo
            String  newTypeNo=getNewSubTypeNo(addBean);
            addBean.setTypeNo(newTypeNo);
            addBean.setTypeName(docFileBizFiletypeConfigEntity.getTypeName());
            addBean.setCreateTime(new Date());
            addBean.setUpdateTime(new Date());
            insert(addBean);
        }



    }

    /**
     * 生成新的typeNo
     * @param docFileBizFiletypeConfigEntity
     * @return
     * @throws ServiceException
     */
    private String getNewSubTypeNo(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity)throws ServiceException{
        String pNo="";
        if(docFileBizFiletypeConfigEntity.getParentTypeNo()!=null){
            pNo=docFileBizFiletypeConfigEntity.getParentTypeNo();
        }
        String result=pNo+"01";
        List<DocFileBizFiletypeConfigEntity> tmpList= findList(docFileBizFiletypeConfigEntity);
        if(tmpList!=null){
            if(tmpList.size()>0){
                int num=0;
                int begInd_=pNo.length();
                for(DocFileBizFiletypeConfigEntity bean:tmpList){
                    String tmpNum=bean.getTypeNo().substring(begInd_);
                    int numVal=Integer.parseInt(tmpNum);
                    if(num<numVal){
                        num=numVal;
                    }
                }
                num=num+1;
                if(num<10){
                    result=pNo+"0"+num;
                }else{
                    result=pNo+num;

                }

            }
        }
        return result;
    }





    @Override
    public JSONObject initBizFileTypeConfig(String initParm) throws ServiceException {
        JSONObject result=new JSONObject();
        try{
            DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity=(DocFileBizFiletypeConfigEntity)JSONObject.parse(initParm);

            DocScFiletypeConfigEntity docScFiletypeConfigEntity =new DocScFiletypeConfigEntity();
            docScFiletypeConfigEntity.setPrdUniqueVal(docFileBizFiletypeConfigEntity.getPrdUniqueVal());
            docScFiletypeConfigEntity.setFlowNo(docFileBizFiletypeConfigEntity.getFlowNo());
            docScFiletypeConfigEntity.setNodeNo(docFileBizFiletypeConfigEntity.getNodeNo());
            docScFiletypeConfigEntity.setParentTypeNo("bizConfig");
            List<DocScFiletypeConfigEntity>configList=docScFiletypeConfigService.findList(docScFiletypeConfigEntity);
            if(configList!=null){
                for(DocScFiletypeConfigEntity configBean:configList){
                    DocFileBizFiletypeConfigEntity addBean=new DocFileBizFiletypeConfigEntity();
                    BeanUtils.copyProperties(configBean,addBean);
                    addBean.setScId(UuidUtils.generateUuid());
                    addBean.setBizNo(docFileBizFiletypeConfigEntity.getBizNo());
                    addBean.setCreateTime(new Date());
                    addBean.setUpdateTime(new Date());
                    insert(addBean);

                }
            }
            result.put("code","0000");
            result.put("msg","模板成功初始化");
        }catch (Exception e){
            result.put("code","1111");
            result.put("msg","模板初始化失败");
            result.put("error",e);
            throw e;
        }
        return result;
    }




    /**
     *
     * @param initParm
     * @return
     * @throws ServiceException
     */
    @Override
    public String  ifFileConfig(String initParm) throws ServiceException {
        String result="0";
        try{
            DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity=(DocFileBizFiletypeConfigEntity)JSONObject.parse(initParm);

            DocScFiletypeConfigEntity docScFiletypeConfigEntity =new DocScFiletypeConfigEntity();
            docScFiletypeConfigEntity.setPrdUniqueVal(docFileBizFiletypeConfigEntity.getPrdUniqueVal());
            docScFiletypeConfigEntity.setFlowNo(docFileBizFiletypeConfigEntity.getFlowNo());
            docScFiletypeConfigEntity.setNodeNo(docFileBizFiletypeConfigEntity.getNodeNo());

            List<DocScFiletypeConfigEntity>configList=docScFiletypeConfigService.findList(docScFiletypeConfigEntity);
            if(configList!=null){
                if(configList.size()>0){
                    result="1";
                }
            }

        }catch (Exception e){

            throw e;
        }
        return result;
    }

    /**
     * 检查节点下是否已上传（是否必填，是否已填， 是否必读，是否已读，是否可编辑。）
     * @param parmMap
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *      * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject checkBizFileStatus( Map<String ,String> parmMap)throws  ServiceException{


        JSONObject result=new JSONObject();
        result.put("code","0000");
        result.put("msg","success");
        QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(parmMap.get("prdUniqueVal")),"prd_unique_val",parmMap.get("prdUniqueVal"));
        queryWrapper.eq(StringUtils.isNotBlank(parmMap.get("flowNo")),"flow_no",parmMap.get("flowNo"));
        queryWrapper.eq(StringUtils.isNotBlank(parmMap.get("nodeNo")),"node_no",parmMap.get("nodeNo"));
        queryWrapper.eq(StringUtils.isNotBlank(parmMap.get("bizNo")),"biz_no",parmMap.get("bizNo"));
        List<DocFileBizFiletypeConfigEntity> tmplateList=docFileBizFiletypeConfigMapper.selectList(queryWrapper);
        String readTemplateName="";
        String writeTemplateName="";

        if(tmplateList!=null){
            for(DocFileBizFiletypeConfigEntity obj:tmplateList){
//                if("1".equals(obj.getIfMustRead()) && "0".equals(obj.get())){
//                    //处理必读未读
//                    readTemplateName+=","+obj.getTemplateName();
//                }

                if("1".equals(obj.getIfUpload())){
                    Map<String,String> queryMap = new HashMap<>();
                    queryMap.putAll(parmMap);
                    //处理必须上传
                    queryMap.put("typeNo",obj.getTypeNo());
                    if(StringUtils.isNotBlank(obj.getMainPrdUniqueVal())){
                        queryMap.put("prdUniqueVal",obj.getMainPrdUniqueVal());
                    }
                    JSONObject resultJson=docFileInfService.ifHasFiles(queryMap);
                    int fileCnt=resultJson.getIntValue("fileCnt");
                    if(fileCnt==0){

                        writeTemplateName+=","+obj.getTypeName();
                    }
                }
            }
        }
        String  msgInfo="";
//        if(StringUtils.isNotEmpty(readTemplateName)){
//            msgInfo+=readTemplateName.substring(1)+"未读";
//
//        }
        if(StringUtils.isNotEmpty(writeTemplateName)){
            msgInfo+=writeTemplateName.substring(1)+"未上传要件";

        }
        if(msgInfo.length()>0){
            result.put("code","1111");
            result.put("msg",msgInfo);
        }

        return result;
    }


    /**
     * 检查节点下是否已上传（是否必填，是否已填， 是否必读，是否已读，是否可编辑。） 入网特殊处理
     * @param parmMap
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject checkBizFileStatusAccess( Map<String ,String> parmMap)throws  ServiceException{


        JSONObject result=new JSONObject();
        result.put("code","0000");
        result.put("msg","success");
        QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(parmMap.get("prdUniqueVal")),"prd_unique_val",parmMap.get("prdUniqueVal"));
        queryWrapper.eq(StringUtils.isNotBlank(parmMap.get("flowNo")),"flow_no",parmMap.get("flowNo"));
        queryWrapper.eq(StringUtils.isNotBlank(parmMap.get("nodeNo")),"node_no",parmMap.get("nodeNo"));
        queryWrapper.eq(StringUtils.isNotBlank(parmMap.get("bizNo")),"biz_no",parmMap.get("bizNo"));
        List<DocFileBizFiletypeConfigEntity> tmplateList=docFileBizFiletypeConfigMapper.selectList(queryWrapper);
        String readTemplateName="";
        String writeTemplateName="";
        //获取机构类型
        String trenchOrgType = parmMap.get("trenchOrgType");

        if(tmplateList!=null){
            for(DocFileBizFiletypeConfigEntity obj:tmplateList){
//                if("1".equals(obj.getIfMustRead()) && "0".equals(obj.get())){
//                    //处理必读未读
//                    readTemplateName+=","+obj.getTemplateName();
//                }
                //进行判断，如果类型为 00 -生产商 02- SP的时候，过滤调 经销商协议书，不校验必填
                if("jingxiao_buss".equals(obj.getTypeNo())){
                    if("00".equals(trenchOrgType) || "02".equals(trenchOrgType)){
                        continue;
                    }else {
                        if("1".equals(obj.getIfUpload())){
                            Map<String,String> queryMap = new HashMap<>();
                            queryMap.putAll(parmMap);
                            //处理必须上传
                            queryMap.put("typeNo",obj.getTypeNo());
                            if(StringUtils.isNotBlank(obj.getMainPrdUniqueVal())){
                                queryMap.put("prdUniqueVal",obj.getMainPrdUniqueVal());
                            }
                            JSONObject resultJson=docFileInfService.ifHasFiles(queryMap);
                            int fileCnt=resultJson.getIntValue("fileCnt");
                            if(fileCnt==0){

                                writeTemplateName+=","+obj.getTypeName();
                            }
                        }
                    }
                }else {
                    if("1".equals(obj.getIfUpload())){
                        Map<String,String> queryMap = new HashMap<>();
                        queryMap.putAll(parmMap);
                        //处理必须上传
                        queryMap.put("typeNo",obj.getTypeNo());
                        if(StringUtils.isNotBlank(obj.getMainPrdUniqueVal())){
                            queryMap.put("prdUniqueVal",obj.getMainPrdUniqueVal());
                        }
                        JSONObject resultJson=docFileInfService.ifHasFiles(queryMap);
                        int fileCnt=resultJson.getIntValue("fileCnt");
                        if(fileCnt==0){

                            writeTemplateName+=","+obj.getTypeName();
                        }
                    }
                }
            }
        }
        String  msgInfo="";
//        if(StringUtils.isNotEmpty(readTemplateName)){
//            msgInfo+=readTemplateName.substring(1)+"未读";
//
//        }
        if(StringUtils.isNotEmpty(writeTemplateName)){
            msgInfo+=writeTemplateName.substring(1)+"未上传要件";

        }
        if(msgInfo.length()>0){
            result.put("code","1111");
            result.put("msg",msgInfo);
        }

        return result;
    }

    @Override
    public DocFileBizFiletypeConfigEntity findById(String scId) throws ServiceException {
        try{
            return docFileBizFiletypeConfigMapper.selectById(scId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,scId,e);
        }
    }

    /**
     * 要件上传
     * @param folderEntity 目录
     * @param file 上传的文件
     * @param uploadParm 上传要件的关联参数
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject upLoadFile(DocFileBizFiletypeConfigEntity folderEntity, MultipartFile file, String appName, String uploadParm)throws  ServiceException {
        JSONObject result = new JSONObject();
        InputStream inputStream = null;
        try {
            //首先，检验文件
            JSONObject chkRes = validateFile(file);
            String suffix = "";
            if ("0000".equals(chkRes.getString("code"))) {
                //校验成功开始保存文件

                String originalName = file.getOriginalFilename();
                String thumbPath = "";
                String dbPath = "";
                String newFileName = "";
                double fileSize = 0;
                DocFileInfEntity addBean = new DocFileInfEntity();
                BeanUtils.copyProperties(folderEntity, addBean);

                //主要产品唯一标识不为空时,新增为主要产品唯一标识
                if (StringUtils.isNotBlank(folderEntity.getMainPrdUniqueVal())){
                    addBean.setPrdUniqueVal(folderEntity.getMainPrdUniqueVal());
                }
                System.out.println("文件大小："+FileTypeUtil.bytes2kb(file.getSize()));
                if ("".equals(uploadOutterFunction)) {
                    String uploadFileName = file.getOriginalFilename();
                    suffix = new FileUtils().getFileSuffixName(uploadFileName);//文件后缀名
                    int lastIndex_=uploadFileName.lastIndexOf(".");
                    originalName = uploadFileName.substring(0,lastIndex_);//文件的原始名称
                    newFileName = UUIDUtil.getUUID() + "." + suffix;//保存的目标文件的物理名称。
                    dbPath = getDbSavePath(folderEntity, appName);//获取数据库路径
                    String savePath = docFileUploadPath + dbPath + newFileName;//最终的物理路径
                    //生成为物理文件
                    // 检测是否存在目录
                    File dest0 = new File(docFileUploadPath + dbPath);
                    File dest = new File(dest0, newFileName);
                    if (!dest0.getParentFile().exists()) {
                        dest0.getParentFile().mkdirs();
                        //检测文件是否存在
                    }
                    if (!dest.exists()) {
                        dest.mkdirs();
                    }
                    fileSize = FileTypeUtil.bytes2kb(file.getSize());
                    if(fileSize==0.0){
                        result.put("msg", "上传的文档内容不能为空");
                        result.put("code", "3333");
                        return result;
                    }
                    file.transferTo(dest);


                    String uploadContentType = file.getContentType();
                    file = null;//gc 回收内存
                    boolean imgFlag = false;
                    InputStream is = new FileInputStream(savePath);
                    String fileType = FileTypeUtil.getFileTypeByFile(is);


                    String thumbName = "";
                    if (uploadContentType != null && uploadContentType.startsWith(IMAGE)) {
                        //如果是图片生成缩略图文件
                        thumbName = "thumb_" + newFileName;
                        thumbPath = dbPath + "thumbnail" + File.separator + thumbName;
                        String saveThumbPath = docFileUploadPath + thumbPath;//最终的物理缩略图路径
                        File thumbFile = new File(docFileUploadPath + dbPath + "thumbnail");
                        if (!thumbFile.exists()) {
                            thumbFile.mkdirs();
                            //检测文件是否存在
                        }
                        new ImgCompress().thumbnailImage(savePath, 52, 52, saveThumbPath, true);

                    }
                    if (is != null) {
                        is.close();
                    }
                }else
                //获取base64
                if ("obs".equals(uploadOutterFunction)) {
                    BASE64Encoder base64Encoder = new BASE64Encoder();
                    String contentType = file.getContentType();
                    byte[] fileBytes = file.getBytes();

                    String base64Str = base64Encoder.encode(fileBytes);
                    base64Str = base64Str.replaceAll("[\\s*\t\n\r]", "");
                    addBean.setBase64Str(base64Str);
                    addBean.setContentType(contentType);
                }else if ("oss".equals(uploadOutterFunction)) {
                    String uploadFileName = file.getOriginalFilename();
                    suffix = new FileUtils().getFileSuffixName(uploadFileName);//文件后缀名
                    if(FileTypeUtil.isImage(suffix)&&FileTypeUtil.bytes2kb(file.getSize())>2048){
                        String fieldName = file.getName();
                        String fileName = file.getOriginalFilename();
                        String contentType = file.getContentType();
                        file = getMulFileByFile(compressImgToSize(file,2048,0.9), fieldName, contentType, fileName);
                        System.out.println("压缩后文件大小："+FileTypeUtil.bytes2kb(file.getSize()));
                    }
                    String contentType = file.getContentType();
                    inputStream = file.getInputStream();
                    addBean.setContentType(contentType);
                }

                addBean.setFileId(UUIDUtil.getUUID());
                addBean.setOrigin("1");
                addBean.setFileName(originalName);
                if(file!=null){
                    fileSize = FileTypeUtil.bytes2kb(file.getSize());
                }

                addBean.setFileSize(new BigDecimal(fileSize));
                addBean.setFilePath(dbPath + newFileName);
                addBean.setThumbnailPath(thumbPath);
                if (StringUtils.isNotEmpty(uploadParm)) {
                    JSONObject uploadParmJson = JSONObject.parseObject(uploadParm);
                    if (uploadParmJson.containsKey("cusRelatedId")) {
                        addBean.setCusRelatedId(uploadParmJson.getString("cusRelatedId"));
                    }
                    if (uploadParmJson.containsKey("busRelatedId")) {
                        addBean.setBusRelatedId(uploadParmJson.getString("busRelatedId"));
                    }
                    if (uploadParmJson.containsKey("applyRelatedId")) {
                        addBean.setApplyRelatedId(uploadParmJson.getString("applyRelatedId"));
                    }
                    if (uploadParmJson.containsKey("constractRelatedId")) {
                        addBean.setConstractRelatedId(uploadParmJson.getString("constractRelatedId"));
                    }
                     if (uploadParmJson.containsKey("finRelatedId")) {
                        addBean.setFinRelatedId(uploadParmJson.getString("finRelatedId"));
                    }
                    if (uploadParmJson.containsKey("corpId")) {
                        addBean.setCorpId(uploadParmJson.getString("corpId"));
                    }
                    if (uploadParmJson.containsKey("parentTypeNo")) {
                        addBean.setParentTypeNo(uploadParmJson.getString("parentTypeNo"));
                    }
                    if (uploadParmJson.containsKey("ifEsignFile")) {
                        addBean.setIfEsignFile(uploadParmJson.getString("ifEsignFile"));
                    }else{
                        addBean.setIfEsignFile(DocConstant.YES_NO_N);
                    }
                    if (uploadParmJson.containsKey("ifGenerateBizNo")) {
                        String  ifGenerateBizNo=uploadParmJson.getString("ifGenerateBizNo");
                        if("1".equals(ifGenerateBizNo)){
                            //如果需要自动生成bizNo
                            addBean.setBizNo(cn.mftcc.bizcommon.utils.UUIDUtil.getUUID());
                            DocFileBizFiletypeConfigEntity bizFileType=new DocFileBizFiletypeConfigEntity();
                            BeanCopyUtil.copyProperties(addBean,bizFileType);
                            bizFileType.setScId(UUIDUtil.getUUID());
                            bizFileType.setBizNo(addBean.getBizNo());
                            if (StringUtils.isNotBlank(folderEntity.getMainPrdUniqueVal())){
                                bizFileType.setMainPrdUniqueVal(folderEntity.getMainPrdUniqueVal());
                            }
                            docFileBizFiletypeConfigService.insert(bizFileType);
                        }
                    }
                }
                Date date = new Date();
                addBean.setCreateTime(date);
                addBean.setUpdateTime(date);
//                addBean.setCreateUserNo(String.valueOf(requestUtil.getUserInfo("opNo")));

              DocFileTypeEntity _docFileTypeEntity =   docFileTypeService.findById(addBean.getTypeNo());
                if(_docFileTypeEntity != null){
                    addBean.setParentTypeNo(_docFileTypeEntity.getGroupNo());
                }


              addBean.setCreateUserNo(requestUtil.getUserInfo("opNo")==null?null:(String) requestUtil.getUserInfo("opNo"));
                addBean.setCreateUserName(requestUtil.getUserInfo("opName")==null?null:(String) requestUtil.getUserInfo("opName"));
                addBean.setCreateDeptNo(requestUtil.getUserInfo("brNo")==null?null:(String) requestUtil.getUserInfo("brNo"));
                addBean.setCreateDeptName(requestUtil.getUserInfo("brName")==null?null:(String) requestUtil.getUserInfo("brName"));




                JSONObject uploadOutter = upLoadOtherServer(addBean,appName,inputStream);//处理上传到外部服务器
                if ("0000".equals(uploadOutter.getString("code"))) {
//                    addBean = (DocFileInfEntity) uploadOutter.get("json");
                    addBean = (DocFileInfEntity) uploadOutter.get("fileObj");
                    docFileInfService.insert(addBean);

                    if ("pdf".equals(suffix.toLowerCase())) {
                        //如果上传的文件时pdf
                        String typeNo = folderEntity.getTypeNo();
                        DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
                        if (docFileTypeEntity != null && "1".equals(docFileTypeEntity.getPdfImageFlag())) {
                            //如果设置了pdf转图片

                            List<String> imgList = FileUtils.getPdfToImage(docFileUploadPath, dbPath, newFileName);
                            if (imgList != null) {
                                if (imgList.size() > 0) {
                                    for (String imgFileName : imgList) {
                                        addBean.setFileId(UUIDUtil.getUUID());
                                        addBean.setFilePath(dbPath + imgFileName);
                                        docFileInfService.insert(addBean);
                                    }
                                }
                            }


                        }

                        result.put("data", addBean);
                        result.put("code", "0000");
                    }


                    else {
                        result.put("data", addBean);
                        result.put("code", "0000");
                    }


                } else {
                    return chkRes;
                }
            }else{
                return chkRes;
            }


            return result;

        } catch (Exception e) {
            MFLogger.error("要件上传", e);
            throw new ServiceException("要件上传", folderEntity, e);
        } finally {
            try{
                if(inputStream != null){
                    inputStream.close();
                }
            }catch (IOException e){
                MFLogger.error("要件流关闭失败", e);
            }
        }
    }

    /**
     * 按照指定最大的kb压缩图片
     * @param multiFile 目标文件 (MultipartFile)
     * @param size 指定大小 （kb）
     * @param accuracy  压缩精度（0-1）
     * @return
     */
    @Override
    public InputStream compressImgToSize(MultipartFile multiFile, double size,double accuracy) {
        // 压缩图片
        InputStream inputStream = null;
        MultipartFile multipartFile = null;
        try {
            byte[] bigContent = multiFile.getBytes();
            inputStream = multiFile.getInputStream();
            System.out.println("压缩前文件大小：" + FileTypeUtil.bytes2kb(multiFile.getSize()));
            double aDouble = FileTypeUtil.bytes2kb(multiFile.getSize());
            if (aDouble > size) {
                byte[] bytes = commpressPicCycle(bigContent, size, accuracy);
                inputStream = new ByteArrayInputStream(bytes);
                return inputStream;
            } else {
                return inputStream;
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return inputStream;
    }

    public static byte[] commpressPicCycle(byte[] bytes, double desFileSize, double accuracy) throws IOException{
        // 获取目标图片
//        File imgFile = new File(desPath);
//        long fileSize = imgFile.length();
        long fileSize = bytes.length;
        System.out.println("=====fileSize======== "+fileSize);
        // 判断图片大小是否小于指定图片大小
        if(fileSize <= desFileSize * 1024){
            return bytes;
        }
        //计算宽高
        BufferedImage bim = ImageIO.read(new ByteArrayInputStream(bytes));
        int imgWidth = bim.getWidth();
        System.out.println(imgWidth+"====imgWidth=====");
        int imgHeight = bim.getHeight();
        int desWidth = new BigDecimal(imgWidth).multiply( new BigDecimal(accuracy)).intValue();
        System.out.println(desWidth+"====desWidth=====");
        int desHeight = new BigDecimal(imgHeight).multiply( new BigDecimal(accuracy)).intValue();
        ByteArrayOutputStream baos = new ByteArrayOutputStream(); //字节输出流（写入到内存）
        Thumbnails.of(new ByteArrayInputStream(bytes)).size(desWidth, desHeight).outputQuality(accuracy).toOutputStream(baos);
        //如果不满足要求,递归直至满足要求
        return commpressPicCycle(baos.toByteArray(), desFileSize, accuracy);
    }

    /**
     *  返回MultipartFile
     * @param fis
     * @param fieldName
     * @param contentType
     * @param fileName
     * @return
     */
    public MultipartFile getMulFileByFile(InputStream fis,String fieldName,String contentType,String fileName) {
        FileItem fileItem = createFileItem(fis,fieldName,contentType,fileName);
        return new CommonsMultipartFile(fileItem);
    }
    public FileItem createFileItem(InputStream fis,String fieldName,String contentType,String fileName){
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem(fieldName, contentType, false,fileName);
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        try
        {
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192))!= -1)
            {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            fis.close();
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }
        return item;
    }

    /**
     * 压缩图片，返回MultipartFile
     * @param multiFile
     * @return
     */
    public MultipartFile compressImg(MultipartFile multiFile) {
        // 压缩图片
        InputStream inputStream = null;
        ByteArrayOutputStream bos = null;
        MultipartFile multipartFile = null;
        InputStream fileInput= null;
        try {
            inputStream = multiFile.getInputStream();
            bos = new ByteArrayOutputStream();
            // 压缩图片核心代码
            Thumbnails.of(inputStream).scale(0.7f).outputQuality(1).toOutputStream(bos);
            fileInput = new ByteArrayInputStream(bos.toByteArray());
            // 转换 MultipartFile
            String fieldName = multiFile.getName();
            String fileName = multiFile.getOriginalFilename();
            String contentType = multiFile.getContentType();
            multipartFile = getMulFileByFile(fileInput, fieldName, contentType, fileName);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null){
                    inputStream.close();
                }
                if (bos != null){
                    bos.close();
                }
                if (fileInput != null){
                    fileInput.close();
                }
            } catch (IOException e) {
                e.printStackTrace();

            }
        }
        return multipartFile;
    }

    /**
     *  上传base64格式的要件
     * @param folderEntity  目录
     * @param docBase64ObjList 上传的base64对象列表
     * @param appName 微服务名称
     * @param uploadParm 上传要件的关联参数
     * @return
     * @throws ServiceException
     */
    @Override
    public   JSONObject upLoadFileBase64Multi(DocFileBizFiletypeConfigEntity folderEntity, List<DocFileInfBase64> docBase64ObjList, String appName, String uploadParm)throws  ServiceException {
        JSONObject result=new JSONObject();
        if(docBase64ObjList!=null){
            JSONArray resData=new JSONArray();
            for(DocFileInfBase64 base64Obj:docBase64ObjList){
                JSONObject uploadOne=upLoadFileBase64(folderEntity,base64Obj,appName,uploadParm);
                if("0000".equals(uploadOne.getString("code"))){
                    resData.add(uploadOne.getJSONObject("data"));

                }
            }
            if(resData.size()>0){

                result.put("code","0000");
                result.put("data",resData);
            }else{
                result.put("code","1111");
                result.put("data",resData);
                result.put("msg","没有要上传的要件");
            }

        }else{
            result.put("code","2222");
            result.put("msg","没有选中要件");
        }
        return result;
    }

    @Override
    public void deleteByBizNo(String bizNo) throws ServiceException {
        try {
            QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("biz_no",bizNo);
            List<DocFileBizFiletypeConfigEntity> docFileBizFiletypeConfigEntities = docFileBizFiletypeConfigMapper.selectList(queryWrapper);
            for(DocFileBizFiletypeConfigEntity i: docFileBizFiletypeConfigEntities){
                docFileBizFiletypeConfigMapper.deleteById(i.getScId());
            }
        }catch (Exception e){
//            throw  new ServiceException();
        }
    }

    @Override
    public DocFileBizFiletypeConfigEntity selectOne(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException {
        QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no",docFileBizFiletypeConfigEntity.getBizNo());
        queryWrapper.eq("flow_no",docFileBizFiletypeConfigEntity.getFlowNo());
        queryWrapper.eq("type_no",docFileBizFiletypeConfigEntity.getTypeNo());
        queryWrapper.eq("node_no",docFileBizFiletypeConfigEntity.getNodeNo());
        List<DocFileBizFiletypeConfigEntity> docFileBizFiletypeConfigEntities = docFileBizFiletypeConfigMapper.selectList(queryWrapper);
        if(null!=docFileBizFiletypeConfigEntities && !docFileBizFiletypeConfigEntities.isEmpty()){
            return docFileBizFiletypeConfigEntities.get(0);
        }
        return null;
    }

    /**
     * 要件上传(base64)
     * @param folderEntity 目录
     * @param docBase64Obj 上传的base64对象
     * @param uploadParm 上传要件的关联参数
     * @return
     * @throws ServiceException
     */

    private  JSONObject upLoadFileBase64(DocFileBizFiletypeConfigEntity folderEntity, DocFileInfBase64 docBase64Obj, String appName, String uploadParm)throws  ServiceException {
        JSONObject result = new JSONObject();
        InputStream inputStream = null;
        try {
            String suffix = "";

                //校验成功开始保存文件

                String originalName = docBase64Obj.getFileName();
                String thumbPath = "";
                String dbPath = "";
                String newFileName = "";
                double fileSize = 0;
                String savePath ="";
                //三一附件影像资料不再上传至服务器，只存oss
//                if (!"obs".equals(uploadOutterFunction)) {
//                    String uploadFileName = originalName;
//
//
//
//                    suffix = new FileUtils().getFileSuffixName(uploadFileName);//文件后缀名
//                    int lastIndex_=uploadFileName.lastIndexOf(".");
//                    originalName = uploadFileName.substring(0,lastIndex_);//文件的原始名称
//                    newFileName = UUIDUtil.getUUID() + "." + suffix;//保存的目标文件的物理名称。
//                    dbPath = getDbSavePath(folderEntity, appName);//获取数据库路径
//                    savePath = docFileUploadPath + dbPath + newFileName;//最终的物理路径
//
//                    String base64Str=docBase64Obj.getBase64Str();
//                    base64Str=base64Str.replaceAll("data:image/jpg;base64,","");
//                    FileUtils.base64ToFile(base64Str,newFileName,docFileUploadPath + dbPath);
//
//                    boolean imgFlag = false;
//                    InputStream is = new FileInputStream(savePath);
//                    String fileType = FileTypeUtil.getFileTypeByFile(is);
//
//
//                    String thumbName = "";
//                    if ("jpg".equals(suffix)) {
//                        //如果是图片生成缩略图文件
//                        thumbName = "thumb_" + newFileName;
//                        thumbPath = dbPath + "thumbnail" + File.separator + thumbName;
//                        String saveThumbPath = docFileUploadPath + thumbPath;//最终的物理缩略图路径
//                        File thumbFile = new File(docFileUploadPath + dbPath + "thumbnail");
//                        if (!thumbFile.exists()) {
//                            thumbFile.mkdirs();
//                            //检测文件是否存在
//                        }
//                        new ImgCompress().thumbnailImage(savePath, 52, 52, saveThumbPath, true);
//
//                    }
//                    if (is != null) {
//                        is.close();
//                    }
//
//                }

                DocFileInfEntity addBean = new DocFileInfEntity();
                BeanUtils.copyProperties(folderEntity, addBean);

                //主要产品唯一标识不为空时,新增为主要产品唯一标识
                if (StringUtils.isNotBlank(folderEntity.getMainPrdUniqueVal())){
                    addBean.setPrdUniqueVal(folderEntity.getMainPrdUniqueVal());
                }

                //获取base64
                if ("obs".equals(uploadOutterFunction)) {
                    BASE64Encoder base64Encoder = new BASE64Encoder();
                    String contentType = "jpg";
                    if("pdf".equals(docBase64Obj.getFileType())){
                        contentType="pdf";
                    }


                    String base64Str = docBase64Obj.getBase64Str();
                    base64Str = base64Str.replaceAll("[\\s*\t\n\r]", "");
                    addBean.setBase64Str(base64Str);
                    addBean.setContentType(contentType);
                }else if ("oss".equals(uploadOutterFunction)) {
                    File savefile = new File(savePath);
                    MultipartFile file = getMultipartFile(savefile);
                    String uploadFileName = file.getOriginalFilename();
                    suffix = new FileUtils().getFileSuffixName(uploadFileName);//文件后缀名
                    if(("jpeg".equals(suffix)||"jpg".equals(suffix)||"png".equals(suffix))&&FileTypeUtil.bytes2kb(file.getSize())>3500){
                        file = compressImg(file);
                        System.out.println("压缩后文件大小："+FileTypeUtil.bytes2kb(file.getSize()));
                    }
                    String contentType = file.getContentType();
//                    byte[] fileBytes = file.getBytes();
//                    addBean.setByteStr(fileBytes);
                    inputStream = file.getInputStream();
                    addBean.setContentType(contentType);
                }

                addBean.setFileId(UUIDUtil.getUUID());
                addBean.setOrigin("1");
                addBean.setFileName(docBase64Obj.getFileName());

                addBean.setFileSize(new BigDecimal(fileSize));
                addBean.setFilePath(dbPath + newFileName);
                addBean.setThumbnailPath(thumbPath);
                if (StringUtils.isNotEmpty(uploadParm)) {
                    JSONObject uploadParmJson = JSONObject.parseObject(uploadParm);
                    if (uploadParmJson.containsKey("cusRelatedId")) {
                        addBean.setCusRelatedId(uploadParmJson.getString("cusRelatedId"));
                    }
                    if (uploadParmJson.containsKey("busRelatedId")) {
                        addBean.setBusRelatedId(uploadParmJson.getString("busRelatedId"));
                    }
                    if (uploadParmJson.containsKey("applyRelatedId")) {
                        addBean.setApplyRelatedId(uploadParmJson.getString("applyRelatedId"));
                    }
                    if (uploadParmJson.containsKey("constractRelatedId")) {
                        addBean.setConstractRelatedId(uploadParmJson.getString("constractRelatedId"));
                    }
                     if (uploadParmJson.containsKey("finRelatedId")) {
                        addBean.setFinRelatedId(uploadParmJson.getString("finRelatedId"));
                    }
                    if (uploadParmJson.containsKey("parentTypeNo")) {
                        addBean.setParentTypeNo(uploadParmJson.getString("parentTypeNo"));
                    }
                    if (uploadParmJson.containsKey("ifGenerateBizNo")) {
                        String  ifGenerateBizNo=uploadParmJson.getString("ifGenerateBizNo");
                        if("1".equals(ifGenerateBizNo)){
                            //如果需要自动生成bizNo
                            addBean.setBizNo(cn.mftcc.bizcommon.utils.UUIDUtil.getUUID());
                            DocFileBizFiletypeConfigEntity bizFileType=new DocFileBizFiletypeConfigEntity();
                            BeanCopyUtil.copyProperties(addBean,bizFileType);
                            bizFileType.setScId(UUIDUtil.getUUID());
                            bizFileType.setBizNo(addBean.getBizNo());
                            if (StringUtils.isNotBlank(folderEntity.getMainPrdUniqueVal())){
                                bizFileType.setMainPrdUniqueVal(folderEntity.getMainPrdUniqueVal());
                            }
                            docFileBizFiletypeConfigService.insert(bizFileType);
                        }
                    }
                }
                Date date = new Date();
                addBean.setCreateTime(date);
                addBean.setUpdateTime(date);
//                addBean.setCreateUserNo(String.valueOf(requestUtil.getUserInfo("opNo")));

              DocFileTypeEntity _docFileTypeEntity =   docFileTypeService.findById(addBean.getTypeNo());
                if(_docFileTypeEntity != null){
                    addBean.setParentTypeNo(_docFileTypeEntity.getGroupNo());
                }
                addBean.setCreateUserNo((String) requestUtil.getUserInfo("opNo"));
                addBean.setCreateUserName((String) requestUtil.getUserInfo("opName"));
                addBean.setCreateDeptNo((String) requestUtil.getUserInfo("brNo"));
                addBean.setCreateDeptName((String) requestUtil.getUserInfo("brName"));
                JSONObject uploadOutter = upLoadOtherServer(addBean,appName,inputStream);//处理上传到外部服务器
            JSONObject chkRes =new JSONObject();
            chkRes.put("code","1111");
            chkRes.put("msg","上传失败");
                if ("0000".equals(uploadOutter.getString("code"))) {
                    addBean = (DocFileInfEntity) uploadOutter.get("fileObj");
                    docFileInfService.insert(addBean);

                    if ("pdf".equals(suffix.toLowerCase())) {
                        //如果上传的文件时pdf
                        String typeNo = folderEntity.getTypeNo();
                        DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
                        if ("1".equals(docFileTypeEntity.getPdfImageFlag())) {
                            //如果设置了pdf转图片

                            List<String> imgList = FileUtils.getPdfToImage(docFileUploadPath, dbPath, newFileName);
                            if (imgList != null) {
                                if (imgList.size() > 0) {
                                    for (String imgFileName : imgList) {
                                        addBean.setFileId(UUIDUtil.getUUID());
                                        addBean.setFilePath(dbPath + imgFileName);
                                        docFileInfService.insert(addBean);
                                    }
                                }
                            }


                        }

                        result.put("data", addBean);
                        result.put("code", "0000");
                    }


                    else {
                        result.put("data", addBean);
                        result.put("code", "0000");
                    }


                } else {
                    return chkRes;
                }



            return result;

        } catch (Exception e) {
            MFLogger.error("要件上传", e);
            throw new ServiceException("要件上传", folderEntity, e);
        } finally {
            try{
                if(inputStream != null){
                    inputStream.close();
                }
            }catch (IOException e){
                MFLogger.error("要件流关闭失败", e);
            }
        }
    }

        /**
     * 向其他服务上传要件
     * @throws ServiceException
     */
    private JSONObject  upLoadOtherServer(DocFileInfEntity docFileInfEntity,String appName ,InputStream inputStream)throws  ServiceException{



        JSONObject resultData=new JSONObject();
        try{

            if("obs".equals(uploadOutterFunction)){
                resultData=upLoadOBSOtherServer(docFileInfEntity);
            }else if("oss".equals(uploadOutterFunction)){
                resultData=upLoadOSSOtherServer(docFileInfEntity,appName,inputStream);
            }else{
                resultData.put("code","0000");
                resultData.put("fileObj",docFileInfEntity);
            }

        }catch (Exception e){
            throw  new ServiceException("向其他服务上传要件",resultData.getString("msg"),e);
        }
        return resultData;
    }

    private JSONObject upLoadOSSOtherServer(DocFileInfEntity docFileInfEntity,String appName,InputStream inputStream) {
        JSONObject resultData=new JSONObject();
        String fileName;
        String genFile = appName;
        resultData.put("fileObj",docFileInfEntity);
        try{

//             if(appName.contains("cus")){
//                genFile = "CLIENT";
//                fileName = docFileInfEntity.getBizNo();
//            }else if(appName.contains("credit")){
//                genFile = "INTERNET-ACCESS";
//                fileName = docFileInfEntity.getBizNo();
//            }else{
//                genFile = "CONTRACT";
            fileName = docFileInfEntity.getBizNo();
            if("".equals(appName)||appName==null){
                genFile = "other";
            }
//            byte[] pass = docFileInfEntity.getByteStr();
            String uploadFileName = docFileInfEntity.getFileName();
            String suffix = new FileUtils().getFileSuffixName(uploadFileName);//文件后缀名
            int lastIndex_=uploadFileName.lastIndexOf(".");
            String newFileName = UUIDUtil.getUUID() + "." + suffix;//保存的目标文件的物理名称。
            String objectName = genFile +"/"+ docFileInfEntity.getFlowNo() + "/" + fileName + "/" + newFileName;

            resultData = ossConfig.writeOss(objectName, inputStream);

            if("0000".equals(resultData.getString("code"))){

                    docFileInfEntity.setFilePath(resultData.getString("filePath"));
                    resultData.put("fileObj",docFileInfEntity);
//                    resultData.put("data",resultJson.getJSONObject("data"));

                    //修改fileName，去除后缀名
                    String tmpAry=docFileInfEntity.getFileName().substring(0,docFileInfEntity.getFileName().lastIndexOf("."));
                    docFileInfEntity.setFileName(tmpAry);
                }else{
                    resultData.put("code","1111");
                    resultData.put("msg","obs上传失败");
                }

        }catch (Exception e){
            log.error("上传失败== error:{}",e.getMessage());
            throw new RuntimeException("上传失败");
        }
        return  resultData;
    }

    /**
     * 通过obs上传到外网服务
     * @param docFileInfEntity
     * @return
     * @throws ServiceException
     */
    private JSONObject  upLoadOBSOtherServer(DocFileInfEntity docFileInfEntity)throws  ServiceException{

        JSONObject resultData=new JSONObject();
        resultData.put("fileObj",docFileInfEntity);
        String elinkReqNo="";
        try{


            JSONObject requestJson = new JSONObject();

            requestJson.put("callNo", "OBS0005");

            JSONObject paramData=new JSONObject();
            paramData.put("appId",docFileInfEntity.getBizNo()); //项目编号
            paramData.put("busReqNo",docFileInfEntity.getFileId()); //业务流水
            paramData.put("tag",docFileInfEntity.getBizNo());//业务标记（建议上传与业务相关联的标识）
            paramData.put("createdBy",docFileInfEntity.getCreateUserNo());//创建人

            /*String savePath=docFileUploadPath+docFileInfEntity.getFilePath();//最终的物理路径
            File f = new File(savePath);
            String  mimeType=new MimetypesFileTypeMap().getContentType(f);
            paramData.put("mimeType",mimeType);//Mime类型
            String suffix=docFileInfEntity.getFilePath().split("\\.")[1];
            String originalName=docFileInfEntity.getFileName()+"."+suffix;

            paramData.put("originalName",originalName);//原文件名
            String base64Str=FileUtils.encodeBase64File(savePath);
            paramData.put("base64File",base64Str);//base64后的文件流*/


            paramData.put("mimeType",docFileInfEntity.getContentType());//Mime类型
            paramData.put("originalName",docFileInfEntity.getFileName());//原文件名
            paramData.put("base64File",docFileInfEntity.getBase64Str());//base64后的文件流

            //paramData是三方接口的入参必传,如果没有参数传new JSONObject()

            requestJson.put("paramData", paramData);


            String result = elinkFeignClient.itoData(requestJson);

            JSONObject resultJson = JSONObject.parseObject(result);
            if("1".equals(resultJson.getString("code"))){
//                如果上传成功
                JSONObject returnData=resultJson.getJSONObject("data");
                if(returnData.getString("code").equals("SUCCESS")){

                    resultData.put("code","0000");
                    docFileInfEntity.setObsId(returnData.getJSONObject("data").getString("id"));
                    docFileInfEntity.setOutterUrl(returnData.getJSONObject("data").getString("hostPath"));
                    resultData.put("fileObj",docFileInfEntity);
                    resultData.put("data",returnData.getJSONObject("data"));

                    //修改fileName，去除后缀名
                    String tmpAry[]=docFileInfEntity.getFileName().split("\\.");
                    docFileInfEntity.setFileName(tmpAry[0]);
                }else{
                    resultData.put("code","1111");
                    resultData.put("msg","obs上传失败");
                }

            }else{
                resultData.put("code","1111");
                resultData.put("msg","obs上传失败");
            }
            //elink的调用编号

            elinkReqNo = resultJson.getString("mftccReqNo");
        }catch (Exception e){
            throw  new ServiceException("向其他服务上传要件",elinkReqNo,e);
        }
        return  resultData;
    }


    /**
     * 通过obsId获取到要件地址。
     * @param obsId
     * @return
     * @throws ServiceException
     */
    public  JSONObject getDocUrlByObsId(String obsId)throws  ServiceException{

        JSONObject resultData=new JSONObject();
//        resultData.put("fileObj",docFileInfEntity);
        String elinkReqNo="";
        try{

            JSONObject requestJson = new JSONObject();

            requestJson.put("callNo", "OBS0002");

            JSONObject paramData=new JSONObject();
            paramData.put("id",obsId); //obsId
            requestJson.put("paramData", paramData);
            String result = elinkFeignClient.itoData(requestJson);

            JSONObject resultJson = JSONObject.parseObject(result);
            if("1".equals(resultJson.getString("code"))){
//                如果获取成功
                JSONObject returnData=resultJson.getJSONObject("data");
                if(returnData.getString("code").equals("SUCCESS")){

                    resultData.put("code","0000");

                    resultData.put("url",returnData.getJSONObject("data").getString("url"));
                    resultData.put("fileName",returnData.getJSONObject("data").getString("id"));
                    resultData.put("id",obsId);
                }else{
                    resultData.put("code","1111");
                    resultData.put("msg","obs获取失败");
                }

            }else{
                resultData.put("code","1111");
                resultData.put("msg","obs获取失败");
            }
            //elink的调用编号

            elinkReqNo = resultJson.getString("mftccReqNo");
        }catch (Exception e){
            throw  new ServiceException("向其他服务上传要件",elinkReqNo,e);
        }
        return  resultData;
    }

    /**
     * 从obs系统添加要件
     * @param parmJson{
     *                bizNo - 业务流水号
     *                prdUniqueVal - 产品编号
     *                flowNo -流程编号
     *                nodeNo - 节点编号
     *                typeNo - 资料类型编号
     *                typeName -资料类型名称
     *                appName -当前服务名称 比如： 客户-cus 押品-pledge
     *                corpId - 机构编号
     *                obsId - obsid
     *
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject addDocFromObs(JSONObject parmJson)throws ServiceException{
        JSONObject result=new JSONObject();
        //获取obs要件的路径
        String  obsId=parmJson.getString("obsId");
        JSONObject urlJson= getDocUrlByObsId(obsId);

        if(urlJson!=null){

            if("0000".equals(urlJson.getString("code"))){

                String  docUrl=urlJson.getString("url");//获取obs系统的url路径
                String  fileName=urlJson.getString("id");//获取obs系统的文件名称
                String  savePath=getDocSavePath(parmJson);//生成保存的相对路径
                savePath+=fileName;
                String  saveFilePath=docFileUploadPath+savePath;
                File toSave = new File(saveFilePath);
                try{

                    new FileUtils().checkCredatePath(saveFilePath);
                    DocUtil.downloadToFile(docUrl, toSave);//从weboffice服务器下载最新保存文档
                    DocFileInfEntity addBean=new DocFileInfEntity();
                    BigDecimal fileSize=new FileUtils().getFileSize(saveFilePath);
                    addBean.setFileId(cn.mftcc.bizcommon.utils.UUIDUtil.getUUID());
                    addBean.setObsId(obsId);
                    addBean.setOutterUrl(docUrl);
//                    addBean.setCreateUserNo((String) requestUtil.getUserInfo("opNo"));
//                    addBean.setCreateUserName((String) requestUtil.getUserInfo("opName"));
//                    addBean.setCreateDeptNo((String) requestUtil.getUserInfo("brNo"));
//                    addBean.setCreateDeptName((String) requestUtil.getUserInfo("brName"));
                    addBean.setBizNo(parmJson.getString("bizNo"));
                    addBean.setNodeNo(parmJson.getString("nodeNo"));
                    addBean.setFlowNo(parmJson.getString("flowNo"));
                    addBean.setPrdUniqueVal(parmJson.getString("prdUniqueVal"));
                    addBean.setTypeNo(parmJson.getString("typeNo"));
                    addBean.setTypeName(parmJson.getString("typeName"));
                    addBean.setFileName(parmJson.getString("typeName"));
                    addBean.setCorpId(parmJson.getString("corpId"));
                    addBean.setFileSize(fileSize);
                    addBean.setFilePath(savePath);
                    addBean.setThumbnailPath(savePath);
                    addBean.setCreateTime(new Date());
                    addBean.setUpdateTime(new Date());
                    addBean.setOrigin("2");
                    addBean.setIsEncrypt("0");
                    addBean.setIfMustRead("0");

                    docFileInfService.insert(addBean);
                    result.put("code","0000");
                    result.put("msg","s");

                }catch (Exception e){
                    MFLogger.error("从obs系统添加要件",e);
                    throw new ServiceException("",parmJson.toJSONString(),e);
                }


            }
        }else{
            result.put("code","1111");
            result.put("msg","信息为空");
        }
        return result;

    }

    /**
     * 根据obsId获取obs展示路径
     * @param obsId
     * @return
     * @throws ServiceException
     */
    @Override
    public  String  getShowUrlByObsid(String obsId)throws ServiceException{
        String  docUrl="";
        JSONObject result=new JSONObject();
        //获取obs要件的路径
//        String  obsId=parmJson.getString("obsId");
        JSONObject urlJson= getDocUrlByObsId(obsId);

        if(urlJson!=null) {

            if ("0000".equals(urlJson.getString("code"))) {

                docUrl = urlJson.getString("url");//获取obs系统的url路径
            }
        }
        return  docUrl;
    }

    private String getDocSavePath(JSONObject parmJson)throws  ServiceException{
        String  savePath="";
        if(StringUtils.isNotEmpty(parmJson.getString("corpId"))){
            savePath+=parmJson.getString("corpId")+"/";
        }
        if(StringUtils.isNotEmpty(parmJson.getString("appName"))){
            savePath+=parmJson.getString("appName")+"/";
        }
        if(StringUtils.isNotEmpty(parmJson.getString("prdUniqueVal"))){
            savePath+=parmJson.getString("prdUniqueVal")+"/";
        }
        if(StringUtils.isNotEmpty(parmJson.getString("flowNo"))){
            savePath+=parmJson.getString("flowNo")+"/";
        }
        if(StringUtils.isNotEmpty(parmJson.getString("nodeNo"))){
            savePath+=parmJson.getString("nodeNo")+"/";
        }
        if(StringUtils.isNotEmpty(parmJson.getString("typeNo"))){
            savePath+=parmJson.getString("typeNo")+"/";
        }
        if(StringUtils.isNotEmpty(parmJson.getString("bizNo"))){
            savePath+=parmJson.getString("bizNo")+"/";
        }
        return savePath;

    }





    /**
     *  获取数据库保存的相对路径
     * @param folder
     * @param appName 微服务名称
     * @return
     * @throws ServiceException
     */
    private String getDbSavePath(DocFileBizFiletypeConfigEntity folder,String appName)throws  ServiceException{

        StringBuilder tmpDbPath=new StringBuilder("");
        if(StringUtils.isNotEmpty(folder.getCorpId())) {
            tmpDbPath.append(folder.getCorpId());
            tmpDbPath.append(File.separator);
        }
        if(StringUtils.isNotEmpty(appName)) {
            tmpDbPath.append(appName);
            tmpDbPath.append(File.separator);
        }
        if(StringUtils.isNotEmpty(folder.getPrdUniqueVal())){
            tmpDbPath.append(folder.getPrdUniqueVal()).append(File.separator);
        }
        if(StringUtils.isNotEmpty(folder.getFlowNo())){
            tmpDbPath.append(folder.getFlowNo()).append(File.separator);
        }
        if(StringUtils.isNotEmpty(folder.getNodeNo())){
            tmpDbPath.append(folder.getNodeNo()).append(File.separator);
        }
        tmpDbPath.append(folder.getBizNo()).append(File.separator);
        DocScFiletypeConfigEntity qBean=new DocScFiletypeConfigEntity();
        qBean.setPrdUniqueVal(folder.getPrdUniqueVal());
        qBean.setFlowNo(folder.getFlowNo());
        qBean.setNodeNo(folder.getNodeNo());
        qBean.setTypeNo(folder.getTypeNo());
        JSONArray rootList = getRootListByTypeBean(folder);
        if(rootList!=null){
            for(int i =0;i<rootList.size();i++){
                String tmpTypeNo=rootList.getJSONObject(i).getString("typeNo");
                if(StringUtils.isNotEmpty(tmpTypeNo)) {
                    tmpDbPath.append(tmpTypeNo).append(File.separator);
                }
            }
        }
        return tmpDbPath.toString();
    }


    /**
     * 逐层获取父亲目录
     * @param curBean
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray getRootListByTypeBean(DocFileBizFiletypeConfigEntity curBean)throws  ServiceException{

        JSONArray result=new JSONArray();
        if(StringUtils.isEmpty(curBean.getTypeNo())){
            DocFileBizFiletypeConfigEntity root=new DocFileBizFiletypeConfigEntity();
            root.setTypeNo("");
            root.setTypeName("根目录");
            result.add(root);
            return result;
        }
        DocFileBizFiletypeConfigEntity obj=getOneObj(curBean);
        List<DocFileBizFiletypeConfigEntity> list=new ArrayList<>();
        list.add(obj);

        while(StringUtils.isNotEmpty(obj.getParentTypeNo())){
            obj.setTypeNo(obj.getParentTypeNo());
            DocFileBizFiletypeConfigEntity pBean=getOneObj(obj);
            list.add(pBean);
            obj.setParentTypeNo(pBean.getParentTypeNo());

        }
        DocFileBizFiletypeConfigEntity root=new DocFileBizFiletypeConfigEntity();
        root.setTypeNo("");
        root.setTypeName("根目录");
        result.add(root);
        if(list.size()>0){
            int len=list.size()-1;
            for(int i=len;i>=0;i-- ){
                result.add(list.get(i));
            }
        }


        return result;
    }

    /**
     * 要件自动初始化
     * @param docFileBizFiletypeConfigEntity
     * @throws ServiceException
     */
    @Override
    public   void  fileAutoInitFun(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity)throws ServiceException{
        //  归档显示资料不再初始化
        String flowNo = docFileBizFiletypeConfigEntity.getFlowNo();
        if(StringUtils.isNotBlank(flowNo) && Constant.ARCHIVE_SHOW_FILE.equals(flowNo)){
            return;
        }
        //首先判断时是否已经经过初始化
        boolean initFlag=false;
        List<DocFileBizFiletypeConfigEntity>initList=findList(docFileBizFiletypeConfigEntity);
        if(initList!=null){
            if(initList.size()>0){
                initFlag=true;
            }
        }
        if(!initFlag){
//            如果不曾初始化过
            DocScFiletypeConfigEntity docScFiletypeConfigEntity =new DocScFiletypeConfigEntity();
            docScFiletypeConfigEntity.setPrdUniqueVal(docFileBizFiletypeConfigEntity.getPrdUniqueVal());
            docScFiletypeConfigEntity.setFlowNo(docFileBizFiletypeConfigEntity.getFlowNo());
            docScFiletypeConfigEntity.setNodeNo(docFileBizFiletypeConfigEntity.getNodeNo());
            docScFiletypeConfigEntity.setParentTypeNo("bizConfig");
            List<DocScFiletypeConfigEntity> configList =docScFiletypeConfigService.findList(docScFiletypeConfigEntity);

            if(configList!=null && configList.size()>0){
                //如果该业务的产品类型，流程，节点 配置过要件，拿过来作为该业务的初始要件配置
                for(DocScFiletypeConfigEntity configBean:configList){
                    DocFileBizFiletypeConfigEntity addBean=new DocFileBizFiletypeConfigEntity();
                    BeanUtils.copyProperties(configBean,addBean);
                    addBean.setScId(UuidUtils.generateUuid());
                    addBean.setBizNo(docFileBizFiletypeConfigEntity.getBizNo());
                    addBean.setMainPrdUniqueVal(docFileBizFiletypeConfigEntity.getMainPrdUniqueVal());
                    addBean.setCreateTime(new Date());
                    addBean.setUpdateTime(new Date());
                    insert(addBean);


                }
            }else{
                //如果没有，代表是临时的配置。直接配置即可
                DocFileBizFiletypeConfigEntity addBean=new DocFileBizFiletypeConfigEntity();
                BeanUtils.copyProperties(docFileBizFiletypeConfigEntity,addBean);
                addBean.setScId(UuidUtils.generateUuid());
                addBean.setBizNo(docFileBizFiletypeConfigEntity.getBizNo());
                addBean.setIsEncrypt("0");
                addBean.setIfMustRead("0");
                addBean.setIfUpload("0");
                addBean.setCreateTime(new Date());
                addBean.setUpdateTime(new Date());
                String  typeNo = docFileBizFiletypeConfigEntity.getTypeNo();
                if(StringUtils.isNoneEmpty(typeNo)){
                    insert(addBean);
                }
            }
        }

        //如果存在主产品编号，初始化主产品要件
        if (StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getFlowNo()) &&
                StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getNodeNo()) &&
                StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getBizNo()) &&
                StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getPrdUniqueVal()) &&
                StringUtils.isNotBlank(docFileBizFiletypeConfigEntity.getMainPrdUniqueVal())){

            JSONObject param = new JSONObject();
            param.put("flowNo",docFileBizFiletypeConfigEntity.getFlowNo());
            param.put("nodeNo",docFileBizFiletypeConfigEntity.getNodeNo());
            param.put("bizNo",docFileBizFiletypeConfigEntity.getBizNo());
            param.put("prdUniqueVal",docFileBizFiletypeConfigEntity.getMainPrdUniqueVal());
            this.deleteByJson(param);

            DocScFiletypeConfigEntity docScFiletypeConfigEntity =new DocScFiletypeConfigEntity();
            docScFiletypeConfigEntity.setPrdUniqueVal(docFileBizFiletypeConfigEntity.getMainPrdUniqueVal());
            docScFiletypeConfigEntity.setFlowNo(docFileBizFiletypeConfigEntity.getFlowNo());
            docScFiletypeConfigEntity.setNodeNo(docFileBizFiletypeConfigEntity.getNodeNo());
            docScFiletypeConfigEntity.setParentTypeNo("bizConfig");
            List<DocScFiletypeConfigEntity> configList =docScFiletypeConfigService.findList(docScFiletypeConfigEntity);

            if(configList!=null && configList.size()>0) {
                //如果该业务的产品类型，流程，节点 配置过要件，拿过来作为该业务的初始要件配置
                for (DocScFiletypeConfigEntity configBean : configList) {
                    DocFileBizFiletypeConfigEntity addBean = new DocFileBizFiletypeConfigEntity();
                    BeanUtils.copyProperties(configBean, addBean);
                    addBean.setScId(UuidUtils.generateUuid());
                    addBean.setBizNo(docFileBizFiletypeConfigEntity.getBizNo());
                    addBean.setCreateTime(new Date());
                    addBean.setUpdateTime(new Date());
                    insert(addBean);
                }
            }
        }

    }

    @Override
    public void fileAutoInitFunMix(JSONObject jsonObject) throws ServiceException {
        JSONArray jsonArray = jsonObject.getJSONArray("fileParameterArray");

        if(jsonArray != null &&jsonArray.size() > 0){
            for(int a = 0; a<jsonArray.size();a++){
                DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = JSONObject.parseObject(jsonArray.getJSONObject(a).toJSONString(),DocFileBizFiletypeConfigEntity.class);
                this.fileAutoInitFun(docFileBizFiletypeConfigEntity);

            }
        }
    }


    /**
     * 获取一个实体
     * @param typeBean
     * @return
     * @throws ServiceException
     */
    private  DocFileBizFiletypeConfigEntity getOneObj(DocFileBizFiletypeConfigEntity typeBean)throws  ServiceException{
        QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
//        mapperUtil.tableQuery(queryWrapper,typeBean);
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getPrdUniqueVal()),"prd_unique_val",typeBean.getPrdUniqueVal());
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getFlowNo()),"flow_no",typeBean.getFlowNo());
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getNodeNo()),"node_no",typeBean.getNodeNo());
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getTypeNo()),"type_no",typeBean.getTypeNo());
        queryWrapper.eq(StringUtils.isNotBlank(typeBean.getBizNo()),"biz_no",typeBean.getBizNo());
        queryWrapper.last("limit 1");
        return docFileBizFiletypeConfigMapper.selectOne(queryWrapper);

    }


    /**
     * 校验要件
     * @param file
     * @return
     * @throws ServiceException
     */
    private JSONObject validateFile(MultipartFile file)throws  ServiceException{
        JSONObject result=new JSONObject();
        result.put("code","0000");
        result.put("msg","校验成功");
        /**
         * 1、检验文件的格式必须在白名单中
         * 2、检验文件的大小
         * 3、校验文件的名字长度
         *
         */
        try{
            MFLogger.info("***********     开始进行文档校验   ***********");
            //获取文件类型,包括图片,若格式不是已配置的,则返回null
            String fileType = FileTypeUtil.getFileTypeByFile(file.getInputStream());
            String fileName = file.getOriginalFilename();
            if ("blank".equals(fileType)) {
                //处理空文件的情况
                fileType=new FileUtils().getFileSuffixName(fileName);
            }

//            MFLogger.info("***********     文件类型："+fileType+"      文件名称： "+fileName+"  ***********");
            if (fileName.endsWith(".txt")) {
                fileType = "txt";
            }
            if (fileName.endsWith(".mp4")) {
                fileType = "mp4";
            }
            if(fileName.toLowerCase().endsWith(".xls")){
                fileType="xls";
            }
            if(fileName.toLowerCase().endsWith(".doc")){
                fileType="doc";
            }
            if(fileName.toLowerCase().endsWith(".pdf")){
                fileType="pdf";
            }
            if (// 不应允许任意文件上传，必须配置白名单 StrUtil.isNotEmpty(uploadFiletypeLimit) &&
                    !ReUtil.contains("\\.\\b" + fileType + "\\b",uploadFiletypeLimit)) {
                fileType = null;
            }
            if(StrUtil.isEmpty(fileType)){
                result.put("code", "1111");
                Map params = new HashMap(2);
                params.put("fileTypes", uploadFiletypeLimit);
                params.put("fileName", fileName);
                result.put("msg", MessageEnum.ERROR_UPLOAD_FILE_TYPE.getMessage(params));
                return result;
            }
//            检验文件的大小

            double fileSize=FileTypeUtil.bytes2kb(file.getSize());//获取文件大小
//            MFLogger.info("***********     文件大小（KB）："+fileSize+"      大小限制： "+uploadFileSize+"  ***********");
            if(fileSize>Double.parseDouble(uploadFileSize)){
                result.put("code", "2222");
                result.put("msg", MessageEnum.ERROR_UPLOAD_FILE_SIZE.getMessage(uploadFileSize));

//                MFLogger.info("***********     文件大小限制的结果 "+result.getString("msg")+"  ***********");
                return  result;
            }
//            校验文件的名字长度
            int  fileNameLen=new FileUtils().length(fileName);
            if ( fileNameLen> Integer.parseInt(uploadFileNameLength)) {
                result.put("code", "2222");
                result.put("msg", MessageEnum.NOT_FORM_LENGTH_GT.getMessage(uploadFileNameLength));
                return result;
            }





        }catch (Exception e){
            MFLogger.error("校验上传要件错误：",e);
            throw  new ServiceException("校验上传要件",file.getOriginalFilename(),e);
        }



        return result;
    }



    @Override
    public void deleteById(String scId) throws ServiceException {
        try{
            docFileBizFiletypeConfigMapper.deleteById(scId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,scId,e);
        }
    }

    /**
     *
     * @param delJson{
     *                bizNo - 业务流水号
     *                prdUniqueVal - 产品编号
     *                flowNo -流程编号
     *                nodeNo - 节点编号
     *                typeNo - 资料类型编号
     * }
     * @throws ServiceException
     */
    @Override
    public void deleteByJson(JSONObject delJson) throws ServiceException {
        try{

            QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotBlank(delJson.getString("bizNo")),"biz_no",delJson.getString("bizNo"));
            queryWrapper.eq(StringUtils.isNotBlank(delJson.getString("prdUniqueVal")),"prd_unique_val",delJson.getString("prdUniqueVal"));
            queryWrapper.eq(StringUtils.isNotBlank(delJson.getString("flowNo")),"flow_no",delJson.getString("flowNo"));
            queryWrapper.eq(StringUtils.isNotBlank(delJson.getString("nodeNo")),"node_no",delJson.getString("nodeNo"));
            queryWrapper.eq(StringUtils.isNotBlank(delJson.getString("typeNo")),"type_no",delJson.getString("typeNo"));



            docFileBizFiletypeConfigMapper.delete(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,delJson.toJSONString(),e);
        }
    }


    @Override
    public  JSONObject getFileConfigByBizId(JSONObject parmJson)throws  ServiceException {
        JSONObject jsonObject = new JSONObject();
        DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();
        String bizNo = parmJson.getString("bizId");
        QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no",bizNo);
        mapperUtil.tableQuery(queryWrapper,docFileBizFiletypeConfigEntity);
        docFileBizFiletypeConfigEntity = docFileBizFiletypeConfigMapper.selectOne(queryWrapper);
        jsonObject.put("scId",docFileBizFiletypeConfigEntity.getScId());
        return jsonObject;
    }

    @Override
    public  JSONObject getFileByBizNoList(JSONObject parmJson)throws  ServiceException {
        JSONObject jsonObject = new JSONObject();
        FileShowVo fileShowVo = new FileShowVo();
        //解析参数查询要件表
        List<String> list = (List<String>)parmJson.get("bizNoList");
        List<DocFileInfEntity> listFile = new ArrayList<>();
        List<DocFileBizFiletypeConfigEntity> listFileBiz = new ArrayList<>();
        List<Map<String,String>> groupList = new ArrayList<>();
        if(list.size()>0){
            for (String bizNo:list) {
                DocFileInfEntity docFileInfEntity = new DocFileInfEntity();
                QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("biz_no",bizNo);
                mapperUtil.tableQuery(queryWrapper,docFileInfEntity);
                List<DocFileInfEntity> listFileA  = docFileInfMapper.selectList(queryWrapper);
                for (DocFileInfEntity docFileInfEntity1:listFileA) {
                    listFile.add(docFileInfEntity1);
                }
            }
        }
        //定义外面参数
        DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();
        docFileBizFiletypeConfigEntity.setFileList(listFile);
        listFileBiz.add(docFileBizFiletypeConfigEntity);
        fileShowVo.setGroupList(groupList);
        fileShowVo.setFileList(listFileBiz);
        jsonObject.put("fileShowVo",fileShowVo);
        return jsonObject;
    }

    public  JSONObject getFileListByBizNo(JSONObject parmJson)throws  ServiceException {
        JSONObject jsonObject = new JSONObject();
        //解析参数查询要件表
        List<String> list = (List<String>)parmJson.get("bizNoList");
        List<DocFileInfEntity> listFile = new ArrayList<>();
        if(list.size()>0){
            for (String bizNo:list) {
                DocFileInfEntity docFileInfEntity = new DocFileInfEntity();
                QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("biz_no",bizNo);
                mapperUtil.tableQuery(queryWrapper,docFileInfEntity);
                List<DocFileInfEntity> listFileA  = docFileInfMapper.selectList(queryWrapper);
                jsonObject.put(bizNo,JSONArray.parseArray(JSON.toJSONString(listFileA)));
            }
        }
        return jsonObject;
    }

    private MultipartFile getMultipartFile(File file){
        FileInputStream fileInputStream = null;
        MultipartFile multipartFile = null;
        try {
            fileInputStream = new FileInputStream(file);
            multipartFile = new MockMultipartFile(file.getName(),file.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(),fileInputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return multipartFile;
    }


    /**
     *  生成合同模板
     * @param parmJson
     * @throws ServiceException
     */
    @Override
    public void  renderTemplate(JSONObject parmJson)throws ServiceException{
        String batchNo = parmJson.getString("batchNo");
        String pactNo = parmJson.getString("pactNo");
        String opNo = parmJson.getString("opNo");
        String targetFilePath = downloadPath+File.separator+opNo+"_"+batchNo+File.separator+pactNo;
        parmJson.put("targetFilePath",targetFilePath) ;
        MFLogger.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"-模板渲染开始");
        docTemplateModelService.renderTemplate(parmJson) ;
        MFLogger.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"-模板渲染结束");
    }

    /**
     *  生成合同模板返回保存路径
     * @param parmJson
     * @throws ServiceException
     */
    @Override
    public JSONObject  renderTemplateJSONObject(JSONObject parmJson)throws ServiceException{
        String batchNo = parmJson.getString("batchNo");
        String pactNo = parmJson.getString("pactNo");
        String opNo = parmJson.getString("opNo");
        String targetFilePath = downloadPath+File.separator+opNo+"_"+batchNo+File.separator+pactNo;
        parmJson.put("targetFilePath",targetFilePath) ;
        MFLogger.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"-模板渲染开始");
        JSONObject jsonObject = docTemplateModelService.renderTemplate(parmJson);
        MFLogger.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"-模板渲染结束");
        return jsonObject;
    }


    /**
     * 打包已经生成的附件
     * @param opNo
     * @param batchNo
     * @throws ServiceException
     */
    public void downloadVoucherFilesZip(String opNo,String batchNo) throws ServiceException {
        String fileName = opNo+"_"+batchNo;
        try {
            MFLogger.info("MQCreatVoucherFile-Name("+fileName+")-打包开始");
            String path = downloadPath+File.separator+fileName ;
            ZipUtil.zip(path) ;
            MFLogger.info("MQCreatVoucherFile-Name("+fileName+")-打包结束");
        }catch (Exception e){
            MFLogger.error("MQCreatVoucherFile-Name("+fileName+")-打包异常",e);
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,fileName,e);
        }
    }


    /**
     *  生成附件到指定目录下
     * @param batchNo
     * @param pactNo
     * @param fileList
     * @throws ServiceException
     */
    public String generateFile(String opNo,String batchNo, String pactNo, List<DocFileInfEntity> fileList) throws ServiceException{
        try {
            if(CollectionUtils.isEmpty(fileList)){
                return null;
            }
            String LSfilePath = null;
            MFLogger.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"-流程附件生成开始");
            String targetFilePath =downloadPath+File.separator+opNo+"_"+batchNo+File.separator+pactNo+File.separator ;
            if ("obs".equals(uploadOutterFunction)) {
                for (DocFileInfEntity fileBean : fileList) {
                    String obsId = fileBean.getObsId();
                    String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                    String suffix = new FileUtils().getFileSuffixName(obsId);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    if(new File(filePath).exists()){
                        FileUtil.copyFile(filePath,targetFilePath+fileRename) ;
                    }
                    LSfilePath = targetFilePath+fileRename;
                }
            } else if ("oss".equals(uploadOutterFunction)) {
                for (DocFileInfEntity fileBean : fileList) {
                    String filePath = fileBean.getFilePath();
                    String suffix = new FileUtils().getFileSuffixName(filePath);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    MFLogger.info("读取OSS文件("+filePath+")");
                    InputStream inputStream = ossConfig.readOss(filePath);
                    MFLogger.info("目标路径文件("+targetFilePath+")");
                    File file = new File(targetFilePath+fileRename);
                    if(!file.exists()){
                        File file1 = new File(targetFilePath);//文件目录
                        if (!file1.exists() && file.getParentFile().mkdirs()){
                            file.createNewFile();//创建文件
                        }else if (file1.exists()){
                            file.createNewFile();//创建文件
                        }
                    }
                    LSfilePath = targetFilePath+fileRename;
                    OutputStream outputStream = new FileOutputStream(targetFilePath+fileRename);
                    if(null != inputStream){
                        MFLogger.info("读取inputStream OSS 长度("+inputStream.available()+")");
                        try {
                            byte[] buffer = new byte[1024];
                            int bytesRead;
                            while ((bytesRead = inputStream.read(buffer)) != -1) {
                                outputStream.write(buffer, 0, bytesRead);
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }finally {
                            if (null != inputStream){
                                inputStream.close();
                            }
                            if (null != outputStream){
                                outputStream.close();
                            }
                        }
                    }
                }
            } else {
                for (DocFileInfEntity fileBean : fileList) {
                    String filePath = fileBean.getFilePath();
                    String suffix = new FileUtils().getFileSuffixName(filePath);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    String realPath = docFileUploadPath + filePath;
                    String targetFile = targetFilePath+fileRename;
                    if(new File(realPath).exists()){
                        FileUtil.copyFile(realPath,targetFile) ;
                    }
                    LSfilePath = targetFilePath+fileRename;
                }
            }
            MFLogger.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"-流程附件生成结束");
            return LSfilePath;
        } catch (Exception e) {
            MFLogger.error("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"-流程附件生成失败",e);
            throw new RuntimeException(e);
        }
    }

    /**
     *  输出信息到文件
     * @param opNo
     * @param batchNo
     * @param infos
     * @throws ServiceException
     */
    public void writInfosToFile(String opNo,String batchNo,List<String> infos) throws ServiceException{
        FileWriter writer = null ;
        try {
            if(CollectionUtils.isNotEmpty(infos)) {
                String targetFilePath = downloadPath + File.separator + opNo + "_" + batchNo + File.separator + "readme.txt";
                File file = new File(targetFilePath) ;
                if(!file.getParentFile().exists()){
                    file.getParentFile().mkdirs();
                    file.createNewFile();
                }
                writer = new FileWriter(targetFilePath);
                for (String info : infos){
                    writer.write(info+"\r\n");
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            if(null != writer){
                try {
                    writer.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    @Override
    public Map<String,Object> downloadFilesZipV1(JSONObject ajaxData)throws   ServiceException {
        log.info("下载模板传入参数:"+ajaxData);
        Map<String, Object> map =new HashMap<>();
        ArrayList<String> fileIds = (ArrayList<String>) ajaxData.get("fileIds");
        String downloadName = ajaxData.getString("fileName");
        List<String> tempLateFileNames = (List<String>) ajaxData.get("tempLateFileNames");
        if(CollectionUtils.isEmpty(fileIds)||StringUtils.isBlank(downloadName)){
            log.info("必传参数为空,fileIds:"+JSONObject.toJSONString(fileIds)+"--downloadName:"+downloadName);
            map.put("msg","必传参数为空");
            return map;
        }

        InputStream fis = null;
        FileOutputStream fos = null;
        OutputStream zos = null;

        try {
            //获取文件信息（此处为业务代码，可根据自己的需要替换）
            QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("file_id",fileIds);
            List<DocFileInfEntity> fileInfoList=docFileInfMapper.selectList(queryWrapper);
            // 将文件流写入zip中
            ZipTool zt = createFilesZip(fileInfoList,downloadPath);


            if(CollectionUtils.isNotEmpty(tempLateFileNames)){
                QueryWrapper<DocBizTemplateEntity> templateQueryWrapper = new QueryWrapper<>();
                templateQueryWrapper.in("template_name",tempLateFileNames);
                templateQueryWrapper.eq("prd_unique_val","paymentDoc");
                templateQueryWrapper.eq("flow_no","paymentDoc");
                templateQueryWrapper.eq("node_no","paymentDoc");
                List<DocBizTemplateEntity> tempLateFileList=docBizTemplateMapper.selectList(templateQueryWrapper);
                log.info("tempLateFileNames 列表 {}",tempLateFileNames.toString());
                log.info("tempLateFileNames"+tempLateFileNames.toString());
                if(CollectionUtils.isNotEmpty(tempLateFileList)){
                    zt = createTemplateFilesZip(tempLateFileList,downloadPath,zt);
                }
            }

            fis = new BufferedInputStream(new ByteArrayInputStream(zt.getOutputStream()));
            fos = new FileOutputStream(new File(downloadPath+File.separator+downloadName));
            zos = new BufferedOutputStream(fos);
            byte[] buffer = new byte[1024 * 1024 * 4];
            int i = -1;
            while ((i = fis.read(buffer)) != -1) {
                zos.write(buffer, 0, i);
            }
            zos.flush();

            map.put("msg","success");
        } catch (Exception e) {
            e.printStackTrace();
            map.put("msg",e.getMessage());
            MFLogger.error("downloadFilesZipV1-付款投放凭证，processInstanceId=[{}],错误信息=[{}]","",e);
        }finally {
            try {
                if(fis!=null){
                    fis.close();
                }
            }catch(IOException e){
                log.error("创建ZIP文件时释放资源失败",e);
            }
            try {
                if (zos != null) {
                    zos.close();
                }
            } catch (IOException e) {
                log.error("创建ZIP文件时释放资源失败2",e);
            }
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                log.error("创建ZIP文件时释放资源失败3",e);
            }
        }
        return map;

    }

    /**
     * 打包下载的文件存至本地
     * @param fileList
     * @param newFileName
     * @throws Exception
     */
    private ZipTool createFilesZip(List<DocFileInfEntity> fileList,String newFileName) throws Exception {
        try {
            //递归获取该目录下所有的文件
            ZipTool zt = new ZipTool();
            if ("obs".equals(uploadOutterFunction)) {
                for (DocFileInfEntity fileBean : fileList) {
                    String obsId = fileBean.getObsId();
                    String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                    String suffix = new FileUtils().getFileSuffixName(obsId);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    zt.addObsFile(filePath, fileRename);
                }
            } else if ("oss".equals(uploadOutterFunction)) {
                for (DocFileInfEntity fileBean : fileList) {
                    String filePath = fileBean.getFilePath();
                    String suffix = new FileUtils().getFileSuffixName(filePath);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    InputStream inputStream = ossConfig.readOss(filePath);
                    zt.addOssFile(fileRename, inputStream);
                }
            } else {
                for (DocFileInfEntity fileBean : fileList) {
                    String filePath = fileBean.getFilePath();
                    String suffix = new FileUtils().getFileSuffixName(filePath);
                    String fileRename = fileBean.getFileName() + "." + suffix;
                    String realPath = docFileUploadPath + filePath;
                    File tmpFile = new File(realPath);
                    if (tmpFile.exists()) {
                        zt.addFileReName(realPath, fileRename);
                    }
                }
            }


            return zt ;
        }catch (Exception e){
            log.error("创建"+newFileName+"ZIP文件失败",e);
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,newFileName,e);
        }
    }

    private ZipTool createTemplateFilesZip(List<DocBizTemplateEntity> fileList,String newFileName,ZipTool zt) throws Exception {
        try {
            String fileUrlPath="";
            String templateName="";
            File file = null ;
            String fileSuffix = "" ;
            log.info("文件数量："+fileList.size());

            for(DocBizTemplateEntity template:fileList){
                fileUrlPath = template.getSavePath()+template.getSaveFileName();
                log.info("文件所在位置："+fileUrlPath);
                fileSuffix=new FileUtils().getFileSuffixName(template.getSaveFileName());//文件后缀名
                log.info("文件后缀名："+fileSuffix);
                templateName = template.getTemplateName()+"."+fileSuffix;
                log.info("文件重命名："+templateName);
                if("1".equals(template.getOssRemoveStatus())){
                    //已迁移模板从oss下载
                    String fileUrl = template.getSavePath()+template.getSaveFileName();
                    InputStream fis = ossConfig.readOss(fileUrl);
                    zt.addOssFile(templateName,fis);
                    fis.close();
                }else{
                    file=new File(fileUrlPath);
                    log.info("文件是否存在："+file.exists());
                    if(file.exists()){
                        zt.addFileReName(fileUrlPath, templateName);
                    }
                }
            }
            return zt ;
        }catch (Exception e){
            log.error("创建"+newFileName+"ZIP文件失败",e);
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,newFileName,e);
        }
    }

    @Override
    public void renderTemplateForExcel(JSONObject parmTempJson) throws ServiceException {
        docTemplateModelService.renderTemplateForExcel(parmTempJson) ;
    }
}