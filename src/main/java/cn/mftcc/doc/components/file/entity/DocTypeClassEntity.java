/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import lombok.Data;

/**
 * 要件类型大类表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-12-06 18:41:55
 */
@Data
@TableName("doc_type_class")
public class DocTypeClassEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String typeId;
	/**
	 * 要件类型编号
	 */
	private String typeNo;
	/**
	 * 要件类型名称
	 */
	private String typeName;
	/**
	 * 要件类型描述
	 */
	private String typeDesc;
	/**
	 * 启用标识 1-启用 0-禁用
	 */
	private String useFlag;
	/**
	 * 法人机构编号
	 */
	private String corpId;
	/**
	 * 分组编号
	 */
	private String groupNo;

}
