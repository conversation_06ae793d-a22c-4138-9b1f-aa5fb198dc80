/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service.impl;

import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.bizcommon.utils.UUIDUtil;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.utils.MapperUtil;
import cn.mftcc.common.utils.ParmCacheUtil;
import cn.mftcc.common.utils.RequestUtil;
import cn.mftcc.doc.components.file.entity.DocFileTypeEntity;
import cn.mftcc.doc.components.file.entity.DocScFiletypeConfigEntity;
import cn.mftcc.doc.components.file.entity.DocTypeClassEntity;
import cn.mftcc.doc.components.file.mapper.DocFileTypeMapper;
import cn.mftcc.doc.components.file.service.DocFileTypeService;
import cn.mftcc.doc.components.file.service.DocScFiletypeConfigService;
import cn.mftcc.doc.components.file.service.DocTypeClassService;
import cn.mftcc.doc.components.mould.entity.DocScTemplateConfigEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 文件类型表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 17:19:12
 */
@Service("docFileTypeService")
@Transactional(rollbackFor = Exception.class)
public class DocFileTypeServiceImpl implements DocFileTypeService {

    @Autowired
    private DocFileTypeMapper docFileTypeMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private ParmCacheUtil parmCacheUtil;
    @Autowired
    private RequestUtil requestUtil;
    @Autowired
    private DocTypeClassService docTypeClassService;
    @Autowired
    private DocScFiletypeConfigService docScFiletypeConfigService;


    @Override
    public IPage<DocFileTypeEntity> findByPage(DocFileTypeEntity docFileTypeEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocFileTypeEntity> page = new Page<>();
            page.setCurrent(docFileTypeEntity.getPageNo());
            page.setSize(docFileTypeEntity.getPageSize());
            QueryWrapper<DocFileTypeEntity> queryWrapper = new QueryWrapper<>();
//            if(StringUtils.isEmpty(docFileTypeEntity.getTypeClass())){
//                queryWrapper.isNull("type_class");
//            }else{
//                queryWrapper.eq("type_class",docFileTypeEntity.getTypeClass());
//            }
            mapperUtil.tableQuery(queryWrapper,docFileTypeEntity);
            return docFileTypeMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docFileTypeEntity.getTypeNo(),e);
        }
    }

    @Override
    public List<DocFileTypeEntity> findList(DocFileTypeEntity docFileTypeEntity) throws ServiceException {
        try{
            //翻页

            QueryWrapper<DocFileTypeEntity> queryWrapper = new QueryWrapper<>();
//            mapperUtil.tableQuery(queryWrapper,docFileTypeEntity);
            queryWrapper.eq(StringUtils.isNotBlank(docFileTypeEntity.getTypeClass()),"type_class",docFileTypeEntity.getTypeClass());
            return docFileTypeMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docFileTypeEntity.getTypeNo(),e);
        }
    }

    /**
     * 获取所有配置权限的类型
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocFileTypeEntity> findRightList() throws ServiceException {
        try{
            //翻页
            QueryWrapper<DocFileTypeEntity> queryWrapper = new QueryWrapper<>();
//            mapperUtil.tableQuery(queryWrapper,docFileTypeEntity);
            queryWrapper.ne("IFNULL(dept_read_auth,'')","");
            queryWrapper.or();
            queryWrapper.ne("IFNULL(dept_read_auth,'')","");

            return docFileTypeMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,"",e);
        }
    }




    /**
     * 获取所有子类的父级类别
     * @return
     * @throws ServiceException
     */
    @Override
    public   JSONObject getParentTypeInf()throws ServiceException{
        JSONObject result=new JSONObject();
        List<DocFileTypeEntity> allTypeList=findList(new DocFileTypeEntity());
        if(allTypeList!=null){

            //封装父级类型
            Map<Object,Object>  groupMap=parmCacheUtil.getDicMap("DOC_TYPE_GROUP");
            JSONObject parent=new JSONObject();
//            for(DocFileTypeEntity docFileType:allTypeList){
//
//                if(StringUtils.isEmpty(docFileType.getTypeClass())){
//                    parent.put(docFileType.getTypeNo(),docFileType);
//                }
//            }





            //为所有的子类 配上父级类型
            for(DocFileTypeEntity docFileType:allTypeList){

                if(StringUtils.isNotEmpty(docFileType.getGroupNo())){
                    String  groupNo=docFileType.getGroupNo();

                    JSONObject groupJson=new JSONObject();
//                    tempData.setParentTypeNo(parentTypeJson.getString("typeNo"));
//                    tempData.setParentTypeName(parentTypeJson.getString("typeName"));

                    if(groupMap!=null && !groupMap.isEmpty()){
                        groupJson.put("typeNo",groupNo);
                        System.out.println("groupNo="+groupNo);
                        System.out.println("value="+groupMap.get(groupNo));
                        groupJson.put("typeName",groupMap.get(groupNo).toString());

                    }

                    result.put(docFileType.getTypeNo(),groupJson);
                }
            }


        }
        return  result;

    }

    /**
     * 获取要件类型大类列表
     * @param queryType  class-获取大类列表 sub-获取子项列表
     * @return
     * @throws ServiceException
     */
    private  List<DocFileTypeEntity> findTypeClassList(String queryType) throws ServiceException {
        try{
            //翻页
            DocFileTypeEntity docFileTypeEntit=new DocFileTypeEntity();
            QueryWrapper<DocFileTypeEntity> queryWrapper = new QueryWrapper<>();
//            mapperUtil.tableQuery(queryWrapper,docFileTypeEntity);
            if("class".equals(queryType)){

                queryWrapper.isNull("type_class");
            }
            if("sub".equals(queryType)){

                queryWrapper.isNotNull("type_class");
            }


            return docFileTypeMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,queryType,e);
        }
    }


    /**
     * 获取所有的要件类型
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray fileTypeArray()throws ServiceException {
        JSONArray result=new JSONArray();
        DocFileTypeEntity docFileTypeEntity = new DocFileTypeEntity();
        List<DocFileTypeEntity> typeList = findList(docFileTypeEntity);
        if(typeList!=null){

            for(DocFileTypeEntity type:typeList){
                result.add(type);
            }
        }
        return result;

    }

    /**
     * 生成 要件类型选择的数据源
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject fileTypeGroupList(DocScFiletypeConfigEntity docScFiletypeConfigEntity)throws ServiceException{
        JSONObject result = new JSONObject();
        DocFileTypeEntity docFileTypeEntity=new DocFileTypeEntity();
        //查询子类
        List<DocFileTypeEntity> typeList= findTypeClassList("sub");
        JSONArray docFileTypeArr=new JSONArray();
        //        let groupData1 = {
//                name: "分组一",
//                data: [
//        { value: "value1", text: "选项1" },
//        { value: "value3", text: "选项3" },
//        { value: "value5", text: "选项5" },
//        { value: "value7", text: "选项7" },
//        { value: "value9", text: "选项9" },
//        ],
        JSONObject tmpResult=new JSONObject();
        if(typeList!=null){

                Map<Object,Object> tagGroup=new HashedMap();

            List<DocFileTypeEntity> classList= findTypeClassList("class");
            DocTypeClassEntity docTypeClassEntity = new DocTypeClassEntity();
            docTypeClassEntity.setUseFlag(CommonConstant.YES_NO_Y);
            List<DocTypeClassEntity> docTypeClassEntities = docTypeClassService.getList(docTypeClassEntity);
            for(DocTypeClassEntity classBean:docTypeClassEntities){
                tagGroup.put(classBean.getTypeNo(),classBean.getTypeName());
            }

            for(DocFileTypeEntity typeEntity:typeList){
                String groupNo=typeEntity.getTypeClass();
                JSONObject option=new JSONObject();
                option.put("value",typeEntity.getTypeNo());
                option.put("text",typeEntity.getTypeName());
                //子类所属多个大类
                if (groupNo.indexOf("|")!=-1){
                    String[] groupNoArr = groupNo.split("\\|");
                    for (int i = 0; i < groupNoArr.length; i++) {
                        String groupNoTmp = groupNoArr[i];
                        if(tmpResult.containsKey(groupNoTmp)){
                            //如果存在该组号，直接加入组内
                            tmpResult.getJSONObject(groupNoTmp).getJSONArray("data").add(option);
                        }else{
                            //如果不存在，创建一个新的分组
                            JSONArray dataArray=new JSONArray();
                            dataArray.add(option);
                            JSONObject groupJson=new JSONObject();
                            groupJson.put("name",tagGroup.get(groupNoTmp));
                            groupJson.put("data",dataArray);
                            tmpResult.put(groupNoTmp,groupJson);
                        }
                    }
                }else {
                    if(tmpResult.containsKey(groupNo)){
                        //如果存在该组号，直接加入组内
                        tmpResult.getJSONObject(groupNo).getJSONArray("data").add(option);
                    }else{
                        //如果不存在，创建一个新的分组
                        JSONArray dataArray=new JSONArray();
                        dataArray.add(option);
                        JSONObject groupJson=new JSONObject();
                        groupJson.put("name",tagGroup.get(groupNo));
                        groupJson.put("data",dataArray);
                        tmpResult.put(groupNo,groupJson);
                    }
                }
            }

            //把jsonObject转换转化成JsonArray
            Iterator iterator=tmpResult.keySet().iterator();
            while(iterator.hasNext()){
                String groupNo=iterator.next().toString();
                docFileTypeArr.add(tmpResult.getJSONObject(groupNo));
            }

            //封装默认值
            List<List<String>>defVal=new ArrayList<>();
            int groupCnt=docFileTypeArr.size();
            for(int i=0;i<groupCnt;i++){
                List<String>tmpList=new ArrayList<>();
                tmpList.add("");
                defVal.add(tmpList);
            }
            List<DocScFiletypeConfigEntity> docScFiletypeConfigEntities = docScFiletypeConfigService.findList(docScFiletypeConfigEntity);
            if(!docScFiletypeConfigEntities.isEmpty()){
                Map<String,String>tmpMap=new HashedMap();
                for(DocScFiletypeConfigEntity tag:docScFiletypeConfigEntities){
                    tmpMap.put(tag.getTypeNo(),tag.getTypeNo());
                }
                for(int i=0;i<groupCnt;i++){
                    JSONArray tmpArray=docFileTypeArr.getJSONObject(i).getJSONArray("data");
                    int tmpSubLen=tmpArray.size();
                    for(int j=0;j<tmpSubLen;j++){
                        String keyNo=tmpArray.getJSONObject(j).getString("value");
                        if(tmpMap.containsKey(keyNo)){
                            defVal.get(i).add(keyNo);
                        }
                    }
                }
            }

            result.put("selectTypeArr",defVal);
        }
        result.put("docFileTypeArr",docFileTypeArr);
        return result;


    }





    @Override
    public void insert(DocFileTypeEntity docFileTypeEntity) throws ServiceException {
        try{

            docFileTypeEntity.setTypeId(UUIDUtil.getUUID());
            docFileTypeEntity.setUseFlag("1");
            String corpId=(String)requestUtil.getUserInfo("corpId");
            docFileTypeEntity.setCorpId(corpId);

            docFileTypeMapper.insert(docFileTypeEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docFileTypeEntity.getTypeNo(),e);
        }
    }

    @Override
    public void update(DocFileTypeEntity docFileTypeEntity) throws ServiceException {
        try{
            docFileTypeMapper.updateById(docFileTypeEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docFileTypeEntity.getTypeNo(),e);
        }
    }

    @Override
    public DocFileTypeEntity findById(String typeNo) throws ServiceException {
        try{
            return docFileTypeMapper.selectById(typeNo);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,typeNo,e);
        }
    }

    @Override
    public void deleteById(String typeNo) throws ServiceException {
        try{
            docFileTypeMapper.deleteById(typeNo);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,typeNo,e);
        }
    }

}