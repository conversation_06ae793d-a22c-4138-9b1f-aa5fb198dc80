/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import lombok.Data;

import java.util.List;

@Data
public class QueryParm {

    private String busRelatedId;
    /**
     * 关联客户号
     */
    private String cusRelatedId;
    /**
     * 关联申请编号
     */
    private String applyRelatedId;
    /**
     * 关联合同编号
     */
    private String constractRelatedId;
    /**
     * 关联借据编号
     */
    private String finRelatedId;

    private String bizNo;

    private String prdUniqueVal;

    // 使用实体中bizNo 、flowNo、nodeNo、prod
    List<DocFileInfEntity> docFileInfEntityList ;
}
