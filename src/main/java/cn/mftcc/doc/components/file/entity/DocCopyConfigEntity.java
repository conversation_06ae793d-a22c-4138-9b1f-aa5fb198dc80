/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 影像资料赋值配置
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-10-13 19:18:03
 */
@Data
@TableName("doc_copy_config")
@ApiModel(value="doc_copy_config", description="影像资料赋值配置")
public class DocCopyConfigEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.UUID)
	@ApiModelProperty(value = "主键")
	private Integer id;
	/**
	 * 客户类型
	 */
	@ApiModelProperty(value = "客户类型")
	private String cusType;
	/**
	 * 客户类型名称
	 */
	@ApiModelProperty(value = "客户类型名称")
	private String cusTypeName;
	/**
	 * 承租人身份影像资料编号
	 */
	@ApiModelProperty(value = "承租人身份影像资料编号")
	private String cusFileTypeNo;
	/**
	 * 承租人身份影像资料名称
	 */
	@ApiModelProperty(value = "承租人身份影像资料名称")
	private String cusFileTypeName;
	/**
	 * 担保人身份影像资料编号
	 */
	@ApiModelProperty(value = "担保人身份影像资料编号")
	private String assureFileTypeNo;
	/**
	 * 担保人身份影像资料名称
	 */
	@ApiModelProperty(value = "担保人身份影像资料名称")
	private String assureFileTypeName;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;

}
