/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.file.entity.DocTypeClassEntity;
import cn.mftcc.doc.components.file.service.DocTypeClassService;

/**
 * 要件类型大类表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-06 18:41:55
 */
@RestController
@RequestMapping("file/docTypeClass")
public class DocTypeClassController {

    @Autowired
    private DocTypeClassService docTypeClassService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocTypeClassEntity docTypeClassEntity) {
        IPage<DocTypeClassEntity> list = this.docTypeClassService.findByPage(docTypeClassEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocTypeClassEntity docTypeClassEntity){
        this.docTypeClassService.insert(docTypeClassEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocTypeClassEntity docTypeClassEntity){
        this.docTypeClassService.update(docTypeClassEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{typeId}")
    public R findById(@PathVariable("typeId") String typeId){
        DocTypeClassEntity docTypeClassEntity = this.docTypeClassService.findById(typeId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docTypeClassEntity);
    }

    @RequestMapping("/deleteById/{typeId}")
    public R deleteById(@PathVariable("typeId") String typeId){
        this.docTypeClassService.deleteById(typeId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}