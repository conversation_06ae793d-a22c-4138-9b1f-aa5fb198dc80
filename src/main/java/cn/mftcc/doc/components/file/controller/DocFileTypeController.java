/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.controller;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.components.file.entity.DocFileTypeEntity;
import cn.mftcc.doc.components.file.entity.DocScFiletypeConfigEntity;
import cn.mftcc.doc.components.file.service.DocFileTypeService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件类型表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 17:19:12
 */
@RestController
@RequestMapping("file/docFileType")
public class DocFileTypeController {

    @Autowired
    private DocFileTypeService docFileTypeService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocFileTypeEntity docFileTypeEntity) {
        IPage<DocFileTypeEntity> list = this.docFileTypeService.findByPage(docFileTypeEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }
    @RequestMapping("/fileTypeGroupList")
    public R fileTypeGroupList(@RequestBody DocScFiletypeConfigEntity docScFiletypeConfigEntity) {
        JSONObject result = this.docFileTypeService.fileTypeGroupList(docScFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", result);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocFileTypeEntity docFileTypeEntity){
        this.docFileTypeService.insert(docFileTypeEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocFileTypeEntity docFileTypeEntity){
        this.docFileTypeService.update(docFileTypeEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{typeNo}")
    public R findById(@PathVariable("typeNo") String typeNo){
        DocFileTypeEntity docFileTypeEntity = this.docFileTypeService.findById(typeNo);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docFileTypeEntity);
    }


    @RequestMapping("/deleteById/{typeNo}")
    public R deleteById(@PathVariable("typeNo") String typeNo){
        this.docFileTypeService.deleteById(typeNo);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}