/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.mapper;

import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 要件信息表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-05-10 15:15:44
 */
@Mapper
public interface DocFileInfMapper extends BaseMapper<DocFileInfEntity> {

    List<DocFileInfEntity> findFileInfListByPactFP(String pactNo);
}
