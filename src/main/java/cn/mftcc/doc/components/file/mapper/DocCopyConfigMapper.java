/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.mapper;

import cn.mftcc.doc.components.file.entity.DocCopyConfigEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 影像资料赋值配置
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-10-13 19:18:03
 */
@Mapper
public interface DocCopyConfigMapper extends BaseMapper<DocCopyConfigEntity> {
	
}
