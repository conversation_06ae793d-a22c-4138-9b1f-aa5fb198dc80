/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.file.entity.DocScFiletypeConfigEntity;

import cn.mftcc.common.exception.ServiceException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 场景的要件类型配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 11:32:22
 */
public interface DocScFiletypeConfigService {

    IPage<DocScFiletypeConfigEntity> findByPage(DocScFiletypeConfigEntity docScFiletypeConfigEntity) throws ServiceException;

    List<DocScFiletypeConfigEntity> findList(DocScFiletypeConfigEntity docScFiletypeConfigEntity) throws ServiceException;

    void insert(DocScFiletypeConfigEntity docScFiletypeConfigEntity) throws ServiceException;

    void insertList(List<DocScFiletypeConfigEntity> docScFiletypeConfigEntityList) throws ServiceException;

    void update(DocScFiletypeConfigEntity docScFiletypeConfigEntity) throws ServiceException;

    DocScFiletypeConfigEntity findById(String scId) throws ServiceException;

    void deleteById(String scId) throws ServiceException;

    JSONObject copyFilesConfigOfPrd( String newPrdId,String motherPrdId)throws  ServiceException;

    JSONArray getRootListByTypeBean(DocScFiletypeConfigEntity typeBean)throws  ServiceException;

    JSONObject delFileNodeSetting(List<Map<String,String>>delNode)throws Exception;

    JSONObject delTemplateNodeSetting(List<Map<String,String>>delNode)throws Exception;

    void delOneFileNodeSetting(Map<String,String>delNode)throws Exception;

    JSONObject getAllFileTypeOfOneBuseness(JSONObject parmJson)throws ServiceException;




}

