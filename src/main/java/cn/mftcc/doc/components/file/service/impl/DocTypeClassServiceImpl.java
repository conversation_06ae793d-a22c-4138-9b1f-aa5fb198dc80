/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service.impl;

import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.bizcommon.utils.UUIDUtil;
import cn.mftcc.common.utils.RequestUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.file.entity.DocTypeClassEntity;
import cn.mftcc.doc.components.file.mapper.DocTypeClassMapper;
import cn.mftcc.doc.components.file.service.DocTypeClassService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

import java.util.List;

/**
 * 要件类型大类表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-06 18:41:55
 */
@Service("docTypeClassService")
@Transactional(rollbackFor = Exception.class)
public class DocTypeClassServiceImpl implements DocTypeClassService {

    @Autowired
    private DocTypeClassMapper docTypeClassMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private RequestUtil requestUtil;

    @Override
    public IPage<DocTypeClassEntity> findByPage(DocTypeClassEntity docTypeClassEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTypeClassEntity> page = new Page<>();
            page.setCurrent(docTypeClassEntity.getPageNo());
            page.setSize(docTypeClassEntity.getPageSize());
            QueryWrapper<DocTypeClassEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docTypeClassEntity);
            return docTypeClassMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTypeClassEntity.getTypeId(),e);
        }
    }

    @Override
    public void insert(DocTypeClassEntity docTypeClassEntity) throws ServiceException {
        try{
            docTypeClassEntity.setTypeId(UUIDUtil.getUUID());
            docTypeClassEntity.setCorpId((String)requestUtil.getUserInfo(CommonConstant.CORP_ID));
            docTypeClassMapper.insert(docTypeClassEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTypeClassEntity.getTypeId(),e);
        }
    }

    @Override
    public void update(DocTypeClassEntity docTypeClassEntity) throws ServiceException {
        try{
            docTypeClassMapper.updateById(docTypeClassEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTypeClassEntity.getTypeId(),e);
        }
    }

    @Override
    public DocTypeClassEntity findById(String typeId) throws ServiceException {
        try{
            return docTypeClassMapper.selectById(typeId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,typeId,e);
        }
    }

    @Override
    public void deleteById(String typeId) throws ServiceException {
        try{
            docTypeClassMapper.deleteById(typeId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,typeId,e);
        }
    }


    @Override
    public  DocTypeClassEntity  findByTypeNo(String typeNo){
        QueryWrapper  queryWrapper = new QueryWrapper();
        queryWrapper.eq("type_no",typeNo);
        DocTypeClassEntity docTypeClassEntity =  docTypeClassMapper.selectOne(queryWrapper);
        return  docTypeClassEntity;
    }

    @Override
    public List<DocTypeClassEntity> getList(DocTypeClassEntity docTypeClassEntity) throws ServiceException {
        try{
            QueryWrapper<DocTypeClassEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.setEntity(docTypeClassEntity);
            return docTypeClassMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTypeClassEntity.getTypeId(),e);
        }
    }
}