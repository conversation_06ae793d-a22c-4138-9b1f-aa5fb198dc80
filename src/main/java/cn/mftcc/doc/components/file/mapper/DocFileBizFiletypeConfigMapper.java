/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.mapper;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.components.file.entity.DocFileBizFiletypeConfigEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * 业务数据的要件类型配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-05-08 14:31:02
 */
@Mapper
public interface DocFileBizFiletypeConfigMapper extends BaseMapper<DocFileBizFiletypeConfigEntity> {
    /**
     * 批量插入要件初始化信息
     * @param docFileBizFiletypeConfigEntities
     */
    void insertBatch(@Param("list") List<DocFileBizFiletypeConfigEntity> docFileBizFiletypeConfigEntities)throws ServiceException;
}
