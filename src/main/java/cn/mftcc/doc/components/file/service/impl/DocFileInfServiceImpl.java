/*
 * Copyright © 2020 北京微金时代科技有限公司 <EMAIL>
 */
package cn.mftcc.doc.components.file.service.impl;

import cn.hutool.core.util.RandomUtil;
import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.bizcommon.utils.BeanCopyUtil;
import cn.mftcc.bizcommon.utils.UUIDUtil;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.DateUtil;
import cn.mftcc.common.utils.MapperUtil;
import cn.mftcc.common.utils.ParmCacheUtil;
import cn.mftcc.common.utils.RequestUtil;
import cn.mftcc.config.feign.dto.ConfigVoucherPushEntityDTO;
import cn.mftcc.cus.feign.dto.CusCustomerDTO;
import cn.mftcc.doc.common.constant.DocConstant;
import cn.mftcc.doc.common.utils.*;
import cn.mftcc.doc.components.file.entity.*;
import cn.mftcc.doc.components.file.mapper.DocFileInfMapper;
import cn.mftcc.doc.components.file.service.*;
import cn.mftcc.doc.feign.client.*;
import cn.mftcc.pledge.feign.dto.PledgeBusRelInfoDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Encoder;

import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 要件信息表
 *
 * <AUTHOR>
 * @email
 * @date 2021-05-10 15:15:44
 */
@Service("docFileInfService")
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DocFileInfServiceImpl implements DocFileInfService {


    @Autowired
    private ParmCacheUtil parmCacheUtil;
    @Autowired
    private RequestUtil requestUtil;
    @Autowired
    private DocFileInfMapper docFileInfMapper;
    @Autowired
    private DocCopyConfigService docCopyConfigService;
    @Autowired
    private LeaseFeiginClient leaseFeiginClient;
    @Autowired
    private CustomerFeignClient customerFeignClient;
    @Autowired
    private DocTypeClassService docTypeClassService;
    @Autowired
    private DocFileTypeService docFileTypeService;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private DocScFiletypeConfigService docScFiletypeConfigService;
    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private PledgeFeignClient pledgeFeignClient;

    @Autowired
    private ConfigFeignClient configFeignClient;
    @Autowired
    private MapperUtil mapperUtil;
    @Value("${mftcc.file.doc-file-upload-path:}")
    private String docFileUploadPath;
    @Value("${mftcc.template.officeServer-path:}")
    private String officeServerPath;
    @Value("${mftcc.file.doc-office-open-url:}")
    private String docOfficeOpenUrl;
    @Value("${mftcc.template.server:}")
    private String gateWayAddr;
    @Value("${spring.application.name:}")
    private String serverName;
    @Value("${mftcc.file.doc-show-type:}")
    private String docShowType;


    /**
     * office是否在线预览标识 0-否 1-是
     */
    @Value("${mftcc.file.doc-office-view:}")
    private String docOfficeView;
    /**
     * 传到外部服务器方式 obs-obs上传 ftp-ftp上传 oss-oss存储
     */
    @Value("${mftcc.file.upload-outter-function:}")
    private String uploadOutterFunction;

    @Autowired
    private ElinkApiFeignClient elinkApiFeignClient;

    @Override
    public IPage<DocFileInfEntity> findByPage(DocFileInfEntity docFileInfEntity) throws ServiceException {
        try {
            //翻页
            IPage<DocFileInfEntity> page = new Page<>();
            page.setCurrent(docFileInfEntity.getPageNo());
            page.setSize(docFileInfEntity.getPageSize());
            QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper, docFileInfEntity);
            return docFileInfMapper.selectPage(page, queryWrapper);
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, docFileInfEntity.getFileId(), e);
        }
    }

    /**
     * 获取要件的文件流
     *
     * @param fileId
     * @return
     * @throws ServiceException
     */
    @Override
    public InputStream getFileStream(String fileId) throws ServiceException, IOException {
//        uploadOutterFunction = "oss";

        DocFileInfEntity fileInfEntity = findById(fileId);

        InputStream inputStream = null;

        String filePath = "";
        if ("obs".equals(uploadOutterFunction)) {
            String obsId = fileInfEntity.getObsId();
            filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
            try {
                //inputStream = new URL(filePath).openStream();
                URL url = new URL(filePath);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setConnectTimeout(5 * 1000);
                inputStream = conn.getInputStream();
            } catch (Exception e) {
                throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileId, e);
            }
        } else if ("oss".equals(uploadOutterFunction)) {

            // 填写Bucket名称，例如examplebucket。

            // 读取文件内容。
            inputStream = ossConfig.readOss(fileInfEntity.getFilePath());

        } else {
            filePath = docFileUploadPath + File.separator + fileInfEntity.getFilePath();
            try {
                inputStream = new FileInputStream(filePath);
            } catch (Exception e) {
                throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileId, e);
            }
        }
        return inputStream;


    }

    /**
     * 获取图片要件缩略图的文件流
     *
     * @param fileId
     * @return
     * @throws ServiceException
     */
    @Override
    public InputStream getFileThumbStream(String fileId) throws ServiceException {

        DocFileInfEntity fileInfEntity = findById(fileId);
        String filePath = docFileUploadPath + File.separator + fileInfEntity.getFilePath();
        InputStream instream = null;
        try {
            if ("oss".equals(uploadOutterFunction)) {
                instream = ossConfig.readOss(fileInfEntity.getFilePath());
            } else {
                instream = new FileInputStream(filePath);
            }
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileId, e);
        }
        return instream;


    }


    /**
     * 获取某一业务某个类型下的所有要件
     *
     * @param docFileInfEntity
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocFileInfEntity> findFileInfList(DocFileInfEntity docFileInfEntity) throws ServiceException {
        try {
            QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
            // 归档参数处理
            String flowNo = docFileInfEntity.getFlowNo();
            if(StringUtils.isNotBlank(flowNo) && Constant.ARCHIVE_SHOW_FILE.equals(flowNo)){
                queryWrapper.in("biz_no",docFileInfEntity.getBizNoSet());
            }else{
                queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getPrdUniqueVal()), "prd_unique_val", docFileInfEntity.getPrdUniqueVal());
                queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getFlowNo()), "flow_no", docFileInfEntity.getFlowNo());
                queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getNodeNo()), "node_no", docFileInfEntity.getNodeNo());
                queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getBizNo()), "biz_no", docFileInfEntity.getBizNo());
            }
            queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getTypeNo()), "type_no", docFileInfEntity.getTypeNo());
            queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getBusRelatedId()), "bus_related_id", docFileInfEntity.getBusRelatedId());
            queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getApplyRelatedId()), "apply_related_id", docFileInfEntity.getApplyRelatedId());
            queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getConstractRelatedId()), "constract_related_id", docFileInfEntity.getConstractRelatedId());
            queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getCusRelatedId()), "cus_related_id", docFileInfEntity.getCusRelatedId());
            queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getFinRelatedId()), "fin_related_id", docFileInfEntity.getFinRelatedId());
            if (StringUtils.isNotEmpty(docFileInfEntity.getOrderBy())) {
                if ("desc".equals(docFileInfEntity.getOrderBy())) {

                    queryWrapper.orderByDesc("create_time");
                } else {
                    queryWrapper.orderByAsc("create_time");
                }
            } else {

                queryWrapper.orderByAsc("create_time");
            }
            return docFileInfMapper.selectList(queryWrapper);

        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, docFileInfEntity.getFileId(), e);
        }
    }

    /**
     * 获取某一业务某个类型下的所有要件
     *
     * @param parmObj{ bizNo:关联业务主键
     *                 prdUniqueVal:产品唯一标识
     *                 flowNo:流程标识
     *                 nodeNo:节点标识
     *                 typeNo:要件类型标识
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray getAllFilesByNode(JSONObject parmObj) throws ServiceException {
        JSONArray result = new JSONArray();
        DocFileInfEntity quereyBean = new DocFileInfEntity();
        String bizNo = parmObj.getString("bizNo");
        String prdUniqueVal = parmObj.getString("prdUniqueVal");
        String flowNo = parmObj.getString("flowNo");
        String nodeNo = parmObj.getString("nodeNo");
        String typeNo = parmObj.getString("typeNo");
        quereyBean.setBizNo(bizNo);
        quereyBean.setPrdUniqueVal(prdUniqueVal);
        quereyBean.setFlowNo(flowNo);
        quereyBean.setNodeNo(nodeNo);
        quereyBean.setTypeNo(typeNo);
        List<DocFileInfEntity> tmpList = findFileInfList(quereyBean);
        if (tmpList != null) {
            for (DocFileInfEntity docInf : tmpList) {
                result.add(docInf);
            }
        }
        return result;

    }

    @Override
    public JSONObject officeFileShowInit() {
        JSONObject result = new JSONObject();
        try {

            String docKey = RandomUtil.randomString(20);

            result.put("gateWayAddr", gateWayAddr);
            result.put("serverName", serverName);
            result.put("officeDocKey", docKey);
            //result.put("officeUrl",fileUrlPath);
            result.put("officeKey", UUIDUtil.getUUID());
            //result.put("officeSuffix",suffix);
            result.put("officeServie", officeServerPath);

        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, "", e);
        }
        MFLogger.info(JSONObject.toJSONString(result));
        return result;
    }


    /**
     * 获取某一业务某个类型下是否有要件
     *
     * @param parmMap{ bizNo:关联业务主键
     *                 prdUniqueVal:产品唯一标识
     *                 flowNo:流程标识
     *                 nodeNo:节点标识
     *                 typeNo:要件类型标识
     *                 <p>
     *                 }
     * @return {
     * code：0000
     * fileCnt：文件的数量
     * * }
     * @throws ServiceException
     */
    @Override
    public JSONObject ifHasFiles(Map<String, String> parmMap) throws ServiceException {
        JSONObject result = new JSONObject();
        String bizNo = parmMap.get("bizNo");
        String prdUniqueVal = parmMap.get("prdUniqueVal");
        String flowNo = parmMap.get("flowNo");
        String nodeNo = parmMap.get("nodeNo");
        String typeNo = parmMap.get("typeNo");


        DocFileInfEntity quereyBean = new DocFileInfEntity();
        quereyBean.setBizNo(bizNo);
        quereyBean.setPrdUniqueVal(prdUniqueVal);
        quereyBean.setFlowNo(flowNo);
        quereyBean.setNodeNo(nodeNo);
        quereyBean.setTypeNo(typeNo);
        List<DocFileInfEntity> tmpList = findFileInfList(quereyBean);
        if (tmpList != null) {
            result.put("fileCnt", String.valueOf(tmpList.size()));

        } else {
            result.put("fileCnt", "0");
        }
        result.put("code", "0000");
        return result;
    }

    /**
     * 是否节点下所有的要件类型都已上传要件
     *
     * @param parmMap{ *      bizNo:关联业务主键
     *                 *      prdUniqueVal:产品唯一标识
     *                 *      flowNo:流程标识
     *                 *      nodeNo:节点标识
     *                 }
     * @throws ServiceException
     * @return{ {
     * code: 0000 都有了 1111 存在没上传的
     * msg:
     * }
     * }
     */
    @Override
    public JSONObject ifEachTypeHasFileOfNode(Map<String, String> parmMap) throws ServiceException {
        JSONObject result = new JSONObject();
        result.put("code", "0000");
        result.put("msg", "都已上传");
        DocFileBizFiletypeConfigEntity qBean = new DocFileBizFiletypeConfigEntity();
        qBean.setBizNo(parmMap.get("bizNo"));
        qBean.setPrdUniqueVal(parmMap.get("prdUniqueVal"));
        qBean.setFlowNo(parmMap.get("flowNo"));
        qBean.setNodeNo(parmMap.get("nodeNo"));
        qBean.setIfUpload(CommonConstant.YES_NO_Y);
        List<DocFileBizFiletypeConfigEntity> fileTypeList = docFileBizFiletypeConfigService.findList(qBean);
        if (fileTypeList != null) {
            for (DocFileBizFiletypeConfigEntity typeBean : fileTypeList) {
                String typeNo = typeBean.getTypeNo();
                parmMap.put("typeNo", typeNo);
                JSONObject checkMap = ifHasFiles(parmMap);
                if (checkMap.getIntValue("fileCnt") == 0) {

                    result.put("code", "1111");
                    result.put("msg", "存在没有上传的");
                    break;
                }

            }
        }
        return result;
    }

    /**
     * 获取某一业务某个类型下的所有要件
     *
     * @param parmMap{ bizNo:关联业务主键
     *                 prdUniqueVal:产品唯一标识
     *                 flowNo:流程标识
     *                 nodeNo:节点标识
     *                 typeNo:要件类型标识
     *                 <p>
     *                 }
     * @return jsonArray
     * @throws ServiceException
     */
    @Override
    public JSONArray getFileList(Map<String, String> parmMap) throws ServiceException {
        JSONArray result = new JSONArray();
        String bizNo = parmMap.get("bizNo");
        String prdUniqueVal = parmMap.get("prdUniqueVal");
        String flowNo = parmMap.get("flowNo");
        String nodeNo = parmMap.get("nodeNo");
        String typeNo = parmMap.get("typeNo");
        //获取文件类型所属大类的map
        List<DocFileTypeEntity> fileTypeList = docFileTypeService.findList(new DocFileTypeEntity());
        Map<String, String> fileTypeMap = new HashMap<>();
        for (DocFileTypeEntity type : fileTypeList) {
            fileTypeMap.put(type.getTypeNo(), type.getTypeClass());
        }


        DocFileInfEntity quereyBean = new DocFileInfEntity();
        quereyBean.setBizNo(bizNo);
        quereyBean.setPrdUniqueVal(prdUniqueVal);
        quereyBean.setFlowNo(flowNo);
        quereyBean.setNodeNo(nodeNo);
        quereyBean.setTypeNo(typeNo);
        List<DocFileInfEntity> tmpList = findFileInfList(quereyBean);
        if (tmpList != null) {
            for (DocFileInfEntity file : tmpList) {
                file.setTypeClass(fileTypeMap.get(file.getTypeNo()));
                result.add(file);
            }


        }

        return result;
    }


    /**
     * 获取要件类型的最新上传的一个要件
     *
     * @param parmJson{ bizNo:关联业务主键
     *                  prdUniqueVal:产品唯一标识
     *                  flowNo:流程标识
     *                  nodeNo:节点标识
     *                  typeNo:要件类型标识
     *                  }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getLastFileOfOneType(JSONObject parmJson) throws ServiceException {
        JSONObject result = new JSONObject();
        DocFileInfEntity lastFile = null;
        DocFileInfEntity quereyBean = new DocFileInfEntity();
        quereyBean.setBizNo(parmJson.getString("bizNo"));
        quereyBean.setPrdUniqueVal(parmJson.getString("prdUniqueVal"));
        quereyBean.setFlowNo(parmJson.getString("flowNo"));
        quereyBean.setNodeNo(parmJson.getString("nodeNo"));
        quereyBean.setTypeNo(parmJson.getString("typeNo"));
        quereyBean.setOrderBy("desc");
        List<DocFileInfEntity> tmpList = findFileInfList(quereyBean);
        if (tmpList != null) {
            if (tmpList.size() > 0) {
                lastFile = tmpList.get(0);
            }
        }
        if (lastFile != null) {
            result.put("code", "0000");
            String docFileUrl = gateWayAddr + serverName + "/file/docFileInf/getFileStream/" + lastFile.getFileId();
            result.put("fileUrl", docFileUrl);
        } else {
            result.put("code", "1111");
            result.put("msg", "没有上传要件");
        }
        return result;
    }

    @Override
    public void insert(DocFileInfEntity docFileInfEntity) throws ServiceException {
        try {
            try {

                docFileInfEntity.setCreateUserNo(requestUtil.getUserInfo(DocConstant.OP_NO) == null ? null : (String) requestUtil.getUserInfo(DocConstant.OP_NO));
                docFileInfEntity.setCreateUserName(requestUtil.getUserInfo(DocConstant.OP_NAME) == null ? null : (String) requestUtil.getUserInfo(DocConstant.OP_NAME));
                docFileInfEntity.setCreateDeptNo(requestUtil.getUserInfo(DocConstant.BR_NO) == null ? null : (String) requestUtil.getUserInfo(DocConstant.BR_NO));
                docFileInfEntity.setCreateDeptName(requestUtil.getUserInfo(DocConstant.BR_NAME) == null ? null : (String) requestUtil.getUserInfo(DocConstant.BR_NAME));
                docFileInfEntity.setCorpId(requestUtil.getUserInfo(DocConstant.CORP_ID) == null ? null : (String) requestUtil.getUserInfo(DocConstant.CORP_ID));
            } catch (Exception e) {
                MFLogger.error("插入要件详情表错误", e);
            }
            docFileInfMapper.insert(docFileInfEntity);
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR, docFileInfEntity.getFileId(), e);
        }
    }

    @Override
    public void update(DocFileInfEntity docFileInfEntity) throws ServiceException {
        try {
            docFileInfMapper.updateById(docFileInfEntity);
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR, docFileInfEntity.getFileId(), e);
        }
    }

    @Override
    public void updateByBizNo(DocFileInfEntity docFileInfEntity) throws ServiceException {
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(docFileInfEntity.getFileId()),"file_id",docFileInfEntity.getFileId());
        queryWrapper.eq("biz_no",docFileInfEntity.getBizNo());
        queryWrapper.eq("type_no",docFileInfEntity.getTypeNo());
        queryWrapper.eq("prd_unique_val",docFileInfEntity.getPrdUniqueVal());
        docFileInfMapper.update(docFileInfEntity,queryWrapper);
    }

    @Override
    public DocFileInfEntity findById(String fileId) throws ServiceException {
        try {
            DocFileInfEntity result = docFileInfMapper.selectById(fileId);
            if (result != null) {
                String fileType = "";
                if ("obs".equals(uploadOutterFunction)) {
                    fileType = new FileUtils().getFileType(result.getObsId());
                } else {
                    fileType = new FileUtils().getFileType(result.getFilePath());
                }
                result.setFileType(fileType);
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileId, e);
        }
    }


    /**
     * 根据id读取文件base64信息
     *
     * @param fileId：要件流水号
     * @return
     * @throws ServiceException
     */
    @Override
    public String readFileBase64(String fileId) throws ServiceException {
        String fieBase64 = "";
        try {
            DocFileInfEntity tmpObj = findById(fileId);

            if ("obs".equals(uploadOutterFunction)) {
                String obsId = tmpObj.getObsId();
                String fileUrlPath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                fieBase64 = FileUtils.encodeImageToBase64(fileUrlPath);
            } else if ("oss".equals(uploadOutterFunction)) {
                InputStream inputStream = ossConfig.readOss(tmpObj.getFilePath());
                //得到图片的二进制数据，以二进制封装得到数据，具有通用性
                ByteArrayOutputStream outStream = new ByteArrayOutputStream();
                Thumbnails.of(inputStream).scale(0.5f).toOutputStream(outStream);
                //创建一个Buffer字符串
//                byte[] buffer = new byte[1024];
//                //每次读取的字符串长度，如果为-1，代表全部读取完毕
//                int len = 0;
//                //使用一个输入流从buffer里把数据读取出来
//                while ((len = inputStream.read(buffer)) != -1) {
//                    //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
//                    outStream.write(buffer, 0, len);
//                }
                //关闭输入流
                inputStream.close();
                byte[] data = outStream.toByteArray();
                //对字节数组Base64编码
                BASE64Encoder encoder = new BASE64Encoder();
                fieBase64 = encoder.encode(data);
            } else {
                String fileUrlPath = docFileUploadPath + File.separator + tmpObj.getFilePath();
                fieBase64 = FileUtils.encodeBase64File(fileUrlPath);
            }

        } catch (Exception e) {
            MFLogger.error("读取文件Base64信息失败 fileid=" + fileId, e);
            throw new ServiceException("读取文件Base64信息", fileId, e);
        }
        return fieBase64;
    }


    /**
     * 处理普通文件转化base64字节
     * @param fileId
     * @return
     * @throws ServiceException
     */
    @Override
    public String ordinaryReadFileBase64(String fileId) throws ServiceException {
        String fieBase64 = "";
        try {
            DocFileInfEntity tmpObj = findById(fileId);

            if ("obs".equals(uploadOutterFunction)) {
                String obsId = tmpObj.getObsId();
                String fileUrlPath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                fieBase64 = FileUtils.encodeImageToBase64(fileUrlPath);
            } else if ("oss".equals(uploadOutterFunction)) {
                InputStream inputStream = ossConfig.readOss(tmpObj.getFilePath());
                //得到图片的二进制数据，以二进制封装得到数据，具有通用性
                ByteArrayOutputStream outStream = new ByteArrayOutputStream();
                //创建一个Buffer字符串
                byte[] buffer = new byte[1024];
                //每次读取的字符串长度，如果为-1，代表全部读取完毕
                int len = 0;
                //使用一个输入流从buffer里把数据读取出来
                while ((len = inputStream.read(buffer)) != -1) {
                    //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                    outStream.write(buffer, 0, len);
                }
                //关闭输入流
                inputStream.close();
                byte[] data = outStream.toByteArray();
                //对字节数组Base64编码
                BASE64Encoder encoder = new BASE64Encoder();
                fieBase64 = encoder.encode(data);
            } else {
                String fileUrlPath = docFileUploadPath + File.separator + tmpObj.getFilePath();
                fieBase64 = FileUtils.encodeBase64File(fileUrlPath);
            }

        } catch (Exception e) {
            MFLogger.error("读取文件Base64信息失败 fileid=" + fileId, e);
            throw new ServiceException("读取文件Base64信息", fileId, e);
        }
        return fieBase64;
    }

    /**
     * 读取orc信息
     *
     * @param parmMap{ fileId：要件流水号
     *                 busReqNo：业务系统请求编号(可不传)
     *                 category：识别的影像类型 id_card-身份证 bank_card-银行卡影像 vehicle_invoice-发票（机动车统一发票，增值税专用发票、普票、电子发票）business_license-营业执照
     *                 side：需有有正反面参数时使用，可用值 front/back
     *                 }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject readOCRInfo(Map<String, Object> parmMap) throws ServiceException {
        JSONObject result = new JSONObject();
        try {
            JSONObject requestJson = new JSONObject();
            requestJson.put("callNo", "OCR0001");
            String busReqNo = (String) parmMap.get("busReqNo");
            if (StringUtils.isNotEmpty(busReqNo)) {

                requestJson.put("busReqNo", busReqNo);
            }

            String fileId = (String) parmMap.get("fileId");
            DocFileInfEntity tmpObj = findById(fileId);

            String fieBase64 = "";
            if ("obs".equals(uploadOutterFunction)) {
                String obsId = tmpObj.getObsId();
                String fileUrlPath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                fieBase64 = FileUtils.encodeImageToBase64(fileUrlPath);
            } else {
                String fileUrlPath = docFileUploadPath + File.separator + tmpObj.getFilePath();

                fieBase64 = FileUtils.encodeBase64File(fileUrlPath);
            }

            JSONObject paramData = new JSONObject();
            paramData.put("file", fieBase64);
            String category = (String) parmMap.get("category");
            paramData.put("category", category);
            paramData.put("asyncState", "false");
            String side = (String) parmMap.get("side");
            if (StringUtils.isNotEmpty(side)) {
                paramData.put("side", side);
            }
            requestJson.put("paramData", paramData);
            System.out.println("参数值：" + requestJson.toJSONString());
            String queryResult = elinkApiFeignClient.itoData(requestJson);
            result = JSONObject.parseObject(queryResult).getJSONObject("data");

        } catch (Exception e) {
            result.put("code", "ERROR");
            result.put("msg", "运行时错误");
            MFLogger.error("读取orc信息 fileid=" + (String) parmMap.get("fileId"), e);
            throw new ServiceException("读取orc信息", (String) parmMap.get("fileId"), e);
        }

        return result;
    }

    /**
     * 生成文件路径
     *
     * @param parmJson{ appName  微服务名称
     *                  corpId  机构编号
     *                  prdUniqueVal 产品唯一编号
     *                  flowNo  流程编号
     *                  nodeNo 节点编号
     *                  bizNo 业务编号
     *                  typeNo 要件二级类型编号
     *                  fileName 文件名称
     *                  }
     * @return
     * @throws ServiceException
     */
    private String getDbSavePath(JSONObject parmJson) throws ServiceException {

        String appName = parmJson.getString("appName");
        String corpId = parmJson.getString("corpId");
        String prdUniqueVal = parmJson.getString("prdUniqueVal");
        String flowNo = parmJson.getString("flowNo");
        String nodeNo = parmJson.getString("nodeNo");
        String bizNo = parmJson.getString("bizNo");
        String typeNo = parmJson.getString("typeNo");
        String fileName = parmJson.getString("fileName");
        StringBuilder tmpDbPath = new StringBuilder("");
        if (StringUtils.isNotEmpty(corpId)) {
            tmpDbPath.append(corpId);
            tmpDbPath.append(File.separator);
        }
        if (StringUtils.isNotEmpty(appName)) {
            tmpDbPath.append(appName);
            tmpDbPath.append(File.separator);
        }
        if (StringUtils.isNotEmpty(prdUniqueVal)) {
            tmpDbPath.append(prdUniqueVal).append(File.separator);
        }
        if (StringUtils.isNotEmpty(flowNo)) {
            tmpDbPath.append(flowNo).append(File.separator);
        }
        if (StringUtils.isNotEmpty(nodeNo)) {
            tmpDbPath.append(nodeNo).append(File.separator);
        }
        if (StringUtils.isNotEmpty(typeNo)) {
            tmpDbPath.append(typeNo).append(File.separator);
        }


        return tmpDbPath.toString();
    }


    /**
     * 通过base64串 生成要件
     *
     * @param fileInf{ uploadFileName：文件名称
     *                 base64Str：base64Str
     *                 uploadParm：{
     *                 cusRelatedId:
     *                 busRelatedId
     *                 }
     *                 appName  微服务名称
     *                 corpId  机构编号
     *                 prdUniqueVal 产品唯一编号
     *                 flowNo  流程编号
     *                 nodeNo 节点编号
     *                 bizNo 业务编号
     *                 typeNo 要件二级类型编号
     *                 <p>
     *                 <p>
     *                 }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject addFileByBase64(JSONObject fileInf) throws ServiceException, IOException {
        JSONObject result = new JSONObject();
        result.put("code", "1111");

        String uploadFileName = fileInf.getString("uploadFileName");
        String base64Str = fileInf.getString("base64Str");
        JSONObject uploadParmJson = fileInf.getJSONObject("uploadParm");

        String corpId = fileInf.getString("corpId");
        String prdUniqueVal = fileInf.getString("prdUniqueVal");
        String flowNo = fileInf.getString("flowNo");
        String nodeNo = fileInf.getString("nodeNo");
        String typeNo = fileInf.getString("typeNo");
        String bizNo = fileInf.getString("bizNo");
//            String fileName = fileInf.getString("fileName");
        String tmpAry[] = uploadFileName.split("\\.");
        String suffix = new FileUtils().getFileSuffixName(uploadFileName);//文件后缀名
        String originalName = tmpAry[0];//文件的原始名称
        String newFileName = cn.mftcc.common.utils.UUIDUtil.getUUID() + "." + suffix;//保存的目标文件的物理名称。
        String dbPath = getDbSavePath(fileInf);//获取数据库路径
        String savePath = docFileUploadPath + dbPath + newFileName;//最终的物理路径
        DocFileInfEntity addBean = new DocFileInfEntity();
        addBean.setCorpId(corpId);
        addBean.setPrdUniqueVal(prdUniqueVal);
        addBean.setFlowNo(flowNo);
        addBean.setNodeNo(nodeNo);
        addBean.setTypeNo(typeNo);
        addBean.setBizNo(bizNo);
        FileUtils.base64ToFile(base64Str, newFileName, docFileUploadPath + dbPath);

        addBean.setFileId(cn.mftcc.common.utils.UUIDUtil.getUUID());
        addBean.setOrigin("1");
        addBean.setFileName(originalName);
        BigDecimal fileSize = new FileUtils().getFileSize(savePath);
        addBean.setFileSize(fileSize);
        addBean.setFilePath(dbPath + newFileName);


        if (uploadParmJson != null) {

            if (uploadParmJson.containsKey("cusRelatedId")) {
                addBean.setCusRelatedId(uploadParmJson.getString("cusRelatedId"));
            }
            if (uploadParmJson.containsKey("busRelatedId")) {
                addBean.setBusRelatedId(uploadParmJson.getString("busRelatedId"));
            }
            if (uploadParmJson.containsKey("applyRelatedId")) {
                addBean.setApplyRelatedId(uploadParmJson.getString("applyRelatedId"));
            }
            if (uploadParmJson.containsKey("constractRelatedId")) {
                addBean.setConstractRelatedId(uploadParmJson.getString("constractRelatedId"));
            }
            if (uploadParmJson.containsKey("finRelatedId")) {
                addBean.setFinRelatedId(uploadParmJson.getString("finRelatedId"));
            }

        }
        Date date = new Date();
        addBean.setCreateTime(date);
        addBean.setUpdateTime(date);
//                addBean.setCreateUserNo(String.valueOf(requestUtil.getUserInfo("opNo")));

        DocFileTypeEntity _docFileTypeEntity = docFileTypeService.findById(addBean.getTypeNo());
        if (_docFileTypeEntity != null) {
            addBean.setParentTypeNo(_docFileTypeEntity.getGroupNo());
            addBean.setTypeName(_docFileTypeEntity.getTypeName());
        }
        addBean.setCreateUserNo((String) requestUtil.getUserInfo("opNo"));
        addBean.setCreateUserName((String) requestUtil.getUserInfo("opName"));
        addBean.setCreateDeptNo((String) requestUtil.getUserInfo("brNo"));
        addBean.setCreateDeptName((String) requestUtil.getUserInfo("brName"));

        //生成为物理文件
        // 检测是否存在目录
        File dest0 = new File(docFileUploadPath + dbPath);
        File dest = new File(dest0, newFileName);
        if (!dest0.getParentFile().exists()) {
            dest0.getParentFile().mkdirs();
            //检测文件是否存在
        }
        if (!dest.exists()) {
            dest.mkdirs();
        }


        String fileType = "";
        try {

            InputStream is = new FileInputStream(savePath);
            fileType = FileTypeUtil.getFileTypeByFile(is);
            if (is != null) {
                is.close();
            }
        } catch (Exception e) {
            throw new ServiceException("", "", e);
        }


        String fileSuffix = fileType.toLowerCase();
        String thumbName = "";
        if ("bmp".equals(fileSuffix) || "gif".equals(fileSuffix) || "jpeg".equals(fileSuffix) || "jpg".equals(fileSuffix)
                || "png".equals(fileSuffix) || "tiff".equals(fileSuffix) || "tga".equals(fileSuffix) || "svg".equals(fileSuffix)
                || "psd".equals(fileSuffix) || "eps".equals(fileSuffix) || "ufo".equals(fileSuffix)) {
            //如果是图片生成缩略图文件
            thumbName = "thumb_" + newFileName;
            String thumbPath = dbPath + "thumbnail" + File.separator + thumbName;
            String saveThumbPath = docFileUploadPath + thumbPath;//最终的物理缩略图路径
            addBean.setThumbnailPath(thumbPath);
            File thumbFile = new File(docFileUploadPath + dbPath + "thumbnail");
            if (!thumbFile.exists()) {
                thumbFile.mkdirs();
                //检测文件是否存在
            }
            new ImgCompress().thumbnailImage(savePath, 52, 52, saveThumbPath, true);

        }

        docFileInfMapper.insert(addBean);
        result.put("code", "0000");
        return result;

    }


    /**
     * 获取某个节点下面的所有要件base64Str
     *
     * @param parm{ bizNo:业务编号
     *              prdUniqueId:产品编号
     *              flowNo:流程编号
     *              nodeNo:节点编号
     *              }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray getFilesBase64Str(JSONObject parm) throws ServiceException {
        DocFileInfEntity quereyBean = new DocFileInfEntity();
        JSONArray result = new JSONArray();
        quereyBean.setBizNo(parm.getString("bizNo"));
        quereyBean.setPrdUniqueVal(parm.getString("prdUniqueId"));
        quereyBean.setFlowNo(parm.getString("flowNo"));
        quereyBean.setNodeNo(parm.getString("nodeNo"));

        //查询出来待拷贝的组件
        try {

            List<DocFileInfEntity> originalFiles = findFileInfList(quereyBean);
            if (originalFiles != null & originalFiles.size() > 0) {
                for (DocFileInfEntity docInfo : originalFiles) {
                    String fileUrlPath = docFileUploadPath + File.separator + docInfo.getFilePath();

                    String fieBase64 = FileUtils.encodeBase64File(fileUrlPath);
                    docInfo.setBase64Str(fieBase64);
                    result.add(docInfo);
                }
            }
        } catch (Exception e) {
            MFLogger.error("获取某个节点下面的所有要件base64Str,参数：" + parm.toJSONString(), e);
            throw new ServiceException("获取某个节点下面的所有要件base64Str", parm.toJSONString(), e);

        }
        return result;
    }

    /**
     * 把一个业务的要件拷贝到另外一个业务中（两个业务的层级必须保持一直）
     *
     * @param parmMap{ originalBizNo:原始业务编号
     *                 originalprdUniqueVal:原始产品编号
     *                 originalFlowNo:原始流程编号
     *                 originalNodeNo:原始节点编号
     *                 originalTypeNo:原始类型编号
     *                 targetBizNo:目标业务编号
     *                 <p>
     *                 }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject copyFile(Map<String, String> parmMap) throws ServiceException {
        JSONObject result = new JSONObject();
        try {

            String bizNo = parmMap.get("originalBizNo");
            String prdUniqueVal = parmMap.get("originalprdUniqueVal");
            String flowNo = parmMap.get("originalFlowNo");
            String nodeNo = parmMap.get("originalNodeNo");
            String typeNo = parmMap.get("originalTypeNo");
            String targetBizNo = parmMap.get("targetBizNo");
            String relMark = parmMap.get("relMark");


            DocFileInfEntity quereyBean = new DocFileInfEntity();
            quereyBean.setBizNo(bizNo);
            quereyBean.setPrdUniqueVal(prdUniqueVal);
            quereyBean.setFlowNo(flowNo);
            quereyBean.setNodeNo(nodeNo);
            quereyBean.setTypeNo(typeNo);
            //查询出来待拷贝的组件
            List<DocFileInfEntity> originalFiles = findFileInfList(quereyBean);
            if (originalFiles != null & originalFiles.size() > 0) {
                for (DocFileInfEntity tmpBean : originalFiles) {
                    if (StringUtils.isNotEmpty(relMark) && "0".equals(relMark)) {
                        //此时删除关联信息
                        tmpBean.setBusRelatedId("");
                        tmpBean.setCusRelatedId("");
                        tmpBean.setCusRelatedId("");
                        tmpBean.setApplyRelatedId("");
                        tmpBean.setConstractRelatedId("");
                        tmpBean.setFinRelatedId("");
                    }
                    tmpBean.setBizNo(targetBizNo);
                    tmpBean.setFileId(UUIDUtil.getUUID());
                    Date curDate = new Date();
                    tmpBean.setCreateTime(curDate);
                    tmpBean.setUpdateTime(curDate);
                    insert(tmpBean);
                }

                result.put("code", "0000");
                result.put("msg", "拷贝成功");
            } else {

                result.put("code", "2222");
                result.put("msg", "没有要拷贝的要件");


            }


        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error("读取orc信息 fileid=" + (String) parmMap.get("fileId"), e);
        }

        return result;

    }

    /**
     * 要件拷贝到其他目录下
     *
     * @param parmArray {{
     *                  <p>
     *                  orignalBizNo: 源流水号
     *                  orignalPrdId:源产品编号
     *                  orignalFlowNo:源流程编号
     *                  orignalNodeNo:源节点编号
     *                  targetBizNo: 目标流水号
     *                  targetPrdId:目标产品编号
     *                  targetFlowNo:目标流程编号
     *                  targetNodeNo:目标节点编号
     *                  <p>
     *                  }]
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject copyFileToOtherFolders(JSONArray parmArray) throws ServiceException {

        JSONObject result = new JSONObject();
        try {
            if (parmArray != null) {
                if (parmArray.size() > 0) {
                    for (int i = 0; i < parmArray.size(); i++) {
                        JSONObject tmpObj = parmArray.getJSONObject(i);
                        copyFileToOtherFolder(tmpObj);
                    }
                }
            }

        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error("要件拷贝到其他目录下" + parmArray.toJSONString(), e);
        }

        return result;
    }

    /**
     * 把一个业务的要件拷贝到另外一个业务中 根据prdUniqueVal（两个业务的层级必须保持一直）
     *
     * @param parmMap{ originalBizNo:原始业务编号
     *                 originalprdUniqueVal:原始产品编号
     *                 originalFlowNo:原始流程编号
     *                 originalNodeNo:原始节点编号
     *                 originalTypeNo:原始类型编号
     *                 targetPrdUniqueVal:目标产品编号
     *                 <p>
     *                 }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject copyFileByPrdUniqueVal(Map<String, String> parmMap) throws ServiceException {
        JSONObject result = new JSONObject();
        try {

            String bizNo = parmMap.get("originalBizNo");
            String prdUniqueVal = parmMap.get("originalPrdUniqueVal");
            String flowNo = parmMap.get("originalFlowNo");
            String nodeNo = parmMap.get("originalNodeNo");
            String typeNo = parmMap.get("originalTypeNo");
            String targetPrdUniqueVal = parmMap.get("targetPrdUniqueVal");
            String relMark = parmMap.get("relMark");


            DocFileInfEntity quereyBean = new DocFileInfEntity();
            quereyBean.setBizNo(bizNo);
            quereyBean.setPrdUniqueVal(prdUniqueVal);
            quereyBean.setFlowNo(flowNo);
            quereyBean.setNodeNo(nodeNo);
            quereyBean.setTypeNo(typeNo);
            //查询出来待拷贝的组件
            List<DocFileInfEntity> originalFiles = findFileInfList(quereyBean);
            if (originalFiles != null & originalFiles.size() > 0) {
                for (DocFileInfEntity tmpBean : originalFiles) {
                    if (StringUtils.isNotEmpty(relMark) && "0".equals(relMark)) {
                        //此时删除关联信息
                        tmpBean.setBusRelatedId("");
                        tmpBean.setCusRelatedId("");
                        tmpBean.setCusRelatedId("");
                        tmpBean.setApplyRelatedId("");
                        tmpBean.setConstractRelatedId("");
                        tmpBean.setFinRelatedId("");
                    }
                    tmpBean.setPrdUniqueVal(targetPrdUniqueVal);
                    tmpBean.setFileId(UUIDUtil.getUUID());
                    Date curDate = new Date();
                    tmpBean.setCreateTime(curDate);
                    tmpBean.setUpdateTime(curDate);
                    insert(tmpBean);
                }
                result.put("code", "0000");
                result.put("msg", "拷贝成功");
            } else {
                result.put("code", "2222");
                result.put("msg", "没有要拷贝的要件");
            }
        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error("读取orc信息 fileid=" + (String) parmMap.get("fileId"), e);
        }
        return result;
    }

    /**
     * 把一个业务的要件拷贝到另外一个业务中 根据参数
     *
     * @param parmMap{ originalBizNo:原始业务编号
     *                 originalprdUniqueVal:原始产品编号
     *                 originalFlowNo:原始流程编号
     *                 originalNodeNo:原始节点编号
     *                 originalTypeNo:原始类型编号
     *                 targetBizNo:目标业务编号
     *                 targetPrdUniqueVal:目标产品编号
     *                 targetFlowNo:目标流程编号
     *                 targetNodeNo:目标节点编号
     *                 targetTypeNo:目标产品编号
     *                 }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject copyFileByParam(Map<String, String> parmMap) throws ServiceException {
        JSONObject result = new JSONObject();
        try {

            String bizNo = parmMap.get("originalBizNo");
            String prdUniqueVal = parmMap.get("originalPrdUniqueVal");
            String flowNo = parmMap.get("originalFlowNo");
            String nodeNo = parmMap.get("originalNodeNo");
            String typeNo = parmMap.get("originalTypeNo");
            String targetBizNo = parmMap.get("targetBizNo");
            String targetPrdUniqueVal = parmMap.get("targetPrdUniqueVal");
            String targetFlowNo = parmMap.get("targetFlowNo");
            String targetNodeNo = parmMap.get("targetNodeNo");
            String targetTypeNo = parmMap.get("targetTypeNo");
            String relMark = parmMap.get("relMark");


            DocFileInfEntity quereyBean = new DocFileInfEntity();
            quereyBean.setBizNo(bizNo);
            quereyBean.setPrdUniqueVal(prdUniqueVal);
            quereyBean.setFlowNo(flowNo);
            quereyBean.setNodeNo(nodeNo);
            quereyBean.setTypeNo(typeNo);
            //查询出来待拷贝的组件
            List<DocFileInfEntity> originalFiles = findFileInfList(quereyBean);
            if (originalFiles != null & originalFiles.size() > 0) {
                for (DocFileInfEntity tmpBean : originalFiles) {
                    if (StringUtils.isNotEmpty(relMark) && "0".equals(relMark)) {
                        //此时删除关联信息
                        tmpBean.setBusRelatedId("");
                        tmpBean.setCusRelatedId("");
                        tmpBean.setCusRelatedId("");
                        tmpBean.setApplyRelatedId("");
                        tmpBean.setConstractRelatedId("");
                        tmpBean.setFinRelatedId("");
                    }
                    if (StringUtils.isNotBlank(targetBizNo)) {
                        tmpBean.setBizNo(targetBizNo);
                    }
                    if (StringUtils.isNotBlank(targetPrdUniqueVal)) {
                        tmpBean.setPrdUniqueVal(targetPrdUniqueVal);
                    }
                    if (StringUtils.isNotBlank(targetFlowNo)) {
                        tmpBean.setFlowNo(targetFlowNo);
                    }
                    if (StringUtils.isNotBlank(targetNodeNo)) {
                        tmpBean.setNodeNo(targetNodeNo);
                    }
                    if (StringUtils.isNotBlank(targetTypeNo)) {
                        tmpBean.setTypeNo(targetTypeNo);
                    }
                    tmpBean.setFileId(UUIDUtil.getUUID());
                    Date curDate = new Date();
                    tmpBean.setCreateTime(curDate);
                    tmpBean.setUpdateTime(curDate);
                    insert(tmpBean);
                }
                result.put("code", "0000");
                result.put("msg", "拷贝成功");
            } else {
                result.put("code", "2222");
                result.put("msg", "没有要拷贝的要件");
            }
        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error("读取orc信息 fileid=" + (String) parmMap.get("fileId"), e);
        }
        return result;
    }

    /**
     * 要件拷贝到其他目录下（包含目录）
     *
     * @param parmArray {{
     *                  <p>
     *                  orignalBizNo: 源流水号
     *                  orignalPrdId:源产品编号
     *                  orignalFlowNo:源流程编号
     *                  orignalNodeNo:源节点编号
     *                  targetBizNo: 目标流水号
     *                  targetPrdId:目标产品编号
     *                  targetFlowNo:目标流程编号
     *                  targetNodeNo:目标节点编号
     *                  <p>
     *                  }]
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject copyFileToOtherFolderIncludeFolder(JSONArray parmArray) throws ServiceException {

        JSONObject result = new JSONObject();
        try {
            if (parmArray != null) {
                if (parmArray.size() > 0) {
                    for (int i = 0; i < parmArray.size(); i++) {
                        JSONObject tmpObj = parmArray.getJSONObject(i);
                        //拷贝目录配置
                        DocScFiletypeConfigEntity docScFiletypeConfigEntity = new DocScFiletypeConfigEntity();
                        docScFiletypeConfigEntity.setPrdUniqueVal(tmpObj.getString("orignalPrdId"));
                        docScFiletypeConfigEntity.setFlowNo(tmpObj.getString("orignalFlowNo"));
                        docScFiletypeConfigEntity.setNodeNo(tmpObj.getString("orignalNodeNo"));
                        docScFiletypeConfigEntity.setParentTypeNo("bizConfig");
                        List<DocScFiletypeConfigEntity> configList = docScFiletypeConfigService.findList(docScFiletypeConfigEntity);

                        if (configList != null && configList.size() > 0) {
                            //如果该业务的产品类型，流程，节点 配置过要件，拿过来作为该业务的初始要件配置
                            for (DocScFiletypeConfigEntity configBean : configList) {
                                DocFileBizFiletypeConfigEntity addBean = new DocFileBizFiletypeConfigEntity();
                                BeanUtils.copyProperties(configBean, addBean);
                                addBean.setScId(UUIDUtil.getUUID());
                                addBean.setBizNo(tmpObj.getString("targetBizNo"));
                                addBean.setPrdUniqueVal(tmpObj.getString("targetPrdId"));
                                addBean.setFlowNo(tmpObj.getString("targetFlowNo"));
                                addBean.setNodeNo(tmpObj.getString("targetNodeNo"));
                                addBean.setCreateTime(new Date());
                                addBean.setUpdateTime(new Date());
                                docFileBizFiletypeConfigService.insert(addBean);


                            }

                            //拷贝要件
                            copyFileToOtherFolder(tmpObj);
                        }
                    }
                }
            }

        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error("要件拷贝到其他目录下" + parmArray.toJSONString(), e);
        }

        return result;
    }


    @Override
    public JSONObject insertBatchFile(List<JSONObject> parmMapList) throws ServiceException {
        try {
            for (JSONObject jsonObject : parmMapList) {
                DocFileInfEntity docFileInfEntity = jsonObject.toJavaObject(DocFileInfEntity.class);
                this.insert(docFileInfEntity);
            }
            return new JSONObject(R.ok());
        } catch (Exception e) {
            return new JSONObject(R.error("批量插入要件报错"));
        }
    }


    /**
     * 要件拷贝到其他目录下
     *
     * @param parmJson{ orignalBizNo: 源流水号
     *                  orignalPrdId:源产品编号
     *                  orignalFlowNo:源流程编号
     *                  orignalNodeNo:源节点编号
     *                  targetBizNo: 目标流水号
     *                  targetPrdId:目标产品编号
     *                  targetFlowNo:目标流程编号
     *                  targetNodeNo:目标节点编号
     *                  <p>
     *                  }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject copyFileToOtherFolder(JSONObject parmJson) throws ServiceException {
        JSONObject result = new JSONObject();
        try {

            DocFileInfEntity docFileInfEntity = new DocFileInfEntity();
            String bizNo = parmJson.getString("originalBizNo");
            String prdUniqueVal = parmJson.getString("orignalPrdId");
            String flowNo = parmJson.getString("originalFlowNo");
            String nodeNo = parmJson.getString("originalNodeNo");


            DocFileInfEntity quereyBean = new DocFileInfEntity();
            quereyBean.setBizNo(bizNo);
            quereyBean.setPrdUniqueVal(prdUniqueVal);
            quereyBean.setFlowNo(flowNo);
            quereyBean.setNodeNo(nodeNo);

            //查询出来待拷贝的组件
            List<DocFileInfEntity> originalFiles = findFileInfList(quereyBean);
            if (originalFiles != null & originalFiles.size() > 0) {
                for (DocFileInfEntity tmpBean : originalFiles) {
                    tmpBean.setBizNo(parmJson.getString("targetBizNo"));
                    tmpBean.setPrdUniqueVal(parmJson.getString("targetPrdId"));

                    tmpBean.setFlowNo(parmJson.getString("targetFlowNo"));
                    tmpBean.setNodeNo(parmJson.getString("targetNodeNo"));
                    tmpBean.setApplyRelatedId(parmJson.getString("targetBizNo"));
                    String typeNo = tmpBean.getTypeNo();
                    chekTypeNoExistAndCreate(parmJson, typeNo);//检查资料类型是否在目标节点中配置，如果没有，进行配置操作
                    tmpBean.setFileId(UUIDUtil.getUUID());
                    Date curDate = new Date();
                    tmpBean.setCreateTime(curDate);
                    tmpBean.setUpdateTime(curDate);
                    insert(tmpBean);
                }

                result.put("code", "0000");
                result.put("msg", "拷贝成功");
            } else {

                result.put("code", "2222");
                result.put("msg", "没有要拷贝的要件");


            }


        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error("要件拷贝到其他目录下" + parmJson.toJSONString(), e);
        }

        return result;

    }


    /**
     * 把指定的要件转移到指定的目录下
     *
     * @param moveParmJson{ fileIdAry：要转移的要件id数组（必填）
     *                  targetBizNo：要转移的要件id（必填）
     *                  targetPrdId：要转移的要件id（必填）
     *                  targetFlowNo：要转移的要件id（必填）
     *                  targetNodeNo：要转移的要件id（必填）
     *                  <p>
     *                  busRelatedId：转移后的关联业务关联编号（非必填）
     *                  cusRelatedId：转移后的客户关联编号（非必填）
     *                  applyRelatedId：转移后的申请关联编号（非必填）
     *                  constractRelatedId：转移后的合同号关联编号（非必填）
     *                  finRelatedId：转移后的借据关联编号（非必填）
     *                  <p>
     *                  <p>
     *                  }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject moveFilesToOtherFolder(JSONObject moveParmJson) throws ServiceException {
        JSONObject result = new JSONObject();
        JSONArray fileAry = moveParmJson.getJSONArray("fileIdAry");//要转移的要件Id
        JSONArray bizAry = moveParmJson.getJSONArray("bizAry");//要转移目标业务参数


        result.put("code", "0000");
        result.put("msg", "转移成功");


        if (fileAry != null) {

            for (int i = 0; i < fileAry.size(); i++) {
                String fileId = fileAry.getString(i);
                for (int k = 0; k < bizAry.size(); k++) {

                    JSONObject parmJson = bizAry.getJSONObject(k);

                    DocFileInfEntity docFileInfEntity = findById(fileId);
                    if (docFileInfEntity == null) {
                        continue;
                    }
                    docFileInfEntity.setFileId(UUIDUtil.getUUID());
                    String busRelatedId = parmJson.getString("busRelatedId");
                    String cusRelatedId = parmJson.getString("cusRelatedId");
                    String applyRelatedId = parmJson.getString("applyRelatedId");
                    String constractRelatedId = parmJson.getString("constractRelatedId");
                    String finRelatedId = parmJson.getString("finRelatedId");

                    docFileInfEntity.setBizNo(parmJson.getString("targetBizNo"));
                    docFileInfEntity.setPrdUniqueVal(parmJson.getString("targetPrdId"));
                    docFileInfEntity.setFlowNo(parmJson.getString("targetFlowNo"));
                    docFileInfEntity.setNodeNo(parmJson.getString("targetNodeNo"));
                    docFileInfEntity.setTypeNo(parmJson.getString("targetTypeNo"));

                    docFileInfEntity.setCusRelatedId(cusRelatedId);
                    docFileInfEntity.setApplyRelatedId(applyRelatedId);
                    docFileInfEntity.setConstractRelatedId(constractRelatedId);
                    docFileInfEntity.setFinRelatedId(finRelatedId);
                    docFileInfEntity.setBusRelatedId(busRelatedId);
                    insert(docFileInfEntity);//插入新的要件


                }
                deleteById(fileId);//删除老的要件
            }


        } else {
            result.put("code", "1111");
            result.put("msg", "没有要转移的要件");
        }


        return result;
    }

    @Override
    public void deleteBy(DocFileInfEntity docFileInfEntity) throws ServiceException {
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no", docFileInfEntity.getBizNo());
        queryWrapper.eq("flow_no", docFileInfEntity.getFlowNo());
        queryWrapper.eq("type_no", docFileInfEntity.getTypeNo());
        queryWrapper.eq("file_name", docFileInfEntity.getFileName());
        docFileInfMapper.delete(queryWrapper);
    }

    @Override
    public void copyAssureFile(JSONObject jsonObject) throws Exception {
        String cusId = jsonObject.getString("assureCusId");
        CusCustomerDTO cusCustomerDTO = customerFeignClient.getCusCustomerByCusId(cusId);
        String cusType = cusCustomerDTO.getCusType();
        String cusTypeQuery = "";
        if(cusType.contains("100")){//法人
            cusTypeQuery = "100";
        }else if(cusType.contains("200")){//自然人
            cusTypeQuery = "200";
        }else{
            log.info("暂不支持自然人和法人之外的影像资料复制");
            return;
        }
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("ori_file_id");
        queryWrapper.eq("biz_no",jsonObject.getString("assureId"));
        docFileInfMapper.delete(queryWrapper);


        String applyId = jsonObject.getString("applyId");
        JSONObject leaseApplyEntityDTO = leaseFeiginClient.getLeaseApply(applyId);
        jsonObject.put("prdUniqueVal",leaseApplyEntityDTO.getString("productNo") + leaseApplyEntityDTO.getString("productVersionNo"));
        DocCopyConfigEntity docCopyConfigEntity = new DocCopyConfigEntity();
        docCopyConfigEntity.setCusType(cusTypeQuery);
        List<DocCopyConfigEntity> docCopyConfigEntityList = docCopyConfigService.selectListBy(docCopyConfigEntity);
        if(CollectionUtils.isNotEmpty(docCopyConfigEntityList)){
            Map<String, List<DocCopyConfigEntity>> docCopyConfigMap = docCopyConfigEntityList.stream()
                    .collect(Collectors.groupingBy(DocCopyConfigEntity::getCusFileTypeNo));
            Map<String ,String> parmMap = new HashMap<>();
            parmMap.put("bizNo",cusId);
            JSONArray jsonArray = this.getFileList(parmMap);
            if(jsonArray != null && jsonArray.size() > 0){
                for (int i = 0;i<jsonArray.size();i++ ) {
                    DocFileInfEntity docFileInfEntity = (DocFileInfEntity)jsonArray.get(i);
                    String typeNo = docFileInfEntity.getTypeNo();
                    List<DocCopyConfigEntity> docCopyConfigEntities = docCopyConfigMap.get(typeNo);
                    if(CollectionUtils.isNotEmpty(docCopyConfigEntities)){
                        DocCopyConfigEntity copyConfigEntity = docCopyConfigEntities.get(0);
                        docFileInfEntity.setTypeNo(copyConfigEntity.getAssureFileTypeNo());
                        docFileInfEntity.setTypeName(copyConfigEntity.getAssureFileTypeName());
                        docFileInfEntity.setBizNo(jsonObject.getString("assureId"));
                        docFileInfEntity.setPrdUniqueVal(jsonObject.getString("prdUniqueVal"));
                        docFileInfEntity.setFlowNo(copyConfigEntity.getFlowNo());
                        docFileInfEntity.setNodeNo(copyConfigEntity.getNodeNo());
                        docFileInfEntity.setOriFileId(docFileInfEntity.getFileId());
                        docFileInfEntity.setFileId(UUIDUtil.getUUID());
                        docFileInfMapper.insert(docFileInfEntity);
                    }
                }
            }
        }
    }

    @Override
    public void copyAssureFileInexistence(JSONObject jsonObject) throws Exception {
        String cusId = jsonObject.getString("assureCusId");
        CusCustomerDTO cusCustomerDTO = customerFeignClient.getCusCustomerByCusId(cusId);
        String cusType = cusCustomerDTO.getCusType();
        String cusTypeQuery = "";
        if(cusType.contains("100")){//法人
            cusTypeQuery = "100";
        }else if(cusType.contains("200")){//自然人
            cusTypeQuery = "200";
        }else{
            log.info("暂不支持自然人和法人之外的影像资料复制");
            return;
        }
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNotNull("ori_file_id");
        queryWrapper.eq("biz_no",jsonObject.getString("assureId"));
        queryWrapper.groupBy("type_no");
        List<DocFileInfEntity> docFileInfEntityList = docFileInfMapper.selectList(queryWrapper);

        String applyId = jsonObject.getString("applyId");
        JSONObject leaseApplyEntityDTO = leaseFeiginClient.getLeaseApply(applyId);
        jsonObject.put("prdUniqueVal",leaseApplyEntityDTO.getString("productNo") + leaseApplyEntityDTO.getString("productVersionNo"));
        DocCopyConfigEntity docCopyConfigEntity = new DocCopyConfigEntity();
        docCopyConfigEntity.setCusType(cusTypeQuery);
        List<DocCopyConfigEntity> docCopyConfigEntityList = docCopyConfigService.selectListBy(docCopyConfigEntity);
        if(CollectionUtils.isNotEmpty(docCopyConfigEntityList)){
            Map<String, List<DocCopyConfigEntity>> docCopyConfigMap = docCopyConfigEntityList.stream()
                    .collect(Collectors.groupingBy(DocCopyConfigEntity::getCusFileTypeNo));

            Map<String ,String> parmMap = new HashMap<>();
            parmMap.put("bizNo",cusId);
            JSONArray jsonArray = this.getFileList(parmMap);
            if(jsonArray != null && jsonArray.size() > 0){
                for (int i = 0;i<jsonArray.size();i++ ) {
                    DocFileInfEntity docFileInfEntity = (DocFileInfEntity)jsonArray.get(i);
                    String typeNo = docFileInfEntity.getTypeNo();

                    List<DocCopyConfigEntity> docCopyConfigEntities = docCopyConfigMap.get(typeNo);
                    if(CollectionUtils.isNotEmpty(docCopyConfigEntities)){
                        DocCopyConfigEntity copyConfigEntity = docCopyConfigEntities.get(0);

                        //判断该要件类型是否已经上传过文件，如果已经上传过文件，不复制
                        boolean copyFlag = true;
                        if (docFileInfEntityList!=null && docFileInfEntityList.size()>0){
                            for (DocFileInfEntity tmpDocFileInfEntity : docFileInfEntityList){
                                if (tmpDocFileInfEntity.getTypeNo().equals(copyConfigEntity.getAssureFileTypeNo())){
                                    copyFlag = false;
                                    break;
                                }
                            }
                        }
                        if (!copyFlag){
                            continue;
                        }
                        docFileInfEntity.setTypeNo(copyConfigEntity.getAssureFileTypeNo());
                        docFileInfEntity.setTypeName(copyConfigEntity.getAssureFileTypeName());
                        docFileInfEntity.setBizNo(jsonObject.getString("assureId"));
                        docFileInfEntity.setPrdUniqueVal(jsonObject.getString("prdUniqueVal"));
                        docFileInfEntity.setFlowNo(copyConfigEntity.getFlowNo());
                        docFileInfEntity.setNodeNo(copyConfigEntity.getNodeNo());
                        docFileInfEntity.setOriFileId(docFileInfEntity.getFileId());
                        docFileInfEntity.setFileId(UUIDUtil.getUUID());
                        docFileInfMapper.insert(docFileInfEntity);
                    }
                }
            }
        }
    }

    /**
     * 检查资料类型是否在目标节点中配置，如果没有，进行配置操作
     *
     * @param parmJson
     * @param typeNo
     * @throws ServiceException
     */
    private void chekTypeNoExistAndCreate(JSONObject parmJson, String typeNo) throws ServiceException {
        DocFileBizFiletypeConfigEntity tmpBean = new DocFileBizFiletypeConfigEntity();
        tmpBean.setBizNo(parmJson.getString("targetBizNo"));
        tmpBean.setPrdUniqueVal(parmJson.getString("targetPrdId"));
        tmpBean.setFlowNo(parmJson.getString("targetFlowNo"));
        tmpBean.setNodeNo(parmJson.getString("targetNodeNo"));
        tmpBean.setTypeNo(typeNo);
        List<DocFileBizFiletypeConfigEntity> list = docFileBizFiletypeConfigService.findList(tmpBean);
        boolean flag = false;
        if (list != null) {
            if (list.size() > 0) {
                flag = true;
            }
        }
        if (!flag) {
            //如果不存在 ，进行配置操作
            DocFileBizFiletypeConfigEntity orignalBean = new DocFileBizFiletypeConfigEntity();
            orignalBean.setBizNo(parmJson.getString("orignalBizNo"));
            orignalBean.setPrdUniqueVal(parmJson.getString("orignalPrdId"));
            orignalBean.setFlowNo(parmJson.getString("orignalFlowNo"));
            orignalBean.setNodeNo(parmJson.getString("orignalNodeNo"));
            orignalBean.setTypeNo(typeNo);
            List<DocFileBizFiletypeConfigEntity> orignallist = docFileBizFiletypeConfigService.findList(orignalBean);
            if (orignallist != null) {
                if (orignallist.size() > 0) {

                    DocFileBizFiletypeConfigEntity addBean = orignallist.get(0);
                    addBean.setBizNo(parmJson.getString("targetBizNo"));
                    addBean.setPrdUniqueVal(parmJson.getString("targetPrdId"));
                    addBean.setFlowNo(parmJson.getString("targetFlowNo"));
                    addBean.setNodeNo(parmJson.getString("targetNodeNo"));
                    addBean.setScId(UUIDUtil.getUUID());
                    addBean.setCreateTime(new Date());
                    addBean.setUpdateTime(new Date());
                    docFileBizFiletypeConfigService.insert(addBean);

                }

            }

        }


    }

    /**
     * 把一个业务的要件拷贝到另外一个业务中（两个业务的层级必须保持一直）
     *
     * @param parmMapList{ originalBizNo:原始业务编号
     *                     originalPrdUniqueId:原始产品编号
     *                     originalFlowNo:原始流程编号
     *                     originalNodeNo:原始节点编号
     *                     targetBizNo:目标业务编号
     *                     <p>
     *                     }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject copyFileByNodes(List<Map<String, String>> parmMapList) throws ServiceException {
        JSONObject result = new JSONObject();
        try {
            result.put("code", "0000");
            result.put("msg", "拷贝成功");
            if (parmMapList != null) {

                if (parmMapList.size() > 0) {

                    for (Map<String, String> parmMap : parmMapList) {
                        JSONObject delRes = copyFile(parmMap);
//                        if("0000".equals(delRes.getString("code"))){
//                            result=delRes;
//                            break;
//                        }
                    }
                } else {
                    result.put("code", "1111");
                    result.put("msg", "没有要拷贝的要件");
                }
            } else {
                result.put("code", "1111");
                result.put("msg", "没有要拷贝的要件");

            }
            return result;

        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error(JSON.toJSONString(parmMapList), e);
        }

        return result;

    }


    /**
     * 获取文档的内容
     *
     * @param fileId
     * @return
     * @throws ServiceException
     */
    @Override
    public List<String> getTxtContent(String fileId) throws ServiceException {
        List<String> content = new ArrayList<>();
        try {
            DocFileInfEntity fileInfEntity = docFileInfMapper.selectById(fileId);
            if ("obs".equals(uploadOutterFunction)) {
                String obsId = fileInfEntity.getObsId();
                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                content = readHtmlFile(filePath);
            } else {
                String filePath = docFileUploadPath + File.separator + fileInfEntity.getFilePath();
                content = getFileContent(filePath);
            }


        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileId, e);
        }
        return content;
    }

    private List<String> readHtmlFile(String sourcePath) {
        List<String> content = new ArrayList<>();
        BufferedReader reader = null;
        String line;
        try {
            URL url = new URL(sourcePath);
            reader = new BufferedReader(new InputStreamReader(url.openStream()));
            while ((line = reader.readLine()) != null) {
                //System.out.println(line);
                content.add(line);
            }
        } catch (Exception ie) {
            ie.printStackTrace();
        } finally {
            try {
                if (reader != null) {
                    reader.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return content;
    }

    /**
     * 根据要件Id获取所用包含的目录
     *
     * @param fileIdAry
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocFileBizFiletypeConfigEntity> getFloderListByFileIds(String[] fileIdAry) throws ServiceException {


        List<DocFileBizFiletypeConfigEntity> result = new ArrayList<>();

        try {
            if (fileIdAry != null) {
                String fileIdStr = "''";
                StringBuilder stringBuilder = new StringBuilder();
                for (String fileId : fileIdAry) {
                    stringBuilder.append(",'");
                    stringBuilder.append(fileId);
                    stringBuilder.append("'");
                }
                if (stringBuilder.length() > 1) {
                    fileIdStr = stringBuilder.substring(1);
                    QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.inSql("file_id", fileIdStr);
                    List<DocFileInfEntity> entityList = docFileInfMapper.selectList(queryWrapper);

                    if (entityList != null) {
                        JSONObject typeJson = new JSONObject();

                        for (DocFileInfEntity doc : entityList) {
                            JSONObject jsonObject = new JSONObject();
                            DocFileBizFiletypeConfigEntity folder = new DocFileBizFiletypeConfigEntity();
                            folder.setTypeNo(doc.getTypeNo());
                            folder.setTypeName(doc.getTypeName());
                            typeJson.put(doc.getTypeNo(), folder);
                        }

                        for (String key : typeJson.keySet()) {
                            result.add(typeJson.getObject(key, DocFileBizFiletypeConfigEntity.class));
                        }

                    }

                }
            }


        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileIdAry.toString(), e);
        }
        return result;
    }

    /**
     * 根据要件Id获取所用包含的目录
     *
     * @param parmJson{ fileIdAry
     *                  typeNo
     *                  }
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocFileBizFiletypeConfigEntity> getFloderRootListByFileIds(JSONObject parmJson) throws ServiceException {

        JSONArray parmArray = parmJson.getJSONArray("fileIdAry");

        String[] fileIdAry = new String[parmArray.size()];
        for (int i = 0; i < parmArray.size(); i++) {
            fileIdAry[i] = parmArray.getString(i);
        }

        String typeNo = (String) parmJson.get("typeNo");


        List<DocFileBizFiletypeConfigEntity> result = new ArrayList<>();
        DocFileBizFiletypeConfigEntity root = new DocFileBizFiletypeConfigEntity();
        root.setTypeName("根目录");
        root.setTypeNo("");
        result.add(root);
        if (StringUtils.isEmpty(typeNo)) {

            return result;
        }
        try {
            if (fileIdAry != null) {
                String fileIdStr = "''";
                StringBuilder stringBuilder = new StringBuilder();
                for (String fileId : fileIdAry) {
                    stringBuilder.append(",'");
                    stringBuilder.append(fileId);
                    stringBuilder.append("'");
                }
                if (stringBuilder.length() > 1) {
                    fileIdStr = stringBuilder.substring(1);
                    QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.inSql("file_id", fileIdStr);
                    List<DocFileInfEntity> entityList = docFileInfMapper.selectList(queryWrapper);

                    if (entityList != null) {
                        JSONObject typeJson = new JSONObject();

                        for (DocFileInfEntity doc : entityList) {
                            JSONObject jsonObject = new JSONObject();
                            DocFileBizFiletypeConfigEntity folder = new DocFileBizFiletypeConfigEntity();
                            folder.setTypeNo(doc.getTypeNo());
                            folder.setTypeName(doc.getTypeName());
                            typeJson.put(doc.getTypeNo(), folder);
                        }
                        if (typeJson.containsKey(typeNo)) {
                            result.add(typeJson.getObject(typeNo, DocFileBizFiletypeConfigEntity.class));
                        }

                    }

                }
            }


        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileIdAry.toString(), e);
        }
        return result;
    }

    /**
     * 根据要件Id获取所用包含的目录
     *
     * @param fileIdAry
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocFileInfEntity> getFileListByFileIds(String fileIdAry[], String typeNo) throws ServiceException {
        List<DocFileInfEntity> result = new ArrayList<>();
        try {
            if (fileIdAry != null) {
                String fileIdStr = "''";
                StringBuilder stringBuilder = new StringBuilder();
                for (String fileId : fileIdAry) {
                    stringBuilder.append(",'");
                    stringBuilder.append(fileId);
                    stringBuilder.append("'");
                }
                if (stringBuilder.length() > 1) {
                    fileIdStr = stringBuilder.substring(1);
                    QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.inSql("file_id", fileIdStr);
//                    if(StringUtils.isNotEmpty(typeNo)){
                    queryWrapper.eq("type_no", typeNo);
//                    }
                    result = docFileInfMapper.selectList(queryWrapper);


                }
            }


        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileIdAry.toString(), e);
        }
        return result;
    }

    /**
     * 获取office文档的属性
     *
     * @param fileId
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getOfficeObj(String fileId) throws ServiceException {
        JSONObject result = new JSONObject();
        try {
            DocFileInfEntity fileInfEntity = docFileInfMapper.selectById(fileId);
//            String fileUrlPath=docFileOfficeUrl+File.separator+fileInfEntity.getFilePath();
            String fileUrlPath = docOfficeOpenUrl + fileId;
            String suffix = "";
            if ("obs".equals(uploadOutterFunction)) {
                String obsId = fileInfEntity.getObsId();
                String tmpAry[] = obsId.split("\\.");
                suffix = tmpAry[tmpAry.length - 1];//后缀名
            } else {
                String tmpAry[] = fileInfEntity.getFilePath().split("\\.");
                suffix = tmpAry[tmpAry.length - 1];//后缀名
            }


            String docKey = RandomUtil.randomString(20);

            result.put("gateWayAddr", gateWayAddr);
            result.put("serverName", serverName);
            result.put("officeDocKey", docKey);
            result.put("officeUrl", fileUrlPath);
            result.put("officeKey", fileId);
            result.put("officeSuffix", suffix);
            result.put("officeServie", officeServerPath);

            result.put("docOfficeView", docOfficeView);


        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileId, e);
        }
        MFLogger.info(JSONObject.toJSONString(result));
        return result;
    }


    /**
     * 读取文件中的内容
     *
     * @param filePath
     * @return
     * @throws ServiceException
     */
    private List<String> getFileContent(String filePath) throws ServiceException {

        InputStreamReader isr;
        List<String> list = new ArrayList<String>();
        try {
            isr = new InputStreamReader(new FileInputStream(filePath), "UTF-8");
            BufferedReader read = new BufferedReader(isr);
            String s = null;

            while ((s = read.readLine()) != null) {
                //System.out.println(s);
                list.add(s);
            }


        } catch (Exception e) {
            // TODO Auto-generated catch block
            MFLogger.error("读取文件中的内容:" + filePath, e);
            throw new ServiceException("读取文件中的内容", filePath, e);

        }
        return list;
    }


    @Override
    public void deleteById(String fileId) throws ServiceException {
        try {
            docFileInfMapper.deleteById(fileId);
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR, fileId, e);
        }
    }


    /**
     * 批量删除要件
     *
     * @param parmMap{ bizNo:关联业务主键
     *                 prdUniqueVal:产品唯一标识
     *                 flowNo:流程标识
     *                 nodeNo:节点标识
     *                 typeNo:要件类型标识
     *                 <p>
     *                 }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject deleteBatch(Map<String, String> parmMap) throws ServiceException {
        JSONObject result = new JSONObject();
        try {

            DocFileInfEntity docFileInfEntity = new DocFileInfEntity();
            String bizNo = parmMap.get("bizNo");
            String prdUniqueVal = parmMap.get("prdUniqueVal");
            String flowNo = parmMap.get("flowNo");
            String nodeNo = parmMap.get("nodeNo");
            String typeNo = parmMap.get("typeNo");

            if (StringUtils.isEmpty(bizNo)) {
                result.put("code", "1111");
                result.put("msg", "关联业务主键不能为空");
                return result;
            }
            if (StringUtils.isEmpty(prdUniqueVal) &&
                    StringUtils.isEmpty(flowNo) &&
                    StringUtils.isEmpty(nodeNo) &&
                    StringUtils.isEmpty(typeNo)) {
                result.put("code", "2222");
                result.put("msg", "prdUniqueVal ,flowNo,nodeNo,typeNo 不能全部为空");
                return result;
            }


            docFileInfEntity.setBizNo(bizNo);
            docFileInfEntity.setPrdUniqueVal(prdUniqueVal);
            docFileInfEntity.setFlowNo(flowNo);
            docFileInfEntity.setNodeNo(nodeNo);
            docFileInfEntity.setTypeNo(typeNo);
            //查询出来待拷贝的组件
            QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotEmpty(docFileInfEntity.getPrdUniqueVal()), "prd_unique_val", docFileInfEntity.getPrdUniqueVal());
            queryWrapper.eq(StringUtils.isNotEmpty(docFileInfEntity.getFlowNo()), "flow_no", docFileInfEntity.getFlowNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docFileInfEntity.getNodeNo()), "node_no", docFileInfEntity.getNodeNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docFileInfEntity.getBizNo()), "biz_no", docFileInfEntity.getBizNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docFileInfEntity.getTypeNo()), "type_no", docFileInfEntity.getTypeNo());
            docFileInfMapper.delete(queryWrapper);
            result.put("code", "0000");
            result.put("msg", "删除成功");


        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error("读取orc信息 fileid=" + (String) parmMap.get("fileId"), e);
        }

        return result;
    }


    /**
     * 批量删除要件(按照不同的节点)
     *
     * @param parmMapList [{
     *                    bizNo:关联业务主键
     *                    prdUniqueVal:产品唯一标识
     *                    flowNo:流程标识
     *                    nodeNo:节点标识
     *                    typeNo:要件类型标识
     *                    <p>
     *                    }]
     * @param parmMapList
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject deleteBatchByNodes(List<Map<String, String>> parmMapList) throws ServiceException {
        JSONObject result = new JSONObject();
        try {
            result.put("code", "0000");
            result.put("msg", "删除成功");
            if (parmMapList != null) {

                if (parmMapList.size() > 0) {

                    for (Map<String, String> parmMap : parmMapList) {
                        JSONObject delRes = deleteBatch(parmMap);
//                        if("0000".equals(delRes.getString("code"))){
//                            result=delRes;
//                            break;
//                        }
                    }
                } else {
                    result.put("code", "1111");
                    result.put("msg", "没有要删除的要件");
                }
            } else {
                result.put("code", "1111");
                result.put("msg", "没有要删除的要件");

            }
            return result;

        } catch (Exception e) {
            result.put("code", "3333");
            result.put("msg", "运行时错误");
            MFLogger.error(JSON.toJSONString(parmMapList), e);
        }

        return result;
    }

    @Override
    public JSONArray getFileIdArray(List<Map<String, String>> parmList) throws ServiceException {
        JSONArray result = new JSONArray();
        for (Map<String, String> parmMap : parmList) {
            String bizNo = parmMap.get("bizNo");
            String prdUniqueVal = parmMap.get("prdUniqueVal");
            String flowNo = parmMap.get("flowNo");
            String nodeNo = parmMap.get("nodeNo");
            String typeNo = parmMap.get("typeNo");


            DocFileInfEntity quereyBean = new DocFileInfEntity();
            quereyBean.setBizNo(bizNo);
            quereyBean.setPrdUniqueVal(prdUniqueVal);
            quereyBean.setFlowNo(flowNo);
            quereyBean.setNodeNo(nodeNo);
            quereyBean.setTypeNo(typeNo);
            List<DocFileInfEntity> tmpList = findFileInfList(quereyBean);
            if (tmpList != null) {
                for (DocFileInfEntity file : tmpList) {
                    result.add(file.getFileId());
                }
            }
        }
        return result;
    }

    @Override
    public JSONArray getFileListArray(List<Map<String, String>> parmList) throws ServiceException {
        JSONArray result = new JSONArray();
        for (Map<String, String> parmMap : parmList) {
            String bizNo = parmMap.get("bizNo");
            String prdUniqueVal = parmMap.get("prdUniqueVal");
            String flowNo = parmMap.get("flowNo");
            String nodeNo = parmMap.get("nodeNo");
            String typeNo = parmMap.get("typeNo");
            String busRelatedId = parmMap.get("busRelatedId");
            String cusRelatedId = parmMap.get("cusRelatedId");
            String applyRelatedId = parmMap.get("applyRelatedId");
            String constractRelatedId = parmMap.get("constractRelatedId");
            String finRelatedId = parmMap.get("finRelatedId");


            DocFileInfEntity quereyBean = new DocFileInfEntity();
            quereyBean.setBizNo(bizNo);
            quereyBean.setPrdUniqueVal(prdUniqueVal);
            quereyBean.setFlowNo(flowNo);
            quereyBean.setNodeNo(nodeNo);
            quereyBean.setTypeNo(typeNo);
            quereyBean.setBusRelatedId(busRelatedId);
            quereyBean.setCusRelatedId(cusRelatedId);
            quereyBean.setApplyRelatedId(applyRelatedId);
            quereyBean.setConstractRelatedId(constractRelatedId);
            quereyBean.setFinRelatedId(finRelatedId);

            List<DocFileInfEntity> tmpList = findFileInfList(quereyBean);
            if (tmpList != null) {
                for (DocFileInfEntity file : tmpList) {
                    result.add(file);
                }
            }
        }
        return result;
    }

    @Override
    public FileShowVo findAllTypeAndList(DocFileInfEntity docFileInfEntity) {

        String cusNo = docFileInfEntity.getCusRelatedId();
        String applyNo = docFileInfEntity.getApplyRelatedId();
        String pactNo = docFileInfEntity.getConstractRelatedId();
        String finRelatedId = docFileInfEntity.getFinRelatedId();

        if (StringUtils.isEmpty(cusNo) && StringUtils.isEmpty(applyNo) && StringUtils.isEmpty(pactNo) && StringUtils.isEmpty(finRelatedId)) {
            throw new NullPointerException("客户号、申请号、合同号、借据号至少传一个");
        }
        QueryWrapper queryWrapper = new QueryWrapper();


        if (StringUtils.isNotEmpty(cusNo)) {
            queryWrapper.eq("cus_related_id", cusNo);
            queryWrapper.or();
        }
        if (StringUtils.isNotEmpty(applyNo)) {
            queryWrapper.eq("apply_related_id", applyNo);
            queryWrapper.or();
        }
        if (StringUtils.isNotEmpty(pactNo)) {
            queryWrapper.eq("constract_related_id", pactNo);
            queryWrapper.or();
        }
        if (StringUtils.isNotEmpty(finRelatedId)) {
            queryWrapper.eq("fin_related_id", finRelatedId);
            queryWrapper.or();
        }

        List<DocFileInfEntity> fileList = docFileInfMapper.selectList(queryWrapper);

        Map<String, List<DocFileInfEntity>> map = fileList.stream().collect(Collectors.groupingBy(p -> p.getTypeNo(), Collectors.toList()));

        List<DocFileBizFiletypeConfigEntity> _fileList = new ArrayList<>();

        JSONObject parentTypeJson = docFileTypeService.getParentTypeInf();

        // 按照typeNo 进行分组
        map.forEach((key, value) -> {
            DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();
            DocFileInfEntity _docFileInfEntity = value.get(0);

            String prdNo = _docFileInfEntity.getPrdUniqueVal();
            String flowNo = _docFileInfEntity.getFlowNo();
            String nodeNo = _docFileInfEntity.getNodeNo();
            String bizNo = _docFileInfEntity.getBizNo();


            DocFileBizFiletypeConfigEntity parmDocFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();

            parmDocFileBizFiletypeConfigEntity.setPrdUniqueVal(prdNo);
            parmDocFileBizFiletypeConfigEntity.setFlowNo(flowNo);
            parmDocFileBizFiletypeConfigEntity.setNodeNo(nodeNo);
            parmDocFileBizFiletypeConfigEntity.setBizNo(bizNo);

            // 目前由于数据问题，导致出现了多条数据，按照业务规划只能有一条，所以区第一条
            List<DocFileBizFiletypeConfigEntity> _list = docFileBizFiletypeConfigService.findList(parmDocFileBizFiletypeConfigEntity);
            // 获取类型信息
            DocFileBizFiletypeConfigEntity tempData = _list.get(0);
            String typeNo = tempData.getTypeNo();
            DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
            if (docFileTypeEntity != null) {
                tempData.setTypeDesc(docFileTypeEntity.getTypeDesc());

            }
            // 设置该类下已经上传的要件信息
            tempData.setFileList(value);

            //为父级类别赋值

            JSONObject parentJson = parentTypeJson.getJSONObject(tempData.getTypeNo());
            if (parentJson != null) {

                tempData.setParentTypeNo(parentJson.getString("typeNo"));
                tempData.setParentTypeName(parentJson.getString("typeName"));
            }
            _fileList.add(tempData);

        });
        // 按照父类编号进行分组

        //   Map<String,String>  group = new HashMap<>();
        Map<String, List<DocFileBizFiletypeConfigEntity>> _map = _fileList.stream().collect(Collectors.groupingBy(p -> p.getParentTypeNo() == null ? "" : p.getParentTypeNo(), Collectors.toList()));

        List<Map<String, String>> groupList = new ArrayList<>();
        _map.forEach((key, value) -> {
            Map<String, String> group = new HashMap<>();

            String groupName = value.get(0).getParentTypeName();
            String groupNo = key;
            group.put("groupName", groupName);
            group.put("groupNo", key);


            if (StringUtils.isNotEmpty(key)) {
                groupList.add(group);
            }
        });
        FileShowVo fileShowVo = new FileShowVo();

        fileShowVo.setGroupList(groupList);
        fileShowVo.setFileList(_fileList);
        return fileShowVo;

    }


    private List<DocFileInfEntity> filler(List<DocFileInfEntity> docFileInfEntityList, List<DocFileInfEntity> fillerList) {
        List<DocFileInfEntity> dataList = new ArrayList<>();

        if (fillerList == null || fillerList.size() == 0) {
            return docFileInfEntityList;
        }

        Iterator<DocFileInfEntity> iterator = docFileInfEntityList.iterator();
        while (iterator.hasNext()) {
            DocFileInfEntity _docFileInfEntity = iterator.next();
            String _prdNo = _docFileInfEntity.getPrdUniqueVal();
            String _bizNo = _docFileInfEntity.getBizNo();
            String _flowNo = _docFileInfEntity.getFlowNo();
            String _nodeNo = _docFileInfEntity.getNodeNo();
            for (DocFileInfEntity tempDocFileInfEntity : fillerList) {
                String parmprdNo = tempDocFileInfEntity.getPrdUniqueVal();
                String parmbizNo = tempDocFileInfEntity.getBizNo();
                String parmflowNo = tempDocFileInfEntity.getFlowNo();
                String parmnodeNo = tempDocFileInfEntity.getNodeNo();

                boolean prodNoFlag = false;
                boolean bizNoFlag = false;
                boolean flowNoFlag = false;
                boolean nodeNoFlag = false;

                if (StringUtils.isNotEmpty(parmprdNo)) {
                    prodNoFlag = StringUtils.equals(_prdNo, parmprdNo);
                } else {
                    prodNoFlag = true;
                }

                if (StringUtils.isNotEmpty(parmbizNo)) {
                    bizNoFlag = StringUtils.equals(_bizNo, parmbizNo);
                } else {
                    bizNoFlag = true;
                }

                if (StringUtils.isNotEmpty(parmflowNo)) {
                    flowNoFlag = StringUtils.equals(_flowNo, parmflowNo);
                } else {
                    flowNoFlag = true;
                }

                if (StringUtils.isNotEmpty(parmnodeNo)) {
                    nodeNoFlag = StringUtils.equals(_nodeNo, parmnodeNo);
                } else {
                    nodeNoFlag = true;
                }

                if (prodNoFlag && bizNoFlag && flowNoFlag && nodeNoFlag) {
                    dataList.add(_docFileInfEntity);
                    break;
                }
            }
        }
        return dataList;
    }
    /**
     * 根据条件查询要件信息
     * @param
     * @return
     */
    @Override
    public FileShowVo getArchiveFile(JSONObject jsonObject) {
        List<Map> queryParmList = (List<Map>) jsonObject.get("docFileInf");
        String sealMaterialType = jsonObject.get("sealMaterialType").toString();
        String bizNo = jsonObject.get("bizNo").toString();
        List<DocFileInfEntity> docFileInfEntityList = new ArrayList<>();
        MFLogger.info("getArchiveFile-查询文件入参:"+jsonObject);
        List<String> fileTypeOrder = new ArrayList<>();
        Map<String, String> fileTypeOrderMap = new HashedMap();
            for (Map object : queryParmList) {
                ObjectMapper objectMapper = new ObjectMapper();
                DocFileInfEntity fileInfEntity = objectMapper.convertValue(object, DocFileInfEntity.class);
                docFileInfEntityList.add(fileInfEntity);
                if (fileTypeOrderMap.containsKey(fileInfEntity.getTypeNo())) {
                    continue;
                } else {
                    fileTypeOrderMap.put(fileInfEntity.getTypeNo(), fileInfEntity.getTypeNo());
                    fileTypeOrder.add(fileInfEntity.getTypeNo());
                }

            }

        Map<String, List<DocFileInfEntity>> map = docFileInfEntityList.stream().collect(Collectors.groupingBy(p -> p.getTypeNo(), Collectors.toList()));
        MFLogger.info("getArchiveFile-查询文件:"+JSONObject.toJSON(map));
        List<Map<String, String>> groupList = new ArrayList<>();
        List<DocFileBizFiletypeConfigEntity> _fileList = new ArrayList<>();

        if ("2".equals(sealMaterialType) && !fileTypeOrder.contains("mortgagePowerOfAttorney")){
            fileTypeOrder.add("mortgagePowerOfAttorney");
        }
        if ("2".equals(sealMaterialType) && !fileTypeOrder.contains("mortgageApplication")){
            fileTypeOrder.add("mortgageApplication");
        }
        if ("1".equals(sealMaterialType) && !fileTypeOrder.contains("releasedMortgageApplication")){
            fileTypeOrder.add("releasedMortgageApplication");
        }
        //纸质业务合同用印   三种都要展示
//        if (!fileTypeOrder.contains("paperBusinessContractsSeal")){
//            fileTypeOrder.add("paperBusinessContractsSeal");
//        }
        MFLogger.info("getArchiveFile-条件查询文件:"+JSONObject.toJSON(fileTypeOrder));
        for (String typeNo : fileTypeOrder) {
            List<DocFileInfEntity> tempDocFileInfEntityList = map.get(typeNo);
            String parentTypeNo = null;
            DocFileBizFiletypeConfigEntity parmDocFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();
            if (tempDocFileInfEntityList != null){
                // 重写实体对象去重list
                Set<DocFileInfEntity> docFileInfEntitySet = new HashSet<>();
                docFileInfEntitySet.addAll(tempDocFileInfEntityList);
                tempDocFileInfEntityList.clear();
                tempDocFileInfEntityList.addAll(docFileInfEntitySet);

                DocFileInfEntity _docFileInfEntity = tempDocFileInfEntityList.get(0);

                String prdNo = _docFileInfEntity.getPrdUniqueVal();
                String flowNo = _docFileInfEntity.getFlowNo();
                String nodeNo = _docFileInfEntity.getNodeNo();
                String _bizNo = _docFileInfEntity.getBizNo();

                parentTypeNo = _docFileInfEntity.getParentTypeNo();

                parmDocFileBizFiletypeConfigEntity.setPrdUniqueVal(prdNo);
                parmDocFileBizFiletypeConfigEntity.setFlowNo(flowNo);
                parmDocFileBizFiletypeConfigEntity.setNodeNo(nodeNo);
                parmDocFileBizFiletypeConfigEntity.setBizNo(_bizNo);
                parmDocFileBizFiletypeConfigEntity.setTypeNo(typeNo);
            }else {
                parmDocFileBizFiletypeConfigEntity.setBizNo(bizNo);
                parmDocFileBizFiletypeConfigEntity.setTypeNo(typeNo);
            }

            // 目前由于数据问题，导致出现了多条数据，按照业务规划只能有一条，所以区第一条
            List<DocFileBizFiletypeConfigEntity> _list = docFileBizFiletypeConfigService.findList(parmDocFileBizFiletypeConfigEntity);
            //不等于空时再取数据
            if (!_list.isEmpty()) {
                // 获取类型信息
                DocFileBizFiletypeConfigEntity tempData = _list.get(0);
                MFLogger.info("getArchiveFile-条件查询业务文件配置信息tempData:"+JSONObject.toJSON(tempData));
                //  String typeNo = tempData.getTypeNo();
                DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
                if (docFileTypeEntity == null) {
                    continue;
                }
                String groupNo = docFileTypeEntity.getGroupNo();
                String groupName = parmCacheUtil.getOptName("DOC_TYPE_GROUP", groupNo);
                DocFileTypeEntity parDocFileTypeEntity = docFileTypeService.findById(parentTypeNo);
                Map<String, String> groupMap = new HashMap<>();
                groupMap.put("groupName", groupName);
                groupMap.put("groupNo", groupNo);
                if(tempDocFileInfEntityList != null){
                    for (DocFileInfEntity docFileInfEntity : tempDocFileInfEntityList) {
                        docFileInfEntity.setParentTypeNo(groupNo);
                        docFileInfEntity.setParentTypeName(groupName);
                    }


                    if (docFileTypeEntity != null) {
                        tempData.setTypeDesc(docFileTypeEntity.getTypeDesc());
                    }
                    // 设置该类下已经上传的要件信息
                    tempDocFileInfEntityList = tempDocFileInfEntityList.stream()
                            .sorted(Comparator.comparing(DocFileInfEntity::getFileId)).
                            sorted(Comparator.comparing(DocFileInfEntity::getCreateTime)).collect(Collectors.toList());
                    if (tempDocFileInfEntityList.size() > 0) {
                        tempData.setCusRelatedId(tempDocFileInfEntityList.get(0).getCusRelatedId());
                        tempData.setApplyRelatedId(tempDocFileInfEntityList.get(0).getApplyRelatedId());
                        tempData.setConstractRelatedId(tempDocFileInfEntityList.get(0).getConstractRelatedId());
                        tempData.setFinRelatedId(tempDocFileInfEntityList.get(0).getFinRelatedId());
                        tempData.setBusRelatedId(tempDocFileInfEntityList.get(0).getBusRelatedId());

                    }
                }

                tempData.setFileList(tempDocFileInfEntityList);
                tempData.setParentTypeNo(groupNo);
                tempData.setParentTypeName(groupName);

                //为父级类别赋值
                boolean iscontin = false;
                for (Map<String, String> map1 : groupList) {
                    String _groupNo = map1.get("groupNo");
                    if (StringUtils.equals(groupNo, _groupNo)) {
                        iscontin = true;
                        break;
                    }
                }
                if (!iscontin) {
                    groupList.add(groupMap);

                }
                _fileList.add(tempData);
                MFLogger.info("getArchiveFile-条件查询业务文件配置信息_fileList:"+_fileList.size()+"------"+JSONObject.toJSON(_fileList));
            }
        }
        //为分组排序。
        JSONArray dicArray = parmCacheUtil.getDict("DOC_TYPE_GROUP");
        JSONArray dicArrayAsc = new JSONArray();

        int k = dicArray.size();

        JSONObject ary[] = new JSONObject[k];
        for (int n = 0; n < k; n++) {
            ary[n] = dicArray.getJSONObject(n);
        }

        for (int i = 0; i < k - 1; i++) {

            for (int m = 0; m < k - 1 - i; m++) {
                if (ary[m].getIntValue("seqn") > ary[m + 1].getIntValue("seqn")) {
                    JSONObject tempObj = ary[m];
                    ary[m] = ary[m + 1];
                    ary[m + 1] = tempObj;
                }
            }

        }

        List<Map<String, String>> groupSortList = new ArrayList<>();
        if (groupList != null) {

            for (JSONObject sort : ary) {

                for (Map<String, String> groupMap : groupList) {
                    if (groupMap.get("groupNo").equals(sort.getString("optCode"))) {
                        groupSortList.add(groupMap);
                        break;
                    }
                }
            }
        }
        FileShowVo fileShowVo = new FileShowVo();
        fileShowVo.setFileList(_fileList);
        fileShowVo.setGroupList(groupSortList);
        if (StringUtils.isNotEmpty(docShowType)) {

            fileShowVo.setDocShowType(docShowType);
        } else {
            fileShowVo.setDocShowType("list");
        }
        MFLogger.info("getArchiveFile-条件查询返回信息:"+JSONObject.toJSON(fileShowVo));
        return fileShowVo;

    }



    @Override
    public FileShowVo getRelatedFileList(List<QueryParm> queryParmList) {
        List<DocFileInfEntity> docFileInfEntityList = new ArrayList<>();
        List<String> fileTypeOrder = new ArrayList<>();
        Map<String, String> fileTypeOrderMap = new HashedMap();
        for (QueryParm queryParm : queryParmList) {
            List<DocFileInfEntity> _docFileInfEntityList = getRelateFile(queryParm);
            MFLogger.info("_docFileInfEntityList"+_docFileInfEntityList);
            for (DocFileInfEntity fileInfEntity : _docFileInfEntityList) {
                if (fileTypeOrderMap.containsKey(fileInfEntity.getTypeNo())) {
                    continue;
                } else {
                    fileTypeOrderMap.put(fileInfEntity.getTypeNo(), fileInfEntity.getTypeNo());
                    fileTypeOrder.add(fileInfEntity.getTypeNo());
                }
            }
            docFileInfEntityList.addAll(_docFileInfEntityList);
        }

        Map<String, List<DocFileInfEntity>> map = docFileInfEntityList.stream().collect(Collectors.groupingBy(p -> p.getTypeNo(), Collectors.toList()));
        List<Map<String, String>> groupList = new ArrayList<>();
        List<DocFileBizFiletypeConfigEntity> _fileList = new ArrayList<>();
        for (String typeNo : fileTypeOrder) {
            List<DocFileInfEntity> tempDocFileInfEntityList = map.get(typeNo);
            // 重写实体对象去重list
            Set<DocFileInfEntity> docFileInfEntitySet = new HashSet<>();
            docFileInfEntitySet.addAll(tempDocFileInfEntityList);
            tempDocFileInfEntityList.clear();
            tempDocFileInfEntityList.addAll(docFileInfEntitySet);

            DocFileInfEntity _docFileInfEntity = tempDocFileInfEntityList.get(0);

            String prdNo = _docFileInfEntity.getPrdUniqueVal();
            String flowNo = _docFileInfEntity.getFlowNo();
            String nodeNo = _docFileInfEntity.getNodeNo();
            String _bizNo = _docFileInfEntity.getBizNo();

            String parentTypeNo = _docFileInfEntity.getParentTypeNo();

            DocFileBizFiletypeConfigEntity parmDocFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();
            parmDocFileBizFiletypeConfigEntity.setPrdUniqueVal(prdNo);
            parmDocFileBizFiletypeConfigEntity.setFlowNo(flowNo);
            parmDocFileBizFiletypeConfigEntity.setNodeNo(nodeNo);
            parmDocFileBizFiletypeConfigEntity.setBizNo(_bizNo);
            parmDocFileBizFiletypeConfigEntity.setTypeNo(typeNo);


            // 目前由于数据问题，导致出现了多条数据，按照业务规划只能有一条，所以区第一条
            List<DocFileBizFiletypeConfigEntity> _list = docFileBizFiletypeConfigService.findList(parmDocFileBizFiletypeConfigEntity);
            //不等于空时再取数据
            if (!_list.isEmpty()) {
                // 获取类型信息
                DocFileBizFiletypeConfigEntity tempData = _list.get(0);
                //  String typeNo = tempData.getTypeNo();
                DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
                if (docFileTypeEntity == null) {
                    continue;
                }
                String groupNo = docFileTypeEntity.getGroupNo();
                String groupName = parmCacheUtil.getOptName("DOC_TYPE_GROUP", groupNo);
                DocFileTypeEntity parDocFileTypeEntity = docFileTypeService.findById(parentTypeNo);
                Map<String, String> groupMap = new HashMap<>();
                groupMap.put("groupName", groupName);
                groupMap.put("groupNo", groupNo);

                for (DocFileInfEntity docFileInfEntity : tempDocFileInfEntityList) {
                    docFileInfEntity.setParentTypeNo(groupNo);
                    docFileInfEntity.setParentTypeName(groupName);
                }


                if (docFileTypeEntity != null) {
                    tempData.setTypeDesc(docFileTypeEntity.getTypeDesc());
                }
                // 设置该类下已经上传的要件信息
                tempDocFileInfEntityList = tempDocFileInfEntityList.stream()
                        .sorted(Comparator.comparing(DocFileInfEntity::getFileId)).
                        sorted(Comparator.comparing(DocFileInfEntity::getCreateTime)).collect(Collectors.toList());
                tempData.setFileList(tempDocFileInfEntityList);
                tempData.setParentTypeNo(groupNo);
                tempData.setParentTypeName(groupName);
                if (tempDocFileInfEntityList.size() > 0) {
                    tempData.setCusRelatedId(tempDocFileInfEntityList.get(0).getCusRelatedId());
                    tempData.setApplyRelatedId(tempDocFileInfEntityList.get(0).getApplyRelatedId());
                    tempData.setConstractRelatedId(tempDocFileInfEntityList.get(0).getConstractRelatedId());
                    tempData.setFinRelatedId(tempDocFileInfEntityList.get(0).getFinRelatedId());
                    tempData.setBusRelatedId(tempDocFileInfEntityList.get(0).getBusRelatedId());

                }

                //为父级类别赋值

                boolean iscontin = false;
                for (Map<String, String> map1 : groupList) {
                    String _groupNo = map1.get("groupNo");
                    if (StringUtils.equals(groupNo, _groupNo)) {
                        iscontin = true;
                        break;
                    }
                }

                if (!iscontin) {
                    groupList.add(groupMap);

                }
                _fileList.add(tempData);
            }
        }

        //为分组排序。
        JSONArray dicArray = parmCacheUtil.getDict("DOC_TYPE_GROUP");
        JSONArray dicArrayAsc = new JSONArray();


        int k = dicArray.size();

        JSONObject ary[] = new JSONObject[k];
        for (int n = 0; n < k; n++) {
            ary[n] = dicArray.getJSONObject(n);
        }

        for (int i = 0; i < k - 1; i++) {

            for (int m = 0; m < k - 1 - i; m++) {
                if (ary[m].getIntValue("seqn") > ary[m + 1].getIntValue("seqn")) {
                    JSONObject tempObj = ary[m];
                    ary[m] = ary[m + 1];
                    ary[m + 1] = tempObj;
                }
            }

        }

        List<Map<String, String>> groupSortList = new ArrayList<>();
        if (groupList != null) {

            for (JSONObject sort : ary) {

                for (Map<String, String> groupMap : groupList) {
                    if (groupMap.get("groupNo").equals(sort.getString("optCode"))) {
                        groupSortList.add(groupMap);
                        break;
                    }
                }
            }
        }

        FileShowVo fileShowVo = new FileShowVo();
        fileShowVo.setFileList(_fileList);
        fileShowVo.setGroupList(groupSortList);
        if (StringUtils.isNotEmpty(docShowType)) {

            fileShowVo.setDocShowType(docShowType);
        } else {
            fileShowVo.setDocShowType("list");
        }
        return fileShowVo;
    }



    public List<DocFileInfEntity> getRelateFile(QueryParm queryParm) {
        String cusRelatedId = queryParm.getCusRelatedId();
        String applyRelatedId = queryParm.getApplyRelatedId();
        String constractRelatedId = queryParm.getConstractRelatedId();
        String finRelatedId = queryParm.getFinRelatedId();
        String bizNo = queryParm.getBizNo();
        String prdUniqueVal = queryParm.getPrdUniqueVal();
        String busRelateId = queryParm.getBusRelatedId();

        boolean cusRelatedIdEmpty = StringUtils.isEmpty(cusRelatedId);
        boolean applyRelatedIdEmpty = StringUtils.isEmpty(applyRelatedId);
        boolean constractRelatedIdEmpty = StringUtils.isEmpty(constractRelatedId);
        boolean finRelatedIdEmpty = StringUtils.isEmpty(finRelatedId);
        boolean bizNoEmpty = StringUtils.isEmpty(bizNo);
        boolean prdUniqueValNoEmpty = StringUtils.isEmpty(prdUniqueVal);
        boolean busRelateIdEmpty = StringUtils.isEmpty(busRelateId);

        QueryWrapper queryWrapper = new QueryWrapper();
        if (!cusRelatedIdEmpty) {
            queryWrapper.eq("cus_related_id", cusRelatedId);
        }

        if (!applyRelatedIdEmpty) {
            queryWrapper.eq("apply_related_id", applyRelatedId);
        }

        if (!constractRelatedIdEmpty) {
            queryWrapper.eq("constract_related_id", constractRelatedId);
        }

        if (!finRelatedIdEmpty) {
            queryWrapper.eq("fin_related_id", finRelatedId);
        }

        if (!bizNoEmpty) {
            queryWrapper.eq("biz_no", bizNo);
        }

        if (!prdUniqueValNoEmpty) {
            queryWrapper.eq("prd_unique_val", prdUniqueVal);
        }

        if (!busRelateIdEmpty) {
            queryWrapper.eq("bus_related_id", busRelateId);
        }
        queryWrapper.orderByAsc("type_no");
        queryWrapper.orderByDesc("create_time");

        List<DocFileInfEntity> tmpDocFileInfEntityList = null;
        if (queryWrapper.isEmptyOfWhere()) {
            // 查询条件为空时不做查询
            tmpDocFileInfEntityList = new ArrayList<DocFileInfEntity>();
        } else {
            tmpDocFileInfEntityList = docFileInfMapper.selectList(queryWrapper);
        }

        List<DocFileInfEntity> docFileInfEntityList = rightFilter(tmpDocFileInfEntityList);


        return filler(docFileInfEntityList, queryParm.getDocFileInfEntityList());

    }


    /**
     * 按权限过滤要件docFileInfEntityList
     *
     * @param
     * @return
     * @throws ServiceException
     */
    private List<DocFileInfEntity> rightFilter(List<DocFileInfEntity> docFileInfEntityList) throws ServiceException {
        List<DocFileInfEntity> result = new ArrayList<>();
        List<DocFileTypeEntity> righList = docFileTypeService.findRightList();
        if (righList != null) {
            JSONObject rightSetting = new JSONObject();
            for (DocFileTypeEntity typeEntity : righList) {
                JSONObject right = new JSONObject();
                right.put("role", typeEntity.getRoleReadAuth());
                right.put("dep", typeEntity.getDeptReadAuth());
                rightSetting.put(typeEntity.getTypeNo(), right);
            }


            if (rightSetting.size() > 0) {

                //开始过滤
                for (DocFileInfEntity file : docFileInfEntityList) {
                    if (rightSetting.containsKey(file.getTypeNo())) {

                        String depRight = rightSetting.getJSONObject(file.getTypeNo()).getString("dep");
                        String roleRight = rightSetting.getJSONObject(file.getTypeNo()).getString("role");
                        //如果配置了权限，开始过滤
                        String depNo = (String) requestUtil.getUserInfo(CommonConstant.BR_NO);//当前登录人部门
                        String roleNo = (String) requestUtil.getUserInfo("roleNo"); //001|003
                        //判断是否有部门 depNO
                        if (StringUtils.isNotEmpty(depRight)) {
                            if (depRight.indexOf(depNo) > -1) {
                                result.add(file);
                            } else {
                                //判断角色
                                if (StringUtils.isNotEmpty(roleRight)) {
                                    String[] roleRightAry = roleRight.split(",");
                                    String[] roleNoAry = roleNo.split("\\|");
                                    boolean existFlag = ary1InAry2(roleNoAry, roleRightAry);
                                    if (existFlag) {
                                        result.add(file);
                                    }

                                }
                            }
                        } else {
                            //判断角色
                            if (StringUtils.isNotEmpty(roleRight)) {
                                String[] roleRightAry = roleRight.split(",");
                                String[] roleNoAry = roleNo.split("\\|");
                                boolean existFlag = ary1InAry2(roleNoAry, roleRightAry);
                                if (existFlag) {
                                    result.add(file);
                                }

                            }
                        }
                    } else {
                        result.add(file);
                    }
                }
            } else {
                result = docFileInfEntityList;
            }
        }


        return result;
    }

    /**
     * 判断ary1中是否有值在ary2中存在，如果存在返回true 否则返回false
     *
     * @param ary1
     * @param ary2
     * @return
     * @throws ServiceException
     */
    private boolean ary1InAry2(String ary1[], String ary2[]) throws ServiceException {
        boolean result = false;
        if (ary1.length > 0) {
            if (ary2.length > 0) {
                for (int i = 0; i < ary1.length; i++) {
                    for (int k = 0; k < ary2.length; k++) {
                        if (ary1[i].equals(ary2[k])) {
                            result = true;
                            break;
                        }
                    }
                    if (result) {
                        break;
                    }
                }
            }
        }
        return result;
    }


    @Override
    public List<GroupVo> getGroupTreeDataNoTypeNo(List<QueryParm> queryParms) {
        List<DocFileInfEntity> docFileInfEntityList = new ArrayList<>();

        for (QueryParm queryParm : queryParms) {
            List<DocFileInfEntity> _docFileInfEntityList = getRelateFile(queryParm);
            docFileInfEntityList.addAll(_docFileInfEntityList);
        }

        List<DocFileInfEntity> copyFileInfEntityList = new ArrayList<>();

        for (DocFileInfEntity _docFileInfEntity : docFileInfEntityList) {
            String typeNo = _docFileInfEntity.getTypeNo();
            DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
            String typeClass = docFileTypeEntity.getTypeClass();
            String groupNo = docFileTypeEntity.getGroupNo();

            String[] groupList = null;
            String[] typeClassList = null;

            if (groupNo != null) {
                groupList = groupNo.split("\\|");
            }
            if (typeClass != null) {
                typeClassList = typeClass.split("\\|");
            }


            if (groupList != null) {
                for (String _group : groupList) {
                    if (typeClassList != null) {
                        for (String _typeClass : typeClassList) {
                            DocFileInfEntity copydocFileInfEntity = new DocFileInfEntity();
                            BeanUtils.copyProperties(_docFileInfEntity, copydocFileInfEntity);
                            copydocFileInfEntity.setTypeClass(_typeClass);
                            copydocFileInfEntity.setGroupNo(_group);
                            copyFileInfEntityList.add(copydocFileInfEntity);
                        }
                    } else {
                        DocFileInfEntity copydocFileInfEntity = new DocFileInfEntity();
                        BeanUtils.copyProperties(_docFileInfEntity, copydocFileInfEntity);
                        copydocFileInfEntity.setGroupNo(groupNo);
                        copyFileInfEntityList.add(copydocFileInfEntity);
                    }
                }
            } else {
                DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                copyFileInfEntityList.add(copydocFileInfEntity);
            }
            _docFileInfEntity.setTypeClass(typeClass);
            _docFileInfEntity.setGroupNo(groupNo);
        }

        docFileInfEntityList = copyFileInfEntityList;


        // 将fileTypeVoList 按照TypeClass进行分组
        Map<String, List<DocFileInfEntity>> typeClassMap = docFileInfEntityList.stream().collect(Collectors.groupingBy(p -> p.getTypeClass(), Collectors.toList()));

        List<FileClassVo> fileClassVoList = new ArrayList();
        typeClassMap.forEach((typeClass, tempDocFileInfEntityList) -> {
            FileClassVo fileClassVo = new FileClassVo();
            // 根据字典设置
            String label = "要件资料";
            // 设置大类名称
            DocTypeClassEntity docTypeClassEntity = docTypeClassService.findByTypeNo(typeClass);
            // 默认为要件资料
            if (docTypeClassEntity != null) {
                String typeName = docTypeClassEntity.getTypeName();
                if (StringUtils.isNotEmpty(typeName)) {
                    label = typeName;
                }
            }

            fileClassVo.setId(typeClass);
            fileClassVo.setLabel(label);
            String groupNo = tempDocFileInfEntityList.get(0).getGroupNo();
            fileClassVo.setGroupNo(groupNo);
            fileClassVo.setChildren(tempDocFileInfEntityList);
            fileClassVoList.add(fileClassVo);
        });

        // fileClassVoList 按照groupNo进行分组

        Map<String, List<FileClassVo>> fileGroupMap = fileClassVoList.stream().collect(Collectors.groupingBy(p -> p.getGroupNo(), Collectors.toList()));
        List<GroupVo> fileGroupVoList = new ArrayList();
        fileGroupMap.forEach((groupNo, tempFileClassList) -> {
            GroupVo groupVo = new GroupVo();
            // 根据字典设置
            String label = "分组名称";
            String greoupName = parmCacheUtil.getOptName("DOC_TYPE_GROUP", groupNo);
            if (StringUtils.isNotEmpty(greoupName)) {
                label = greoupName;
            }
            groupVo.setLabel(label);
            groupVo.setId(groupNo);
            groupVo.setChildren(tempFileClassList);
            fileGroupVoList.add(groupVo);
        });

        return fileGroupVoList;
    }

    /**
     * 功能:获取属性格式的数据没有大类
     *
     * @param queryParms
     */
    @Override
    public List<GroupVo> getGroupTreeDataNoTypeClass(List<QueryParm> queryParms) {
        log.info("getGroupTreeDataNoTypeClass queryParms:"+JSONObject.toJSONString(queryParms));
        List<DocFileInfEntity> docFileInfEntityList = new ArrayList<>();

        String bizNo = null;
        Set set =  new HashSet<>();
        for (QueryParm queryParm : queryParms) {
            bizNo =  queryParm.getBizNo();
            if (!set.contains(queryParm)){
                set.add(queryParm);
                List<DocFileInfEntity> _docFileInfEntityList = getRelateFile(queryParm);
                docFileInfEntityList.addAll(_docFileInfEntityList);
            }
        }

        List<DocFileInfEntity> copyFileInfEntityList = new ArrayList<>();

        List<String> typeNoOrderList = new ArrayList<>();
        Map<String, String> typeNoMap = new HashedMap();
        for (DocFileInfEntity _docFileInfEntity : docFileInfEntityList) {
            String typeNo = _docFileInfEntity.getTypeNo();

            if (!typeNoMap.containsKey(typeNo)) {
                typeNoMap.put(typeNo, typeNo);
                typeNoOrderList.add(typeNo);
            }
            DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
            String typeClass = docFileTypeEntity.getTypeClass();
            String groupNo = docFileTypeEntity.getGroupNo();

            String[] groupList = null;
            String[] typeClassList = null;

            if (groupNo != null) {
                groupList = groupNo.split("\\|");
            }
            if (typeClass != null) {
                typeClassList = typeClass.split("\\|");
            }


            if (groupList != null) {
                for (String _group : groupList) {
                    if (typeClassList != null) {
                        for (String _typeClass : typeClassList) {
                            DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                            copydocFileInfEntity.setTypeClass(typeClass);
                            copydocFileInfEntity.setGroupNo(groupNo);
                            copyFileInfEntityList.add(copydocFileInfEntity);
                        }
                    } else {
                        DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                        copydocFileInfEntity.setGroupNo(groupNo);
                        copyFileInfEntityList.add(copydocFileInfEntity);
                    }
                }
            } else {
                DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                copyFileInfEntityList.add(copydocFileInfEntity);
            }
            _docFileInfEntity.setTypeClass(typeClass);
            _docFileInfEntity.setGroupNo(groupNo);
        }

        docFileInfEntityList = copyFileInfEntityList;


        // 按照typeNo进行分组
        Map<String, List<DocFileInfEntity>> fileTypeMap = docFileInfEntityList.stream().sorted(Comparator.comparing(DocFileInfEntity::getCreateTime)).collect(Collectors.groupingBy(p -> p.getTypeNo(), Collectors.toList()));
        List<FileTypeVo> fileTypeVoList = new ArrayList();
//        fileTypeMap.forEach((typeNo,tempDocFileInfEntityList)->{
        for (String typeNo : typeNoOrderList) {
            List<DocFileInfEntity> tempDocFileInfEntityList = fileTypeMap.get(typeNo);
            FileTypeVo fileTypeVo = new FileTypeVo();
            fileTypeVo.setId(typeNo);
            String label = tempDocFileInfEntityList.get(0).getTypeName();
            String typeClass = tempDocFileInfEntityList.get(0).getTypeClass();
            String groupNo = tempDocFileInfEntityList.get(0).getGroupNo();
            if (StringUtils.isEmpty(groupNo)) {
                groupNo = "default";
            }
            fileTypeVo.setGroupNo(groupNo);
            fileTypeVo.setLabel(label);
            fileTypeVo.setTypeClass(typeClass);
            fileTypeVo.setChildren(tempDocFileInfEntityList);
            fileTypeVoList.add(fileTypeVo);
        }

        Map<String, List<FileTypeVo>> fileGroupMap = fileTypeVoList.stream().collect(Collectors.groupingBy(p -> p.getGroupNo(), Collectors.toList()));
        List<GroupVo> fileGroupVoList = new ArrayList();
        fileGroupMap.forEach((groupNo, tempFileTypeList) -> {
            GroupVo groupVo = new GroupVo();
            // 根据字典设置
            String label = "分组名称";

            String greoupName = parmCacheUtil.getOptName("DOC_TYPE_GROUP", groupNo);
            if (StringUtils.isNotEmpty(greoupName)) {
                label = greoupName;
            }

            groupVo.setLabel(label);
            groupVo.setId(groupNo);
            groupVo.setChildren(tempFileTypeList);
            fileGroupVoList.add(groupVo);
        });

        return fileGroupVoList;
    }

    @Override
    public List<GroupVo> getGroupTreeData(List<QueryParm> queryParms) {
        List<DocFileInfEntity> docFileInfEntityList = new ArrayList<>();

        for (QueryParm queryParm : queryParms) {
            List<DocFileInfEntity> _docFileInfEntityList = getRelateFile(queryParm);
            docFileInfEntityList.addAll(_docFileInfEntityList);
        }

        List<DocFileInfEntity> copyFileInfEntityList = new ArrayList<>();
        List<String> typeNoOrderList = new ArrayList<>();
        Map<String, String> typeNoMap = new HashedMap();
        for (DocFileInfEntity _docFileInfEntity : docFileInfEntityList) {
            String typeNo = _docFileInfEntity.getTypeNo();
            if (!typeNoMap.containsKey(typeNo)) {
                typeNoMap.put(typeNo, typeNo);
                typeNoOrderList.add(typeNo);
            }
            DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
            String typeClass = docFileTypeEntity.getTypeClass();
            String groupNo = docFileTypeEntity.getGroupNo();

            String[] groupList = null;
            String[] typeClassList = null;

            if (groupNo != null) {
                groupList = groupNo.split("\\|");
            }
            if (typeClass != null) {
                typeClassList = typeClass.split("\\|");
            }


            if (groupList != null) {
                for (String _group : groupList) {
                    if (typeClassList != null) {
                        for (String _typeClass : typeClassList) {
                            DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                            copydocFileInfEntity.setTypeClass(typeClass);
                            copydocFileInfEntity.setGroupNo(groupNo);
                            copyFileInfEntityList.add(copydocFileInfEntity);
                        }
                    } else {
                        DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                        copydocFileInfEntity.setGroupNo(groupNo);
                        copyFileInfEntityList.add(copydocFileInfEntity);
                    }
                }
            } else {
                DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                copyFileInfEntityList.add(copydocFileInfEntity);
            }
            _docFileInfEntity.setTypeClass(typeClass);
            _docFileInfEntity.setGroupNo(groupNo);
        }

        docFileInfEntityList = copyFileInfEntityList;


        // 按照typeNo进行分组
        Map<String, List<DocFileInfEntity>> fileTypeMap = docFileInfEntityList.stream().collect(Collectors.groupingBy(p -> p.getTypeNo(), Collectors.toList()));
        List<FileTypeVo> fileTypeVoList = new ArrayList();
//        fileTypeMap.forEach((typeNo,tempDocFileInfEntityList)->{
        for (String typeNo : typeNoOrderList) {
            List<DocFileInfEntity> tempDocFileInfEntityList = fileTypeMap.get(typeNo);
            FileTypeVo fileTypeVo = new FileTypeVo();

            fileTypeVo.setId(typeNo);
            String label = tempDocFileInfEntityList.get(0).getTypeName();
            String typeClass = tempDocFileInfEntityList.get(0).getTypeClass();
            String groupNo = tempDocFileInfEntityList.get(0).getGroupNo();
            if (StringUtils.isEmpty(groupNo)) {
                groupNo = "default";
            }
            fileTypeVo.setGroupNo(groupNo);
            fileTypeVo.setLabel(label);
            fileTypeVo.setTypeClass(typeClass);
            fileTypeVo.setChildren(tempDocFileInfEntityList);
            fileTypeVoList.add(fileTypeVo);
        }

        // 将fileTypeVoList 按照TypeClass进行分组
        Map<String, List<FileTypeVo>> typeClassMap = fileTypeVoList.stream().collect(Collectors.groupingBy(p -> p.getTypeClass(), Collectors.toList()));

        List<FileClassVo> fileClassVoList = new ArrayList();
        typeClassMap.forEach((typeClass, tempFileTypeList) -> {
            FileClassVo fileClassVo = new FileClassVo();
            // 根据字典设置
            String label = "要件资料";
            // 设置大类名称
            DocTypeClassEntity docTypeClassEntity = docTypeClassService.findByTypeNo(typeClass);
            // 默认为要件资料
            if (docTypeClassEntity != null) {
                String typeName = docTypeClassEntity.getTypeName();
                if (StringUtils.isNotEmpty(typeName)) {
                    label = typeName;
                }
            }

            fileClassVo.setId(typeClass);
            fileClassVo.setLabel(label);
            String groupNo = tempFileTypeList.get(0).getGroupNo();
            fileClassVo.setGroupNo(groupNo);
            fileClassVo.setChildren(tempFileTypeList);
            fileClassVoList.add(fileClassVo);
        });

        // fileClassVoList 按照groupNo进行分组

        Map<String, List<FileClassVo>> fileGroupMap = fileClassVoList.stream().collect(Collectors.groupingBy(p -> p.getGroupNo(), Collectors.toList()));
        List<GroupVo> fileGroupVoList = new ArrayList();
        fileGroupMap.forEach((groupNo, tempFileClassList) -> {
            GroupVo groupVo = new GroupVo();
            // 根据字典设置
            String label = "分组名称";
            String greoupName = parmCacheUtil.getOptName("DOC_TYPE_GROUP", groupNo);
            if (StringUtils.isNotEmpty(greoupName)) {
                label = greoupName;
            }
            groupVo.setLabel(label);
            groupVo.setId(groupNo);
            groupVo.setChildren(tempFileClassList);
            fileGroupVoList.add(groupVo);
        });

        return fileGroupVoList;
    }

    /**
     * 功能：按照大类进行分组
     *
     * @param queryParms
     * @return
     */
    @Override
    public List<FileClassVo> getClassTreeData(List<QueryParm> queryParms) {
        List<DocFileInfEntity> docFileInfEntityList = new ArrayList<>();

        for (QueryParm queryParm : queryParms) {
            List<DocFileInfEntity> _docFileInfEntityList = getRelateFile(queryParm);
            docFileInfEntityList.addAll(_docFileInfEntityList);
        }

        List<DocFileInfEntity> copyFileInfEntityList = new ArrayList<>();

        for (DocFileInfEntity _docFileInfEntity : docFileInfEntityList) {
            String typeNo = _docFileInfEntity.getTypeNo();
            DocFileTypeEntity docFileTypeEntity = docFileTypeService.findById(typeNo);
            String typeClass = docFileTypeEntity.getTypeClass();
            String groupNo = docFileTypeEntity.getGroupNo();

            String[] groupList = null;
            String[] typeClassList = null;

            if (groupNo != null) {
                groupList = groupNo.split("|");
            }
            if (typeClass != null) {
                typeClassList = typeClass.split("|");
            }


            if (groupList != null) {
                for (String _group : groupList) {
                    if (typeClassList != null) {
                        for (String _typeClass : typeClassList) {
                            DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                            copydocFileInfEntity.setTypeClass(typeClass);
                            copydocFileInfEntity.setGroupNo(groupNo);
                            copyFileInfEntityList.add(copydocFileInfEntity);
                        }
                    } else {
                        DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                        copydocFileInfEntity.setGroupNo(groupNo);
                        copyFileInfEntityList.add(copydocFileInfEntity);
                    }
                }
            } else {
                DocFileInfEntity copydocFileInfEntity = _docFileInfEntity;
                copyFileInfEntityList.add(copydocFileInfEntity);
            }
            _docFileInfEntity.setTypeClass(typeClass);
            _docFileInfEntity.setGroupNo(groupNo);
        }

        docFileInfEntityList = copyFileInfEntityList;


        // 按照typeNo进行分组
        Map<String, List<DocFileInfEntity>> fileTypeMap = docFileInfEntityList.stream().collect(Collectors.groupingBy(p -> p.getTypeNo(), Collectors.toList()));
        List<FileTypeVo> fileTypeVoList = new ArrayList();
        fileTypeMap.forEach((typeNo, tempDocFileInfEntityList) -> {
            FileTypeVo fileTypeVo = new FileTypeVo();
            fileTypeVo.setId(typeNo);
            String label = tempDocFileInfEntityList.get(0).getTypeName();
            String typeClass = tempDocFileInfEntityList.get(0).getTypeClass();
            String groupNo = tempDocFileInfEntityList.get(0).getGroupNo();
            if (StringUtils.isEmpty(groupNo)) {
                groupNo = "default";
            }
            fileTypeVo.setGroupNo(groupNo);
            fileTypeVo.setLabel(label);
            fileTypeVo.setTypeClass(typeClass);
            fileTypeVo.setChildren(tempDocFileInfEntityList);
            fileTypeVoList.add(fileTypeVo);
        });

        // 将fileTypeVoList 按照TypeClass进行分组
        Map<String, List<FileTypeVo>> typeClassMap = fileTypeVoList.stream().collect(Collectors.groupingBy(p -> p.getTypeClass(), Collectors.toList()));

        List<FileClassVo> fileClassVoList = new ArrayList();
        typeClassMap.forEach((typeClass, tempFileTypeList) -> {
            FileClassVo fileClassVo = new FileClassVo();
            // 设置大类名称
            DocTypeClassEntity docTypeClassEntity = docTypeClassService.findByTypeNo(typeClass);
            // 默认为要件资料
            String label = "要件资料";
            if (docTypeClassEntity != null) {
                String typeName = docTypeClassEntity.getTypeName();
                if (StringUtils.isNotEmpty(typeName)) {
                    label = typeName;
                }
            }
            fileClassVo.setId(typeClass);
            fileClassVo.setLabel(label);
            String groupNo = tempFileTypeList.get(0).getGroupNo();
            fileClassVo.setGroupNo(groupNo);
            fileClassVo.setChildren(tempFileTypeList);
            fileClassVoList.add(fileClassVo);
        });

        return fileClassVoList;
    }

    /**
     *  根据id批量删除
     * @param fileIds
     * @throws ServiceException
     */
    @Override
    public void deleteByIds(List<String> fileIds) throws ServiceException{
        try {
            docFileInfMapper.deleteBatchIds(fileIds);
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR, fileIds, e);
        }
    }

    @Override
    public void deleteByBizNo(String bizNo) throws ServiceException {
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no", bizNo);
        docFileInfMapper.delete(queryWrapper);
    }

    @Override
    public String getDocFileIfType(JSONObject jsonObject) {
        String applyId = jsonObject.getString("applyId");
        String pactId = jsonObject.getString("pactId");
        log.info("请求参数{}","申请id："+applyId+"。合同id："+pactId);
        PledgeBusRelInfoDTO pledgeBusRelInfoDTO = new PledgeBusRelInfoDTO();
        pledgeBusRelInfoDTO.setRelId(applyId);
        pledgeBusRelInfoDTO.setRelPactId(pactId);
        List<PledgeBusRelInfoDTO> pledgeBusList = pledgeFeignClient.getPledgeBusList(pledgeBusRelInfoDTO);
        Integer max = 0;
        JSONObject result = new JSONObject();
        if (pledgeBusList.size()>0){
            for (PledgeBusRelInfoDTO busRelInfoDTO : pledgeBusList) {
                Integer num = 0;
                String pledgeId = busRelInfoDTO.getPledgeId();
                LambdaQueryWrapper<DocFileInfEntity> qw = new LambdaQueryWrapper<>();
                qw.eq(DocFileInfEntity::getBizNo,pledgeId);
                List<DocFileInfEntity> docFileInfEntityList = docFileInfMapper.selectList(qw);
                for (DocFileInfEntity docFileInfEntity : docFileInfEntityList) {
                    String filePath = docFileInfEntity.getFilePath();
                    String substring = filePath.substring(filePath.lastIndexOf(".") + 1);
                    switch (substring){
                        case "7z" : num++; break;
                        case "zip": num++; break;
                        case "rar": num++; break;
                        case "apz": num++; break;
                        case "ar" : num++; break;
                        case "bz" : num++; break;
                        case "car": num++; break;
                        case "dar": num++; break;
                        case "f"  : num++; break;
                        case "ha" : num++; break;
                        case "hbc": num++; break;
                        case "hbe": num++; break;
                        case "hpk": num++; break;
                        case "hyp": num++; break;
                        case "cpgz": num++; break;
                        case "hbc2": num++; break;
                    }
                    log.info("num:{}",num);
                    if (num>0){
                        result.put("doc_202212101704",num);
                        return result.toJSONString();
                    }
                }
            }
        }
        LambdaQueryWrapper<DocFileInfEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(DocFileInfEntity::getBizNo,pactId);
        List<DocFileInfEntity> docFileInfEntityList = docFileInfMapper.selectList(qw);
        for (DocFileInfEntity docFileInfEntity : docFileInfEntityList) {
            String filePath = docFileInfEntity.getFilePath();
            String substring = filePath.substring(filePath.lastIndexOf(".") + 1);
            switch (substring){
                case "7z" : max++; break;
                case "zip": max++; break;
                case "rar": max++; break;
                case "apz": max++; break;
                case "ar" : max++; break;
                case "bz" : max++; break;
                case "car": max++; break;
                case "dar": max++; break;
                case "f"  : max++; break;
                case "ha" : max++; break;
                case "hbc": max++; break;
                case "hbe": max++; break;
                case "hpk": max++; break;
                case "hyp": max++; break;
                case "cpgz": max++; break;
                case "hbc2": max++; break;
            }
            log.info("max:{}",max);
            if (max>0){
                result.put("doc_202212101704",max);
                return result.toJSONString();
            }
        }
        result.put("doc_202212101704",max);
        return result.toJSONString();
    }


    @Override
    public JSONArray getDocIdFileByCusId(String cusId) throws Exception {
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no", cusId);
        queryWrapper.eq("flow_no", "cus_type");
        queryWrapper.eq("node_no", "cus_apply");
        queryWrapper.eq("prd_unique_val","92017390341c4818a0e44704f041f9db");
        queryWrapper.in("type_no","02-01","02-02");
        queryWrapper.orderByAsc("create_time");
        List<DocFileInfEntity> docFileInfEntityList = docFileInfMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(docFileInfEntityList)){
            return null;
        }
        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(docFileInfEntityList));
        return jsonArray;
    }

    @Override
    public JSONArray getDocIdFileByApplyId(String applyId) throws Exception {
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no", applyId);
        queryWrapper.in("type_no","FH-111","FH-112");
        queryWrapper.orderByAsc("create_time");
        List<DocFileInfEntity> docFileInfEntityList = docFileInfMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(docFileInfEntityList)){
            return null;
        }
        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(docFileInfEntityList));
        return jsonArray;
    }

    @Override
    public void uploadFileForZd(String fileName, String savePathStr, String bizNo) throws Exception {
        DocFileBizFiletypeConfigEntity bizFileType=new DocFileBizFiletypeConfigEntity();
        bizFileType.setScId(cn.mftcc.common.utils.UUIDUtil.getUUID());
        bizFileType.setBizNo(bizNo);
        bizFileType.setTypeNo("reg_prove");
        bizFileType.setTypeName("登记证明");
        bizFileType.setFlowNo("reg_prove");
        bizFileType.setNodeNo("reg_prove");
        bizFileType.setCorpId("1000000002");
        bizFileType.setIsEditFlag(false);
        bizFileType.setPrdUniqueVal("reg_prove");
        bizFileType.setIfUpload("1");
        bizFileType.setIsEncrypt("0");
        bizFileType.setIfMustRead("0");
        bizFileType.setUploadFileLimit(0);
        docFileBizFiletypeConfigService.insert(bizFileType);
        MultipartFile file = FileUtils.base64ToMultipartFileForZip(fileName, savePathStr);
        JSONObject uploadParmJson = new JSONObject();
        JSONObject gson = this.docFileBizFiletypeConfigService.upLoadFile(bizFileType, file, "lease", JSONObject.toJSONString(uploadParmJson));
        if (!"0000".equals(gson.getString("code"))) {
            MFLogger.info("保存影像资料 失败！");
            throw new Exception("保存影像资料 失败");
        }
    }
    @Override
    public String ordinaryReadFileBase64ForCompress(String fileId) throws Exception {
        String fieBase64 = "";
        InputStream inputStream = null;
        InputStream inputStreamNew = null;
        ByteArrayOutputStream outStream = null;
        try {
            DocFileInfEntity tmpObj = findById(fileId);
            inputStream = ossConfig.readOss(tmpObj.getFilePath());
            String filePath = tmpObj.getFilePath();
            String fileTyle = filePath.substring(filePath.lastIndexOf("."),filePath.length());
            String fileName = tmpObj.getFileName() + fileTyle;
            String contentType = "";
            //image/jpeg ：jpg图片格式
            //image/png：png图片格式

            if(".jpg".equals(fileTyle)){
                contentType = "image/jpeg";
            }else if(".png".equals(fileTyle)){
                contentType = "image/png";
            }else{
                throw new Exception("不支持jpg和png之外的文件格式压缩");
            }
            log.info("文件名称：" + fileName);
            log.info("文件类型：" + contentType);
            MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, contentType, inputStream);

            inputStreamNew = docFileBizFiletypeConfigService.compressImgToSize(multipartFile,2048.0,0.9);
            //得到图片的二进制数据，以二进制封装得到数据，具有通用性
            outStream = new ByteArrayOutputStream();
            //创建一个Buffer字符串
            byte[] buffer = new byte[1024];
            //每次读取的字符串长度，如果为-1，代表全部读取完毕
            int len = 0;
            //使用一个输入流从buffer里把数据读取出来
            while ((len = inputStreamNew.read(buffer)) != -1) {
                //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                outStream.write(buffer, 0, len);
            }

            byte[] data = outStream.toByteArray();
            //对字节数组Base64编码
            BASE64Encoder encoder = new BASE64Encoder();
            fieBase64 = encoder.encode(data);
        } catch (Exception e) {
            MFLogger.error("读取文件Base64信息失败 fileid=" + fileId, e);
            throw new ServiceException("读取文件Base64信息", fileId, e);
        }finally {
            if(inputStream != null){
                //关闭输入流
                inputStream.close();
            }
            if(inputStreamNew != null){
                //关闭输入流
                inputStreamNew.close();
            }
            if(outStream != null){
                //关闭输出流
                outStream.close();
            }
        }
        return fieBase64;
    }
    @Override
    public JSONArray getDocIdFileSocByCusId(String cusId) throws Exception {
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no", cusId);
        queryWrapper.eq("flow_no", "cus_type");
        queryWrapper.eq("node_no", "cus_apply");
        queryWrapper.eq("prd_unique_val","92017390341c4818a0e44704f041f9db");
        queryWrapper.in("type_no","FH-91","FH-92");
        queryWrapper.orderByAsc("create_time");
        List<DocFileInfEntity> docFileInfEntityList = docFileInfMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(docFileInfEntityList)){
            return null;
        }
        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(docFileInfEntityList));
        return jsonArray;
    }

    /**
     *  根据文件名称查询文件
     * @param fileNames
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocFileInfEntity> findFileInfListByFileName(List<String> fileNames) throws ServiceException {
        try {
            QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("file_name", fileNames);
            return docFileInfMapper.selectList(queryWrapper);
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, fileNames, e);
        }
    }

    @Override
    public List<DocFileInfEntity> findFilesInfoByBizNo(List<String> bizNos) throws ServiceException {
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("biz_no", bizNos);
        return docFileInfMapper.selectList(queryWrapper);
    }

    @Override
    public InputStream getFileStreamOss(String id) {
//        DocFileInfEntity fileInfEntity = findById(fileId);

        ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = configFeignClient.selectById(id);
        InputStream inputStream = null;

        String filePath = "";
        if ("obs".equals(uploadOutterFunction)) {
            String obsId = configVoucherPushEntityDTO.getConfigCwVoucherMstId();
            filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
            try {
                //inputStream = new URL(filePath).openStream();
                URL url = new URL(filePath);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setConnectTimeout(5 * 1000);
                inputStream = conn.getInputStream();
            } catch (Exception e) {
                throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, id, e);
            }
        } else if ("oss".equals(uploadOutterFunction)) {

            // 填写Bucket名称，例如examplebucket。

            // 读取文件内容。
            inputStream = ossConfig.readOss(configVoucherPushEntityDTO.getFilePath());

        } else {
            filePath = docFileUploadPath + File.separator + configVoucherPushEntityDTO.getFilePath();
            try {
                inputStream = new FileInputStream(filePath);
            } catch (Exception e) {
                throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, id, e);
            }
        }
        return inputStream;
    }

    /**
     * 获取凭证txt类型文件内容
     * @param id
     * @return
     */
    @Override
    public List getVoucherTxtContent(String id) {
        List<String> content = new ArrayList<>();
        try {
            ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = configFeignClient.selectById(id);
            if ("obs".equals(uploadOutterFunction)) {
                String obsId = configVoucherPushEntityDTO.getConfigCwVoucherMstId();
                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                content = readHtmlFile(filePath);
            } else {
                String filePath = configVoucherPushEntityDTO.getFilePath();
                content = getVoucherFileContent(filePath);
            }


        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, id, e);
        }
        return content;
    }

    private List<String> getVoucherFileContent(String filePath) throws ServiceException {

        InputStreamReader isr = null;
        InputStream inputStream = null;
        BufferedReader read = null;
        List<String> list = new ArrayList<String>();
        try {
            inputStream = ossConfig.readOss(filePath);
            isr = new InputStreamReader(inputStream, "UTF-8");
            read = new BufferedReader(isr);
            String s = null;
            while ((s = read.readLine()) != null) {
                //System.out.println(s);
                list.add(s);
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            MFLogger.error("读取文件中的内容:" + filePath, e);
            throw new ServiceException("读取文件中的内容", filePath, e);

        }finally {
            try {
                if (isr != null){
                    isr.close();
                }
                if (inputStream != null){
                    inputStream.close();
                }
                if (read != null){
                    read.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        return list;
    }

    @Override
    public JSONObject getVoucherOfficeFileObj(String id) {
        JSONObject result = new JSONObject();
        try {
            ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = configFeignClient.selectById(id);
//            String fileUrlPath=docFileOfficeUrl+File.separator+fileInfEntity.getFilePath();
            String fileUrlPath = docOfficeOpenUrl + id;
            String suffix = "";
            if ("obs".equals(uploadOutterFunction)) {
                String obsId = configVoucherPushEntityDTO.getConfigCwVoucherMstId();
                String tmpAry[] = obsId.split("\\.");
                suffix = tmpAry[tmpAry.length - 1];//后缀名
            } else {
                String tmpAry[] = configVoucherPushEntityDTO.getFilePath().split("\\.");
                suffix = tmpAry[tmpAry.length - 1];//后缀名
            }


            String docKey = RandomUtil.randomString(20);

            result.put("gateWayAddr", gateWayAddr);
            result.put("serverName", serverName);
            result.put("officeDocKey", docKey);
            result.put("officeUrl", fileUrlPath);
            result.put("officeKey", id);
            result.put("officeSuffix", suffix);
            result.put("officeServie", officeServerPath);

            result.put("docOfficeView", docOfficeView);


        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, id, e);
        }
        MFLogger.info(JSONObject.toJSONString(result));
        return result;
    }

    @Override
    public InputStream getVoucherFileThumbStream(String id) {
        ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = configFeignClient.selectById(id);
        String filePath = docFileUploadPath + File.separator + configVoucherPushEntityDTO.getFilePath();
        InputStream instream = null;
        try {
            if ("oss".equals(uploadOutterFunction)) {
                instream = ossConfig.readOss(configVoucherPushEntityDTO.getFilePath());
            } else {
                instream = new FileInputStream(filePath);
            }
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, id, e);
        }
        return instream;
    }

    /**
     * 根据合同号查询对应的发票信息
     * @param pactNo
     * @return
     */
    @Override
    public List<DocFileInfEntity> findFileInfListByPactFP(String pactNo) {
        return docFileInfMapper.findFileInfListByPactFP(pactNo);
    }
}