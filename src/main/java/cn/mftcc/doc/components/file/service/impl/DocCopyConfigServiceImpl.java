/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.file.entity.DocCopyConfigEntity;
import cn.mftcc.doc.components.file.mapper.DocCopyConfigMapper;
import cn.mftcc.doc.components.file.service.DocCopyConfigService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.utils.RequestUtil;

import java.util.List;

/**
 * 影像资料赋值配置
 *
 * <AUTHOR>
 * @email 
 * @date 2022-10-13 19:18:03
 */
@Service("docCopyConfigService")
@Transactional(rollbackFor = ServiceException.class)
public class DocCopyConfigServiceImpl implements DocCopyConfigService {

    @Autowired
    private DocCopyConfigMapper docCopyConfigMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private RequestUtil requestUtil;

    @Override
    public IPage<DocCopyConfigEntity> findByPage(DocCopyConfigEntity docCopyConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocCopyConfigEntity> page = new Page<>();
            page.setCurrent(docCopyConfigEntity.getPageNo());
            page.setSize(docCopyConfigEntity.getPageSize());
            QueryWrapper<DocCopyConfigEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docCopyConfigEntity);
            return docCopyConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docCopyConfigEntity.getId(),e);
        }
    }

    @Override
    public void insert(DocCopyConfigEntity docCopyConfigEntity) throws ServiceException {
        try{
            docCopyConfigMapper.insert(docCopyConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docCopyConfigEntity.getId(),e);
        }
    }

    @Override
    public void update(DocCopyConfigEntity docCopyConfigEntity) throws ServiceException {
        try{
            docCopyConfigMapper.updateById(docCopyConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docCopyConfigEntity.getId(),e);
        }
    }

    @Override
    public DocCopyConfigEntity findById(Integer id) throws ServiceException {
        try{
            return docCopyConfigMapper.selectById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,id,e);
        }
    }

    @Override
    public void deleteById(Integer id) throws ServiceException {
        try{
            docCopyConfigMapper.deleteById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,id,e);
        }
    }

    @Override
    public List<DocCopyConfigEntity> selectListBy(DocCopyConfigEntity docCopyConfigEntity) throws ServiceException {
        try{
            QueryWrapper<DocCopyConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.setEntity(docCopyConfigEntity);
            return docCopyConfigMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,JSONObject.toJSONString(docCopyConfigEntity),e);
        }
    }

}