package cn.mftcc.doc.components.file.controller;


import cn.mftcc.common.utils.StringUtil;
import cn.mftcc.config.feign.dto.ConfigVoucherPushEntityDTO;
import cn.mftcc.doc.common.utils.OssConfig;
import cn.mftcc.doc.components.file.service.BatchVoucherPushService;
import cn.mftcc.doc.feign.client.ConfigFeignClient;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Map;

/**
 * 凭证推送金蝶
 */
@RestController
@RequestMapping("batchVoucherPush/voucherPush")
public class BatchVoucherPushController {

    @Autowired
    BatchVoucherPushService batchVoucherPushService;
    @Autowired
    private ConfigFeignClient configFeignClient;
    @Autowired
    private OssConfig ossConfig;
    public static final String HEADER_NAME_DEFAULT = "Content-Disposition";
    private static final String HEADER_VALUE_DEFAULT = "attchement;filename=";
    private static final String CONTENT_TYPE_DEF = "application/x-download";
    private static final String CONTENT_TYPE_DEF1 = "application/octet-stream";
    /**
     * 批量推送凭证
     * @return
     */
    @RequestMapping("voucherPush")
    public void batchVoucherPush() throws Exception {
        batchVoucherPushService.batchVoucherPush();
    }


    /**
     * 定时推送凭证附件到金蝶
     * @return
     */
    @RequestMapping("batchVoucherPushJD")
    public void batchVoucherPushJD() throws Exception {
        batchVoucherPushService.batchVoucherPushJD();
    }

    /**
     * 下载凭证附件
     * @param ajaxData
     */
    @RequestMapping("/downloadFile")
    public void downloadFile(@RequestBody JSONObject ajaxData, HttpServletResponse response) throws Exception {
        if (!ajaxData.containsKey("id") && StringUtil.isEmpty(ajaxData.getString("id"))){
            return;
        }
        String id = ajaxData.getString("id");
        InputStream fis =null;
        OutputStream toClient =null;
        try {
            response.setContentType("multipart/form-data");
            ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = configFeignClient.selectById(id);
            String fileName = configVoucherPushEntityDTO.getFileName();
            String filePath = configVoucherPushEntityDTO.getFilePath();
            fis = ossConfig.readOss(filePath);
            response.reset();
            response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
            response.addHeader("file-name*", URLEncoder.encode(fileName, "UTF-8"));
            toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType(CONTENT_TYPE_DEF1);
            byte[] buffer = new byte[1024 * 1024 * 4];
            int i = -1;
            while ((i = fis.read(buffer)) != -1) {
                toClient.write(buffer, 0, i);
            }
            toClient.flush();
            toClient.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            if (fis != null){
                fis.close();
            }
        }
    }
}
