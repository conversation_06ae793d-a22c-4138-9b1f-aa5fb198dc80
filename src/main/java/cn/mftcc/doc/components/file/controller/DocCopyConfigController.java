/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.file.entity.DocCopyConfigEntity;
import cn.mftcc.doc.components.file.service.DocCopyConfigService;

/**
 * 影像资料赋值配置
 *
 * <AUTHOR>
 * @email 
 * @date 2022-10-13 19:18:03
 */
@RestController
@RequestMapping("file/docCopyConfig")
@Api("影像资料赋值配置")
public class DocCopyConfigController {

    @Autowired
    private DocCopyConfigService docCopyConfigService;

    @PostMapping("/findByPage")
    @ApiOperation(value="分页查询", notes="分页查询")
    public R findByPage(@RequestBody DocCopyConfigEntity docCopyConfigEntity) {
        IPage<DocCopyConfigEntity> list = this.docCopyConfigService.findByPage(docCopyConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @PostMapping("/insert")
    @ApiOperation(value="新增方法", notes="新增方法")
    public R insert(@RequestBody DocCopyConfigEntity docCopyConfigEntity){
        this.docCopyConfigService.insert(docCopyConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @PutMapping("/update")
    @ApiOperation(value="更新方法", notes="更新方法")
    public R update(@RequestBody DocCopyConfigEntity docCopyConfigEntity){
        this.docCopyConfigService.update(docCopyConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @GetMapping("/findById/{id}")
    @ApiOperation(value="根据主键查询", notes="根据主键查询")
    @ApiImplicitParam(name="id",value="主键",required=true,paramType="path",dataType="Integer")
    public R findById(@PathVariable("id") Integer id){
        DocCopyConfigEntity docCopyConfigEntity = this.docCopyConfigService.findById(id);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docCopyConfigEntity);
    }

    @DeleteMapping("/deleteById/{id}")
    @ApiOperation(value="删除方法", notes="删除方法")
    public R deleteById(@PathVariable("id") Integer id){
        this.docCopyConfigService.deleteById(id);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}