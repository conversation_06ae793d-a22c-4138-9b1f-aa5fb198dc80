/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.controller;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.components.file.entity.DocFileBizFiletypeConfigEntity;
import cn.mftcc.doc.components.file.entity.DocFileInfBase64;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.entity.FileShowObj;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.file.service.DocFileInfService;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 业务数据的要件类型配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-05-08 14:31:02
 */
@RestController
@RequestMapping("file/docFileBizFiletypeConfig")
public class DocFileBizFiletypeConfigController {

    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;

    @Autowired
    private DocFileInfService docFileInfService;

    @Value("${mftcc.file.doc-show-type:}")
    private  String docShowType;


    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) {
        IPage<DocFileBizFiletypeConfigEntity> list = this.docFileBizFiletypeConfigService.findByPage(docFileBizFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }
    @RequestMapping("/findList")
    public R findList(@RequestBody DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) {
        List<FileShowObj> list = this.docFileBizFiletypeConfigService.getFileShowList(docFileBizFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    /**
     * 功能:获取当前节点的要件类型及该要件类型已经上传的附件信息
     * @param docFileBizFiletypeConfigEntity
     * @return
     */
    @RequestMapping("/findTypeAndList")
    public  R  findTypeAndList(@RequestBody DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) {
        List<DocFileBizFiletypeConfigEntity> list = this.docFileBizFiletypeConfigService.findTypeAndList(docFileBizFiletypeConfigEntity);
        String tmpDocShow="list";
        if(StringUtils.isNotEmpty(docShowType)){

            tmpDocShow=docShowType;
        }
        return  R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list).put("docShowType",tmpDocShow);

    }

    /**
     * 功能:获取当前节点的要件类型及该要件类型已经上传的附件信息
     * @param jsonObject
     * @return
     */
    @RequestMapping("/findTypeAndListMix")
    public  R  findTypeAndListMix(@RequestBody JSONObject jsonObject) {
        List<DocFileBizFiletypeConfigEntity> list = this.docFileBizFiletypeConfigService.findTypeAndListMix(jsonObject);
        String tmpDocShow="list";
        if(StringUtils.isNotEmpty(docShowType)){

            tmpDocShow=docShowType;
        }
        return  R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list).put("docShowType",tmpDocShow);

    }






    @RequestMapping("/findListByFileIdAry")
    public R findListByFileIdAry(@RequestBody JSONObject parmJson ) {
        List<FileShowObj> list = this.docFileBizFiletypeConfigService.findListByFileIdAry(parmJson);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/getFileIdArray")
    public R getFileIdArray(@RequestBody List<Map<String ,String>> parmList) {
        JSONArray list = this.docFileInfService.getFileIdArray(parmList);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }





    /**
     * 下载全部附件
     */

    @RequestMapping("/downloadAllFileZip")
    public void downloadAllFileZip(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
        this.docFileBizFiletypeConfigService.downloadAllFile(response, ajaxData);
    }

    /**
     * 下载全部模板文件
     */
    @RequestMapping("/downloadTemplateAllFileZip")
    public void downloadTemplateAllFileZip(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
        ajaxData.put("nodeNo","contractPrint");
        ajaxData.put("flowNo","apply_flow");
        this.docFileBizFiletypeConfigService.downloadTemplateAllFileZip(response, ajaxData);
    }
    @RequestMapping("/downloadAllSltFileZip")
    public void downloadAllSltFileZip(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
        this.docFileBizFiletypeConfigService.downloadAllSltFileZip(response, ajaxData);
    }
    @RequestMapping("/deleteSelectFile")
    public R deleteSelectFile(@RequestBody JSONObject ajaxData) {
        JSONObject result = this.docFileBizFiletypeConfigService.deleteSelectFile(ajaxData);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS).put("data",result);
    }

    @RequestMapping("/downloadAllFilesZip")
    public void downloadAllFilesZip(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
        this.docFileBizFiletypeConfigService.downloadAllFilesZip(response, ajaxData);
    }



    @RequestMapping("/downloadTmpZip")
    public void downloadTmpZip(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
        String fileName = ajaxData.getString("fileName");
        this.docFileBizFiletypeConfigService.downloadTmpZip(response, fileName);
    }


    @RequestMapping("/downloadArchiveFileZip")
    public void downloadArchiveFileZip(@RequestBody JSONObject fileObj, HttpServletResponse response) {
        JSONArray ajaxData=fileObj.getJSONArray("ajaxData");
        String zipName=fileObj.getString("zipName");
        this.docFileBizFiletypeConfigService.downloadArchiveFileZip(ajaxData,zipName,response);
    }


    @RequestMapping("/insert")
    public R insert(@RequestBody DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity){
        this.docFileBizFiletypeConfigService.insert(docFileBizFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }
    @RequestMapping("/addOneFloder")
    public R addOneFloder(@RequestBody DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity){
        this.docFileBizFiletypeConfigService.addOneFloder(docFileBizFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @PostMapping(value = "/upLoadFile")
//    (@RequestParam(value="test",required=false) int test,@RequestParam("file") MultipartFile file){

    public R uploadFile( @RequestParam("folderId")String folderId, @RequestParam("appName")String appName,  @RequestParam("file") MultipartFile file,@RequestParam("uploadParm") String  uploadParm ) {

        /**
         * 获取要保存的文件的目标路径
         */
        DocFileBizFiletypeConfigEntity folder = this.docFileBizFiletypeConfigService.findById(folderId);

        JSONObject gson = this.docFileBizFiletypeConfigService.upLoadFile(folder,file,appName,uploadParm);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS).put("data",gson);

    }
    @PostMapping(value = "/upLoadFileBase64Multi")
//    (@RequestParam(value="test",required=false) int test,@RequestParam("file") MultipartFile file){

    public R upLoadFileBase64Multi(  @RequestBody JSONObject parmJson ) {

        /**
         * 获取要保存的文件的目标路径
         */
//        @RequestParam("folderId")String folderId, @RequestParam("appName")String appName,
//        @RequestBody List<DocFileInfBase64> file, @RequestParam("uploadParm") String  uploadParm
        String  folderId=parmJson.getString("folderId");
        String  appName=parmJson.getString("appName");
        String  uploadParm=parmJson.getString("uploadParm");
        JSONArray tmpArray=parmJson.getJSONArray("file");
        List<DocFileInfBase64>  file=JSONObject.parseArray(tmpArray.toJSONString(),DocFileInfBase64.class);

        DocFileBizFiletypeConfigEntity folder = this.docFileBizFiletypeConfigService.findById(folderId);

        JSONObject gson = this.docFileBizFiletypeConfigService.upLoadFileBase64Multi(folder,file,appName,uploadParm);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS).put("data",gson);

    }

    @PostMapping(value = "/upLoadFileForRichEditor")
    public R upLoadFileForRichEditor(@RequestParam("tmplId") String tmplId, @RequestParam("file") MultipartFile file){
        String appName = "richEditor";
        String uploadParm = null; //业务关联编号？
        DocFileBizFiletypeConfigEntity folder = new DocFileBizFiletypeConfigEntity();
        folder.setBizNo(tmplId);

        JSONObject gson = this.docFileBizFiletypeConfigService.upLoadFile(folder,file,appName,uploadParm);
        if ("0000".equals(gson.get("code"))){
            DocFileInfEntity docFile = (DocFileInfEntity) gson.get("data");

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("url", "/file/docFileInf/getFileStream/" + docFile.getFileId());// 图片 src ，必须
            dataMap.put("alt", docFile.getFileName());// 图片描述文字，非必须
            dataMap.put("href", "");// 图片的链接，非必须？点击图片可以浏览器打开新窗口跳转。

            return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS).put("data",dataMap);
        } else {
            return R.error().put("msg", gson.get("msg"));
        }

    }


    @RequestMapping("/getUploadLimitParam")
    public R  getUploadLimitParam()throws ServiceException {
        JSONObject result=this.docFileBizFiletypeConfigService.getUploadLimitParam();
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS).put("data",result);
    }



    @RequestMapping("/getRootListByTypeBean")
    public R getRootListByTypeBean(@RequestBody DocFileBizFiletypeConfigEntity DocFileBizFiletypeConfigEntity){
        JSONArray rootList = this.docFileBizFiletypeConfigService.getRootListByTypeBean(DocFileBizFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", rootList);
    }

    @RequestMapping("/fileAutoInitFun")
    public R fileAutoInitFun(@RequestBody DocFileBizFiletypeConfigEntity DocFileBizFiletypeConfigEntity){
         this.docFileBizFiletypeConfigService.fileAutoInitFun(DocFileBizFiletypeConfigEntity);
         JSONObject res=new JSONObject();
         res.put("flag","1");
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", res);
    }

    @RequestMapping("/fileAutoInitFunMix")
    public R fileAutoInitFunMix(@RequestBody JSONObject jsonObject){
         this.docFileBizFiletypeConfigService.fileAutoInitFunMix(jsonObject);
         JSONObject res=new JSONObject();
         res.put("flag","1");
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", res);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity){
        this.docFileBizFiletypeConfigService.update(docFileBizFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/getConfigFolders")
    public R getConfigFolders(@RequestBody DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity){
        List<DocFileBizFiletypeConfigEntity> floderList=this.docFileBizFiletypeConfigService.findList(docFileBizFiletypeConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS).put("list", floderList);
    }






    @RequestMapping("/initBizFileTypeConfig/{initParm}")
    public R initBizFileTypeConfig(@PathVariable("initParm") String initParm){
        this.docFileBizFiletypeConfigService.initBizFileTypeConfig(initParm);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{scId}")
    public R findById(@PathVariable("scId") String scId){
        DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = this.docFileBizFiletypeConfigService.findById(scId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docFileBizFiletypeConfigEntity);
    }

    @RequestMapping("/deleteById/{scId}")
    public R deleteById(@PathVariable("scId") String scId){
        this.docFileBizFiletypeConfigService.deleteById(scId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }

    @RequestMapping("/getShowUrlByObsid/{obsId}")
    public R getShowUrlByObsid(@PathVariable("obsId") String obsId){
        String url = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
        return R.ok().put("data",url);
    }
}