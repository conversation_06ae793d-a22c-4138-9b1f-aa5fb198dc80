package cn.mftcc.doc.components.file.service.impl;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.rb.BaseMap;
import cn.mftcc.common.utils.DateUtil;
import cn.mftcc.common.utils.StringUtil;
import cn.mftcc.common.utils.UUIDUtil;
import cn.mftcc.config.feign.dto.CWEnumBean;
import cn.mftcc.config.feign.dto.ConfigCwVoucherMstDTO;
import cn.mftcc.config.feign.dto.ConfigVoucherPushEntityDTO;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.common.utils.OssConfig;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.service.BatchVoucherPushService;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.file.service.DocFileInfService;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;
import cn.mftcc.doc.components.mould.mapper.DocBizTemplateMapper;
import cn.mftcc.doc.feign.client.ConfigFeignClient;
import cn.mftcc.doc.feign.client.ElinkApiFeignClient;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;
import cn.mftcc.lease.feign.dto.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.io.ByteStreams;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 凭证推送金蝶
 */
@Slf4j
@Service("BatchVoucherPushService")
@Transactional(rollbackFor = ServiceException.class)
public class BatchVoucherPushServiceImpl implements BatchVoucherPushService {

    @Autowired
    private LeaseFeiginClient leaseFeiginClient;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private ConfigFeignClient configFeignClient;

    @Autowired
    private ElinkApiFeignClient elinkApiFeignClient;

    @Autowired
    private DocFileInfService docFileInfService;

    @Autowired
    private DocBizTemplateMapper docBizTemplateMapper;

    @Autowired
    private OssConfig ossConfig;
    //doc/batchVoucherPush/
    @Value("${mftcc.file.batchVoucherPushOss}")
    private String batchVoucherPushOss;
    @Value("${mftcc.aliyun.oss.sftpIp}")
    private String sftpIp;
    @Value("${mftcc.aliyun.oss.sftpUser}")
    private String sftpUser;
    @Value("${mftcc.aliyun.oss.sftpPassword}")
    private String sftpPassword;
    @Value("${mftcc.aliyun.oss.sftpPort}")
    private String sftpPort;

    /**
     * 凭证推送到金蝶
     *
     * @return
     */
    @Override
    public boolean batchVoucherPush() throws Exception {
        //查询凭证主表,根据中间表过滤获取未推送的凭证
        log.info("TaskCall-batchVoucherPush-批量生成凭证附件开始");
        List<String> stringList = new ArrayList<>();
        stringList.add(CWEnumBean.XD_SERVICE_DATA.capitalPay.getNum());
        stringList.add(CWEnumBean.XD_SERVICE_DATA.BACKMONEY.getNum());
        stringList.add(CWEnumBean.XD_SERVICE_DATA.CAPITALDEDUCT.getNum());
        stringList.add(CWEnumBean.XD_SERVICE_DATA.LGJK.getNum());
        stringList.add(CWEnumBean.XD_SERVICE_DATA.PREFINISH.getNum());
        stringList.add(CWEnumBean.XD_SERVICE_DATA.INTSTCHG.getNum());
        stringList.add(CWEnumBean.XD_SERVICE_DATA.XDFK.getNum());
        for (String lis : stringList) {
            //资金支付
            if (CWEnumBean.XD_SERVICE_DATA.capitalPay.getNum().equals(lis)) {
                //查询凭证表获取凭证信息
                List<ConfigCwVoucherMstDTO> list = configFeignClient.selectBatchVouch(lis);
                log.info("TaskCall-资金支付批量生成凭证开始总条数为:" + list.size());
                for (ConfigCwVoucherMstDTO configCwVoucherMstDTO : list) {
                    Map<String, String> map = new HashMap<>();
                    map.put("voucherKdId", configCwVoucherMstDTO.getUid());
                    map.put("batchNo", configCwVoucherMstDTO.getVoucherKdId());
                    map.put("opNo", configCwVoucherMstDTO.getBusinessno());
                    map.put("pactNo", configCwVoucherMstDTO.getVoucherNo());
                    this.voucherFileDown06(configCwVoucherMstDTO.getBusinessno(), map);
                }
            }
            //退款
            if (CWEnumBean.XD_SERVICE_DATA.BACKMONEY.getNum().equals(lis)) {
                List<ConfigCwVoucherMstDTO> list = configFeignClient.selectBatchVouch(lis);
                log.info("TaskCall-退款批量生成凭证开始总条数为:" + list.size());
                for (ConfigCwVoucherMstDTO configCwVoucherMstDTO : list) {
                    Map<String, String> map = new HashMap<>();
                    map.put("voucherKdId", configCwVoucherMstDTO.getUid());
                    map.put("batchNo", configCwVoucherMstDTO.getVoucherKdId());
                    map.put("opNo", configCwVoucherMstDTO.getBusinessno());
                    map.put("pactNo", configCwVoucherMstDTO.getPactNo());
                    this.voucherFileDown08(configCwVoucherMstDTO.getBusinessno(), map);
                }
            }
            //资金抵扣
            if (CWEnumBean.XD_SERVICE_DATA.CAPITALDEDUCT.getNum().equals(lis)) {
                List<ConfigCwVoucherMstDTO> list = configFeignClient.selectBatchVouch(lis);
                log.info("TaskCall-资金抵扣批量生成凭证开始总条数为:" + list.size());
                for (ConfigCwVoucherMstDTO configCwVoucherMstDTO : list) {
                    //根据凭证主表查询lease_assure_amt_deduct_apply的id
                    List<LeaseAssureAmtDeductApplyDTO> leaseAssureAmtDeductApplyDTOS = leaseFeiginClient.selectLeaseAssureAmtDeductApply(configCwVoucherMstDTO.getBusinessno());
                    if (CollectionUtils.isEmpty(leaseAssureAmtDeductApplyDTOS)) {
                        continue;
                    }
                    for (LeaseAssureAmtDeductApplyDTO leaseAssureAmtDeductApplyDTO : leaseAssureAmtDeductApplyDTOS) {
                        if (leaseAssureAmtDeductApplyDTO == null) {
                            log.error("Taskdetailex-pushKINGDEE-" + configCwVoucherMstDTO.getBusinessno() + "-error-保证金抵扣申请信息为空");
                            log.info("资金抵扣凭证主表信息" + JSONObject.toJSON(configCwVoucherMstDTO) + "资金抵扣详情信息:" + JSONObject.toJSON(leaseAssureAmtDeductApplyDTO));
                            continue;
                        }
                        //查询凭证表获取凭证信息
                        Map<String, String> map = new HashMap<>();
                        map.put("voucherKdId", configCwVoucherMstDTO.getUid());
                        map.put("batchNo", configCwVoucherMstDTO.getVoucherKdId());
                        map.put("opNo", configCwVoucherMstDTO.getBusinessno());
                        this.voucherFileDown05(leaseAssureAmtDeductApplyDTO.getDeductAppId(), map);
                    }
                }
            }

            //留购费收入确认
            if (CWEnumBean.XD_SERVICE_DATA.LGJK.getNum().equals(lis)) {
                List<ConfigCwVoucherMstDTO> list = configFeignClient.selectBatchVouch(lis);
                log.info("TaskCall-留购费收入确认批量生成凭证开始总条数为:" + list.size());
                for (ConfigCwVoucherMstDTO configCwVoucherMstDTO : list) {
                    //根据凭证主表获取结项信息
                    LeaseLiftManageDTO leaseLiftManageDTO = leaseFeiginClient.selectleaseLiftManageEntity(configCwVoucherMstDTO.getBusinessno());
                    if (leaseLiftManageDTO == null) {
                        log.error("Taskdetailex-pushKINGDEE-" + configCwVoucherMstDTO.getBusinessno() + "-error-结项管理信息为空");
                        log.info("留购费收入确认凭证主表信息" + JSONObject.toJSON(configCwVoucherMstDTO) + "留购费收入确认结项管理信息:" + JSONObject.toJSON(leaseLiftManageDTO));
                        continue;
                    }
                    //查询凭证表获取凭证信息
                    Map<String, String> map = new HashMap<>();
                    map.put("voucherKdId", configCwVoucherMstDTO.getUid());
                    map.put("batchNo", configCwVoucherMstDTO.getVoucherKdId());//金蝶凭证id
                    map.put("opNo", configCwVoucherMstDTO.getBusinessno());//业务编号
                    this.voucherFileDown02(leaseLiftManageDTO.getLiftId(), map);
                }
            }

            //提前结清
            if (CWEnumBean.XD_SERVICE_DATA.PREFINISH.getNum().equals(lis) || CWEnumBean.XD_SERVICE_DATA.INTSTCHG.getNum().equals(lis)) {
                List<ConfigCwVoucherMstDTO> list = configFeignClient.selectBatchVouch(lis);
                for (ConfigCwVoucherMstDTO configCwVoucherMstDTO : list) {
                    //过滤利息变更和提前结清
                    //查询凭证表获取凭证信息
                    Map<String, String> map = new HashMap<>();
                    map.put("voucherKdId", configCwVoucherMstDTO.getUid());
                    map.put("batchNo", configCwVoucherMstDTO.getVoucherKdId());
                    map.put("opNo", configCwVoucherMstDTO.getBusinessno());
                    this.voucherFileDown03(configCwVoucherMstDTO.getBusinessno(), map);
                }
            }

            //利息变更,还款计划变更
            if (CWEnumBean.XD_SERVICE_DATA.INTSTCHG.getNum().equals(lis)) {
                List<ConfigCwVoucherMstDTO> list = configFeignClient.selectBatchVouch(lis);
                log.info("TaskCall-还款计划变更批量生成凭证开始总条数为:" + list.size());
                for (ConfigCwVoucherMstDTO configCwVoucherMstDTO : list) {
                    //查询凭证表获取凭证信息
                    Map<String, String> map = new HashMap<>();
                    map.put("voucherKdId", configCwVoucherMstDTO.getUid());
                    map.put("batchNo", configCwVoucherMstDTO.getVoucherKdId());
                    map.put("opNo", configCwVoucherMstDTO.getBusinessno());
                    this.voucherFileDown11(configCwVoucherMstDTO.getBusinessno(), map);
                }
            }
            //支付投放
            if (CWEnumBean.XD_SERVICE_DATA.XDFK.getNum().equals(lis)) {
                List<ConfigCwVoucherMstDTO> list = configFeignClient.selectBatchVouch(lis);
                log.info("TaskCall-支付投放批量生成凭证开始总条数为:" + list.size());
                for (ConfigCwVoucherMstDTO configCwVoucherMstDTO : list) {
                    //查询凭证表获取凭证信息
                    Map<String, String> map = new HashMap<>();
                    map.put("voucherKdId", configCwVoucherMstDTO.getUid());
                    map.put("batchNo", configCwVoucherMstDTO.getVoucherKdId());
                    map.put("pactNo", configCwVoucherMstDTO.getPactNo());
                    map.put("opNo", configCwVoucherMstDTO.getBusinessno());
                    this.voucherFileDown012(configCwVoucherMstDTO.getBusinessno(), map);
                }
            }
        }
        return true;
    }


    /**
     * 定时推送凭证附件至金蝶
     */
    @Override
    public void batchVoucherPushJD() throws Exception {
        int i = 0;
        int j = 0;
        ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
        configVoucherPushEntityDTO.setState("0");
        //查询凭证中间表,获取未推送的凭证
        List<ConfigVoucherPushEntityDTO> list = configFeignClient.selectVoucherPush(configVoucherPushEntityDTO);
        log.info("TaskCall-batchVoucherPushJD-获取未推送的凭证开始推送至金蝶总条数为:" + list.size());
        //根据中间表获取凭证信息
        for (ConfigVoucherPushEntityDTO configVoucherPushEntityDTO1 : list) {
            ConfigCwVoucherMstDTO configCwVoucherMstDTO = null;
            try {
                //根据凭证id获取凭证信息
                configCwVoucherMstDTO = configFeignClient.selectByVoucherId(configVoucherPushEntityDTO1.getConfigCwVoucherMstId());
                byte[] bytes = this.fileByte(configVoucherPushEntityDTO1.getFilePath(), configVoucherPushEntityDTO1.getConfigCwVoucherMstId());
                if (bytes == null) {
                    i++;
                    log.error("Taskdetailex-pushKINGDEE-" + configVoucherPushEntityDTO1.getConfigCwVoucherMstId() + "-error-文件名称为:" + configVoucherPushEntityDTO1.getFileName() + "未获取到凭证附件");
                    continue;
                }
                //调用金蝶接口推送凭证附件
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("callNo", "KDVOU0010");
                jsonObject.put("busReqNo", UUIDUtil.getUUID());
                Map<String, Object> paramData = new HashMap<>();
                paramData.put("voucherId", configCwVoucherMstDTO.getVoucherKdId());
                paramData.put("fileId", UUIDUtil.getUUID() + configVoucherPushEntityDTO1.getFileName().substring(configVoucherPushEntityDTO1.getFileName().lastIndexOf(".")));
                paramData.put("fileName", configVoucherPushEntityDTO1.getFileName());
                Gson gson = new Gson();
                String toJson = gson.toJson(bytes);
                paramData.put("fileByte", toJson);
                paramData.put("sftpIp", sftpIp);
                paramData.put("sftpUser", sftpUser);
                paramData.put("sftpPassword", sftpPassword);
                paramData.put("sftpPort", sftpPort);
                log.info("凭证推送:" + sftpIp + "-" + sftpUser + "-" + sftpPassword + "-" + sftpPort);
                jsonObject.put("paramData", paramData);
                String resultJson = elinkApiFeignClient.itoData(jsonObject);
                log.info("凭证推送返回:" + resultJson);
                JSONObject jsonObjects = JSONObject.parseObject(resultJson);
                String code = jsonObjects.getString("code");
                if (!"1".equals(code)) {//elink调用是否成功
                    i++;
                    log.error("elink调用失败错误信息为:" + jsonObjects.getString("msg"));
                    log.error("Taskdetailex-pushKINGDEE-" + configCwVoucherMstDTO.getVoucherKdNo() + "-error-文件名称为:" + configVoucherPushEntityDTO1.getFileName() + "凭证推送失败,elink调用失败");
                    continue;
                }
                JSONObject dataMap = (JSONObject) jsonObjects.get("data");
                if ("1".equals(dataMap.getString("status"))) {
                    //成功之后修改中间表推送状态
                    configVoucherPushEntityDTO1.setState("1");
                    configVoucherPushEntityDTO1.setPushDate(DateUtil.strToDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), "yyyy-MM-dd HH:mm:ss"));
                    configFeignClient.updateVoucherPush(configVoucherPushEntityDTO1);
                } else {
                    i++;
                    log.error("Taskdetailex-pushKINGDEE-" + configCwVoucherMstDTO.getVoucherKdNo() + "-error-文件名称为:" + configVoucherPushEntityDTO1.getFileName() + "凭证推送失败,失败错误信息为:" + dataMap.getString("errMsg"));
                }
            } catch (Exception e) {
                i++;
                log.error("Taskdetailex-pushKINGDEE-" + configCwVoucherMstDTO.getVoucherKdNo() + "-error-文件名称为:" + configVoucherPushEntityDTO1.getFileName() + "凭证推送失败,错误信息为:" + e.getMessage());
            }
            j++;
        }
        log.info("TaskCall-batchVoucherPushJD-获取未推送的凭证开始推送至金蝶总条数为:" + list.size()+"条");
        log.info("TaskCall-batchVoucherPushJD-失败条数为:" + i+"条");
        log.info("TaskCall-batchVoucherPushJD-成功条数为:" + j+"条");
    }

    /**
     * 批量删除凭证附件
     *
     * @throws Exception
     */
    @Override
    public void deleteVoucherFile() throws Exception {
        //查询凭证推送中间表获取已推送的凭证
        ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
        configVoucherPushEntityDTO.setState("1");
        //查询凭证中间表,获取未推送的凭证
        List<ConfigVoucherPushEntityDTO> list = configFeignClient.selectVoucherPush(configVoucherPushEntityDTO);
        for (ConfigVoucherPushEntityDTO configVoucherPush : list) {
            Path fileToDelete = Paths.get(configVoucherPush.getFilePath());
            try {
                if (Files.exists(fileToDelete) && Files.isRegularFile(fileToDelete)) {
                    Files.deleteIfExists(fileToDelete);
                }
                deleteEmptyParentDirectories(fileToDelete.getParent());
            } catch (IOException e) {
                log.error("deleteVoucherFile-删除文件" + configVoucherPush.getFileName() + "失败,失败原因为:" + e.getMessage());
            }
        }
    }

    /**
     * 递归删除上级空目录
     *
     * @param dir
     * @throws IOException
     */
    private static void deleteEmptyParentDirectories(Path dir) throws IOException {
        if (dir != null && Files.exists(dir) && Files.isDirectory(dir) && !dir.equals(dir.getRoot()) && !isDirectoryEmpty(dir)) {
            Files.delete(dir);
            deleteEmptyParentDirectories(dir.getParent());
        }
    }

    /**
     * 判断文件夹中是否为空
     *
     * @param dir
     * @return
     * @throws IOException
     */
    private static boolean isDirectoryEmpty(Path dir) throws IOException {
        return Files.list(dir).anyMatch(p -> Files.isRegularFile(p) || Files.isDirectory(p));
    }


    public byte[] fileByte(String filePath, String id) throws IOException {
        //根据凭证路径获取凭证附件转换为Byte[]
//        File file = new File(filePath);
        InputStream inputStream = null;
        try {
            inputStream = ossConfig.readOss(filePath);
//            inputStream =new FileInputStream(file);
//            int size = (int) file.length();
//            byte[] bytes = new byte[size];
//            inputStream.read(bytes);
            byte[] array = ByteStreams.toByteArray(inputStream);
            return array;
        } catch (IOException e) {
            log.error(id + "获取凭证附件失败");
            return null;
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }

    }

    /**
     * 付款投放
     *
     * @param busIds
     * @param map
     */
    private void voucherFileDown012(String busIds, Map<String, String> map) throws IOException {
        log.info("付款投放凭证附件下载消费开始voucherFileDownload map:{}", busIds);
        String opNo = map.get("opNo");
        String batchNo = map.get("batchNo");
        String voucherKdId = map.get("voucherKdId");
        String pactNo = map.get("pactNo");
        InputStream inputStream = null;
        try {
            long startTime = System.currentTimeMillis();
            List<String> fileNames = new ArrayList<>();
            if (StringUtil.isNotEmpty(pactNo)) {
                fileNames.add("付款审批单(" + pactNo + ")");
            }
            log.info("付款投放凭证附件下载fileNames:{}", JSONObject.toJSONString(fileNames));
            List<DocFileInfEntity> fileInfList = docFileInfService.findFileInfListByFileName(fileNames);
            log.info("付款投放凭证附件下载消费fileInfList:{}", JSONObject.toJSONString(fileInfList));
            for (DocFileInfEntity entity : fileInfList) {
                if (!fileNames.contains(entity.getFileName())) {
                    QueryWrapper<DocBizTemplateEntity> templateQueryWrapper = new QueryWrapper<>();
                    templateQueryWrapper.in("template_name", entity.getFileName());
                    templateQueryWrapper.eq("prd_unique_val", "paymentDoc");
                    templateQueryWrapper.eq("flow_no", "paymentDoc");
                    templateQueryWrapper.eq("node_no", "paymentDoc");
                    List<DocBizTemplateEntity> docBizTemplateEntities = docBizTemplateMapper.selectList(templateQueryWrapper);
                    for (DocBizTemplateEntity template: docBizTemplateEntities) {
                        String path = template.getSavePath() + template.getSaveFileName();
                        log.info("文件所在位置：" + path);
                        String fileSuffix = new FileUtils().getFileSuffixName(template.getSaveFileName());//文件后缀名
                        log.info("文件后缀名：" + fileSuffix);
                        String templateName = template.getTemplateName() + "." + fileSuffix;
                        log.info("文件重命名：" + templateName);
                        String fileUrl = null;
                        if ("1".equals(template.getOssRemoveStatus())) {
                            //已迁移模板从oss下载
                            fileUrl = template.getSavePath() + template.getSaveFileName();
                        } else {
                            //存在本地,上传oss
                            String substring = path.substring(path.lastIndexOf("."));
                            inputStream = new FileInputStream(path);
                            JSONObject writeOss = ossConfig.writeOss(batchVoucherPushOss + "/" + pactNo + "/" + voucherKdId + "/" + UUIDUtil.getUUID() + substring, inputStream);
                            if (!"0000".equals(writeOss.get("code"))) {
                                log.error("Taskdetailex-pushKINGDEE-付款投放凭证附件-error-凭证主表关联业务编号:" + busIds + "生成失败");
                                continue;
                            }
                            fileUrl = writeOss.getString("filePath");
                        }
                        ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                        configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                        configVoucherPushEntityDTO.setFilePath(fileUrl);
                        configVoucherPushEntityDTO.setFileName(pactNo+"鸿云投放审批" + fileUrl.substring(fileUrl.lastIndexOf(".")));
                        configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                        configVoucherPushEntityDTO.setState("0");//未推送
                        configFeignClient.insert(configVoucherPushEntityDTO);
                    }
                } else {
                    ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                    configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                    configVoucherPushEntityDTO.setFilePath(entity.getFilePath());
                    configVoucherPushEntityDTO.setFileName(pactNo+"鸿云投放审批" + entity.getFilePath().substring(entity.getFilePath().lastIndexOf(".")));
                    configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                    configVoucherPushEntityDTO.setState("0");//未推送
                    configFeignClient.insert(configVoucherPushEntityDTO);
                }
            }
            LeasePactDTO leasePactByPactNo = leaseFeiginClient.getLeasePactByPactNo(pactNo);
            //合同为回租类型则不需要上传发票
            if (!Objects.nonNull(leasePactByPactNo) || "2".equals(leasePactByPactNo.getLeaseType())){
                return;
            }
            //根据合同号获取合同对应的发票信息
            List<DocFileInfEntity> fileInfListFP = docFileInfService.findFileInfListByPactFP(pactNo);
            log.info(pactNo+"相应发票条数为:"+fileInfListFP.size());
            for (DocFileInfEntity docFileInfEntityFP : fileInfListFP){
                ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                configVoucherPushEntityDTO.setFilePath(docFileInfEntityFP.getFilePath());
                configVoucherPushEntityDTO.setFileName(docFileInfEntityFP.getFileName() + docFileInfEntityFP.getFilePath().substring(docFileInfEntityFP.getFilePath().lastIndexOf(".")));
                configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                configVoucherPushEntityDTO.setState("0");//未推送
                configFeignClient.insert(configVoucherPushEntityDTO);
            }
            long endTime = System.currentTimeMillis();
            log.info("付款投放凭证附件生成-凭证主表关联业务编号:" + pactNo + "，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime, endTime);
            log.info("付款投放凭证附件生成-凭证主表关联业务编号:" + pactNo + "，总耗时:" + duration + "毫秒");
        } catch (Exception e) {
            log.error("Taskdetailex-pushKINGDEE-付款投放凭证附件失败-error-,合同号：" + pactNo + "，异常信息：{}", e.getMessage());
            throw new RuntimeException();
        }finally {
            if (inputStream != null){
                inputStream.close();
            }
        }
    }

    /**
     * 提前结清凭证附件下载
     *
     * @param busIds
     * @param map
     */
    public void voucherFileDown03(String busIds, Map<String, String> map) throws Exception {
        log.info("提前结清凭证下载消费开始voucherFileDownload map:{}", busIds);
        String opNo = map.get("opNo");
        String batchNo = map.get("batchNo");
        String voucherKdId = map.get("voucherKdId");
        FileInputStream inputStream = null;
        try {
            //流程中的附件
            List<String> list = new ArrayList<>();
            list.add(busIds);
            JSONObject parmJson = new JSONObject();
            parmJson.put("prepayIds", list);
            //获取提前结清信息
            List<LeasePrepRepayApplyDTO> leasePrepRepayApplyDTOS = leaseFeiginClient.leasePrepRepayApplyByPrepayIds(parmJson);
            Map<String, LeasePrepRepayApplyDTO> dtoMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(leasePrepRepayApplyDTOS)) {
                for (LeasePrepRepayApplyDTO dto : leasePrepRepayApplyDTOS) {
                    if (dtoMap.get(dto.getPrepayId()) == null) {
                        dtoMap.put(dto.getPrepayId(), dto);
                    }
                }
            } else {
                return;
            }

            //合同模板渲染，上传附件下载
            LeasePrepRepayApplyDTO dto = null;
            String pactNo = "";
            JSONObject parmList = null;

            long startTime = System.currentTimeMillis();
            log.info("提前结清凭证附件生成:" + batchNo + "，文件生成开始时间:" + startTime);

            if (null != dtoMap && !dtoMap.isEmpty()) {
                JSONObject parmTempJson = new JSONObject();
                parmTempJson.put("batchNo", batchNo);
                parmTempJson.put("opNo", opNo);
                DocFileInfEntity docFileInf = new DocFileInfEntity();
                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key);
                    pactNo = dto.getPactNo();
                    //如果是部分提前结清
                    if ("2".equals(dto.getPrepayType())) {
                        parmTempJson.put("templateId", "31c334daccf443eba7832f12f2ca483b");
                        parmTempJson.put("pactNo", pactNo + "鸿云部分提前结清审批");
                    } else {
                        parmTempJson.put("templateId", "71cb5892bf944732b0dabb17a1c9b7ae");
                        parmTempJson.put("pactNo", pactNo + "鸿云提前结清审批");
                    }
                    //生成合同套打参数
                    parmList = getLeasePrepRepayParmList(dto, key);
                    parmTempJson.put("parmList", parmList);
                    //生成合同模板
                    JSONObject jsonObject = docFileBizFiletypeConfigService.renderTemplateJSONObject(parmTempJson);
                    if (!jsonObject.containsKey("path")) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成提前结清凭证附件-error-凭证主表关联业务编号:" + busIds + "生成失败");
                        continue;
                    }
                    String path = jsonObject.getString("path");
                    String substring = path.substring(path.lastIndexOf("."));
                    inputStream = new FileInputStream(path);
                    JSONObject writeOss = ossConfig.writeOss(batchVoucherPushOss + "/" + pactNo + "/" + voucherKdId + "/" + UUIDUtil.getUUID() + substring, inputStream);
                    if (!"0000".equals(writeOss.get("code"))) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成提前结清凭证附件-error-凭证主表关联业务编号:" + busIds + "生成失败");
                        continue;
                    }
                    String filePath = writeOss.getString("filePath");
                    //将文件和凭证对应关系存入凭证中间表
                    String pushUuid = UUIDUtil.getUUID();
                    ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                    configVoucherPushEntityDTO.setId(pushUuid);
                    configVoucherPushEntityDTO.setFilePath(filePath);
                    configVoucherPushEntityDTO.setFileName(jsonObject.getString("fileName"));
                    configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                    configVoucherPushEntityDTO.setState("0");//未推送
                    configFeignClient.insert(configVoucherPushEntityDTO);
                    docFileInf.setBizNo(key);
                    List<DocFileInfEntity> fileList = docFileInfService.findFileInfList(docFileInf);
                    //如果没有附件，则给出提示
                    if (CollectionUtils.isEmpty(fileList)) {
                        log.info("Taskdetailex-pushKINGDEE-(凭证:" + voucherKdId + ")-" + pactNo + "-流程中没有上传附件");
                        continue;
                    }
                    //流程中上传的文件
                    for (DocFileInfEntity docFileInfEntity : fileList) {
//                        List<DocFileInfEntity> list1 = new ArrayList();
//                        list1.add(docFileInfEntity);
//                        //生成文件到指定位置
//                        String generateFile = docFileBizFiletypeConfigService.generateFile(opNo, batchNo, pactNo, list1);
//                        if (StringUtil.isNotEmpty(generateFile)){
                        configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                        configVoucherPushEntityDTO.setFilePath(docFileInfEntity.getFilePath());
                        configVoucherPushEntityDTO.setFileName(docFileInfEntity.getFileName() + docFileInfEntity.getFilePath().substring(docFileInfEntity.getFilePath().lastIndexOf(".")));
                        configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                        configVoucherPushEntityDTO.setState("0");//未推送
                        configFeignClient.insert(configVoucherPushEntityDTO);
//                        }else {
//                            log.error("Taskdetailex-pushKINGDEE-error-提前结清流程中上传的文件"+docFileInfEntity.getFileName()+"下载失败,关联业务编号为:"+docFileInfEntity.getBizNo());
//                        }
                    }
                    //删除临时目录
                    if (StringUtil.isNotEmpty(path)) {
                        Path fileToDelete = Paths.get(path);
                        if (Files.exists(fileToDelete) && Files.isRegularFile(fileToDelete)) {
                            Files.deleteIfExists(fileToDelete);
                        }
                        deleteEmptyParentDirectories(fileToDelete.getParent());
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("提前结清凭证附件生成-凭证主表关联业务编号:" + busIds + "，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime, endTime);
            log.info("提前结清凭证附件生成-凭证主表关联业务编号:" + busIds + "，总耗时:" + duration + "毫秒");
        } catch (Exception e) {
            log.error("Taskdetailex-pushKINGDEE-(提前结清凭证附件生成)-error-(-凭证主表关联业务编号:" + busIds + ")-生成附件失败-异常信息：{}", e.getMessage());
            throw new RuntimeException();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /**
     * 提前结清合同套打参数
     *
     * @param dto
     * @param key
     * @return
     */
    private JSONObject getLeasePrepRepayParmList(LeasePrepRepayApplyDTO dto, String key) {
        JSONObject parmList = new JSONObject();
        parmList.put("prepayId", key);
        parmList.put("dueId", dto.getDueId());
        parmList.put("pactId", dto.getPactId());
        parmList.put("busId", key);
        parmList.put("applyId", dto.getApplyId());
        JSONObject leaseDue = leaseFeiginClient.getLeaseDueId(dto.getDueId());

        if (null != leaseDue
                && null != leaseDue.getDate("dueBeginDate")
                && null != leaseDue.getDate("dueEndDate")) {
            parmList.put("dueBeginDate", DateUtil.DateToStr(leaseDue.getDate("dueBeginDate"), "yyyy-MM-dd"));
            parmList.put("dueEndDate", DateUtil.DateToStr(leaseDue.getDate("dueEndDate"), "yyyy-MM-dd"));
        } else {
            parmList.put("dueBeginDate", DateUtil.DateToStr(leaseDue.getDate("dueBeginDate"), "yyyy-MM-dd"));
            parmList.put("dueEndDate", DateUtil.DateToStr(leaseDue.getDate("dueEndDate"), "yyyy-MM-dd"));
        }

        return parmList;
    }


    /**
     * 还款计划变更生成凭证
     *
     * @param busIds
     * @param map
     */
    public void voucherFileDown11(String busIds, Map<String, String> map) throws Exception {
        log.info("还款计划变更生成凭证开始voucherFileDownload map:{}", busIds);
        String opNo = map.get("opNo");
        String batchNo = map.get("batchNo");
        String voucherKdId = map.get("voucherKdId");
        FileInputStream inputStream = null;
        try {
            List<String> list = new ArrayList<>();
            list.add(busIds);
            JSONObject parmJson = new JSONObject();
            parmJson.put("chgPactIds", list);
            //获取提前结清信息
            List<LeaseDueChangePactDTO> leasePrepRepayApplyDTOS = leaseFeiginClient.findLeaseDuePactByIds(parmJson);
            Map<String, LeaseDueChangePactDTO> dtoMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(leasePrepRepayApplyDTOS)) {
                for (LeaseDueChangePactDTO dto : leasePrepRepayApplyDTOS) {
                    if (dtoMap.get(dto.getChgPactId()) == null) {
                        dtoMap.put(dto.getChgPactId(), dto);
                        list.add(dto.getPactId());
                    }
                }
            } else {
                return;
            }

            Map<String, LeasePactDTO> pactDtoMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(list)) {
                JSONObject pactJson = new JSONObject();
                pactJson.put("pactIds", list);
                List<LeasePactDTO> pactDtos = leaseFeiginClient.getLeasePactByPactIds(pactJson);
                if (CollectionUtils.isNotEmpty(pactDtos)) {
                    for (LeasePactDTO dto : pactDtos) {
                        if (dtoMap.get(dto.getPactId()) == null) {
                            pactDtoMap.put(dto.getPactId(), dto);
                        }
                    }
                }
            }

            //合同模板渲染，上传附件下载
            LeaseDueChangePactDTO dto = null;
            String pactNo = "";
            JSONObject parmList = null;


            long startTime = System.currentTimeMillis();
            log.info("还款计划变更生成凭证:" + batchNo + "，文件生成开始时间:" + startTime);

            if (null != dtoMap && !dtoMap.isEmpty()) {
                JSONObject parmTempJson = new JSONObject();
                parmTempJson.put("batchNo", batchNo);
                parmTempJson.put("opNo", opNo);

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key);
                    pactNo = dto.getPactNo();

                    DocFileInfEntity docFileInf = new DocFileInfEntity();
                    //还款计划变更
                    if ("2".equals(dto.getPactChangType())) {
                        parmTempJson.put("pactNo", pactNo + "鸿云还款计划变更审批");
                        //展期
                        if ("1".equals(dto.getPactChangeSubType())) {
                            parmTempJson.put("templateId", "8b56f9b271aa4c2b87353059cb63eb60");
//                            parmTempJson.put("pactNo", pactNo+"-鸿云还款计划变更审批");
                            //付息延期
                        } else if ("2".equals(dto.getPactChangeSubType())) {
                            parmTempJson.put("templateId", "9f5e24978c734659bd06552f17a0e1d1");
//                            parmTempJson.put("pactNo", pactNo+"-鸿云还款计划变更审批");
                            //缩期
                        } else if ("6".equals(dto.getPactChangeSubType())) {
                            parmTempJson.put("templateId", "a11ff685c9be4481a6b70408534677a3");
//                            parmTempJson.put("pactNo", pactNo+"-鸿云还款计划变更审批");
                            //自定义还款
                        } else {
                            parmTempJson.put("templateId", "8b2da7ac219f4e78b6dca75a85fc45d6");
//                            parmTempJson.put("pactNo", pactNo+"-鸿云还款计划变更审批");
                        }
                    }

                    //生成合同套打参数
                    parmList = getLeaseDueChangePactParmList(dto, key);
                    parmTempJson.put("parmList", parmList);
                    //生成合同模板
                    JSONObject jsonObject = docFileBizFiletypeConfigService.renderTemplateJSONObject(parmTempJson);
                    if (!jsonObject.containsKey("path")) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成还款计划变更生成凭证附件-error-凭证主表关联业务编号:" + busIds + "生成失败");
                        continue;
                    }
                    String path = jsonObject.getString("path");
                    String substring = path.substring(path.lastIndexOf("."));
                    inputStream = new FileInputStream(path);
                    JSONObject writeOss = ossConfig.writeOss(batchVoucherPushOss + "/" + pactNo + "/" + voucherKdId + "/" + UUIDUtil.getUUID() + substring, inputStream);
                    if (!"0000".equals(writeOss.get("code"))) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成还款计划变更凭证附件-error-凭证主表关联业务编号:" + busIds + "生成失败");
                        continue;
                    }
                    String filePath = writeOss.getString("filePath");
                    //将文件和凭证对应关系存入凭证中间表
                    String pushUuid = UUIDUtil.getUUID();
                    ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                    configVoucherPushEntityDTO.setId(pushUuid);
                    configVoucherPushEntityDTO.setFilePath(filePath);
                    configVoucherPushEntityDTO.setFileName(jsonObject.getString("fileName"));
                    configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                    configVoucherPushEntityDTO.setState("0");//未推送
                    configFeignClient.insert(configVoucherPushEntityDTO);
                    docFileInf.setBizNo(key);
                    List<DocFileInfEntity> fileList = docFileInfService.findFileInfList(docFileInf);
                    //如果没有附件，则给出提示
                    if (CollectionUtils.isEmpty(fileList)) {
                        log.info("Taskdetailex-pushKINGDEE-(凭证:" + voucherKdId + ")-" + pactNo + "-流程中没有上传附件");
                        continue;
                    }
                    //流程中上传的文件
                    for (DocFileInfEntity docFileInfEntity : fileList) {
//                        List<DocFileInfEntity> list1 = new ArrayList();
//                        list1.add(docFileInfEntity);
//                        //生成文件到指定位置
//                        String generateFile = docFileBizFiletypeConfigService.generateFile(opNo, batchNo, pactNo, list1);
//                        if (StringUtil.isNotEmpty(generateFile)){
                        configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                        configVoucherPushEntityDTO.setFilePath(docFileInfEntity.getFilePath());
                        configVoucherPushEntityDTO.setFileName(docFileInfEntity.getFileName() + docFileInfEntity.getFilePath().substring(docFileInfEntity.getFilePath().lastIndexOf(".")));
                        configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                        configVoucherPushEntityDTO.setState("0");//未推送
                        configFeignClient.insert(configVoucherPushEntityDTO);
//                        }else {
//                            log.error("还款计划变更流程中上传的文件"+docFileInfEntity.getFileName()+"下载失败,关联业务编号为:"+docFileInfEntity.getBizNo());
//                        }
                    }
                    //删除临时目录
                    if (StringUtil.isNotEmpty(path)) {
                        Path fileToDelete = Paths.get(path);
                        if (Files.exists(fileToDelete) && Files.isRegularFile(fileToDelete)) {
                            Files.deleteIfExists(fileToDelete);
                        }
                        deleteEmptyParentDirectories(fileToDelete.getParent());
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("还款计划变更生成凭证-凭证主表关联业务编号:" + busIds + "，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime, endTime);
            log.info("还款计划变更生成凭证-凭证主表关联业务编号:" + busIds + "，总耗时:" + duration + "毫秒");

        } catch (Exception e) {
            log.error("Taskdetailex-pushKINGDEE-(还款计划变更生成凭证)-error-(-凭证主表关联业务编号:" + busIds + ")-生成附件失败-异常信息：{}", e.getMessage());
            throw new RuntimeException();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /**
     * 合同变更参数处理
     *
     * @param dto
     * @param key
     * @return
     */
    private JSONObject getLeaseDueChangePactParmList(LeaseDueChangePactDTO dto, String key) {
        JSONObject parmList = new JSONObject();
        parmList.put("chgPactId", key);
        parmList.put("prepayId", key);
        parmList.put("dueId", dto.getDueId());
        parmList.put("pactId", dto.getPactId());
        parmList.put("busId", key);
        parmList.put("applyId", dto.getApplyId());
        return parmList;
    }


    /**
     * 留购费收入确认凭证附件生成
     *
     * @param busIds
     * @param map
     */
    public void voucherFileDown02(String busIds, Map<String, String> map) throws Exception {
        log.info("留购费收入确认凭证附件生成开始voucherFileDownload map:{}", busIds);
        String opNo = map.get("opNo");
        String batchNo = map.get("batchNo");
        String voucherKdId = map.get("voucherKdId");
        FileInputStream inputStream = null;
        try {
            //流程中的附件
            List<String> list = new ArrayList<>();
            list.add(busIds);
            JSONObject parmJson = new JSONObject();
            parmJson.put("liftIds", list);
            //获取提前结清信息
            List<LeaseLiftManageDTO> leasePrepRepayApplyDTOS = leaseFeiginClient.getLeaseLiftManageByIds(parmJson);
            Map<String, LeaseLiftManageDTO> dtoMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(leasePrepRepayApplyDTOS)) {
                for (LeaseLiftManageDTO dto : leasePrepRepayApplyDTOS) {
                    if (dtoMap.get(dto.getLiftId()) == null) {
                        dtoMap.put(dto.getLiftId(), dto);
                    }
                }
            }

            //合同模板渲染，上传附件下载
            LeaseLiftManageDTO dto = null;
            JSONObject parmList = new JSONObject();
            String pactNo = "";
            long startTime = System.currentTimeMillis();
            log.info("留购费收入确认凭证附件生成:" + batchNo + "，文件生成开始时间:" + startTime);


            if (null != dtoMap && !dtoMap.isEmpty()) {
                JSONObject parmTempJson = new JSONObject();
                parmTempJson.put("batchNo", batchNo);
                parmTempJson.put("opNo", opNo);
                DocFileInfEntity docFileInf = new DocFileInfEntity();
                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key);
                    pactNo = dto.getPactNo();
                    parmTempJson.put("pactNo", pactNo + "鸿云正常结清审批");
                    parmTempJson.put("templateId", "3c64b97f00e14c249503d6a31918f666");
                    //生成合同套打参数
                    parmList.put("liftId", key);
                    parmList.put("prepayId", key);
                    parmList.put("pactId", dto.getPactId());
                    parmTempJson.put("parmList", parmList);
                    //生成合同模板
                    //根据模板生成凭证
                    JSONObject jsonObject = docFileBizFiletypeConfigService.renderTemplateJSONObject(parmTempJson);
                    if (!jsonObject.containsKey("path")) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成留购费收入确认凭证附件-error-凭证主表关联业务编号:" + busIds + "生成失败");
                        continue;
                    }
                    String path = jsonObject.getString("path");
                    String substring = path.substring(path.lastIndexOf("."));
                    inputStream = new FileInputStream(path);
                    JSONObject writeOss = ossConfig.writeOss(batchVoucherPushOss + "/" + pactNo + "/" + voucherKdId + "/" + UUIDUtil.getUUID() + substring, inputStream);
                    if (!"0000".equals(writeOss.get("code"))) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成留购费收入确认凭证附件-error-凭证主表关联业务编号:" + busIds + "生成失败");
                        continue;
                    }
                    String filePath = writeOss.getString("filePath");
                    //将文件和凭证对应关系存入凭证中间表
                    String pushUuid = UUIDUtil.getUUID();
                    ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                    configVoucherPushEntityDTO.setId(pushUuid);
                    configVoucherPushEntityDTO.setFilePath(filePath);
                    configVoucherPushEntityDTO.setFileName(jsonObject.getString("fileName"));
                    configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                    configVoucherPushEntityDTO.setState("0");//未推送
                    configFeignClient.insert(configVoucherPushEntityDTO);
                    docFileInf.setBizNo(key);
                    List<DocFileInfEntity> fileList = docFileInfService.findFileInfList(docFileInf);
                    //如果没有附件，则给出提示
                    if (CollectionUtils.isEmpty(fileList)) {
                        log.info("Taskdetailex-pushKINGDEE-(凭证:" + voucherKdId + ")-" + pactNo + "-流程中没有上传附件");
                        continue;
                    }
                    //流程中上传的文件
                    for (DocFileInfEntity docFileInfEntity : fileList) {
//                        List<DocFileInfEntity> list1 = new ArrayList();
//                        list1.add(docFileInfEntity);
//                        //生成文件到指定位置
//                        String generateFile = docFileBizFiletypeConfigService.generateFile(opNo, batchNo, pactNo, list1);
//                        if (StringUtil.isNotEmpty(generateFile)){
                        configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                        configVoucherPushEntityDTO.setFilePath(docFileInfEntity.getFilePath());
                        configVoucherPushEntityDTO.setFileName(docFileInfEntity.getFileName() + docFileInfEntity.getFilePath().substring(docFileInfEntity.getFilePath().lastIndexOf(".")));
                        configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                        configVoucherPushEntityDTO.setState("0");//未推送
                        configFeignClient.insert(configVoucherPushEntityDTO);
//                        }else {
//                            log.error("Taskdetailex-pushKINGDEE-error-留购费收入确认流程中上传的文件"+docFileInfEntity.getFileName()+"下载失败,关联业务编号为:"+docFileInfEntity.getBizNo());
//                        }
                    }
                    //删除临时目录
                    if (StringUtil.isNotEmpty(path)) {
                        Path fileToDelete = Paths.get(path);
                        if (Files.exists(fileToDelete) && Files.isRegularFile(fileToDelete)) {
                            Files.deleteIfExists(fileToDelete);
                        }
                        deleteEmptyParentDirectories(fileToDelete.getParent());
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("留购费收入确认凭证附件生成-凭证主表关联业务编号:" + busIds + "，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime, endTime);
            log.info("留购费收入确认凭证附件生成-凭证主表关联业务编号:" + busIds + "，总耗时:" + duration + "毫秒");

        } catch (Exception e) {
            log.error("Taskdetailex-pushKINGDEE-(留购费收入确认凭证附件生成)-error-(-凭证主表关联业务编号:" + busIds + ")-生成附件失败-异常信息：{}", e.getMessage());
            throw new RuntimeException();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }


    /**
     * 费用抵扣
     *
     * @param busIds
     * @param map
     */
    public void voucherFileDown05(String busIds, Map<String, String> map) throws Exception {
        log.info("费用抵扣凭证附件下载消费开始voucherFileDownload map:{}", busIds);
        String opNo = map.get("opNo");
        String batchNo = map.get("batchNo");
        String voucherKdId = map.get("voucherKdId");
        FileInputStream inputStream = null;
        try {
            //流程中的附件
            List<String> list = new ArrayList<>();
            list.add(busIds);
            JSONObject parmJson = new JSONObject();
            parmJson.put("deductAppIds", list);
            //获取提前结清信息
            List<LeaseAssureAmtDeductApplyDTO> applyDTOS = leaseFeiginClient.getLeaseAssureAmtDeductApplyByIds(parmJson);
            Map<String, LeaseAssureAmtDeductApplyDTO> dtoMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(applyDTOS)) {
                for (LeaseAssureAmtDeductApplyDTO dto : applyDTOS) {
                    if (dtoMap.get(dto.getDeductAppId()) == null) {
                        dtoMap.put(dto.getDeductAppId(), dto);
                    }
                }
            }

            //合同模板渲染，上传附件下载
            LeaseAssureAmtDeductApplyDTO dto = null;
            JSONObject parmList = new JSONObject();
            String pactNo = "";
            long startTime = System.currentTimeMillis();
            log.info("费用抵扣凭证附件生成-批号:" + batchNo + "，文件生成开始时间:" + startTime);

            if (null != dtoMap && !dtoMap.isEmpty()) {
                JSONObject parmTempJson = new JSONObject();
                parmTempJson.put("batchNo", batchNo);
                parmTempJson.put("opNo", opNo);
                DocFileInfEntity docFileInf = new DocFileInfEntity();
                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key);
                    pactNo = dto.getPactNo();
                    parmTempJson.put("pactNo", pactNo + "鸿云资金抵扣审批");
                    parmTempJson.put("templateId", "c8a140fcf4314fe995c70b6f61fa693c");
                    //生成合同套打参数
                    parmList.put("busId", key);
                    parmList.put("prepayId", key);
                    parmList.put("pactId", dto.getPactId());
                    parmTempJson.put("parmList", parmList);
                    //生成合同模板
                    JSONObject jsonObject = docFileBizFiletypeConfigService.renderTemplateJSONObject(parmTempJson);
                    if (!jsonObject.containsKey("path")) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成费用抵扣凭证附件-error-凭证主表关联业务编号:" + busIds + "生成失败");
                        continue;
                    }
                    String path = jsonObject.getString("path");
                    String substring = path.substring(path.lastIndexOf("."));
                    inputStream = new FileInputStream(path);
                    JSONObject writeOss = ossConfig.writeOss(batchVoucherPushOss + "/" + pactNo + "/" + voucherKdId + "/" + UUIDUtil.getUUID() + substring, inputStream);
                    if (!"0000".equals(writeOss.get("code"))) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成付分润费用凭证附件凭-error-证主表关联业务编号" + busIds + "生成失败");
                        continue;
                    }
                    String filePath = writeOss.getString("filePath");
                    String pushUuid = UUIDUtil.getUUID();
                    //将文件和凭证对应关系存入凭证中间表
                    ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                    configVoucherPushEntityDTO.setId(pushUuid);
                    configVoucherPushEntityDTO.setFilePath(filePath);
                    configVoucherPushEntityDTO.setFileName(jsonObject.getString("fileName"));
                    configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                    configVoucherPushEntityDTO.setState("0");//未推送
                    configFeignClient.insert(configVoucherPushEntityDTO);
                    docFileInf.setBizNo(key);
                    List<DocFileInfEntity> fileList = docFileInfService.findFileInfList(docFileInf);
                    //如果没有附件，则给出提示
                    if (CollectionUtils.isEmpty(fileList)) {
                        log.info("Taskdetailex-pushKINGDEE-(凭证:" + voucherKdId + ")-" + pactNo + "-流程中没有上传附件");
                        continue;
                    }
                    //流程中上传的文件
                    for (DocFileInfEntity docFileInfEntity : fileList) {
//                        List<DocFileInfEntity> list1 = new ArrayList();
//                        list1.add(docFileInfEntity);
//                        //生成文件到指定位置
//                        String generateFile = docFileBizFiletypeConfigService.generateFile(opNo, batchNo, pactNo, list1);
//                        if (StringUtil.isNotEmpty(generateFile)){
                        configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                        configVoucherPushEntityDTO.setFilePath(docFileInfEntity.getFilePath());
                        configVoucherPushEntityDTO.setFileName(docFileInfEntity.getFileName() + docFileInfEntity.getFilePath().substring(docFileInfEntity.getFilePath().lastIndexOf(".")));
                        configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                        configVoucherPushEntityDTO.setState("0");//未推送
                        configFeignClient.insert(configVoucherPushEntityDTO);
//                        }else {
//                            log.error("Taskdetailex-pushKINGDEE-error-费用抵扣流程中上传的文件"+docFileInfEntity.getFileName()+"下载失败,关联业务编号为:"+docFileInfEntity.getBizNo());
//                        }
                    }
                    //删除临时目录
                    if (StringUtil.isNotEmpty(path)) {
                        Path fileToDelete = Paths.get(path);
                        if (Files.exists(fileToDelete) && Files.isRegularFile(fileToDelete)) {
                            Files.deleteIfExists(fileToDelete);
                        }
                        deleteEmptyParentDirectories(fileToDelete.getParent());
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("费用抵扣凭证附件-凭证主表关联业务编号:" + busIds + "，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime, endTime);
            log.info("费用抵扣凭证附件-凭证主表关联业务编号:" + busIds + "，总耗时:" + duration + "毫秒");


        } catch (Exception e) {
            log.error("Taskdetailex-pushKINGDEE-(费用抵扣凭证附件)-error-(-凭证主表关联业务编号:" + busIds + ")-生成附件失败参数信息：{}-异常信息：{}", e.getMessage());
            throw new RuntimeException();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /**
     * 付分润费用和SP费用
     *
     * @param busIds
     * @param map
     */
    public void voucherFileDown06(String busIds, Map<String, String> map) throws Exception {
        log.info("付分润费用和SP费用凭证附件下载消费开始voucherFileDownload map:{}", busIds);
        String opNo = map.get("opNo");
        String batchNo = map.get("batchNo");
        String voucherKdId = map.get("voucherKdId");
//        String pactNo = map.get("pactNo");
        String pactNo = "";
        InputStream inputStream = null;
        try {
            //流程中的附件
            List<String> list = new ArrayList<>();
            list.add(busIds);
            JSONObject parmJson = new JSONObject();
            parmJson.put("ids", list);

            //获取提前结清信息
            List<LeasePayApplyDTO> applyDTOS = leaseFeiginClient.getLeasePayApplyByIds(parmJson);
            Map<String, LeasePayApplyDTO> dtoMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(applyDTOS)) {
                for (LeasePayApplyDTO dto : applyDTOS) {
                    if (dtoMap.get(dto.getId()) == null) {
                        dtoMap.put(dto.getId(), dto);
                    }
                }
            }
            //合同模板渲染，上传附件下载
            LeasePayApplyDTO dto = null;
            JSONObject parmList = new JSONObject();
            //没有上传文件的合同号
            List<String> pactNosNoFiles = new ArrayList<>();
            pactNosNoFiles.add("以下付款流程中未上传附件");
            long startTime = System.currentTimeMillis();
            log.info("付分润费用凭证附件下载-批号:" + batchNo + "，文件生成开始时间:" + startTime);
            DocFileInfEntity docFileInf = new DocFileInfEntity();
            if (null != dtoMap && !dtoMap.isEmpty()) {
                JSONObject parmTempJson = new JSONObject();
                parmTempJson.put("batchNo", batchNo);
                parmTempJson.put("opNo", opNo);
                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key);
                    //根据申请id查询付款申请关联表获取合同编号
                    pactNo = dto.getPayNo()  ;
                    parmTempJson.put("pactNo", pactNo + "鸿云资金支付审批");
                    parmTempJson.put("templateId", "449b75181cb84d65b79b5979e6cf7c2e");
                    //生成合同套打参数
                    parmList.put("busId", key);
                    parmList.put("prepayId", key);
                    parmTempJson.put("parmList", parmList);
                    //根据模板生成凭证
                    JSONObject jsonObject = docFileBizFiletypeConfigService.renderTemplateJSONObject(parmTempJson);
                    if (!jsonObject.containsKey("path")) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成付分润费用凭证附件凭-error-证主表关联业务编号" + busIds + "生成失败");
                        continue;
                    }
                    String path = jsonObject.getString("path");
                    String substring = path.substring(path.lastIndexOf("."));
                    inputStream = new FileInputStream(path);
                    JSONObject writeOss = ossConfig.writeOss(batchVoucherPushOss + "/" + pactNo + "/" + voucherKdId + "/" + UUIDUtil.getUUID() + substring, inputStream);
                    if (!"0000".equals(writeOss.get("code"))) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成付分润费用凭证附件凭-error-证主表关联业务编号" + busIds + "生成失败");
                        continue;
                    }
                    String filePath = writeOss.getString("filePath");
                    //将文件和凭证对应关系存入凭证中间表
                    String pushUuid = UUIDUtil.getUUID();
                    ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                    configVoucherPushEntityDTO.setId(pushUuid);
                    configVoucherPushEntityDTO.setFilePath(filePath);
                    configVoucherPushEntityDTO.setFileName(jsonObject.getString("fileName"));
                    configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                    configVoucherPushEntityDTO.setState("0");//未推送
                    configFeignClient.insert(configVoucherPushEntityDTO);
                    docFileInf.setBizNo(key);
                    List<DocFileInfEntity> fileList = docFileInfService.findFileInfList(docFileInf);
                    //如果没有附件，则给出提示
                    if (CollectionUtils.isEmpty(fileList)) {
                        log.info("Taskdetailex-pushKINGDEE-(凭证:" + voucherKdId + ")-" + pactNo + "-流程中没有上传附件");
                        continue;
                    }
                    //流程中上传的文件
                    for (DocFileInfEntity docFileInfEntity : fileList) {
//                        List<DocFileInfEntity> list1 = new ArrayList();
//                        list1.add(docFileInfEntity);
                        //生成文件到指定位置
//                        String generateFile = docFileBizFiletypeConfigService.generateFile(opNo, batchNo, pactNo, list1);
//                        if (StringUtil.isNotEmpty(generateFile)){
                        configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                        configVoucherPushEntityDTO.setFilePath(docFileInfEntity.getFilePath());
                        configVoucherPushEntityDTO.setFileName(docFileInfEntity.getFileName() + docFileInfEntity.getFilePath().substring(docFileInfEntity.getFilePath().lastIndexOf(".")));
                        configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                        configVoucherPushEntityDTO.setState("0");//未推送
                        configFeignClient.insert(configVoucherPushEntityDTO);
//                        }else {
//                            log.error("Taskdetailex-pushKINGDEE-error-资金支付流程中上传的文件"+docFileInfEntity.getFileName()+"下载失败,关联业务编号为:"+docFileInfEntity.getBizNo());
//                        }
                    }
                    //删除临时目录
                    if (StringUtil.isNotEmpty(path)) {
                        Path fileToDelete = Paths.get(path);
                        if (Files.exists(fileToDelete) && Files.isRegularFile(fileToDelete)) {
                            Files.deleteIfExists(fileToDelete);
                        }
                        deleteEmptyParentDirectories(fileToDelete.getParent());
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("付分润费用凭证附件下载-凭证主表关联业务编号:" + busIds + "，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime, endTime);
            log.info("付分润费用凭证附件下载-凭证主表关联业务编号:" + busIds + "，总耗时:" + duration + "毫秒");
        } catch (Exception e) {
            log.error("Taskdetailex-pushKINGDEE-(付分润费用凭证附件推送)-error-(凭证主表关联业务编号:" + busIds + ")-生成附件失败-异常信息：{}", e.getMessage());
            throw new RuntimeException();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /**
     * 退款
     *
     * @param busIds
     * @param map
     */
    public void voucherFileDown08(String busIds, Map<String, String> map) throws Exception {
        log.info("退款凭证附件生成开始voucherFileDownload map:{}", busIds);
        String opNo = map.get("opNo");
        String batchNo = map.get("batchNo");
        String voucherKdId = map.get("voucherKdId");
        String pactNo = map.get("pactNo");
        InputStream inputStream = null;
        try {
            //流程中的附件
            List<String> list = new ArrayList<>();
            list.add(busIds);
            JSONObject parmJson = new JSONObject();
            parmJson.put("ids", list);
            //获取提前结清信息
            List<LeasePayApplyDTO> applyDTOS = leaseFeiginClient.getLeasePayApplyByIds(parmJson);
            Map<String, LeasePayApplyDTO> dtoMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(applyDTOS)) {
                for (LeasePayApplyDTO dto : applyDTOS) {
                    if (dtoMap.get(dto.getId()) == null) {
                        dtoMap.put(dto.getId(), dto);
                    }
                }
            }
            //合同模板渲染，上传附件下载
            LeasePayApplyDTO dto = null;
            JSONObject parmList = new JSONObject();

            long startTime = System.currentTimeMillis();
            log.info("退款凭证附件生成:" + batchNo + "，文件生成开始时间:" + startTime);

            if (null != dtoMap && !dtoMap.isEmpty()) {
                JSONObject parmTempJson = new JSONObject();
                parmTempJson.put("batchNo", batchNo);
                parmTempJson.put("opNo", opNo);
                DocFileInfEntity docFileInf = new DocFileInfEntity();
                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key);
//                    pactNo = dto.getPactNo()  ;
                    if ("RETURN_3".equals(dto.getPayType())) {//退款_保证金池
                        parmTempJson.put("templateId", "f049abe3a09740c8baf57478a2586d4d");
                        parmTempJson.put("pactNo", pactNo + "鸿云退保证金池审批");
                    } else if ("RETURN_1".equals(dto.getPayType())) {//退款_暂收款
                        parmTempJson.put("templateId", "bbb31ce3a4b640a1b1d2e318b5c304bd");
                        parmTempJson.put("pactNo", pactNo + "鸿云退暂收款审批");
                    } else {//退款_通用,
                        parmTempJson.put("templateId", "815250ee63e640f88405beacf5d9c588");
                        parmTempJson.put("pactNo", pactNo + "鸿云退款审批");
                    }

                    //生成合同套打参数
                    parmList.put("busId", key);
                    parmList.put("prepayId", key);
                    parmTempJson.put("parmList", parmList);
                    //根据模板生成凭证
                    JSONObject jsonObject = docFileBizFiletypeConfigService.renderTemplateJSONObject(parmTempJson);
                    if (!jsonObject.containsKey("path")) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成退款凭证附件-error-凭证主表关联业务编号" + busIds + "生成失败");
                        continue;
                    }
                    String path = jsonObject.getString("path");
                    String substring = path.substring(path.lastIndexOf("."));
                    inputStream = new FileInputStream(path);
                    JSONObject writeOss = ossConfig.writeOss(batchVoucherPushOss + "/" + pactNo + "/" + voucherKdId + "/" + UUIDUtil.getUUID() + substring, inputStream);
                    if (!"0000".equals(writeOss.get("code"))) {
                        log.error("Taskdetailex-pushKINGDEE-批量生成付分润费用凭证附件凭-error-证主表关联业务编号" + busIds + "生成失败");
                        continue;
                    }
                    String filePath = writeOss.getString("filePath");
                    //将文件和凭证对应关系存入凭证中间表
                    String pushUuid = UUIDUtil.getUUID();
                    ConfigVoucherPushEntityDTO configVoucherPushEntityDTO = new ConfigVoucherPushEntityDTO();
                    configVoucherPushEntityDTO.setId(pushUuid);
                    configVoucherPushEntityDTO.setFilePath(filePath);
                    configVoucherPushEntityDTO.setFileName(jsonObject.getString("fileName"));
                    configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                    configVoucherPushEntityDTO.setState("0");//未推送
                    configFeignClient.insert(configVoucherPushEntityDTO);
                    docFileInf.setBizNo(key);
                    List<DocFileInfEntity> fileList = docFileInfService.findFileInfList(docFileInf);
                    //如果没有附件，则给出提示
                    if (CollectionUtils.isEmpty(fileList)) {
                        log.info("Taskdetailex-pushKINGDEE-(凭证:" + voucherKdId + ")-" + pactNo + "-流程中没有上传附件");
                        continue;
                    }
                    //流程中上传的文件
                    for (DocFileInfEntity docFileInfEntity : fileList) {
//                        List<DocFileInfEntity> list1 = new ArrayList();
//                        list1.add(docFileInfEntity);
//                        //生成文件到指定位置
//                        String generateFile = docFileBizFiletypeConfigService.generateFile(opNo, batchNo, pactNo, list1);
//                        if (StringUtil.isNotEmpty(generateFile)){
                        configVoucherPushEntityDTO.setId(UUIDUtil.getUUID());
                        configVoucherPushEntityDTO.setFilePath(docFileInfEntity.getFilePath());
                        configVoucherPushEntityDTO.setFileName(docFileInfEntity.getFileName() + docFileInfEntity.getFilePath().substring(docFileInfEntity.getFilePath().lastIndexOf(".")));
                        configVoucherPushEntityDTO.setConfigCwVoucherMstId(voucherKdId);//凭证id
                        configVoucherPushEntityDTO.setState("0");//未推送
                        configFeignClient.insert(configVoucherPushEntityDTO);
//                        }else {
//                            log.error("Taskdetailex-pushKINGDEE-error-退款流程中上传的文件"+docFileInfEntity.getFileName()+"下载失败,关联业务编号为:"+docFileInfEntity.getBizNo());
//                        }
                    }
                    //删除临时目录
                    if (StringUtil.isNotEmpty(path)) {
                        Path fileToDelete = Paths.get(path);
                        if (Files.exists(fileToDelete) && Files.isRegularFile(fileToDelete)) {
                            Files.deleteIfExists(fileToDelete);
                        }
                        deleteEmptyParentDirectories(fileToDelete.getParent());
                    }
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("退款凭证附件生成-凭证主表关联业务编号:" + busIds + "，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime, endTime);
            log.info("退款凭证附件生成-凭证主表关联业务编号:" + busIds + "，总耗时:" + duration + "毫秒");
        } catch (Exception e) {
            log.error("Taskdetailex-pushKINGDEE-(退款凭证附件生成)-error-(凭证主表关联业务编号:" + busIds + ")-生成附件失败-异常信息：{}", e.getMessage());
            throw new RuntimeException();
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }


    /**
     * 合同变更承租人变更
     *
     * @param busIds
     * @param map
     */
    public void voucherFileDown10(String busIds, BaseMap map) {
        log.info("合同变更凭证附件下载消费开始voucherFileDownload map:{}", busIds);
        String opNo = map.get("opNo");
        String batchNo = map.get("batchNo");
        try {
            //流程中的附件
            List<String> pactIds = Arrays.stream(busIds.split(",")).collect(Collectors.toList());
            JSONObject parmJson = new JSONObject();
            parmJson.put("pactIds", pactIds);
            //获取提前结清信息
            List<LeasePactDTO> leasePactDTOS = leaseFeiginClient.getLeasePacts(parmJson);
            Map<String, LeasePactDTO> dtoMap = new HashMap<>();

            if (CollectionUtils.isNotEmpty(leasePactDTOS)) {
                for (LeasePactDTO dto : leasePactDTOS) {
                    if (dtoMap.get(dto.getPactId()) == null) {
                        dtoMap.put(dto.getPactId(), dto);
                    }
                }
            }

            List<DocFileInfEntity> fileList = null;
            //合同模板渲染，上传附件下载
            LeasePactDTO dto = null;
            String pactNo = "";
            JSONObject parmList = null;
            //没有上传文件的合同号
            List<String> pactNosNoFiles = new ArrayList<>();
            pactNosNoFiles.add("以下合同流程中未上传附件");

            long startTime = System.currentTimeMillis();
            log.info("合同变更承租人变更凭证附件下载-批号:" + batchNo + "，文件生成开始时间:" + startTime);

            if (null != dtoMap && !dtoMap.isEmpty()) {
                JSONObject parmTempJson = new JSONObject();
                parmTempJson.put("batchNo", batchNo);
                parmTempJson.put("opNo", opNo);
                DocFileInfEntity docFileInf = new DocFileInfEntity();

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key);
                    pactNo = dto.getPactNo();
                    parmTempJson.put("pactNo", pactNo);
                    //合同变更(承租人变更)
                    parmTempJson.put("templateId", "8d441255ec42439088367803bd597470");
                    //生成合同套打参数
                    parmList = getLeasePactParmList(dto, key);
                    parmTempJson.put("parmList", parmList);
                    //生成合同模板
                    docFileBizFiletypeConfigService.renderTemplate(parmTempJson);
//                    docFileInf.setBizNo(key);
//                    fileList = docFileInfService.findFileInfList(docFileInf);
//                    //如果没有附件，则给出提示
//                    if(CollectionUtils.isEmpty(fileList)){
//                        pactNosNoFiles.add(pactNo) ;
//                        log.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"流程中没有上传附件");
//                        continue;
//                    }
//                    //生成文件到指定位置
//                    docFileBizFiletypeConfigService.generateFile(opNo,batchNo, pactNo,fileList) ;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("合同变更承租人变更凭证附件下载-批号:" + batchNo + "，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime, endTime);
            log.info("合同变更承租人变更凭证附件下载-批号:" + batchNo + "，总耗时:" + duration + "毫秒");

            //没有上传文件的合同记录到文件
//            docFileBizFiletypeConfigService.writInfosToFile(opNo,batchNo,pactNosNoFiles) ;

            long startTime1 = System.currentTimeMillis();
            log.info("合同变更承租人变更凭证附件下载-批号:" + batchNo + "，打包开始时间:" + startTime1);
            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo, batchNo);

            long endTime1 = System.currentTimeMillis();
            log.info("合同变更承租人变更凭证附件下载-批号:" + batchNo + "，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1, endTime1);
            log.info("合同变更承租人变更凭证附件下载-批号:" + batchNo + "，总耗时:" + duration1 + "毫秒");
        } catch (Exception e) {
            log.error("MQCreatVoucherFile-Name(合同变更承租人变更凭证附件下载)-批号：(" + batchNo + ")-生成附件失败异常信息：{}", e.getMessage());

        }
    }


    /**
     * 合同变更(承租人变更)参数处理
     *
     * @param dto
     * @param key
     * @return
     */
    private JSONObject getLeasePactParmList(LeasePactDTO dto, String key) {
        JSONObject parmList = new JSONObject();
        parmList.put("prepayId", dto.getPrepayId());
        parmList.put("pactId", dto.getOriginalPactId());
        parmList.put("chengedCusPactId", dto.getPactId());
        parmList.put("dueId", dto.getDueId());
        parmList.put("busId", dto.getPrepayId());
        parmList.put("applyId", dto.getApplyId());

        parmList.put("occType", dto.getOccType());
        parmList.put("originalPactNo", dto.getOriginalPactNo());
        parmList.put("originalCusName", dto.getOriginalCusName());
        parmList.put("pactNo", dto.getPactNo());
        parmList.put("cusName", dto.getCusName());
        parmList.put("totalLeasePrice", dto.getTotalLeasePrice());
        if (null != dto.getDownPaymentsRate()) {
            parmList.put("downPaymentsRate", dto.getDownPaymentsRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
        }

        parmList.put("downPaymentsAmt", dto.getDownPaymentsAmt());
        if (null != dto.getPactAmt()) {
            parmList.put("applyAmt", dto.getPactAmt().setScale(2, RoundingMode.HALF_UP));
        }
        if (null != dto.getTotalTinterest()) {
            parmList.put("totalTinterest", dto.getTotalTinterest().setScale(2, RoundingMode.HALF_UP));
        }

        if (null != dto.getCusTirrShow()) {
            parmList.put("cusTirrShow", dto.getCusTirrShow().setScale(4, RoundingMode.HALF_UP));
        }
        if (null != dto.getTotalTreceivables()) {
            parmList.put("totalTreceivables", dto.getTotalTreceivables().setScale(2, RoundingMode.HALF_UP));
            parmList.put("totalTreceivables_dx", dto.getTotalTreceivables().setScale(2, RoundingMode.HALF_UP));
        }

        parmList.put("termShow", dto.getTermShow());
        if (null != dto.getRate()) {
            parmList.put("rate", dto.getRate().setScale(2, RoundingMode.HALF_UP));
        }
        parmList.put("repayType", dto.getRepayType());
        parmList.put("repayCycle", dto.getRepayCycle());
        parmList.put("graceType", dto.getGraceType());
        parmList.put("graceDays", dto.getGraceDays());
        parmList.put("rateAdjustFlag", dto.getRateAdjustFlag());
        parmList.put("hasMarginDeduction", dto.getHasMarginDeduction());
        parmList.put("beforePay", dto.getBeforePay());
        parmList.put("rebateFlag", dto.getRebateFlag());
        parmList.put("discountFlag", dto.getDiscountFlag());
        parmList.put("discountType", dto.getDiscountType());
        parmList.put("signMethod", dto.getSignMethod());
        parmList.put("pactType", dto.getPactType());
        parmList.put("pledgeInfoFlag", dto.getPledgeInfoFlag());
        parmList.put("repayAccountNo", dto.getRepayAccountNo());
        parmList.put("repayAccountName", dto.getRepayAccountName());
        parmList.put("repayBankName", dto.getRepayBankName());
        parmList.put("invoiceRequire", dto.getInvoiceRequire());
        parmList.put("collateralRequire", dto.getCollateralRequire());
        parmList.put("channelGuaranteeType", dto.getChannelGuaranteeType());
        parmList.put("itemCategory", dto.getItemCategory());
        parmList.put("productName", dto.getProductName());
        parmList.put("channelSource", dto.getChannelSource());
        parmList.put("creditCusName", dto.getCreditCusName());
        parmList.put("limitTypeName", dto.getLimitTypeName());
        parmList.put("leaseType", dto.getLeaseType());
        parmList.put("sellerName", dto.getSellerName());
        parmList.put("hasControlledKangfu", dto.getHasControlledKangfu());
        parmList.put("putoutDate", dto.getPutoutDate());
        parmList.put("insuranceFeeBal", dto.getInsuranceFeeBal());
        parmList.put("loanBalance", dto.getLoanBalance());
        parmList.put("extendBalance", dto.getExtendBalance());
        parmList.put("insuranceDepositBal", dto.getInsuranceDepositBal());
        parmList.put("mortgageNotaryFeeBal", dto.getMortgageNotaryFeeBal());
        parmList.put("managerName", dto.getManagerName());
        parmList.put("pactCreateTime", dto.getPactCreateTime());
        parmList.put("company", dto.getCompany());
        /**
         * 起租日
         */
        parmList.put("dueBeginDate", dto.getDueBeginDate());
        /**
         * 预计结束日
         */
        parmList.put("dueEndDate", dto.getDueEndDate());
        return parmList;
    }


    private long calcTime(long startTime, long endTime) {
        return (endTime - startTime);
    }

}
