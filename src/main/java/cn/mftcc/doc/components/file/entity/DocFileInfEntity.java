/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import cn.mftcc.common.MftccEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

/**
 * 要件信息表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-05-10 15:15:44
 */
@Data
@TableName("doc_file_inf")
public class DocFileInfEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 唯一编号
	 */
	@TableId(type = IdType.UUID)
	private String fileId;
	/**
	 * 关联业务号
	 */
	private String bizNo;
	/**
	 * 关联业务号
	 */
	@TableField(exist = false)
	private Set bizNoSet;
	/**
	 * 产品唯一标识
	 */
	private String prdUniqueVal;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;
	/**
	 * 要件类型大类 01-客户资料 02-申请资料 03-合同资料 04-放款资料 05-还款资料 06-贷后资料
	 */
	@TableField(exist = false)
	private String typeClass;

	@TableField(exist = false)
	private String groupNo;

	/**
	 * 要件类型编号
	 */
	private String typeNo;
	/**
	 * 要件类型名称
	 */
	private String typeName;


	private String parentTypeNo;
	/**
	 * 要件类型父亲名称
	 */
	@TableField(exist=false)
	private String parentTypeName;
	/**
	 * 要件来源1-pc 2-移动端
	 */
	private String origin;
	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 文件大小
	 */
	private BigDecimal fileSize;
	/**
	 * 文件路径
	 */
	private String filePath;
	/**
	 * 文件加密路径
	 */
	private String fileEncryptPath;
	/**
	 * 是否加密 1-是 0-否
	 */
	private String isEncrypt;
	/**
	 * 密码
	 */
	private String password;
	/**
	 * 是否必读  1-是 0-否
	 */
	private String ifMustRead;
	/**
	 * 已阅标识  1-是 0-否
	 */
	private String readFlag;
	/**
	 * img_base64
	 */
	private String imgBase64;
	/**
	 * 缩略图路径
	 */
	private String thumbnailPath;
	/**
	 * 是否归档   1-是 0-否
	 */
	private String archiveFlag;
	/**
	 * 要件顺序
	 */
	private Integer fileOrder;
	/**
	 * 操作员编号
	 */
	private String createUserNo;
	/**
	 * 操作员名称
	 */
	private String createUserName;
	/**
	 * 部门编号
	 */
	private String createDeptNo;
	/**
	 * 部门名称
	 */
	private String createDeptName;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	// @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;
	/**
	 * 法人机构编号
	 */
	private String corpId;
	/**
	 * 上传的obs系统的唯一标识
	 */
	private String obsId;
	/**
	 * 上传到外网服务器的路径
	 */
	private String outterUrl;
	/**
	 * 关联业务编号
	 */
	private String busRelatedId;
	/**
	 * 关联客户号
	 */
	private String cusRelatedId;
	/**
	 * 关联申请编号
	 */
	private String applyRelatedId;
	/**
	 * 关联合同编号
	 */
	private String constractRelatedId;
	/**
	 * 关联借据编号
	 */
	private String finRelatedId;
	//是否电签文件
    private String ifEsignFile;
	//是否电签文件
    private String oriFileId;
	/**
	 * 文件审核状态，0待审、1已审
	 * 合同/权证归档上传得文件且审核流程未完结时=0
	 */
	private String FileApprove;

	/**
	 *  //1-目录 2-word 3-excel 4-pdf 5-图片 6-压缩包 7-txt 8-视频 9-音频 10-其他。
	 */
	@TableField(exist = false)
	private String   fileType;

	@TableField(exist = false)
	String base64Str;
	@TableField(exist = false)
	byte[] byteStr;
	@TableField(exist = false)
	String contentType;
	/**
	 * 指定排序
	 */
	@TableField(exist = false)
	String orderBy;
	/**
	 * 是否下载
	 */
	@TableField(exist=false)
	private Boolean downLoadFlag=false;
	/**
	 * 是否允许删除
	 */
	private String deleteFlag;



	/**
	 * 位置
	 */
	private String location;


	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;
		DocFileInfEntity docFileInfEntity = (DocFileInfEntity) o;
		return Objects.equals(fileId, docFileInfEntity.fileId);
	}

	@Override
	public int hashCode() {
		return Objects.hash(fileId);
	}


}
