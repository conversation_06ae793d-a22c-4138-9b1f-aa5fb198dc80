/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import lombok.Data;

/**
 * 文件类型表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 17:19:12
 */
@Data
@TableName("doc_file_type")
public class DocFileTypeEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	private String typeId;

	/**
	 * 要件类型编号
	 */
	@TableId
	private String typeNo;
	/**
	 * 要件类型名称
	 */
	private String typeName;
	/**
	 * 要件类型描述
	 */
	private String typeDesc;
	/**
	 * 启用标识 1-启用 0-禁用
	 */
	private String useFlag;
	/**
	 * 法人机构编号
	 */
	private String corpId;
	/**
	 * 要件类型分组编号
	 */

	private String groupName;

	/**
	 * pdf是否生成一张图片:1-是 0-否
	 */
	private String pdfImageFlag;
	/**
	 * 是否使用高拍仪:1-是 0-否
	 */
	private String useSdkFlag;
	/**
	 * 要件类型大类,支持多选
	 */
	private String typeClass;
	/**
	 * 要件类型大类,支持多选
	 */
	private String typeClassName;
	/**
	 * 分组编号,支持多选
	 */
	private String groupNo;
	/**
	 * 部门查看权限控制,支持多选
	 */
	private String deptReadAuth;
	/**
	 * 部门查看权限控制,支持多选
	 */
	private String deptReadAuthName;
	/**
	 * 角色查看权限控制,支持多选
	 */
	private String roleReadAuth;
	/**
	 * 角色查看权限控制,支持多选
	 */
	private String roleReadAuthName;
	/**
	 * 多维度feigin接口
	 */
	private String multiFeign;
	/**
	 * 多维度feigin接口调用的方法
	 */
	private String multiFeignMethod;

	/**
	 * 多维度参数列表
	 */
	private String parmList;
	/**
	 * 归档类型 ARCHIVE_FILE_TYPE_CLASS
	 */
	private String archiveType;

}
