/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.file.entity.DocCopyConfigEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 影像资料赋值配置
 *
 * <AUTHOR>
 * @email 
 * @date 2022-10-13 19:18:03
 */
public interface DocCopyConfigService {

    IPage<DocCopyConfigEntity> findByPage(DocCopyConfigEntity docCopyConfigEntity) throws ServiceException;

    void insert(DocCopyConfigEntity docCopyConfigEntity) throws ServiceException;

    void update(DocCopyConfigEntity docCopyConfigEntity) throws ServiceException;

    DocCopyConfigEntity findById(Integer id) throws ServiceException;

    void deleteById(Integer id) throws ServiceException;

    List<DocCopyConfigEntity> selectListBy(DocCopyConfigEntity docCopyConfigEntity) throws ServiceException;
}

