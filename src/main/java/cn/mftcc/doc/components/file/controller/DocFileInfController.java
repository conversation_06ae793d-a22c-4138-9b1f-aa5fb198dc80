/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.controller;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.config.feign.dto.ConfigVoucherPushEntityDTO;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.common.utils.OssConfig;
import cn.mftcc.doc.components.file.entity.DocFileBizFiletypeConfigEntity;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.entity.FileShowVo;
import cn.mftcc.doc.components.file.entity.GroupVo;
import cn.mftcc.doc.components.file.entity.QueryParm;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.file.service.DocFileInfService;
import cn.mftcc.doc.feign.client.ConfigFeignClient;
import cn.mftcc.doc.feign.client.ElinkApiFeignClient;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;

/**
 * 要件信息表
 *getOfficeFileObj
 * <AUTHOR>
 * @email 
 * @date 2021-05-10 15:15:44
 */
@RestController
@RequestMapping("file/docFileInf")
@Slf4j
public class DocFileInfController {
    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private DocFileInfService docFileInfService;
    private ConfigFeignClient configFeignClient;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private ElinkApiFeignClient elinkFeignClient;

    public static final String HEADER_NAME_DEFAULT = "Content-Disposition";

    private static final String STR6 = "alert(\"not find the file\")";

    private static final String CONTENT_TYPE_DEF = "application/x-download";
    private static final String CONTENT_TYPE_DEF1 = "application/octet-stream";
;
    private static final String HEADER_VALUE_DEFAULT = "attchement;filename=";

    private static final String SCRIPT_HEAD = "<script>";
    private static final String SCRIPT_TAIL = "</script>";
    private static int fileNameLength = 150;
    @Value("${mftcc.file.doc-file-upload-path:}")
    private  String docFileUploadPath;
    @Value("${mftcc.template.mould-path:}")
    private String docMouldPath;

    @Value("${mftcc.file.upload-outter-function:}")
    private String uploadOutterFunction;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocFileInfEntity docFileInfEntity) {
        IPage<DocFileInfEntity> list = this.docFileInfService.findByPage(docFileInfEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }
    @RequestMapping("/findFileInfList")
    public R findFileInfList(@RequestBody DocFileInfEntity docFileInfEntity) {
        List<DocFileInfEntity> list = this.docFileInfService.findFileInfList(docFileInfEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping(value = "/getFileStream/{fileId}")
    @ResponseBody
    public void getStreamData(@PathVariable("fileId") String fileId, HttpServletResponse response) {
//        uploadOutterFunction = "oss";
        BufferedInputStream buf = null;
        ServletOutputStream out = null;
        BufferedOutputStream bot = null;
        InputStream instream = null;
        try {
            MFLogger.info("获取缩略图*****************************************************************");
            instream=this.docFileInfService.getFileStream(fileId);
            DocFileInfEntity tmpObj=this.docFileInfService.findById(fileId);
            String filePath = "";
            if("obs".equals(uploadOutterFunction)){
                filePath = tmpObj.getObsId();
            }else{
                filePath = tmpObj.getFilePath();
            }
            String tmpAry[]=filePath.split("\\.");

            String  suffix=tmpAry[tmpAry.length-1];//后缀名
            response.reset();
            response.setContentType(CONTENT_TYPE_DEF);
            response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
//            response.addHeader("Content-Length", "" + file.length());
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(tmpObj.getFileName()+"."+suffix, "UTF-8"));
//            response.addHeader("file-name*", URLEncoder.encode(tmpObj.getFileName()+"."+suffix, "UTF-8"));
            byte[] b=new byte[1024];
            buf=new BufferedInputStream(instream);
            out=response.getOutputStream();
            bot=new BufferedOutputStream(out);


            int readLength;
            while ((readLength = buf.read(b)) != -1) {
                bot.write(b, 0, readLength);
            }
            bot.flush();
            instream.close();
            buf.close();
            out.close();
            bot.close();

        } catch (Exception  e) {
            // TODO Auto-generated catch block
            MFLogger.error("",e);
        }finally {
            try {
                if (instream != null){
                    instream.close();
                }
                if (buf != null){
                    buf.close();
                }
                if (out != null){
                    out.close();
                }
                if (bot != null){
                    bot.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }


    /**
     * 获取凭证附件
     * @param id
     * @param response
     */
    @RequestMapping(value = "/getFileStreamOss/{id}")
    @ResponseBody
    public void getFileStreamOss(@PathVariable("id") String id, HttpServletResponse response) {
        BufferedInputStream buf = null;
        ServletOutputStream out = null;
        BufferedOutputStream bot = null;
        InputStream instream = null;
        try {
            MFLogger.info("获取缩略图*****************************************************************");
            instream=this.docFileInfService.getFileStreamOss(id);
            ConfigVoucherPushEntityDTO tmpObj = configFeignClient.selectById(id);
//            DocFileInfEntity tmpObj=this.docFileInfService.findById(fileId);
            String filePath = "";
            if("obs".equals(uploadOutterFunction)){
                filePath = tmpObj.getConfigCwVoucherMstId();
            }else{
                filePath = tmpObj.getFilePath();
            }
            String tmpAry[]=filePath.split("\\.");

            String  suffix=tmpAry[tmpAry.length-1];//后缀名
            response.reset();
            response.setContentType(CONTENT_TYPE_DEF);
            response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(tmpObj.getFileName()+"."+suffix, "UTF-8"));
//            response.addHeader("file-name*", URLEncoder.encode(tmpObj.getFileName()+"."+suffix, "UTF-8"));
            byte[] b=new byte[1024];
            buf=new BufferedInputStream(instream);
            out=response.getOutputStream();
            bot=new BufferedOutputStream(out);
            int readLength;
            while ((readLength = buf.read(b)) != -1) {
                bot.write(b, 0, readLength);
            }
            bot.flush();
            instream.close();
            buf.close();
            out.close();
            bot.close();
        } catch (Exception  e) {
            // TODO Auto-generated catch block
            MFLogger.error("",e);
        }finally {
            try {
                if (instream != null){
                    instream.close();
                }
                if (buf != null){
                    buf.close();
                }
                if (out != null){
                    out.close();
                }
                if (bot != null){
                    bot.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }

    /**
     * 读取凭证其他文件
     * @param id
     * @param response
     */
    @RequestMapping(value = "/previewVoucherOfficeFile/{id}")
    public  void previewVoucherOfficeFile(@PathVariable("id") String id, HttpServletResponse response) {

//        JSONObject ajaxData=new JSONObject();
//        ajaxData.put("fileId",id);
        InputStream fis =null;
        PrintWriter out = null;
        try {
            ConfigVoucherPushEntityDTO tmpObj = configFeignClient.selectById(id);
            // 1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            OutputStream toClient =null;
            if("oss".equals(uploadOutterFunction)){
                // 2.设置文件头：最后一个参数是设置下载文件名
                String FilePath = tmpObj.getFilePath();
                String suffix=FilePath.split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;
                fis = ossConfig.readOss(FilePath);
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                toClient.flush();
                toClient.close();
            }if("obs".equals(uploadOutterFunction)){
                // 2.设置文件头：最后一个参数是设置下载文件名
                String obsId = tmpObj.getConfigCwVoucherMstId();
                String suffix=obsId.split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;
                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                fis = new URL(filePath).openStream();
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                toClient.flush();
                toClient.close();
            }else{
                // 2.设置文件头：最后一个参数是设置下载文件名
                String suffix=tmpObj.getFilePath().split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;

                String fileUrlPath = docFileUploadPath+File.separator+tmpObj.getFilePath();
                File file=new File(fileUrlPath);
                if (file.exists()) {
                    fis = new BufferedInputStream(new FileInputStream( file));
                    response.reset();
                    response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                    response.addHeader("Content-Length", "" + file.length());
//                    response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                    response.setHeader("Content-Disposition", "attachment; filename=" + java.net.URLEncoder.encode(newFileName, "UTF-8"));
                    toClient = new BufferedOutputStream(response.getOutputStream());
                    response.setContentType(CONTENT_TYPE_DEF1);
                    byte[] buffer = new byte[1024 * 1024 * 4];
                    int i = -1;
                    while ((i = fis.read(buffer)) != -1) {
                        toClient.write(buffer, 0, i);
                    }
                    toClient.flush();
                    toClient.close();
                } else {
                    out = response.getWriter();
                    out.print(SCRIPT_HEAD);
                    out.print(STR6);
                    out.print(SCRIPT_TAIL);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if(null != out){
                out.close();
            }
            if (null !=fis){
                try {
                    fis.close();
                }catch (Exception e){
                }
            }
        }
    }












    /**
     * 获取模板文件的流
     * @param flag 文件类型 1-原始模板文件 2-生成的模板文件
     * @param fileName 文件名称
     * @param response
     */
    @RequestMapping(value = "/getMouldStream/{flag}/{fileName}")
    public  void getMouldStream(@PathVariable("flag") String flag,@PathVariable("fileName") String fileName, HttpServletResponse response) {

//        JSONObject ajaxData=new JSONObject();
//        ajaxData.put("fileId",fileId);
        InputStream fis =null;
        try {

            response.setContentType("multipart/form-data");
            // 2.设置文件头：最后一个参数是设置下载文件名
            String  newFileName=fileName;
            OutputStream toClient =null;
            String fileUrlPath="";
            if("1".equals(flag)){
                //如果是模板文件
                fileUrlPath=docMouldPath+fileName;
            }else{
                //如果是生成的模板文件
                fileUrlPath=docMouldPath+"saveMould"+File.separator+fileName;
            }
            File file=new File(fileUrlPath);
            if (file.exists()) {
//                    String dfileName = file.getName();
                fis = new BufferedInputStream(new FileInputStream( file));
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                response.addHeader("Content-Length", "" + file.length());
                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();
            } else {
                PrintWriter out = response.getWriter();
                out.print(SCRIPT_HEAD);
                out.print(STR6);
                out.print(SCRIPT_TAIL);
            }


//            PrintWriter out = response.getWriter();
//            out.print(SCRIPT_HEAD);
//            out.print(STR6);
//            out.print(SCRIPT_TAIL);

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (null !=fis){
                try {
                    fis.close();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }


        }

    }


    /**
     * 获取模板文件的流
     * 响应体加入文件流
     * @param response
     * @param filePath	文件从盘符开始的完整路径
     */
    @RequestMapping(value = "/responseFileStream")
    private void responseFileStream(HttpServletResponse response, @RequestParam("filePath")String filePath){
        if(filePath.contains("%")){
            try {
                filePath = URLDecoder.decode(filePath,"UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.info("responseFileStream decode error:"+e.toString());
            }
        }

        ServletOutputStream out = null;
        FileInputStream in = null;
        try {
            in = new FileInputStream(new File(filePath));
            String[] dir = filePath.split("/");
            String fileName = dir[dir.length-1];
            String[] array = fileName.split("[.]");
            String fileType = array[array.length-1].toLowerCase();
            //设置文件ContentType类型
            if("jpg,jepg,gif,png".contains(fileType)){//图片类型
                response.setContentType("image/"+fileType);
            }else if("pdf".contains(fileType)){//pdf类型
                response.setContentType("application/pdf");
            }else{//自动判断下载文件类型
                response.setContentType("multipart/form-data");
            }
            //设置文件头：最后一个参数是设置下载文件名
//            response.setHeader("Content-Disposition", "attachment;fileName="+"开票收据");
            out = response.getOutputStream();
            // 读取文件流
            int len = 0;
            byte[] buffer = new byte[1024 * 10];
            while ((len = in.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
            out.flush();
        } catch (FileNotFoundException e) {
            log.error("responseFileStream error:FileNotFoundException" + e.toString());
        } catch (Exception e) {
            log.error("responseFileStream error:" + e.toString());
        } finally {
            try {
                out.close();
                in.close();
            } catch (NullPointerException e) {
                log.error("responseFileStream stream close() error:NullPointerException" + e.toString());
            } catch (Exception e) {
                log.error("responseFileStream stream close() error:" + e.toString());
            }
        }
    }







    @RequestMapping(value = "/previewOfficeFile/{fileId}")
    public  void previewOfficeFile(@PathVariable("fileId") String fileId, HttpServletResponse response) {

        JSONObject ajaxData=new JSONObject();
        ajaxData.put("fileId",fileId);
        InputStream fis =null;
        PrintWriter out = null;
        try {
           ajaxData.getString("fileId");
            DocFileInfEntity tmpObj=this.docFileInfService.findById(fileId);
            // 1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");

            OutputStream toClient =null;

            if("oss".equals(uploadOutterFunction)){
                // 2.设置文件头：最后一个参数是设置下载文件名
                String FilePath = tmpObj.getFilePath();
                String suffix=FilePath.split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;

//                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
//                fis = new URL(filePath).openStream();
                fis = ossConfig.readOss(FilePath);
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);

                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));

                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();

            }if("obs".equals(uploadOutterFunction)){
                // 2.设置文件头：最后一个参数是设置下载文件名
                String obsId = tmpObj.getObsId();
                String suffix=obsId.split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;

                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                fis = new URL(filePath).openStream();
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);

                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();
            }else{
                // 2.设置文件头：最后一个参数是设置下载文件名
                String suffix=tmpObj.getFilePath().split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;

                String fileUrlPath = docFileUploadPath+File.separator+tmpObj.getFilePath();
                File file=new File(fileUrlPath);
                if (file.exists()) {
//                    String dfileName = file.getName();
                    fis = new BufferedInputStream(new FileInputStream( file));
                    response.reset();
                    response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                    response.addHeader("Content-Length", "" + file.length());
//                    response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                    response.setHeader("Content-Disposition", "attachment; filename=" + java.net.URLEncoder.encode(newFileName, "UTF-8"));
                    toClient = new BufferedOutputStream(response.getOutputStream());
                    response.setContentType(CONTENT_TYPE_DEF1);
                    byte[] buffer = new byte[1024 * 1024 * 4];
                    int i = -1;
                    while ((i = fis.read(buffer)) != -1) {
                        toClient.write(buffer, 0, i);
                    }
                    //fis.close();
                    toClient.flush();
                    toClient.close();
                } else {
                    out = response.getWriter();
                    out.print(SCRIPT_HEAD);
                    out.print(STR6);
                    out.print(SCRIPT_TAIL);
                }
            }

//            PrintWriter out = response.getWriter();
//            out.print(SCRIPT_HEAD);
//            out.print(STR6);
//            out.print(SCRIPT_TAIL);

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if(null != out){
                out.close();
            }
            if (null !=fis){
                try {
                    fis.close();
                }catch (Exception e){
                }
            }


        }

    }

    @RequestMapping("/downLoadOneFile")
    public void downLoadOneFile(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
//    @RequestMapping(value = "/downLoadOneFile/{fileId}")
//    public void downLoadOneFile(String fileId, HttpServletRequest request, HttpServletResponse response){
        // 开始下载文件
//        uploadOutterFunction = "oss";
        InputStream fis =null;
        try {
            String fileId=ajaxData.getString("fileId");
            DocFileInfEntity tmpObj=this.docFileInfService.findById(fileId);
            // 1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");

            OutputStream toClient =null;

            if("obs".equals(uploadOutterFunction)){
                // 2.设置文件头：最后一个参数是设置下载文件名
                String obsId = tmpObj.getObsId();
                String suffix=obsId.split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;

                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                fis = new URL(filePath).openStream();
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);

                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();

            }else if("oss".equals(uploadOutterFunction)){
                // 2.设置文件头：最后一个参数是设置下载文件名
                String obsId = tmpObj.getFilePath();
                String suffix=obsId.split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;

//                String filePath = docFileBizFiletypeConfigService.getShowUrlByOssid(obsId);
                fis = ossConfig.readOss(tmpObj.getFilePath());
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);

                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();
            } else{
                // 2.设置文件头：最后一个参数是设置下载文件名

                String suffix=new FileUtils().getFileSuffixName(tmpObj.getFilePath());
                String  newFileName=tmpObj.getFileName()+"."+suffix;

                String fileUrlPath=docFileUploadPath+File.separator+tmpObj.getFilePath();
                File file=new File(fileUrlPath);
                if (file.exists()) {
//                    String dfileName = file.getName();
                    fis = new BufferedInputStream(new FileInputStream( file));
                    response.reset();
                    response.setContentType(CONTENT_TYPE_DEF);
                    response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                    response.addHeader("Content-Length", "" + file.length());
                    response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                    toClient = new BufferedOutputStream(response.getOutputStream());
                    response.setContentType(CONTENT_TYPE_DEF1);
                    byte[] buffer = new byte[1024 * 1024 * 4];
                    int i = -1;
                    while ((i = fis.read(buffer)) != -1) {
                        toClient.write(buffer, 0, i);
                    }
                    //fis.close();
                    toClient.flush();
                    toClient.close();
                } else {
                    PrintWriter out = response.getWriter();
                    out.print(SCRIPT_HEAD);
                    out.print(STR6);
                    out.print(SCRIPT_TAIL);
                }
            }



//                    PrintWriter out = response.getWriter();
//                    out.print(SCRIPT_HEAD);
//                    out.print(STR6);
//                    out.print(SCRIPT_TAIL);

          }catch (Exception e){
                e.printStackTrace();
            }finally {
                if (null !=fis){
                    try {
                        fis.close();
                    }catch (Exception e){
                    e.printStackTrace();
                    }
                }


                }


    }
    @RequestMapping(value = "/getFileThumStream/{fileId}")
    @ResponseBody
    public void getFileThumStream(@PathVariable("fileId") String fileId, HttpServletResponse response) {
        try {
            InputStream instream=this.docFileInfService.getFileThumbStream(fileId);

            ServletOutputStream out=null;
            byte[] b=new byte[1024];
            int length=0;
            BufferedInputStream buf=new BufferedInputStream(instream);
            out=response.getOutputStream();
            BufferedOutputStream bot=new BufferedOutputStream(out);
            while((length=buf.read(b))!=-1) {
                bot.write(b,0, b.length);
            }
        } catch (Exception  e) {
            // TODO Auto-generated catch block
            MFLogger.error("",e);

        }
    }
    @RequestMapping(value = "/getPdffile/{fileId}")
    @ResponseBody
    public void getPdffile(@PathVariable("fileId") String fileId, HttpServletRequest request, HttpServletResponse response) {
        InputStream fis =null;
        try {
            InputStream instream=this.docFileInfService.getFileThumbStream(fileId);

            response.setContentType("application/pdf".toLowerCase());

            OutputStream out = new BufferedOutputStream(response.getOutputStream());
//            byte[] b = new byte[1024];
//            while ((instream.read(b)) != -1) {
//                out.write(b);
//            }
            fis = new BufferedInputStream(instream);
            byte[] buffer = new byte[1024 * 1024 * 4];
            int i = -1;
            while ((i = fis.read(buffer)) != -1) {
                out.write(buffer, 0, i);
            }
            out.flush();
            instream.close();
            out.close();

        } catch (Exception  e) {
            // TODO Auto-generated catch block
            MFLogger.error("",e);

        }finally {
            if (null !=fis){
                try {
                    fis.close();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }


        }
    }


    @RequestMapping("/getTxtContent/{fileId}")
    public R getTxtContent(@PathVariable("fileId") String fileId){
        List content = this.docFileInfService.getTxtContent(fileId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", content);
    }

    /**
     * 读取凭证TXT文件内容
     * @param id
     * @return
     */
    @RequestMapping("/getVoucherTxtContent/{id}")
    public R getVoucherTxtContent(@PathVariable("id") String id){
        List content = this.docFileInfService.getVoucherTxtContent(id);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", content);
    }

    /**
     * 读取凭证pdf
     * @param id
     * @param request
     * @param response
     */
    @RequestMapping(value = "/getVoucherPdffile/{id}")
    @ResponseBody
    public void getVoucherPdffile(@PathVariable("id") String id, HttpServletRequest request, HttpServletResponse response) {
        InputStream fis =null;
        try {
            InputStream instream=this.docFileInfService.getVoucherFileThumbStream(id);

            response.setContentType("application/pdf".toLowerCase());

            OutputStream out = new BufferedOutputStream(response.getOutputStream());
//            byte[] b = new byte[1024];
//            while ((instream.read(b)) != -1) {
//                out.write(b);
//            }
            fis = new BufferedInputStream(instream);
            byte[] buffer = new byte[1024 * 1024 * 4];
            int i = -1;
            while ((i = fis.read(buffer)) != -1) {
                out.write(buffer, 0, i);
            }
            out.flush();
            instream.close();
            out.close();

        } catch (Exception  e) {
            // TODO Auto-generated catch block
            MFLogger.error("",e);

        }finally {
            if (null !=fis){
                try {
                    fis.close();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }


        }
    }
    @RequestMapping("/getVoucherOfficeFileObj/{id}")
    public R getVoucherOfficeFileObj(@PathVariable("id") String id){
        JSONObject obj = this.docFileInfService.getVoucherOfficeFileObj(id);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", obj);
    }
    @RequestMapping("/getOfficeFileObj/{fileId}")
    public R getOfficeFileObj(@PathVariable("fileId") String fileId){
        JSONObject obj = this.docFileInfService.getOfficeObj(fileId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", obj);
    }
    @RequestMapping("/getFloderListByFileIds")
    public R getFloderListByFileIds(@RequestBody JSONObject parmJson){

        List<DocFileBizFiletypeConfigEntity> folderList = this.docFileInfService.getFloderRootListByFileIds(parmJson);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", folderList);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocFileInfEntity docFileInfEntity){
        this.docFileInfService.insert(docFileInfEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocFileInfEntity docFileInfEntity){
        this.docFileInfService.update(docFileInfEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{fileId}")
    public R findById(@PathVariable("fileId") String fileId){
        DocFileInfEntity docFileInfEntity = this.docFileInfService.findById(fileId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docFileInfEntity);
    }

    @RequestMapping("/deleteById/{fileId}")
    public R deleteById(@PathVariable("fileId") String fileId){
        DocFileInfEntity docFileInfEntity = this.docFileInfService.findById(fileId);
        this.docFileInfService.deleteById(fileId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS).put("delObj",docFileInfEntity);
    }

    @RequestMapping("/officeFileShowInit")
    public R officeFileShowInit(){
        JSONObject obj = this.docFileInfService.officeFileShowInit();
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", obj);
    }


    /**
     * 根据客户传入的参数获取要价列表 ，其中客户号、申请号、合同号、借据号至少传一个，
     * 当之传入客户时，仅查询和客户关联的id；当传入申请号时额外查询申请相关的资料；后面依次类推
     * @param docFileInfEntity
     * @return
     */
    @RequestMapping("/allFile")
    public R fingAllTypeAndList(@RequestBody DocFileInfEntity docFileInfEntity){

       FileShowVo fileShowVo = this.docFileInfService.findAllTypeAndList(docFileInfEntity);

        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", fileShowVo);
    }

    /**
     * 获取关联的要件信息
     * @param queryParms
     * @return
     */
    @RequestMapping("/relatedFileList")
    public  R  getCusFile(@RequestBody List<QueryParm> queryParms){
        FileShowVo fileShowVo  = docFileInfService.getRelatedFileList(queryParms);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", fileShowVo);
    }
    /**
     * 获取关联的要件信息
     * @param jsonObject
     * @return
     */
    @RequestMapping("/getArchiveFile")
    public  R  getArchiveFile(@RequestBody JSONObject jsonObject){
        FileShowVo fileShowVo  = docFileInfService.getArchiveFile(jsonObject);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", fileShowVo);
    }
    @RequestMapping("/treeData")
    public R getTreeData(@RequestBody List<QueryParm> queryParms){
        List<GroupVo> fileShowVo  = docFileInfService.getGroupTreeData(queryParms);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", fileShowVo);
    }

    @RequestMapping("/noTypeClassTreeData")
    public R getTreeDataNoTypeClass(@RequestBody List<QueryParm> queryParms){
        List<GroupVo> fileShowVo  = docFileInfService.getGroupTreeDataNoTypeClass(queryParms);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", fileShowVo);
    }

    @RequestMapping("/noTypeNoTreeData")
    public R getGroupTreeDataNoTypeNo(@RequestBody List<QueryParm> queryParms){
        List<GroupVo> fileShowVo  = docFileInfService.getGroupTreeDataNoTypeNo(queryParms);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", fileShowVo);
    }




}
