/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.components.file.entity.DocFileTypeEntity;
import cn.mftcc.doc.components.file.entity.DocScFiletypeConfigEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 文件类型表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 17:19:12
 */
public interface DocFileTypeService {

    IPage<DocFileTypeEntity> findByPage(DocFileTypeEntity docFileTypeEntity) throws ServiceException;

    void insert(DocFileTypeEntity docFileTypeEntity) throws ServiceException;

    JSONObject fileTypeGroupList(DocScFiletypeConfigEntity docScFiletypeConfigEntity)throws ServiceException;

    List<DocFileTypeEntity> findList(DocFileTypeEntity docFileTypeEntity) throws ServiceException;

    void update(DocFileTypeEntity docFileTypeEntity) throws ServiceException;

    DocFileTypeEntity findById(String typeNo) throws ServiceException;

    JSONArray fileTypeArray()throws ServiceException;

    void deleteById(String typeNo) throws ServiceException;

    /**
     * 获取所有子类的父级类别
     * @return
     * @throws ServiceException
     */
     JSONObject getParentTypeInf()throws ServiceException;
    List<DocFileTypeEntity> findRightList() throws ServiceException;
}

