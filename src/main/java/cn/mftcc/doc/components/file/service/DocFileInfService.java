/*
 * Copyright © 2020 北京微金时代科技有限公司 <EMAIL>
 */
package cn.mftcc.doc.components.file.service;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.components.file.entity.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 要件信息表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-05-10 15:15:44
 */
public interface DocFileInfService {

    IPage<DocFileInfEntity> findByPage(DocFileInfEntity docFileInfEntity) throws ServiceException;

    List<DocFileInfEntity> findFileInfList(DocFileInfEntity docFileInfEntity) throws ServiceException;

    InputStream getFileStream(String fileId) throws ServiceException, IOException;

    JSONObject getOfficeObj(String fileId)throws  ServiceException;

    InputStream getFileThumbStream(String fileId)throws ServiceException;

    void insert(DocFileInfEntity docFileInfEntity) throws ServiceException;

    void update(DocFileInfEntity docFileInfEntity) throws ServiceException;

    void updateByBizNo(DocFileInfEntity docFileInfEntity) throws ServiceException;

    DocFileInfEntity findById(String fileId) throws ServiceException;

    String readFileBase64(String fileId)throws  ServiceException;

    String ordinaryReadFileBase64(String fileId)throws  ServiceException;

    List<String> getTxtContent(String fileId)throws  ServiceException;

    void deleteById(String fileId) throws ServiceException;

    List<DocFileBizFiletypeConfigEntity> getFloderRootListByFileIds(JSONObject parmJson)throws  ServiceException;

    List<DocFileBizFiletypeConfigEntity> getFloderListByFileIds( String[]fileIdAry)throws  ServiceException;

    List<DocFileInfEntity> getFileListByFileIds(String fileIdAry[],String typeNo)throws  ServiceException;
/**
 *  读取orc信息
 * @param parmMap{
 *     fileId：要件流水号
 *     busReqNo：业务系统请求编号(可不传)
 *     category：识别的影像类型 id_card-身份证 bank_card-银行卡影像 vehicle_invoice-发票（机动车统一发票，增值税专用发票、普票、电子发票）business_license-营业执照
 *     side：需有有正反面参数时使用，可用值 front/back
 * }
 * */
    JSONObject readOCRInfo(Map<String ,Object> parmMap)throws  ServiceException;
    JSONObject copyFile(Map<String ,String>parmMap)throws ServiceException;
    JSONObject copyFileByNodes(List<Map<String ,String>>parmMapList)throws ServiceException;

    JSONObject copyFileByPrdUniqueVal(Map<String ,String>parmMap)throws ServiceException;

    JSONObject copyFileByParam(Map<String ,String>parmMap)throws ServiceException;

    JSONObject deleteBatch(Map<String ,String>parmMap)throws ServiceException;

    JSONObject deleteBatchByNodes(List<Map<String ,String>>parmMapList)throws ServiceException;

    JSONObject ifHasFiles(Map<String ,String>parmMap)throws ServiceException;

    JSONArray getFileList(Map<String ,String>parmMap)throws ServiceException;

    JSONArray getFileIdArray(List<Map<String ,String>> parmList)throws ServiceException;

    JSONArray getFileListArray(List<Map<String ,String>> parmList)throws ServiceException;

    JSONObject copyFileToOtherFolder(JSONObject parmJson)throws ServiceException;

    JSONObject copyFileToOtherFolders(JSONArray parmArray)throws ServiceException;
    JSONObject copyFileToOtherFolderIncludeFolder(JSONArray parmArray)throws ServiceException;
    /**
     * 批量插入文件
     * @param parmMapList
     * @return com.alibaba.fastjson.JSONObject
     */
    JSONObject insertBatchFile(List<JSONObject> parmMapList)throws ServiceException;

    JSONObject ifEachTypeHasFileOfNode(Map<String ,String>parmMap)throws ServiceException;
    /** 获取某一业务某个类型下是否有要件
     * @param parmObj{
     *      bizNo:关联业务主键
     *      prdUniqueVal:产品唯一标识
     *      flowNo:流程标识
     *      nodeNo:节点标识
     *      typeNo:要件类型标识
     * @return
     * @throws ServiceException
     */
      JSONArray getAllFilesByNode(JSONObject parmObj)throws  ServiceException;

    /**
     * office展示组件初始化
     * @param
     * @return com.alibaba.fastjson.JSONObject
     */
    JSONObject officeFileShowInit();


    /**
     *
      * @param docFileBizFiletypeConfigEntity
     * @return
     */
    FileShowVo findAllTypeAndList(DocFileInfEntity  docFileInfEntity );

    /**
     * 获取关联的要件信息
     * @param queryParm
     * @return
     */
    FileShowVo getRelatedFileList(List<QueryParm> queryParms);

    /**
     * 根据条件查询关联的要件信息
     * @param queryParms
     * @return
     */
    FileShowVo getArchiveFile(JSONObject jsonObject);
    /**
     * 功能：获取要件树形结构数据
     * @param queryParms
     * @return
     */
    List<GroupVo> getGroupTreeData(List<QueryParm> queryParms);

    List<GroupVo>  getGroupTreeDataNoTypeClass(List<QueryParm> queryParms);

    /**
     * 功能：
     * @param queryParms
     * @return
     */
    List<FileClassVo> getClassTreeData(List<QueryParm> queryParms);

    List<GroupVo> getGroupTreeDataNoTypeNo(List<QueryParm> queryParms);

    JSONObject getLastFileOfOneType(JSONObject parmJson)throws  ServiceException;

    JSONArray getFilesBase64Str(JSONObject parm)throws ServiceException;

    JSONObject  addFileByBase64(JSONObject fileInf) throws ServiceException, IOException;

    JSONObject moveFilesToOtherFolder(JSONObject parmJson)throws ServiceException;

    void deleteBy(DocFileInfEntity docFileInfEntity)throws ServiceException;

    void copyAssureFile(JSONObject jsonObject) throws Exception;

    void copyAssureFileInexistence(JSONObject jsonObject) throws Exception;

    /**
     *  根据id批量删除
     * @param fileIds
     * @throws ServiceException
     */
    void deleteByIds(List<String> fileIds) throws ServiceException;

    void deleteByBizNo(String bizNo) throws ServiceException;

    String getDocFileIfType(JSONObject jsonObject);

    JSONArray getDocIdFileByCusId(String cusId)throws Exception;

    JSONArray getDocIdFileByApplyId(String applyId)throws Exception;

    void uploadFileForZd(String fileName, String savePathStr, String registerId)throws Exception;

    String ordinaryReadFileBase64ForCompress(String fileId)throws Exception;

    JSONArray getDocIdFileSocByCusId(String cusId)throws Exception;

    /**
     *  根据文件名称查询文件
     * @param fileNames
     * @return
     * @throws ServiceException
     */
    public List<DocFileInfEntity> findFileInfListByFileName(List<String> fileNames) throws ServiceException ;

    List<DocFileInfEntity> findFilesInfoByBizNo(List<String> bizNos)throws ServiceException;

    InputStream getFileStreamOss(String id);

    List getVoucherTxtContent(String id);

    JSONObject getVoucherOfficeFileObj(String id);

    InputStream getVoucherFileThumbStream(String id);

    List<DocFileInfEntity> findFileInfListByPactFP(String pactNo);
}

