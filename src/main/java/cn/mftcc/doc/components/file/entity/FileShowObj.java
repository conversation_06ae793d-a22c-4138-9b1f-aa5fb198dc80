/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;


public class FileShowObj  {

    private String  scId;//唯一流水标识
    private String  fileType;//1-目录 2-word 3-excel 4-pdf 5-图片 6-压缩包 7-txt 8-视频 9-音频 10-其他。
    private String  typeNo;// 要件目录编号
    private String  className;//前台展示样式名称
    private String  imgPath;//列表展示下图片路径
    private String  typeName;//要件目录名称
    private String imgThumbUrl;//图片缩略图路径
    private String fileShowUrl;//要件的原始路径
    private String  fileShortName;//要件名称省略

    public String getIfMustUpload() {
        return ifMustUpload;
    }

    public void setIfMustUpload(String ifMustUpload) {
        this.ifMustUpload = ifMustUpload;
    }

    private String  ifMustUpload;//是否必须上传 1-是 0-否
    private String  createTime;//创建时间
    private String  createUserName;//上传人
    private String  fileSize;//文件大小


    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    private String[]imgUrlAry;//图片要件的路径数组

    public String[] getImgUrlAry() {
        return imgUrlAry;
    }

    public void setImgUrlAry(String[] imgUrlAry) {
        this.imgUrlAry = imgUrlAry;
    }

    public String getImgThumbUrl() {
        return imgThumbUrl;
    }

    public void setImgThumbUrl(String imgThumbUrl) {
        this.imgThumbUrl = imgThumbUrl;
    }

    public String getFileShowUrl() {
        return fileShowUrl;
    }

    public void setFileShowUrl(String fileShowUrl) {
        this.fileShowUrl = fileShowUrl;
    }

    public String getFileShortName() {
        return fileShortName;
    }

    public void setFileShortName(String fileShortName) {
        this.fileShortName = fileShortName;
    }



    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }



    public String getScId() {
        return scId;
    }

    public void setScId(String scId) {
        this.scId = scId;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getTypeNo() {
        return typeNo;
    }

    public void setTypeNo(String typeNo) {
        this.typeNo = typeNo;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }
}
