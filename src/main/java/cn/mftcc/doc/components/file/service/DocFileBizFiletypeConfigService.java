/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.components.file.entity.DocFileBizFiletypeConfigEntity;
import cn.mftcc.doc.components.file.entity.DocFileInfBase64;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.entity.FileShowObj;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.JsonObject;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 业务数据的要件类型配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-05-08 14:31:02
 */
public interface DocFileBizFiletypeConfigService {

    IPage<DocFileBizFiletypeConfigEntity> findByPage(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException;

    List<DocFileBizFiletypeConfigEntity> findList(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException;
    List<DocFileBizFiletypeConfigEntity> findTypeAndList(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException;

    List<DocFileBizFiletypeConfigEntity> findTypeAndListMix(JSONObject jsonObject) throws ServiceException;


    void downloadArchiveFileZip(JSONArray fileObjs,String zipName,HttpServletResponse response) throws ServiceException;

    List<FileShowObj> getFileShowList(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException ;


    void insert(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException;

    void insertBatch(List<DocFileBizFiletypeConfigEntity> docFileBizFiletypeConfigEntitys) throws ServiceException;

    void update(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity) throws ServiceException;

    DocFileBizFiletypeConfigEntity findById(String scId) throws ServiceException;

    void deleteById(String scId) throws ServiceException;

    JSONObject initBizFileTypeConfig(String initParm)throws ServiceException;

    JSONObject upLoadFile(DocFileBizFiletypeConfigEntity folderEntity, MultipartFile file, String appName, String uploadParm)throws  ServiceException;

    JSONArray getRootListByTypeBean(DocFileBizFiletypeConfigEntity curBean)throws  ServiceException;

    void   addOneFloder(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity)throws ServiceException;

     void downloadAllFile(HttpServletResponse response, JSONObject ajaxData)throws   ServiceException;

     void downloadAllSltFileZip(HttpServletResponse response, JSONObject ajaxData)throws   ServiceException;

    JSONObject deleteSelectFile(JSONObject ajaxData)throws   ServiceException;

    void downloadAllFilesZip(HttpServletResponse response, JSONObject ajaxData)throws   ServiceException;


    void downloadTmpZip(HttpServletResponse response, String fileName)throws   ServiceException;

    Map<String,Object>  downloadFilesZip(JSONObject ajaxData)throws ServiceException;

    void downloadTemplateAllFileZip(HttpServletResponse response, JSONObject ajaxData);

    void  fileAutoInitFun(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity)throws ServiceException;

    void  fileAutoInitFunMix(JSONObject jsonObject)throws ServiceException;

    JSONObject getUploadLimitParam()throws ServiceException;

    String  ifFileConfig(String initParm) throws ServiceException;

    List<FileShowObj> findListByFileIdAry(JSONObject parmJson) throws ServiceException;

    JSONObject checkBizFileStatus( Map<String ,String> parmMap)throws  ServiceException;

    JSONObject checkBizFileStatusAccess( Map<String ,String> parmMap)throws  ServiceException;

    JSONObject addDocFromObs(JSONObject parmJson)throws ServiceException;

    /**
     * 根据obsId获取obs展示路径
     * @param obsId
     * @return
     * @throws ServiceException
     */
     String  getShowUrlByObsid(String obsId)throws ServiceException;

    JSONArray findTypeAndList(JSONObject jsonObject)throws  ServiceException;

    JSONArray findTypeScList(JSONObject jsonObject)throws  ServiceException;

    void deleteByJson(JSONObject delJson) throws ServiceException;
    JSONObject upLoadFileBase64Multi(DocFileBizFiletypeConfigEntity folderEntity, List<DocFileInfBase64> docBase64ObjList, String appName, String uploadParm)throws  ServiceException;

    void deleteByBizNo(String bizNo)throws ServiceException;

    DocFileBizFiletypeConfigEntity selectOne(DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity)throws ServiceException;

    JSONObject  getFileConfigByBizId(JSONObject parmJson)throws ServiceException;

    JSONObject  getFileByBizNoList(JSONObject parmJson)throws ServiceException;

    JSONObject getFileListByBizNo(JSONObject parmJson)throws  ServiceException;

    InputStream compressImgToSize(MultipartFile multiFile, double size, double accuracy)throws Exception;

    /**
     * 打包已经生成的附件
     * @param newFileName
     * @param batchNo
     * @return
     * @throws ServiceException
     */
    void  downloadVoucherFilesZip(String newFileName,String batchNo)throws ServiceException;

    /**
     *  生成合同模板
     * @param parmJson
     * @throws ServiceException
     */
    void  renderTemplate(JSONObject parmJson)throws ServiceException;

    /**
     *  生成合同模板返回保存路径
     * @param parmJson
     * @throws ServiceException
     */
    JSONObject  renderTemplateJSONObject(JSONObject parmJson)throws ServiceException;

    /**
     *  生成上传的附件到指定目录
     * @param batchNo
     * @param pactNo
     * @param fileInfList
     */
    String generateFile(String opNo,String batchNo, String pactNo, List<DocFileInfEntity> fileInfList) throws ServiceException;

    /**
     *  输出信息到文件
     * @param opNo
     * @param batchNo
     * @param infos
     * @throws ServiceException
     */
    void writInfosToFile(String opNo,String batchNo, List<String> infos) throws ServiceException;

    void renderTemplateForExcel(JSONObject parmTempJson)throws ServiceException;


    /**
     *  下载模板
     * @param ajaxData
     * @return
     * @throws ServiceException
     */
    Map<String,Object>  downloadFilesZipV1(JSONObject ajaxData)throws ServiceException;
}

