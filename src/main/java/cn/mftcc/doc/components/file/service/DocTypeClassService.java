/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.service;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.components.file.entity.DocTypeClassEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 要件类型大类表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-12-06 18:41:55
 */
public interface DocTypeClassService {

    IPage<DocTypeClassEntity> findByPage(DocTypeClassEntity docTypeClassEntity) throws ServiceException;

    void insert(DocTypeClassEntity docTypeClassEntity) throws ServiceException;

    void update(DocTypeClassEntity docTypeClassEntity) throws ServiceException;

    DocTypeClassEntity findById(String typeId) throws ServiceException;

    DocTypeClassEntity findByTypeNo(String typeNo) throws ServiceException;

    void deleteById(String typeId) throws ServiceException;

    List<DocTypeClassEntity> getList(DocTypeClassEntity docTypeClassEntity) throws ServiceException;


}

