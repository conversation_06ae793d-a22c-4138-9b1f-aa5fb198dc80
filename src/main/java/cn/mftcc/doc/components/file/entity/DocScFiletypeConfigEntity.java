/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 场景的要件类型配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-29 11:32:22
 */
@Data
@TableName("doc_sc_filetype_config")
public class DocScFiletypeConfigEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String scId;
	/**
	 * 产品唯一标识
	 */
	private String prdUniqueVal;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;
	/**
	 * 要件类型编号
	 */
	private String typeNo;
	/**
	 * 要件类型名称
	 */
	private String typeName;
	/**
	 * 要件类型编号
	 */
	private String parentTypeNo;
	/**
	 * 是否加密 1-是 0-否
	 */
	private String isEncrypt;
	/**
	 * 是否必读  1-是 0-否
	 */
	private String ifMustRead;
	/**
	 * 是否必须上传1-是0-否
	 */
	private String ifUpload;

	/**
	 * 允许上传要件的个数
	 */
	private int uploadFileLimit;

	/**
	 * 产品版本号
	 */
	private Integer prdVerNo;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 法人机构号
	 */
	private  String  corpId;

}
