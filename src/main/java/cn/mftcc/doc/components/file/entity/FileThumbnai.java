/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.file.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 要件需要缩略图实体，用于支持前端数据的展示
 */
@Data
public class FileThumbnai {

    private String fileTypeName;
    // '这是提示信息',
    private String fileTooltip;
    // 是否必填
    private Boolean require;
    // 是否支持一次上传多张图片
    private Boolean  multiple;
    // 上传文件类型限制
    private String  uploadFiletypeLimit;
    // 是否允许上传
    private Boolean uploadFlag;
    // 已经上传的要件信息
    List<Map<String,String>> fileList;

}
