/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagSetEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateTagSetService;

/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-15 19:02:36
 */
@RestController
@RequestMapping("mould/docTemplateTagSet")
public class DocTemplateTagSetController {

    @Autowired
    private DocTemplateTagSetService docTemplateTagSetService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocTemplateTagSetEntity docTemplateTagSetEntity) {
        IPage<DocTemplateTagSetEntity> list = this.docTemplateTagSetService.findByPage(docTemplateTagSetEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocTemplateTagSetEntity docTemplateTagSetEntity){
        this.docTemplateTagSetService.insert(docTemplateTagSetEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocTemplateTagSetEntity docTemplateTagSetEntity){
        this.docTemplateTagSetService.update(docTemplateTagSetEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{serialNo}")
    public R findById(@PathVariable("serialNo") String serialNo){
        DocTemplateTagSetEntity docTemplateTagSetEntity = this.docTemplateTagSetService.findById(serialNo);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docTemplateTagSetEntity);
    }

    @RequestMapping("/deleteById/{serialNo}")
    public R deleteById(@PathVariable("serialNo") String serialNo){
        this.docTemplateTagSetService.deleteById(serialNo);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}