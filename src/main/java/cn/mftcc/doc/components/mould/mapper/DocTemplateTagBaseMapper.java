/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.mapper;

import cn.mftcc.doc.components.mould.entity.DocTemplateTagBaseEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标签表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-03-27 10:39:22
 */
@Mapper
public interface DocTemplateTagBaseMapper extends BaseMapper<DocTemplateTagBaseEntity> {
	
}
