/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.mapper;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.rmi.ServerException;
import java.util.List;

/**
 * 业务模板关联表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-27 14:18:47
 */
@Mapper
public interface DocBizTemplateMapper extends BaseMapper<DocBizTemplateEntity> {
    /**
     * 查询待完成oss迁移的合同模板文件
     * @return
     * @throws ServiceException
     */
    List<DocBizTemplateEntity> findToDoRemoveFileInfo()throws ServiceException;

    List<DocFileInfEntity> getDocIdFileByContractIdAndTypeNo(String constractRelatedId) throws ServerException;

}
