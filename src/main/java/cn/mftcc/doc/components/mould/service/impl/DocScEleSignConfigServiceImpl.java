/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocScEleSignConfigEntity;
import cn.mftcc.doc.components.mould.mapper.DocScEleSignConfigMapper;
import cn.mftcc.doc.components.mould.service.DocScEleSignConfigService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

/**
 * 电子签章配置流程节点表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-28 17:37:58
 */
@Service("docScEleSignConfigService")
@Transactional(rollbackFor = Exception.class)
public class DocScEleSignConfigServiceImpl implements DocScEleSignConfigService {

    @Autowired
    private DocScEleSignConfigMapper docScEleSignConfigMapper;
    @Autowired
    private MapperUtil mapperUtil;

    @Override
    public IPage<DocScEleSignConfigEntity> findByPage(DocScEleSignConfigEntity docScEleSignConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocScEleSignConfigEntity> page = new Page<>();
            page.setCurrent(docScEleSignConfigEntity.getPageNo());
            page.setSize(docScEleSignConfigEntity.getPageSize());
            QueryWrapper<DocScEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docScEleSignConfigEntity);
            return docScEleSignConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docScEleSignConfigEntity.getId(),e);
        }
    }

    @Override
    public void insert(DocScEleSignConfigEntity docScEleSignConfigEntity) throws ServiceException {
        try{
            docScEleSignConfigMapper.insert(docScEleSignConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docScEleSignConfigEntity.getId(),e);
        }
    }

    @Override
    public void update(DocScEleSignConfigEntity docScEleSignConfigEntity) throws ServiceException {
        try{
            docScEleSignConfigMapper.updateById(docScEleSignConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docScEleSignConfigEntity.getId(),e);
        }
    }

    @Override
    public DocScEleSignConfigEntity findById(String id) throws ServiceException {
        try{
            return docScEleSignConfigMapper.selectById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,id,e);
        }
    }

    @Override
    public void deleteById(String id) throws ServiceException {
        try{

            docScEleSignConfigMapper.deleteById(id);

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,id,e);
        }
    }
    //删除对应的电签配置
    @Override
    public void deleteByTemplateId(String templateId) throws ServiceException {
        try{
            DocScEleSignConfigEntity delBean=new DocScEleSignConfigEntity();
            delBean.setTemplateId(templateId);
            QueryWrapper<DocScEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("template_id",templateId);

            docScEleSignConfigMapper.delete(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,templateId,e);
        }
    }

}