/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import cn.hutool.core.util.RandomUtil;
import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;
import cn.mftcc.doc.components.mould.service.DocBizTemplateService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 业务模板关联表
 *
 * <AUTHOR>
 * @email
 * @date 2021-04-27 14:18:47
 */
@RestController
@RequestMapping("mould/docBizTemplate")
public class DocBizTemplateController {

    @Autowired
    private DocBizTemplateService docBizTemplateService;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;

    @Value("${mftcc.template.officeServer-path:}")
    private  String officeServerPath;
    @Value("${mftcc.file.doc-office-open-url:}")
    private  String docOfficeOpenUrl;
    @Value("${mftcc.template.server:}")
    private String gateWayAddr;
    @Value("${spring.application.name:}")
    private String serverName;

    public static final String HEADER_NAME_DEFAULT = "Content-Disposition";
    private static final String CONTENT_TYPE_DEF = "application/x-download";
    private static final String HEADER_VALUE_DEFAULT = "attchement;filename=";
    /**
     * office是否在线预览标识 0-否 1-是
     */
    @Value("${mftcc.file.doc-office-view:}")
    private  String docOfficeView;


    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocBizTemplateEntity docBizTemplateEntity) {
        IPage<DocBizTemplateEntity> list = this.docBizTemplateService.findByPage(docBizTemplateEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocBizTemplateEntity docBizTemplateEntity){
        this.docBizTemplateService.insert(docBizTemplateEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }
//

    /**
     * 获取业务的模板列表
     * @param docBizTemplateEntity
     * @return
     */
    @RequestMapping("/getBizTemplateList")
    public R getBizTemplateList(@RequestBody DocBizTemplateEntity docBizTemplateEntity ) {

        DocBizTemplateEntity docBizTemplateEntity1 = new DocBizTemplateEntity();
        docBizTemplateEntity1.setBizNo(docBizTemplateEntity.getBizNo());
        docBizTemplateEntity1.setFlowNo(docBizTemplateEntity.getFlowNo());
        docBizTemplateEntity1.setNodeNo(docBizTemplateEntity.getNodeNo());
        List<DocBizTemplateEntity> list = this.docBizTemplateService.findList(docBizTemplateEntity1);
        if(list.size()==0){
            //开始初始化
            JSONObject parmJson=new JSONObject();
            parmJson.put("prdUniqueVal",docBizTemplateEntity.getPrdUniqueVal());
            parmJson.put("flowNo",docBizTemplateEntity.getFlowNo());
            parmJson.put("nodeNo",docBizTemplateEntity.getNodeNo());
            parmJson.put("bizNo",docBizTemplateEntity.getBizNo());
            parmJson.put("btId",docBizTemplateEntity.getBtId());

            parmJson.put("cusRelatedId",docBizTemplateEntity.getCusRelatedId());
            parmJson.put("busRelatedId",docBizTemplateEntity.getBusRelatedId());
            parmJson.put("applyRelatedId",docBizTemplateEntity.getApplyRelatedId());
            parmJson.put("constractRelatedId",docBizTemplateEntity.getConstractRelatedId());
            parmJson.put("finRelatedId",docBizTemplateEntity.getFinRelatedId());

            parmJson.put("parmList",JSONObject.parseObject(docBizTemplateEntity.getParmList()));
            this.docBizTemplateService.initTemplate(parmJson.toJSONString());
            list = this.docBizTemplateService.findList(docBizTemplateEntity);
        }
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }
    /**
     * 获取业务的模板列表
     * @param docBizTemplateEntity
     * @return
     */
    @RequestMapping("/getBizTemplateShowList")
    public R getBizTemplateShowList(@RequestBody DocBizTemplateEntity docBizTemplateEntity ) {

        List<DocBizTemplateEntity> list = this.docBizTemplateService.findList(docBizTemplateEntity);

        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    /**
     * 展示多个地方的模板
     * @param parmArray [{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * }]
     * @return
     * @throws ServiceException
     */
    @RequestMapping("/findMultiMouldList")
    public R findMultiMouldList(@RequestBody JSONArray parmArray){
        List<cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity> list = this.docBizTemplateService.findMultiMouldList(parmArray);

        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }





    @RequestMapping("/update")
    public R update(@RequestBody DocBizTemplateEntity docBizTemplateEntity){
        this.docBizTemplateService.update(docBizTemplateEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{btId}")
    public R findById(@PathVariable("btId") String btId){
        DocBizTemplateEntity docBizTemplateEntity = this.docBizTemplateService.findById(btId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docBizTemplateEntity);
    }

    @RequestMapping("/findBizTemplateById/{btId}")
    public R findBizTemplateById(@PathVariable("btId") String btId){
        DocBizTemplateEntity docBizTemplateEntity = this.docBizTemplateService.findById(btId);
        JSONObject result = new JSONObject();
        if (docBizTemplateEntity != null) {
            result = (JSONObject)JSONObject.toJSON(docBizTemplateEntity);
            if (StringUtils.isNotEmpty(docBizTemplateEntity.getDocObsId())) {
                String docKey= RandomUtil.randomString(20);

                String obsId = docBizTemplateEntity.getDocObsId();
                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);

                String tmpAry[] = obsId.split("\\.");
                //后缀名
                String suffix = tmpAry[tmpAry.length-1];

                result.put("gateWayAddr",gateWayAddr);
                result.put("serverName",serverName);
                result.put("officeDocKey",docKey);
                result.put("officeUrl",filePath);
                result.put("officeKey",btId);
                result.put("officeSuffix",suffix);
                result.put("officeServie",officeServerPath);

                result.put("docOfficeView",docOfficeView);
            }
        }


        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", result);
    }

    @RequestMapping(value = "/getTemplateFileStream/{btId}")
    @ResponseBody
    public void getStreamData(@PathVariable("btId") String btId, HttpServletResponse response) {
        try {
            DocBizTemplateEntity tmpObj = this.docBizTemplateService.findById(btId);
            if(tmpObj == null){
                return;
            }
            // 设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            String filePath = tmpObj.getSavePath() + tmpObj.getSaveFileName();
            File file = new File(filePath);
            InputStream inputStream = new FileInputStream(filePath);
            String tmpAry[]=filePath.split("\\.");

            String  suffix=tmpAry[tmpAry.length-1];//后缀名
            response.reset();
            response.setContentType(CONTENT_TYPE_DEF);
            if(StringUtils.equals(suffix,"pdf")){
                response.setContentType("application/pdf");
            }
            response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
            response.addHeader("Content-Length", "" + file.length());
            response.addHeader("file-name*", URLEncoder.encode(tmpObj.getTemplateName()+"."+suffix, "UTF-8"));
            byte[] b=new byte[1024];
            BufferedInputStream buf=new BufferedInputStream(inputStream);
            ServletOutputStream out=response.getOutputStream();
            BufferedOutputStream bot=new BufferedOutputStream(out);

            int readLength;
            while ((readLength = buf.read(b)) != -1) {
                bot.write(b, 0, readLength);
            }
            bot.flush();
            bot.close();
        } catch (Exception  e) {
            // TODO Auto-generated catch block
            MFLogger.error("",e);

        }
    }

    @RequestMapping("/deleteById/{btId}")
    public R deleteById(@PathVariable("btId") String btId){
        this.docBizTemplateService.deleteById(btId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }

    @RequestMapping("/checkBizTemplateStatus")
    public R checkBizTemplateStatus(@RequestBody JSONObject parmJson)throws  ServiceException{
        Map<String ,String> parmMap = JSONObject.parseObject(parmJson.toJSONString(),Map.class);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data",docBizTemplateService.checkBizTemplateStatus(parmMap));
    }



    @RequestMapping(value = "/getTemplateFileStreamForName")
    @ResponseBody
    public void getStreamDataForName(@RequestBody JSONObject parmJson, HttpServletResponse response) {
        try {
            String btId=parmJson.getString("btId");
            String fileName=parmJson.getString("fileName");
            DocBizTemplateEntity tmpObj = this.docBizTemplateService.findById(btId);
            if(tmpObj == null){
                return;
            }
            // 设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            String filePath = tmpObj.getSavePath() + tmpObj.getSaveFileName();
            File file = new File(filePath);
            InputStream inputStream = new FileInputStream(filePath);
            String tmpAry[]=filePath.split("\\.");

            String  suffix=tmpAry[tmpAry.length-1];//后缀名
            response.reset();
            response.setContentType(CONTENT_TYPE_DEF);
            if(StringUtils.equals(suffix,"pdf")){
                response.setContentType("application/pdf");
            }
            response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
            response.addHeader("Content-Length", "" + file.length());
            response.addHeader("file-name*", URLEncoder.encode(fileName+tmpObj.getTemplateName()+"."+suffix, "UTF-8"));
            byte[] b=new byte[1024];
            BufferedInputStream buf=new BufferedInputStream(inputStream);
            ServletOutputStream out=response.getOutputStream();
            BufferedOutputStream bot=new BufferedOutputStream(out);

            int readLength;
            while ((readLength = buf.read(b)) != -1) {
                bot.write(b, 0, readLength);
            }
            bot.flush();
            bot.close();
        } catch (Exception  e) {
            // TODO Auto-generated catch block
            MFLogger.error("",e);

        }
    }
    @RequestMapping("/findPactFileByPage")
    public R findPactFileByPage(@RequestBody DocBizTemplateEntity docBizTemplateEntity) {
        IPage<DocBizTemplateEntity> list = this.docBizTemplateService.findPactFileByPage(docBizTemplateEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

}