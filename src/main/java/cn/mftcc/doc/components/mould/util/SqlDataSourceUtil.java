/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.util;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.common.multids.MultiDataSourceManage;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagBaseEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;

@Component
public class SqlDataSourceUtil {
    @Value("${mftcc.template.tagval-query-way:}")
    private String  tagValQueryWay;
    @Autowired
    private MultiDataSourceManage multiDataSourceManage;

    /**
     * 获取查询sql的结果集
     * @param sql
     * @param dataBase
     * @param paramAry
     * @return
     * @throws ServiceException
     */
    public JSONArray getQueryResult(String sql, String dataBase, String paramAry[])throws  ServiceException{
        JSONArray result=null;
        if(tagValQueryWay.equals("multids")){

            result= multiDataSourceManage.getQueryResult(sql,dataBase,paramAry);
        }else{
            if(tagValQueryWay.equals("feign")){

            }
        }
        return  result;
    }
    /**
     * 获取查询sql的结果实体
     * @param sql
     * @param dataBase
     * @param paramAry
     * @return
     * @throws ServiceException
     */
    public JSONObject getQueryObject(String sql, String dataBase, String paramAry[])throws  ServiceException{
        JSONObject result=null;
        if(tagValQueryWay.equals("multids")){

            result= multiDataSourceManage.getQueryObject(sql,dataBase,paramAry);
        }else{
            if(tagValQueryWay.equals("feign")){

            }
        }
        return  result;
    }


}
