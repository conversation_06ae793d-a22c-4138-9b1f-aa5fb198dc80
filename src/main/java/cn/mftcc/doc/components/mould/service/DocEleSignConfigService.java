/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocEleSignConfigEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 电子签章配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-27 11:24:48
 */
public interface DocEleSignConfigService {

    IPage<DocEleSignConfigEntity> findByPage(DocEleSignConfigEntity docEleSignConfigEntity) throws ServiceException;

    List<DocEleSignConfigEntity> findListByObj(DocEleSignConfigEntity docEleSignConfigEntity) throws ServiceException;

    void insert(DocEleSignConfigEntity docEleSignConfigEntity) throws ServiceException;

    void update(DocEleSignConfigEntity docEleSignConfigEntity) throws ServiceException;

    DocEleSignConfigEntity findById(String id) throws ServiceException;

    void deleteById(String id) throws ServiceException;

    void deleteByTemplateId(String templateId) throws ServiceException;

    JSONArray findListByTemplateId(String templateId)throws ServiceException;
}

