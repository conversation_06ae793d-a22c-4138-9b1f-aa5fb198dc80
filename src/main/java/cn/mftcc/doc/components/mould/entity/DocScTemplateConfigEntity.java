/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 场景的模板配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-25 17:30:20
 */
@Data
@TableName("doc_sc_template_config")
public class DocScTemplateConfigEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String scId;



	/**
	 * 产品唯一标识
	 */
	private String prdUniqueVal;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;
	/**
	 * 模板的唯一标识
	 */
	private String templateId;
	/**
	 * 模板名称
	 */
	private String templateName;

	/**
	 * 多份模板feigin接口
	 */
	private String multiFeign;
	/**
	 * 多份模板feigin接口调用的方法
	 */
	private String multiFeignMethod;

	/**
	 * 多份模板参数列表
	 */
	private String parmList;

	/**
	 * 是否电签0-否; 1-是
	 */
	private String ifElectricSign;
	/**
	 * 是否用印0-否; 1-是
	 */
	private String useSealFlag;
	/**
	 * 是否可编辑 1-是 0-否
	 */
	private String canWrite;
	/**
	 * 是否必读  1-是 0-否
	 */
	private String ifMustRead;
	/**
	 * 是否必须填写1-是0-否
	 */
	private String ifMustWrite;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 是否生成pdf 0-否; 1-是
	 */
	private String  pdfFlag;
	/**
	 * 模板类型
	 */
	private String templateType;
	/**
	 * 模板格式1-word 2-excel 3-pdf
	 */
	@TableField(exist = false)
	private String templateSuffix;


	@ApiModelProperty(value = "是否可提供设备编号/车架号")
	private String pledgeInfoFlag;
}
