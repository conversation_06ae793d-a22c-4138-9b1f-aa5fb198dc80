/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocTemplateEsignerListEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 电子签章配置业务关联表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-11-25 15:47:45
 */
public interface DocTemplateEsignerListService {

    IPage<DocTemplateEsignerListEntity> findByPage(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException;

    IPage<DocTemplateEsignerListEntity> querySsqEsignPage(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException;

    IPage<DocTemplateEsignerListEntity> queryCusSsqEsignPage(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException;

    List<DocTemplateEsignerListEntity>  findCusSsqEsignList(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException;

    void insert(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException;

    void update(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException;

    DocTemplateEsignerListEntity findById(String id) throws ServiceException;
    List<DocTemplateEsignerListEntity> getEsignersList(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException;

    void deleteById(String id) throws ServiceException;

    void reNoticeSign(String bizNo) throws ServiceException;

    void deleteEsiger(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException;

    List<DocTemplateEsignerListEntity> batchDownloadEsignPactFile() throws Exception;

    void downloadEsignPactFile(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws Exception;

    List<DocTemplateEsignerListEntity> processCallbackData(DocTemplateEsignerListEntity docTemplateEsignerListEntity)throws Exception;

    JSONObject downSSQFile(DocTemplateEsignerListEntity docTemplateEsignerListEntity)throws  ServiceException;

    List<DocTemplateEsignerListEntity> findNonSignListByBizNo(DocTemplateEsignerListEntity query)throws  ServiceException;

    void deleteByBizNo(JSONObject jsonObject)throws  ServiceException;

    /**
     * 获取所有待迁移至oss的文件名称
     * @return
     * @throws ServiceException
     */
    void batchFileMove();
}

