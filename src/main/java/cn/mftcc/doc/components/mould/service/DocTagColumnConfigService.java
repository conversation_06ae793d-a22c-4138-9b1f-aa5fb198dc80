/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocTagColumnConfigEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 标签表格列对应字段配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-16 15:10:40
 */
public interface DocTagColumnConfigService {

    IPage<DocTagColumnConfigEntity> findByPage(DocTagColumnConfigEntity docTagColumnConfigEntity) throws ServiceException;

    void insert(DocTagColumnConfigEntity docTagColumnConfigEntity) throws ServiceException;

    void update(DocTagColumnConfigEntity docTagColumnConfigEntity) throws ServiceException;

    DocTagColumnConfigEntity findById(String configId) throws ServiceException;

    void deleteById(String configId) throws ServiceException;

    List<DocTagColumnConfigEntity> findList(DocTagColumnConfigEntity docTagColumnConfigEntity) throws ServiceException;
}

