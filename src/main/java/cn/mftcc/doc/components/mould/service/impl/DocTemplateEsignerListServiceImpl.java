/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.sysutils.RedisUtil;
import cn.mftcc.common.utils.DateUtil;
import cn.mftcc.common.utils.MapperUtil;
import cn.mftcc.common.utils.StringUtil;
import cn.mftcc.common.utils.UUIDUtil;
import cn.mftcc.cus.feign.dto.CusCustomerDTO;
import cn.mftcc.doc.common.constant.DocConstant;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.common.utils.OssConfig;
import cn.mftcc.doc.components.file.entity.DocFileBizFiletypeConfigEntity;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.file.service.DocFileInfService;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;
import cn.mftcc.doc.components.mould.entity.DocTemplateEsignerListEntity;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import cn.mftcc.doc.components.mould.mapper.DocTemplateEsignerListMapper;
import cn.mftcc.doc.components.mould.service.DocBizTemplateService;
import cn.mftcc.doc.components.mould.service.DocTemplateEsignerListService;
import cn.mftcc.doc.components.mould.service.DocTemplateModelService;
import cn.mftcc.doc.feign.client.CustomerFeignClient;
import cn.mftcc.doc.feign.client.ElinkApiFeignClient;
import cn.mftcc.doc.feign.client.EsignFeignClient;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;
import cn.mftcc.doc.feign.dto.DocTemplateEsignerListDto;
import cn.mftcc.elink.feign.dto.ssq.SSQDocmentsVo;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 电子签章配置业务关联表
 *
 * <AUTHOR>
 * @email
 * @date 2021-11-25 15:47:45
 */
@Service("docTemplateEsignerListService")
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DocTemplateEsignerListServiceImpl implements DocTemplateEsignerListService {

    @Autowired
    private DocTemplateEsignerListMapper docTemplateEsignerListMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private EsignFeignClient esignFeignClient;
    @Autowired
    private LeaseFeiginClient leaseFeiginClient;
    @Autowired
    private ElinkApiFeignClient elinkFeignClient;
    @Autowired
    private DocFileInfService docFileInfService;
    @Autowired
    private CustomerFeignClient customerFeignClient;
    @Autowired
    private DocTemplateModelService docTemplateModelService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Value("${mftcc.upload.esignPactSavePath:}")
    private String esignPactSavePath;
    @Value("${mftcc.upload.savePactPath}")
    private String savePactPath ;
    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private DocBizTemplateService docBizTemplateService;

    @Override
    public IPage<DocTemplateEsignerListEntity> findByPage(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTemplateEsignerListEntity> page = new Page<>();
            page.setCurrent(docTemplateEsignerListEntity.getPageNo());
            page.setSize(docTemplateEsignerListEntity.getPageSize());
            QueryWrapper<DocTemplateEsignerListEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docTemplateEsignerListEntity);
            return docTemplateEsignerListMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateEsignerListEntity.getId(),e);
        }
    }

    @Override
    public IPage<DocTemplateEsignerListEntity> querySsqEsignPage(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTemplateEsignerListEntity> page = new Page<>();
            page.setCurrent(docTemplateEsignerListEntity.getPageNo());
            page.setSize(docTemplateEsignerListEntity.getPageSize());
            QueryWrapper<DocTemplateEsignerListEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getBizNo()),DocTemplateEsignerListEntity::getBizNo,docTemplateEsignerListEntity.getBizNo())
                    .ne(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getEsignerType()),DocTemplateEsignerListEntity::getEsignerType,"13");
            queryWrapper.lambda().groupBy(DocTemplateEsignerListEntity::getEsigner,DocTemplateEsignerListEntity::getEsignerNo);
            mapperUtil.tableQuery(queryWrapper,docTemplateEsignerListEntity);
            return docTemplateEsignerListMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateEsignerListEntity.getId(),e);
        }
    }

    @Override
    public IPage<DocTemplateEsignerListEntity> queryCusSsqEsignPage(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTemplateEsignerListEntity> page = new Page<>();
            page.setCurrent(docTemplateEsignerListEntity.getPageNo());
            page.setSize(docTemplateEsignerListEntity.getPageSize());
            QueryWrapper<DocTemplateEsignerListEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getBizNo()),DocTemplateEsignerListEntity::getBizNo,docTemplateEsignerListEntity.getBizNo());
            queryWrapper.lambda().groupBy(DocTemplateEsignerListEntity::getTemplateName);
            mapperUtil.tableQuery(queryWrapper,docTemplateEsignerListEntity);
            return docTemplateEsignerListMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateEsignerListEntity.getId(),e);
        }
    }
    @Override
    public List<DocTemplateEsignerListEntity>  findCusSsqEsignList(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        try{
            QueryWrapper<DocTemplateEsignerListEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getBizNo()),DocTemplateEsignerListEntity::getBizNo,docTemplateEsignerListEntity.getBizNo()).
                    ne(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getEsignerType()),DocTemplateEsignerListEntity::getEsignerType,"13");
            queryWrapper.lambda().groupBy(DocTemplateEsignerListEntity::getRoleName);
            return docTemplateEsignerListMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateEsignerListEntity.getId(),e);
        }
    }

    @Override
    public void insert(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        try{
            docTemplateEsignerListMapper.insert(docTemplateEsignerListEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTemplateEsignerListEntity.getId(),e);
        }
    }

    @Override
    public void update(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        try{
            docTemplateEsignerListEntity.setUpdateTime(new Date());
            docTemplateEsignerListMapper.updateById(docTemplateEsignerListEntity);

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTemplateEsignerListEntity.getId(),e);
        }
    }

    @Override
    public DocTemplateEsignerListEntity findById(String id) throws ServiceException {
        try{
            return docTemplateEsignerListMapper.selectById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,id,e);
        }
    }

    @Override
    public List<DocTemplateEsignerListEntity> getEsignersList(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        QueryWrapper<DocTemplateEsignerListEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(docTemplateEsignerListEntity);
        return docTemplateEsignerListMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteById(String id) throws ServiceException {
        try{
            docTemplateEsignerListMapper.deleteById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,id,e);
        }
    }

    @Override
    public void reNoticeSign(String bizNo) throws ServiceException {
        try{
            esignFeignClient.reNoticeSign(bizNo);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,bizNo,e);
        }
    }

    @Override
    public void deleteEsiger(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        QueryWrapper<DocTemplateEsignerListEntity> queryWrapper=new QueryWrapper();
        queryWrapper.eq(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getTemplateBizId()),"template_biz_id",docTemplateEsignerListEntity.getTemplateBizId());
        queryWrapper.eq("biz_no",docTemplateEsignerListEntity.getBizNo());
        queryWrapper.eq(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getNodeNo()),"node_no",docTemplateEsignerListEntity.getNodeNo());
        docTemplateEsignerListMapper.delete(queryWrapper);
    }

    @Override
    public List<DocTemplateEsignerListEntity> batchDownloadEsignPactFile() throws Exception {
        QueryWrapper<DocTemplateEsignerListEntity> queryWrapper=new QueryWrapper();
        queryWrapper.eq("if_download","0");//是否已下载
        queryWrapper.eq("if_esigned","2");// 是否签约完成
        queryWrapper.orderByAsc("esign_time");
        return docTemplateEsignerListMapper.selectList(queryWrapper);
    }
    /**
     * todo 电签合同下载
     * @参数
     * @返回
     * @作者 仇招
     * @日期 2022/8/28 18:05
     **/
    @Override
    public void downloadEsignPactFile(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws Exception {
        String bizNo = docTemplateEsignerListEntity.getBizNo();
        String zjBizNo = docTemplateEsignerListEntity.getBizNo();
        if(StringUtil.isEmpty(bizNo)){
            MFLogger.info("业务编号为空" + JSONObject.toJSONString(docTemplateEsignerListEntity));
            return;
        }
        String flowNo = docTemplateEsignerListEntity.getFlowNo();
        String nodeNo = docTemplateEsignerListEntity.getNodeNo();

        if(StringUtil.isEmpty(flowNo)||StringUtil.isEmpty(nodeNo)){
            MFLogger.info("节点编号为空" + JSONObject.toJSONString(docTemplateEsignerListEntity));
            return;
        }
        String templateName = docTemplateEsignerListEntity.getTemplateName();
        if(StringUtil.isEmpty(templateName)){
            MFLogger.info("模板名称为空" + JSONObject.toJSONString(docTemplateEsignerListEntity));
            return;
        }
        String typeNo = docTemplateEsignerListEntity.getTypeNo();
        if(StringUtil.isEmpty(typeNo)){
            DocTemplateModelEntity docTemplateModelEntity = docTemplateModelService.findByNo(docTemplateEsignerListEntity.getTemplateNo());
            if(docTemplateModelEntity == null){
                MFLogger.info("关联要件编号为空" + JSONObject.toJSONString(docTemplateEsignerListEntity));
                return;
            }
            typeNo = docTemplateModelEntity.getTypeNo();
        }
        String docNo = bizNo + flowNo + typeNo;
        try {
            if (!redisUtil.getInsertDocFlag(docNo, redisUtil)) {
                MFLogger.info("唯一id为：" + docNo + "的模板正在处理中，请稍后重试");
                throw new Exception("唯一id为：" + docNo + "的模板正在处理中，请稍后重试");
            }
            String subContractId = docTemplateEsignerListEntity.getSubContractId();
            if (StringUtil.isEmpty(subContractId)) {
                MFLogger.info("合同模板信息为空" + JSONObject.toJSONString(docTemplateEsignerListEntity));
                return;
            }
            /*---- 下载子合同模板 ----*/
            JSONObject requestJson = new JSONObject();
            requestJson.put("callNo", "SSQ_DOWN_ZI");
            requestJson.put("busReqNo", UUIDUtil.getUUID());
            JSONObject paramData = new JSONObject();
            paramData.put("subContractId", subContractId);//子合同账号
            requestJson.put("paramData", paramData);
            MFLogger.info("下载子合同模板 开始：" + requestJson);
            String resultJson = elinkFeignClient.itoData(requestJson);
//        MFLogger.info("下载子合同模板 结束：" + resultJson);
            JSONObject js = JSONObject.parseObject(resultJson);
            String code = js.getString("code");
            if (!DocConstant.YES_NO_Y.equals(code)) {//elink调用是否成功
                MFLogger.info("下载子合同模板 失败！" + subContractId, js.getString("msg"));
                throw new Exception("下载子合同模板 失败" + subContractId);
            }
            // 获取三方返回数据
            Map<String, Object> dataMap = (Map<String, Object>) js.get("data");
            Map<String, Object> resultMap = (Map<String, Object>) dataMap.get("data");
            String base64FileData = StringUtil.valueOf(resultMap.get("base64FileData"));
            if (StringUtil.isEmpty(base64FileData)) {
                MFLogger.info("获取的base64FileData为空！");
                throw new Exception("获取的base64FileData为空 失败");
            }
            /**
             * 获取要保存的文件的目标路径
             */
            JSONObject jsonObject = leaseFeiginClient.getLeasePact(bizNo);
            JSONObject leaseDue = leaseFeiginClient.getLeaseDueId(bizNo);
            String signMethod = "";
            if (jsonObject == null) {
                if (leaseDue != null) {
                    bizNo = leaseDue.getString("pactId");
                    String categoryId = leaseDue.getString("categoryId");
                    if(!"10309".equals(categoryId)){
                        nodeNo = "contractPrint";
                    }
                    JSONObject pactJson = leaseFeiginClient.getLeasePact(leaseDue.getString("pactId"));
                    signMethod = pactJson.getString("signMethod");
                }
                log.info("-----------bizNo:{}-------------", bizNo);
                MFLogger.info("根据合同ID未找到合同信息" + JSONObject.toJSONString(docTemplateEsignerListEntity));
            }else{
                if(leaseDue!=null){
                    String categoryId = leaseDue.getString("categoryId");
                    if(!"10309".equals(categoryId)){
                        nodeNo = "contractPrint";
                    }
                }
            }

            DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();
            docFileBizFiletypeConfigEntity.setBizNo(bizNo);
            docFileBizFiletypeConfigEntity.setFlowNo(flowNo);
            docFileBizFiletypeConfigEntity.setTypeNo(typeNo);
            docFileBizFiletypeConfigEntity.setNodeNo(nodeNo);
            if(CommonConstant.APPLY_FLOW_SPLIT.equals(flowNo)&&"contractPrint".equals(nodeNo)){
                //项目类电签模板初始化在如下节点，下载时需要初始化成该节点
                docFileBizFiletypeConfigEntity.setFlowNo(CommonConstant.APPLY_FLOW_APPROVAL);
                docFileBizFiletypeConfigEntity.setNodeNo("UserTask_0j4wj9e");
            }
            if(CommonConstant.APPLY_FLOW_SPLIT.equals(flowNo)&&"putoutConfirm".equals(nodeNo)){
                //租金支付表要件下载初始化节点信息
                docFileBizFiletypeConfigEntity.setFlowNo(CommonConstant.APPLY_FLOW_SPLIT);
                docFileBizFiletypeConfigEntity.setNodeNo("contractSign");
            }
            MFLogger.info("查询参数" + JSONObject.toJSONString(docFileBizFiletypeConfigEntity));
            DocFileBizFiletypeConfigEntity bizFiletypeConfigEntity = this.docFileBizFiletypeConfigService.selectOne(docFileBizFiletypeConfigEntity);
            if (bizFiletypeConfigEntity == null) {
                MFLogger.info("获取要保存的文件的目标路径 失败！" + JSONObject.toJSONString(docFileBizFiletypeConfigEntity));
                throw new Exception("获取要保存的文件的目标路径 失败" + JSONObject.toJSONString(docFileBizFiletypeConfigEntity));
            }
            DocFileInfEntity docFileInfEntity = new DocFileInfEntity();
            docFileInfEntity.setBizNo(bizNo);
            docFileInfEntity.setTypeNo(typeNo);
            docFileInfEntity.setFlowNo(flowNo);
            docFileInfEntity.setFileName(templateName);
            if(null!= jsonObject && "10309".equals(jsonObject.getString("categoryId"))){
                docFileInfEntity.setFlowNo("contract_create_approval");
            }
            docFileInfService.deleteBy(docFileInfEntity);
            /**
             * 获取要保存的文件的目标路径
             */
            DocFileBizFiletypeConfigEntity folder = this.docFileBizFiletypeConfigService.findById(bizFiletypeConfigEntity.getScId());
            if (folder == null) {
                MFLogger.info("获取要保存的文件的目标路径 失败！" + JSONObject.toJSONString(bizFiletypeConfigEntity));
                throw new Exception("获取要保存的文件的目标路径 失败" + JSONObject.toJSONString(bizFiletypeConfigEntity));
            }
            double random = Math.random();
            String fileName = templateName + ".pdf";
            String esignPactSavePathStr = esignPactSavePath + File.separator + bizNo + File.separator;
            FileUtils.base64ToFile(base64FileData, fileName, esignPactSavePathStr);
            MultipartFile file = FileUtils.base64ToMultipartFile(fileName, esignPactSavePathStr);
            JSONObject leasePact = leaseFeiginClient.getLeasePact(bizNo);
            JSONObject uploadParmJson = new JSONObject();
            if (leasePact != null) {
                uploadParmJson.put("constractRelatedId", bizNo);
                uploadParmJson.put("busRelatedId", leasePact.getString("mainId"));
                uploadParmJson.put("cusRelatedId", leasePact.getString("cusId"));
                uploadParmJson.put("applyRelatedId", leasePact.getString("applyId"));
                uploadParmJson.put("corpId", leasePact.getString("corpId"));
            }
            if ("cus_apply".equals(nodeNo)) {//征信授权书，委托授权书
                CusCustomerDTO customerDTO = customerFeignClient.getCusCustomerByCusId(bizNo);
                uploadParmJson.put("corpId", customerDTO.getCorpId());
                uploadParmJson.put("bizNo", bizNo);
                uploadParmJson.put("cusRelatedId", bizNo);
            }
            uploadParmJson.put("ifEsignFile",DocConstant.YES_NO_Y);//是否电签文件
            JSONObject gson = this.docFileBizFiletypeConfigService.upLoadFile(folder, file, "lease", JSONObject.toJSONString(uploadParmJson));
            if (!"0000".equals(gson.getString("code"))) {
                MFLogger.info("保存影像资料 失败！" + JSONObject.toJSONString(bizFiletypeConfigEntity));
                throw new Exception("保存影像资料 失败" + JSONObject.toJSONString(bizFiletypeConfigEntity));
            }

            DocTemplateEsignerListEntity update = new DocTemplateEsignerListEntity();
            update.setIfDownload("1");
            QueryWrapper<DocTemplateEsignerListEntity> qw = new QueryWrapper<>();
            if (leaseDue!=null){
                qw.eq("biz_no", leaseDue.getString("dueId")).eq("receiver_phone", docTemplateEsignerListEntity.getReceiverPhone());
            }else {
                qw.eq("biz_no", bizNo).eq("receiver_phone", docTemplateEsignerListEntity.getReceiverPhone());
                qw.eq("esigner_type",docTemplateEsignerListEntity.getEsignerType());
                qw.eq("template_name",templateName);
            }
            docTemplateEsignerListMapper.update(update, qw);
        }catch(Exception e){
            MFLogger.error("下载电签模板失败！",e);
            throw e;
        }finally {
            redisUtil.delInsertDocFlag(docNo,redisUtil);
        }
    }

    @Override
    public List<DocTemplateEsignerListEntity> processCallbackData(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws Exception {
        QueryWrapper<DocTemplateEsignerListEntity> queryWrapper=new QueryWrapper();
        queryWrapper.eq(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getReceiverPhone()),"if_download","0");//是否已下载
        queryWrapper.eq("if_esigned","2");// 是否签约完成
        queryWrapper.eq("biz_no",docTemplateEsignerListEntity.getBizNo());
        queryWrapper.eq(StringUtil.isNotEmpty(docTemplateEsignerListEntity.getReceiverPhone()),"receiver_phone",docTemplateEsignerListEntity.getReceiverPhone());
        queryWrapper.in("if_download","0","1");
        queryWrapper.orderByAsc("esign_time");
        queryWrapper.groupBy("template_name");
        return docTemplateEsignerListMapper.selectList(queryWrapper);
    }

    @Override
    public JSONObject downSSQFile(DocTemplateEsignerListEntity docTemplateEsignerListEntity) throws ServiceException {
        JSONObject result = new JSONObject();
        try {
            String subContractId = docTemplateEsignerListEntity.getSubContractId();
            if (StringUtil.isBlank(subContractId)) {
                Map<String, Object> paramData = new HashMap<>();
                String contractId = docTemplateEsignerListEntity.getContractId();
                paramData.put("contractId", contractId); //合同编号 必填
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("callNo", "SSQ_FIND");
                jsonObject.put("busReqNo", contractId);
                jsonObject.put("paramData", JSONObject.toJSON(paramData));
                String resultString = elinkFeignClient.itoData(jsonObject);
                MFLogger.info("查询响应参数：" + resultString);
                JSONObject json = JSONObject.parseObject(resultString);
                JSONObject data = json.getJSONObject("data");
                JSONObject data1 = data.getJSONObject("data");
                if (!"1".equals(json.getString("code"))) {
                    MFLogger.info("查询上上签合同 失败！" + docTemplateEsignerListEntity.getContractId(), json.getString("msg"));
                    throw new Exception("查询上上签合同 失败" + docTemplateEsignerListEntity.getContractId());
                } else {
                    if (!"0".equals(data.getString("code"))) {
                        MFLogger.info("查询上上签合同 失败！" + docTemplateEsignerListEntity.getContractId(), json.getString("msg"));
                        throw new Exception("查询上上签合同 失败" + docTemplateEsignerListEntity.getContractId());
                    } else {
                        String status=data1.getString("status");
                        if("COMPLETE".equals(status)){
                            JSONArray docDetails = data1.getJSONArray("docDetails");
                            List<SSQDocmentsVo> ssqDocmentsVos = JSONArray.parseArray(docDetails.toJSONString(), SSQDocmentsVo.class);
                            for (SSQDocmentsVo ssqDocmentsVo : ssqDocmentsVos) {
                                if(StringUtil.isNotBlank(ssqDocmentsVo.getDocumentTitle())&&ssqDocmentsVo.getDocumentTitle().equals(docTemplateEsignerListEntity.getTemplateName())){
                                    subContractId = ssqDocmentsVo.getSubContractId();
                                    break;
                                }
                            }
                        }
                    }
                }

            }
            if (StringUtil.isBlank(subContractId)) {
                MFLogger.info("子合同号为空不能下载！" + docTemplateEsignerListEntity.getContractId());
                throw new Exception("子合同号为空不能下载" + docTemplateEsignerListEntity.getContractId());
            }

            /*---- 下载上上签盖章文档 ----*/
            JSONObject requestJson = new JSONObject();
            requestJson.put("callNo", "SSQ_DOWN_ZI");
            requestJson.put("busReqNo", cn.mftcc.common.utils.UUIDUtil.getUUID());
            JSONObject paramData = new JSONObject();

            //合同账号
            paramData.put("subContractId", subContractId);//子合同账号
            requestJson.put("paramData", paramData);
            MFLogger.info("下载上上签文档 开始：" + requestJson);
            String resultJson = elinkFeignClient.itoData(requestJson);
            JSONObject js = JSONObject.parseObject(resultJson);
            String code = js.getString("code");
            if (!DocConstant.YES_NO_Y.equals(code)) {
                //elink调用是否成功
                MFLogger.info("下载上上签文档 失败！" + docTemplateEsignerListEntity.getContractId(), js.getString("msg"));
                throw new Exception("下载上上签文档 失败" + docTemplateEsignerListEntity.getContractId());
            }
            // 获取三方返回数据
            Map<String, Object> dataMap = (Map<String, Object>) js.get("data");
            Map<String, Object> resultMap = (Map<String, Object>) dataMap.get("data");
            String base64FileData = StringUtil.valueOf(resultMap.get("base64FileData"));
            if (StringUtil.isEmpty(base64FileData)) {
                MFLogger.info("获取的base64FileData为空！");
                throw new Exception("获取的base64FileData为空 失败");
            }
            FileUtils.base64ToFile(base64FileData, docTemplateEsignerListEntity.getSaveFileName(), docTemplateEsignerListEntity.getSavePath());
            DocTemplateEsignerListEntity update = new DocTemplateEsignerListEntity();
            update.setIfDownload("1");
            update.setIfRead("1");
            QueryWrapper<DocTemplateEsignerListEntity> qw = new QueryWrapper<>();
            qw.eq("biz_no", docTemplateEsignerListEntity.getBizNo());
            docTemplateEsignerListMapper.update(update, qw);

            result.put("msg", "保存成功");
            result.put("code", "0000");
            return result;
        } catch (Exception e) {
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, docTemplateEsignerListEntity.getBizNo(), e);
        }

    }

    @Override
    public List<DocTemplateEsignerListEntity> findNonSignListByBizNo(DocTemplateEsignerListEntity query) throws ServiceException {
        QueryWrapper<DocTemplateEsignerListEntity> queryWrapper=new QueryWrapper();
        queryWrapper.eq("if_esigned","0");// 是否签约完成
        queryWrapper.eq("biz_no",query.getBizNo());
        return docTemplateEsignerListMapper.selectList(queryWrapper);
    }

    @Override
    public void deleteByBizNo(JSONObject jsonObject) throws ServiceException {
        QueryWrapper<DocTemplateEsignerListEntity> queryWrapper=new QueryWrapper();
        queryWrapper.eq("biz_no",jsonObject.getString("bizNo"));
        docTemplateEsignerListMapper.delete(queryWrapper);
    }

    @Override
    public void batchFileMove(){
        log.info("StartTaskCall-pactRemoveToOss-合同文件迁移至oss-call");
        int count = 0;
        try{
            //1.筛选出需要迁移的文件(按合同模板编号分组，筛选出doc_template_esigner_list.if_esigned=2的组)
            //1.已完成电签的合同模板，2非电签的合同模板
            List<DocBizTemplateEntity> entities = docBizTemplateService.findToDoRemoveFileInfo();
            //2.完成oss推送
            count =  entities.size();
            log.info("TaskCall-pactRemoveToOss-合同文件迁移至oss-本次处理数据条数："+count+"条。");
            batchPushFile(entities);
        }catch (Exception e){
            log.info("TaskCall-pactRemoveToOss-合同文件迁移至oss-error-本次处理数据条数："+count+"条。");
            log.info("Taskdetailex-pactRemoveToOss-合同文件迁移至oss-失败原因/异常信息"+e.getMessage());
            MFLogger.error("文件迁移失败",e);
        }
    }

    /**
     * 将本地服务器文件推送至oss
     * @param entities
     * @throws Exception
     */
    private void batchPushFile(List<DocBizTemplateEntity> entities)throws Exception{
        String name = "";
        InputStream inputStream = null;
        for(DocBizTemplateEntity entity:entities) {
            try{
                name=entity.getSaveFileName();
                //新增oss文件路径
                StringBuffer ossFilePath = new StringBuffer();
                ossFilePath.append(savePactPath).append(DateUtil.getDate()).append("/");
                //新增oss文件全路径 /fhzbuat/lease/template/yyyyMMdd/localFileName
                String ossFileName = ossFilePath+name;
                String localFilePath = entity.getSavePath()+entity.getSaveFileName();
                File fileCheck = new File(localFilePath);
                if(!fileCheck.exists()){
                    log.info("文件迁移跳过，文件名："+localFilePath+"不存在");
                    continue;
                }
                inputStream = new FileInputStream(localFilePath);
                //上传文件至oss
                ossConfig.writeOss(ossFileName, inputStream);
                //删除本地服务器pdf文件
                File file = new File(localFilePath);
                if (file.exists() && file.isFile()){
                    file.delete();
                }
                //删除本地服务器docx文件
                String localDocxFilePath = localFilePath.split("\\.")[0]+".docx";
                File localDocxFile = new File(localDocxFilePath);
                if (localDocxFile.exists() && localDocxFile.isFile()){
                    localDocxFile.delete();
                }
                //更新原保存路径和oss迁移状态
                entity.setOssRemoveStatus("1");
                entity.setSavePath(ossFilePath.toString());
                docBizTemplateService.update(entity);
                inputStream.close();
            }catch (Exception e) {
                log.error("文件迁移失败，文件名："+name,e);
            }finally {
                if(null!=inputStream){
                    inputStream.close();
                }
            }
        }

    }
}