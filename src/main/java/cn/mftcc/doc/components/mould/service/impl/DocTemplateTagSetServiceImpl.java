/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.doc.components.mould.service.DocTemplateTagBaseService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocTemplateTagSetEntity;
import cn.mftcc.doc.components.mould.mapper.DocTemplateTagSetMapper;
import cn.mftcc.doc.components.mould.service.DocTemplateTagSetService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-15 19:02:36
 */
@Service("docTemplateTagSetService")
@Transactional(rollbackFor = Exception.class)
public class DocTemplateTagSetServiceImpl implements DocTemplateTagSetService {

    @Autowired
    private DocTemplateTagSetMapper docTemplateTagSetMapper;

    @Autowired
    private MapperUtil mapperUtil;

    @Override
    public IPage<DocTemplateTagSetEntity> findByPage(DocTemplateTagSetEntity docTemplateTagSetEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTemplateTagSetEntity> page = new Page<>();
            page.setCurrent(docTemplateTagSetEntity.getPageNo());
            page.setSize(docTemplateTagSetEntity.getPageSize());
            QueryWrapper<DocTemplateTagSetEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docTemplateTagSetEntity);
            return docTemplateTagSetMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateTagSetEntity.getSerialNo(),e);
        }
    }
    @Override
    public List<DocTemplateTagSetEntity> findByList(DocTemplateTagSetEntity docTemplateTagSetEntity) throws ServiceException {
        try{
            QueryWrapper<DocTemplateTagSetEntity> queryWrapper = new QueryWrapper<>();
            if(StringUtils.isNotBlank(docTemplateTagSetEntity.getTemplateId())){

                queryWrapper.eq("template_id", docTemplateTagSetEntity.getTemplateId());
            }
            if(StringUtils.isNotBlank(docTemplateTagSetEntity.getVersionNo())){

                queryWrapper.eq("version_no", docTemplateTagSetEntity.getVersionNo());
            }

            return docTemplateTagSetMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateTagSetEntity.getSerialNo(),e);
        }
    }

    /**
     *  根据模板id获取对象
     * @param templateId
     * @return
     * @throws ServiceException
     */
    @Override
    public DocTemplateTagSetEntity findOneObject(String templateId) throws ServiceException {
        try{
            DocTemplateTagSetEntity queryBean=new DocTemplateTagSetEntity();
            queryBean.setTemplateId(templateId);
            List<DocTemplateTagSetEntity>tmpList=findByList(queryBean);
            if(tmpList!=null && tmpList.size()>0){

                return tmpList.get(0);
            }else{
                return  new DocTemplateTagSetEntity();
            }
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,templateId,e);
        }
    }

    @Override
    public DocTemplateTagSetEntity getDocTemplateTagSetEntity(String  templateId,String templateVersionNo)throws  ServiceException{
        DocTemplateTagSetEntity result=null;
        DocTemplateTagSetEntity docTemplateTagSetEntity=new DocTemplateTagSetEntity();
        docTemplateTagSetEntity.setTemplateId(templateId);
//        docTemplateTagSetEntity.setVersionNo(templateVersionNo);
        String tagKeyNos="";
        List<DocTemplateTagSetEntity>tagList= findByList(docTemplateTagSetEntity);
        if(tagList!=null){
            if(tagList.size()>0){
                result=tagList.get(0);

            }
        }
        return result;
    }



    @Override
    public void insert(DocTemplateTagSetEntity docTemplateTagSetEntity) throws ServiceException {
        try{
            docTemplateTagSetMapper.insert(docTemplateTagSetEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTemplateTagSetEntity.getSerialNo(),e);
        }
    }

    @Override
    public void update(DocTemplateTagSetEntity docTemplateTagSetEntity) throws ServiceException {
        try{
            docTemplateTagSetMapper.updateById(docTemplateTagSetEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTemplateTagSetEntity.getSerialNo(),e);
        }
    }

    @Override
    public DocTemplateTagSetEntity findById(String serialNo) throws ServiceException {
        try{
            return docTemplateTagSetMapper.selectById(serialNo);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,serialNo,e);
        }
    }

    @Override
    public void deleteById(String serialNo) throws ServiceException {
        try{
            docTemplateTagSetMapper.deleteById(serialNo);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,serialNo,e);
        }
    }

}