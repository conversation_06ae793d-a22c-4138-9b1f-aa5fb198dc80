/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import cn.mftcc.doc.components.mould.mapper.DocBizTemplateMapper;
import cn.mftcc.doc.components.mould.service.DocTemplateModelService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocBizEleSignConfigEntity;
import cn.mftcc.doc.components.mould.mapper.DocBizEleSignConfigMapper;
import cn.mftcc.doc.components.mould.service.DocBizEleSignConfigService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;
import springfox.documentation.spring.web.json.Json;

import java.util.List;

/**
 * 电子签章配置业务关联表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-28 17:36:42
 */
@Service("docBizEleSignConfigService")
@Transactional(rollbackFor = Exception.class)
public class DocBizEleSignConfigServiceImpl implements DocBizEleSignConfigService {

    @Autowired
    private DocBizEleSignConfigMapper docBizEleSignConfigMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private DocTemplateModelService docTemplateModelService;
    @Autowired
    private DocBizTemplateMapper docBizTemplateMapper;

    @Override
    public IPage<DocBizEleSignConfigEntity> findByPage(DocBizEleSignConfigEntity docBizEleSignConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocBizEleSignConfigEntity> page = new Page<>();
            page.setCurrent(docBizEleSignConfigEntity.getPageNo());
            page.setSize(docBizEleSignConfigEntity.getPageSize());
            QueryWrapper<DocBizEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docBizEleSignConfigEntity);
            return docBizEleSignConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docBizEleSignConfigEntity.getId(),e);
        }
    }
    @Override
    public List<DocBizEleSignConfigEntity> findListByObj(DocBizEleSignConfigEntity docEleSignConfigEntity) throws ServiceException {
        try{
            //翻页

            QueryWrapper<DocBizEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotEmpty(docEleSignConfigEntity.getTemplateId()),"template_id",docEleSignConfigEntity.getTemplateId());
            queryWrapper.eq(StringUtils.isNotEmpty(docEleSignConfigEntity.getBizNo()),"biz_no",docEleSignConfigEntity.getBizNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docEleSignConfigEntity.getSubBizNo()),"sub_biz_no",docEleSignConfigEntity.getSubBizNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docEleSignConfigEntity.getFinishFlag()),"finish_flag",docEleSignConfigEntity.getFinishFlag());
            queryWrapper.orderByAsc("esign_sort");
            return docBizEleSignConfigMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docEleSignConfigEntity.getId(),e);
        }
    }

    /**
     * 判断当前步骤是否是最后一部
     * @param templateId 模板id
     * @param bizNo 流水号
     * @param subBizNo 子项编号
     * @param curSignId 当前电签流水号
     * @return  1-是 0-否
     * @throws ServiceException
     */
    @Override
    public String  lastStepFlag(String templateId,String bizNo,String subBizNo,String curSignId)throws ServiceException{
        String result="0";
        DocBizEleSignConfigEntity qBean=new DocBizEleSignConfigEntity();
        qBean.setTemplateId(templateId);
        qBean.setBizNo(bizNo);
        qBean.setSubBizNo(subBizNo);
        List<DocBizEleSignConfigEntity> allSignList= findListByObj(qBean);
        if(allSignList!=null){
            int  len=allSignList.size();
            String lastId=allSignList.get(len-1).getId();
            if(lastId.equals(curSignId)){
                result="1";
            }

        }
        return  result;

    }


    @Override
    public void insert(DocBizEleSignConfigEntity docBizEleSignConfigEntity) throws ServiceException {
        try{
            docBizEleSignConfigMapper.insert(docBizEleSignConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docBizEleSignConfigEntity.getId(),e);
        }
    }

    @Override
    public void update(DocBizEleSignConfigEntity docBizEleSignConfigEntity) throws ServiceException {
        try{
            docBizEleSignConfigMapper.updateById(docBizEleSignConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docBizEleSignConfigEntity.getId(),e);
        }
    }

    @Override
    public DocBizEleSignConfigEntity findById(String id) throws ServiceException {
        try{
            return docBizEleSignConfigMapper.selectById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,id,e);
        }
    }

    @Override
    public void deleteById(String id) throws ServiceException {
        try{
            docBizEleSignConfigMapper.deleteById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,id,e);
        }
    }
    @Override
    public JSONObject findBySignOrderId(String signOrderId) throws ServiceException {
        JSONObject jsonObject = new JSONObject();
        try {
            // 获取电子签章配置业务关联表
            DocBizEleSignConfigEntity docBizEleSignConfigEntity = findById(signOrderId);
            if (docBizEleSignConfigEntity!=null){
                jsonObject.put("docBizEleSignConfigEntity",docBizEleSignConfigEntity);
                //模板ID
                String templateId = docBizEleSignConfigEntity.getTemplateId();
                // 获取模板表数据
                DocTemplateModelEntity docTemplateModelEntity = docTemplateModelService.findById(templateId);
                if (docTemplateModelEntity!=null){
                    jsonObject.put("docTemplateModelEntity",docTemplateModelEntity);
                }
                //如果已经完成签约，则返回当前已签的文件
                if("1".equals(docBizEleSignConfigEntity.getFinishFlag())){
                    jsonObject.put("obsId", docBizEleSignConfigEntity.getDocObsId());
                    return jsonObject;
                }

                Integer esignSort = docBizEleSignConfigEntity.getEsignSort();// 获取当前签章人顺序
                esignSort=esignSort-1;
                if (esignSort==0){// 如果是0证明是第一个签章人，则obsId返回空
                    jsonObject.put("obsId", "");
                    return jsonObject;
                }

                // 获取签章人列表，用来获取上一个签章人obsId
                DocBizEleSignConfigEntity tmpSignConfigEntity =new DocBizEleSignConfigEntity();
                tmpSignConfigEntity.setBizNo(docBizEleSignConfigEntity.getBizNo());
                tmpSignConfigEntity.setTemplateId(docBizEleSignConfigEntity.getTemplateId());
                tmpSignConfigEntity.setSubBizNo(docBizEleSignConfigEntity.getSubBizNo());
                List<DocBizEleSignConfigEntity> docBizEleSignConfigList = findListByObj(tmpSignConfigEntity);
                for (int i = 0; i <docBizEleSignConfigList.size() ; i++) {
                    // 获取签章顺序
                    Integer frontSign = docBizEleSignConfigList.get(i).getEsignSort();
                    // 获取到的签章顺序是否等于当前签章顺序，如果等于证明是上一个。获取obsId
                    if(frontSign.equals(esignSort)){
                        jsonObject.put("obsId", docBizEleSignConfigList.get(i).getDocObsId());
                        return jsonObject;
                    }
                }
            }else {
                MFLogger.info("没有获取到签章配置业务");
            }
            return jsonObject;
        } catch (Exception e) {
            throw new ServiceException("签章配置业务信息查询失败", signOrderId, e);
        }
    }

    /**
     * 修改和业务相关的签章属性
     * @param signInf
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject updateBizSignInfo(JSONObject signInf)throws ServiceException{
        JSONObject res=new JSONObject();
        if(signInf!=null){
            DocBizEleSignConfigEntity updBean = JSON.toJavaObject(signInf, DocBizEleSignConfigEntity.class);
            update(updBean);
            res.put("code","0000");
            res.put("msg","success");
        }else{
            res.put("code","1111");
            res.put("msg","没有要修改的信息");
        }
        return  res;
    }
    /**
     * 删除模板
     * @param object
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *          "subBizNo" :""
     *      * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  void  deleteByBean(JSONObject object)throws  ServiceException {
        QueryWrapper<DocBizEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("prdUniqueVal")), "prd_unique_val", object.getString("prdUniqueVal"));
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("flowNo")), "flow_no", object.getString("flowNo"));
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("nodeNo")), "node_no", object.getString("nodeNo"));
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("bizNo")), "biz_no", object.getString("bizNo"));
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("subBizNo")), "sub_biz_no", object.getString("subBizNo"));
        docBizEleSignConfigMapper.delete(queryWrapper);
    }


    /**
     * 获取需要电签的模板
     * @param object{
     *      "prdUniqueVal":"prdId",
     *      "flowNo":"flowNo",
     *      "nodeNo":"nodeNo",
     *      "bizNo":"pact_00001"
     *      "subBizNo" :""
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray  getNeedEsignTemplate(JSONObject object) throws ServiceException {
        try{
            //翻页

            QueryWrapper<DocBizEleSignConfigEntity> queryWrapper = new QueryWrapper<>();

            queryWrapper.eq(StringUtils.isNotEmpty(object.getString("prdUniqueVal")), "prd_unique_val", object.getString("prdUniqueVal"));
            queryWrapper.eq(StringUtils.isNotEmpty(object.getString("flowNo")), "flow_no", object.getString("flowNo"));
            queryWrapper.eq(StringUtils.isNotEmpty(object.getString("nodeNo")), "node_no", object.getString("nodeNo"));
            queryWrapper.eq(StringUtils.isNotEmpty(object.getString("bizNo")), "biz_no", object.getString("bizNo"));
            queryWrapper.eq(StringUtils.isNotEmpty(object.getString("subBizNo")), "sub_biz_no", object.getString("subBizNo"));
            queryWrapper.orderByAsc("esign_sort");
            List<DocBizEleSignConfigEntity> esignList= docBizEleSignConfigMapper.selectList(queryWrapper);
            JSONArray result=new JSONArray();
            if(esignList!=null){
                for(DocBizEleSignConfigEntity obj:esignList){
                    result.add(obj);
                }
            }
            return  result;
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,object.toJSONString(),e);
        }
    }
    /**
     * 根据申请号，所属模板编号获取获取签章顺序id
     * @param paramJson
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getSignSortId(JSONObject paramJson) throws ServiceException {
        JSONObject resJson = new JSONObject();
        try {
            String bizNo = paramJson.getString("bizNo");
            String templateId = paramJson.getString("templateId") ;
            // 获取当前模板是否完成电签
            QueryWrapper<DocBizTemplateEntity> bizTemplateQueryWrapper = new QueryWrapper<>();
            bizTemplateQueryWrapper.eq("biz_no", bizNo);
            bizTemplateQueryWrapper.eq("template_id", templateId);
            bizTemplateQueryWrapper.eq("prd_unique_val", paramJson.getString("prId"));
            bizTemplateQueryWrapper.eq("flow_no", paramJson.getString("flowNo"));
            bizTemplateQueryWrapper.eq("node_no", paramJson.getString("nodeNo"));
            DocBizTemplateEntity docBizTemplateEntity = docBizTemplateMapper.selectOne(bizTemplateQueryWrapper);
            // 获取签约状态是否完成电签
            String esignFinishFlag = docBizTemplateEntity.getEsignFinishFlag();//该模板是否已完成电签： 0-否; 1-是
            QueryWrapper<DocBizEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("biz_no", bizNo);
            queryWrapper.eq("template_id", templateId);
            queryWrapper.eq("prd_unique_val", paramJson.getString("prId"));
            queryWrapper.eq("flow_no", paramJson.getString("flowNo"));
            queryWrapper.eq("node_no", paramJson.getString("nodeNo"));
            List<DocBizEleSignConfigEntity> docBizEleSignConfigList = docBizEleSignConfigMapper.selectList(queryWrapper);
            resJson.put("code", "0000");
            int docSize = docBizEleSignConfigList.size();
            for (DocBizEleSignConfigEntity docBizEleSignConfigEntity :docBizEleSignConfigList){
                // 如果该模板没有完成电签则获取第一个签约人，签约顺序id
                if("0".equals(esignFinishFlag)){
                    // 获取第一个签约人
                    if(docBizEleSignConfigEntity.getEsignSort()==1){
                        resJson.put("sortId", docBizEleSignConfigEntity.getId());
                        return resJson;
                    }
                }else {
                    // 如果签约完成，则获取最后一个签约人顺序
                    if(docBizEleSignConfigEntity.getEsignSort()==docSize){
                        resJson.put("sortId", docBizEleSignConfigEntity.getId());
                        return resJson;
                    }
                }
            }
            return resJson;
        } catch (Exception e) {
            throw new ServiceException("获取签约顺序ID失败！","" ,e );
        }
    }

}