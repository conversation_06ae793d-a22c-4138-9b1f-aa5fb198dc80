/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocBizEleSignConfigEntity;
import cn.mftcc.doc.components.mould.service.DocBizEleSignConfigService;

/**
 * 电子签章配置业务关联表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-28 17:36:42
 */
@RestController
@RequestMapping("mould/docBizEleSignConfig")
public class DocBizEleSignConfigController {

    @Autowired
    private DocBizEleSignConfigService docBizEleSignConfigService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocBizEleSignConfigEntity docBizEleSignConfigEntity) {
        IPage<DocBizEleSignConfigEntity> list = this.docBizEleSignConfigService.findByPage(docBizEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocBizEleSignConfigEntity docBizEleSignConfigEntity){
        this.docBizEleSignConfigService.insert(docBizEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocBizEleSignConfigEntity docBizEleSignConfigEntity){
        this.docBizEleSignConfigService.update(docBizEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{id}")
    public R findById(@PathVariable("id") String id){
        DocBizEleSignConfigEntity docBizEleSignConfigEntity = this.docBizEleSignConfigService.findById(id);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docBizEleSignConfigEntity);
    }

    @RequestMapping("/deleteById/{id}")
    public R deleteById(@PathVariable("id") String id){
        this.docBizEleSignConfigService.deleteById(id);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}