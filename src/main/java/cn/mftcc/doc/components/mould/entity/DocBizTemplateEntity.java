/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.alibaba.fastjson.JSONPObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 业务模板关联表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-27 14:18:47
 */
@Data
@TableName("doc_biz_template")
public class DocBizTemplateEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String btId;
	/**
	 * 业务流水号
	 */
	private String bizNo;
	/**
	 * 子项业务流水号
	 */
	private String subBizNo;
	/**
	 * 模板唯一流水号
	 */
	private String templateId;

	/**
	 * 指定模板Id
	 */
	@TableField(exist = false)
	private String showTemplateIds;
	/**
	 * 模板编号
	 */
	private String templateNo;
	/**
	 * 模板名称
	 */
	private String templateName;
	/**
	 * 子项模板名称
	 */
	private String subTemplateName;


	/**
	 * 多份模板feigin接口
	 */
	private String multiFeign;
	/**
	 * 多份模板feigin接口调用的方法
	 */
	private String multiFeignMethod;

	/**
	 * 多份模板参数列表
	 */
	private String parmList;
	/**
	 * 要件类型编号
	 */
	private String typeNo;
	/**
	 * 要件类型名称
	 */
	private String typeName;

	/**
	 * 版本号
	 */
	private String versionNo;

	/**
	 * 产品唯一标识
	 */
	private String prdUniqueVal;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;
	/**
	 * 是否可编辑 1-是 0-否
	 */
	private String canWrite;
	/**
	 * 是否必读  1-是 0-否
	 */
	private String ifMustRead;
	/**
	 * 是否已读  1-是 0-否
	 */
	private String readFlag;
	/**
	 * 是否必须填写1-是0-否
	 */
	private String ifMustWrite;
	/**
	 * 是否已填写1-是0-否
	 */
	private String writeFlag;
	/**
	 * 文档存储路径
	 */
	private String savePath;

	/**
	 * 电签时间
	 */
	private Date elecSealDt;

	/**
	 * 是否电签0-否; 1-是
	 */
	private String ifElectricSign;
	/**
	 * 是否用印0-否; 1-是
	 */
	private String useSealFlag;

	/**
	 * 是否已电签 1-是0-否
	 */
	private String elecSealFlag;





	/**
	 * 电签人员，用逗号隔开
	 */
	private String elecSealPersons;

	/**
	 * 保存后的文件名称
	 */
	private String saveFileName;
	/**
	 * 操作员编号
	 */
	private String createUserNo;
	/**
	 * 操作员名称
	 */
	private String createUserName;
	/**
	 * 部门编号
	 */
	private String createOrgNo;
	/**
	 * 部门名称
	 */
	private String createOrgName;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;

	/**
	 * 电签顺序列表
	 */
	@TableField(exist = false)
	private List<DocBizEleSignConfigEntity> eleSignConfigEntityList;
	/**
	 * 电签顺序列表
	 */
	@TableField(exist = false)
	private JSONPObject parmListJson;

	/**
	 * 模板电签状态：-1-未提交 0-签约中 1-已经完成电签
	 */
	private String esignFinishFlag;

	/**
	 * 签约文档后的obsId
	 */
	private String docObsId;
	/**
	 * 关联业务编号
	 */
	private String busRelatedId;
	/**
	 * 关联客户号
	 */
	private String cusRelatedId;
	/**
	 * 关联申请编号
	 */
	private String applyRelatedId;
	/**
	 * 关联合同编号
	 */
	private String constractRelatedId;
	/**
	 * 关联借据编号
	 */
	private String finRelatedId;

	/**
	 * 是否生成pdf 0-否; 1-是
	 */
	private String  pdfFlag;
	/**
	 * 生成pdf的obsId
	 */
	private String  pdfObsId;

	/**
	 * 模板格式1-word 2-excel 3-pdf
	 */
	private String templateSuffix;

	@TableField(exist = false)
	private String base64File;

	@ApiModelProperty(value = "是否可提供设备编号/车架号")
	private String pledgeInfoFlag;

	/**
	 * oss文件迁移标志 0否 1是
	 */
	private String ossRemoveStatus;

	@TableField(exist = false)
	private String prepayId;

	@TableField(exist = false)
	private String dueId;

	@TableField(exist = false)
	private String dueBeginDate;

	@TableField(exist = false)
	private String prepayDate;
}
