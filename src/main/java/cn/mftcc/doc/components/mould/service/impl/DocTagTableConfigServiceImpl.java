/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.common.utils.UUIDUtil;
import cn.mftcc.doc.components.mould.entity.DocTagColumnConfigEntity;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagBaseEntity;
import cn.mftcc.doc.components.mould.service.DocTagColumnConfigService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocTagTableConfigEntity;
import cn.mftcc.doc.components.mould.mapper.DocTagTableConfigMapper;
import cn.mftcc.doc.components.mould.service.DocTagTableConfigService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

import java.util.List;

/**
 * 标签表格类配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-16 15:07:29
 */
@Service("docTagTableConfigService")
@Transactional(rollbackFor = Exception.class)
public class DocTagTableConfigServiceImpl implements DocTagTableConfigService {

    @Autowired
    private DocTagTableConfigMapper docTagTableConfigMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private DocTagColumnConfigService docTagColumnConfigService;

    @Override
    public IPage<DocTagTableConfigEntity> findByPage(DocTagTableConfigEntity docTagTableConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTagTableConfigEntity> page = new Page<>();
            page.setCurrent(docTagTableConfigEntity.getPageNo());
            page.setSize(docTagTableConfigEntity.getPageSize());
            QueryWrapper<DocTagTableConfigEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docTagTableConfigEntity);
            return docTagTableConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTagTableConfigEntity.getConfigId(),e);
        }
    }

    @Override
    public List<DocTagTableConfigEntity> findListByKeyno(String keyNo) throws ServiceException {
        try{
            DocTagTableConfigEntity docTagTableConfigEntity=new DocTagTableConfigEntity();
            docTagTableConfigEntity.setKeyNo(keyNo);
            QueryWrapper<DocTagTableConfigEntity> queryWrapper = new QueryWrapper<>();

            queryWrapper
                    .eq(StringUtils.isNotBlank(keyNo),"key_no",keyNo);
            queryWrapper.orderByAsc("tab_sort");

            return docTagTableConfigMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,keyNo,e);
        }
    }

    @Override
    public void insert(DocTagTableConfigEntity docTagTableConfigEntity) throws ServiceException {
        try{
            docTagTableConfigEntity.setConfigId(UUIDUtil.getUUID());
            docTagTableConfigMapper.insert(docTagTableConfigEntity);
            //插入对应的列表
            DocTagColumnConfigEntity columnBean=getColumnByTabObj(docTagTableConfigEntity);
            docTagColumnConfigService.insert(columnBean);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTagTableConfigEntity.getConfigId(),e);
        }
    }

    private DocTagColumnConfigEntity getColumnByTabObj(DocTagTableConfigEntity docTagTableConfigEntity)throws  ServiceException{
        DocTagColumnConfigEntity resBean=new DocTagColumnConfigEntity();
        BeanUtils.copyProperties(docTagTableConfigEntity ,resBean);
        return resBean;
    }

    @Override
    public void update(DocTagTableConfigEntity docTagTableConfigEntity) throws ServiceException {
        try{
            docTagTableConfigMapper.updateById(docTagTableConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTagTableConfigEntity.getConfigId(),e);
        }
    }

    @Override
    public DocTagTableConfigEntity findById(String configId) throws ServiceException {
        try{
            return docTagTableConfigMapper.selectById(configId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,configId,e);
        }
    }

    @Override
    public void deleteById(String configId) throws ServiceException {
        try{
            docTagTableConfigMapper.deleteById(configId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,configId,e);
        }
    }

}