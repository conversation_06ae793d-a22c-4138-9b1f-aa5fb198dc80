/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocTagColumnConfigEntity;
import cn.mftcc.doc.components.mould.mapper.DocTagColumnConfigMapper;
import cn.mftcc.doc.components.mould.service.DocTagColumnConfigService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

import java.util.List;

/**
 * 标签表格列对应字段配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-16 15:10:40
 */
@Service("docTagColumnConfigService")
@Transactional(rollbackFor = Exception.class)
public class DocTagColumnConfigServiceImpl implements DocTagColumnConfigService {

    @Autowired
    private DocTagColumnConfigMapper docTagColumnConfigMapper;
    @Autowired
    private MapperUtil mapperUtil;

    @Override
    public IPage<DocTagColumnConfigEntity> findByPage(DocTagColumnConfigEntity docTagColumnConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTagColumnConfigEntity> page = new Page<>();
            page.setCurrent(docTagColumnConfigEntity.getPageNo());
            page.setSize(docTagColumnConfigEntity.getPageSize());
            QueryWrapper<DocTagColumnConfigEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docTagColumnConfigEntity);
            return docTagColumnConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTagColumnConfigEntity.getConfigId(),e);
        }
    }

    @Override
    public void insert(DocTagColumnConfigEntity docTagColumnConfigEntity) throws ServiceException {
        try{
            docTagColumnConfigMapper.insert(docTagColumnConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTagColumnConfigEntity.getConfigId(),e);
        }
    }

    @Override
    public void update(DocTagColumnConfigEntity docTagColumnConfigEntity) throws ServiceException {
        try{
            docTagColumnConfigMapper.updateById(docTagColumnConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTagColumnConfigEntity.getConfigId(),e);
        }
    }

    @Override
    public DocTagColumnConfigEntity findById(String configId) throws ServiceException {
        try{
            return docTagColumnConfigMapper.selectById(configId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,configId,e);
        }
    }

    @Override
    public void deleteById(String configId) throws ServiceException {
        try{
            docTagColumnConfigMapper.deleteById(configId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,configId,e);
        }
    }

    @Override
    public List<DocTagColumnConfigEntity> findList(DocTagColumnConfigEntity docTagColumnConfigEntity) throws ServiceException {
        try{
            QueryWrapper<DocTagColumnConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.setEntity(docTagColumnConfigEntity);
            return docTagColumnConfigMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTagColumnConfigEntity.getConfigId(),e);
        }
    }

}