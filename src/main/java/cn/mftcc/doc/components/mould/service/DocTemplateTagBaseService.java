/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagBaseEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 标签表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-03-27 10:39:22
 */
public interface DocTemplateTagBaseService {

    IPage<DocTemplateTagBaseEntity> findByPage(DocTemplateTagBaseEntity docTemplateTagBaseEntity) throws ServiceException;

    void insert(DocTemplateTagBaseEntity docTemplateTagBaseEntity) throws ServiceException;

    public List<DocTemplateTagBaseEntity> findListByTagkeynos(String tagKeyNos) throws ServiceException;

    void update(DocTemplateTagBaseEntity docTemplateTagBaseEntity) throws ServiceException;

    JSONArray getAllTagSltJson()throws  ServiceException;

    DocTemplateTagBaseEntity findById(String serialId) throws ServiceException;

    void deleteById(String serialId) throws ServiceException;

    IPage<DocTemplateTagBaseEntity> selectTagByPage(DocTemplateTagBaseEntity docTemplateTagBaseEntity) throws ServiceException;
}

