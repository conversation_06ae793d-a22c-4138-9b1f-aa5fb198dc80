/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.bizcommon.utils.BeanCopyUtil;
import cn.mftcc.bizcommon.utils.UUIDUtil;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.ParmCacheUtil;
import cn.mftcc.doc.components.mould.entity.DocEleSignConfigEntity;
import cn.mftcc.doc.components.mould.entity.DocScEleSignConfigEntity;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import cn.mftcc.doc.components.mould.service.DocEleSignConfigService;
import cn.mftcc.doc.components.mould.service.DocScEleSignConfigService;
import cn.mftcc.doc.components.mould.service.DocTemplateModelService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.catalina.LifecycleState;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocScTemplateConfigEntity;
import cn.mftcc.doc.components.mould.mapper.DocScTemplateConfigMapper;
import cn.mftcc.doc.components.mould.service.DocScTemplateConfigService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

import java.util.*;

/**
 * 场景的模板配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-25 17:30:20
 */
@Service("docScTemplateConfigService")
@Transactional(rollbackFor = Exception.class)
public class DocScTemplateConfigServiceImpl implements DocScTemplateConfigService {

    @Autowired
    private DocScTemplateConfigMapper docScTemplateConfigMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private DocTemplateModelService docTemplateModelService;
    @Autowired
    private  DocEleSignConfigService docEleSignConfigService;
    @Autowired
    private DocScEleSignConfigService docScEleSignConfigService;
    @Autowired
    private ParmCacheUtil parmCacheUtil;

    @Override
    public IPage<DocScTemplateConfigEntity> findByPage(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocScTemplateConfigEntity> page = new Page<>();
            page.setCurrent(docScTemplateConfigEntity.getPageNo());
            page.setSize(docScTemplateConfigEntity.getPageSize());
            QueryWrapper<DocScTemplateConfigEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docScTemplateConfigEntity);
            return docScTemplateConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docScTemplateConfigEntity.getScId(),e);
        }
    }

    /**
     * 获取某个场景下配置的所有模板
     * @param parmObj{
     *     "prdUniqueVal":"prdId",
     *     flowNo":"flowNo",
     *     "nodeNo":"nodeNo",
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocScTemplateConfigEntity> findScTemplateList(JSONObject parmObj) throws ServiceException {
        List<DocScTemplateConfigEntity>result=new ArrayList<>();
        if(parmObj!=null){
            if(parmObj.size()>0){
                DocScTemplateConfigEntity qBean=JSONObject.parseObject(parmObj.toJSONString(),DocScTemplateConfigEntity.class);
                result=findConfigList(qBean);

            }
        }
        return  result;
    }

    @Override
    public List<DocScTemplateConfigEntity> findConfigList(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException {
        try{

            if(docScTemplateConfigEntity!=null){
                String  tmpStr=JSONObject.toJSONString(docScTemplateConfigEntity);

                MFLogger.info("传递的参数是：　　　"+tmpStr);
            }else{
                MFLogger.info("传递的参数是：　　null　");
            }
            //翻页
            List<DocScTemplateConfigEntity>result=new ArrayList<>();
            if(StringUtils.isEmpty(docScTemplateConfigEntity.getPrdUniqueVal())&&
            StringUtils.isEmpty(docScTemplateConfigEntity.getFlowNo())&&
            StringUtils.isEmpty(docScTemplateConfigEntity.getNodeNo())){
                return result;
            }
//            MFLogger.error("result=：　"+JSONObject.toJSONString(result),new Exception(""));
            QueryWrapper<DocScTemplateConfigEntity> queryWrapper = new QueryWrapper<>();
//            mapperUtil.tableQuery(queryWrapper,docScTemplateConfigEntity);
            queryWrapper.eq(StringUtils.isNotEmpty(docScTemplateConfigEntity.getPrdUniqueVal()),"prd_unique_val",docScTemplateConfigEntity.getPrdUniqueVal());
            queryWrapper.eq(StringUtils.isNotEmpty(docScTemplateConfigEntity.getFlowNo()),"flow_no",docScTemplateConfigEntity.getFlowNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docScTemplateConfigEntity.getNodeNo()),"node_no",docScTemplateConfigEntity.getNodeNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docScTemplateConfigEntity.getTemplateType()),"template_type",docScTemplateConfigEntity.getTemplateType());

            result=docScTemplateConfigMapper.selectList(queryWrapper);
            if(result!=null){
                MFLogger.info("result=：　"+JSONObject.toJSONString(result));
                if(result.size()==0){
                    MFLogger.info("result=：没有查询到记录　");
                }
            }else{

                MFLogger.info("result=null 　");
            }

            if (!result.isEmpty()){
                DocTemplateModelEntity query = new DocTemplateModelEntity();
                query.setUseFlag(CommonConstant.YES_NO_Y);
                List<DocTemplateModelEntity> docTemplateModelEntities = docTemplateModelService.findList(query);
                JSONObject templateJson = new JSONObject();
                if (docTemplateModelEntities != null) {
                    MFLogger.info("docTemplateModelEntities=：　"+docTemplateModelEntities.size()+":::"+JSONObject.toJSONString(docTemplateModelEntities));
                }else{
                    MFLogger.info("docTemplateModelEntities：　　null　");
                }

                Map<String,DocTemplateModelEntity> templateMap = new HashMap<>();
                for (DocTemplateModelEntity templateModelEntity:docTemplateModelEntities){
                    templateMap.put(templateModelEntity.getTemplateId(),templateModelEntity);
                }

                for (DocScTemplateConfigEntity scTemplateConfigEntity:result){
                    DocTemplateModelEntity templateModelEntity = templateMap.get(scTemplateConfigEntity.getTemplateId());
                    if(templateModelEntity != null){
                        scTemplateConfigEntity.setTemplateSuffix(templateModelEntity.getTemplateSuffix());
                    }
                }
            }

            return  result;
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docScTemplateConfigEntity.getScId(),e);
        }
    }
    private  List<DocScTemplateConfigEntity> findConfigListByObj(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException {
        try{
            //翻页
            List<DocScTemplateConfigEntity>result=new ArrayList<>();
            if(StringUtils.isEmpty(docScTemplateConfigEntity.getPrdUniqueVal())&&
            StringUtils.isEmpty(docScTemplateConfigEntity.getFlowNo())&&
            StringUtils.isEmpty(docScTemplateConfigEntity.getNodeNo())){
                return result;
            }
            QueryWrapper<DocScTemplateConfigEntity> queryWrapper = new QueryWrapper<>();
//            mapperUtil.tableQuery(queryWrapper,docScTemplateConfigEntity);
            queryWrapper.eq(StringUtils.isNotEmpty(docScTemplateConfigEntity.getPrdUniqueVal()),"prd_unique_val",docScTemplateConfigEntity.getPrdUniqueVal());
            queryWrapper.eq(StringUtils.isNotEmpty(docScTemplateConfigEntity.getFlowNo()),"flow_no",docScTemplateConfigEntity.getFlowNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docScTemplateConfigEntity.getNodeNo()),"node_no",docScTemplateConfigEntity.getNodeNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docScTemplateConfigEntity.getTemplateId()),"template_id",docScTemplateConfigEntity.getTemplateId());

            result=docScTemplateConfigMapper.selectList(queryWrapper);
            return  result;
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docScTemplateConfigEntity.getScId(),e);
        }
    }

    /**
     * 拷贝一个产品的模板配置到另外一个产品中
     * {
     *   newPrdId  新的产品唯一标识
     *   motherPrdId 母体产品唯一标识
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject copyTemplateConfigOfPrd( String newPrdId, String motherPrdId)throws ServiceException{



        JSONObject result=new JSONObject();


        if(StringUtils.isEmpty(newPrdId)){
            result.put("code","1111");
            result.put("msg","参数[newPrdId]不能为空");
            return result;

        }

        if(StringUtils.isEmpty(motherPrdId)){
            result.put("code","1111");
            result.put("msg","参数[motherPrdId]不能为空");
            return result;

        }

        //获取母体产品所有的模板配置列表
        DocScTemplateConfigEntity qBean=new DocScTemplateConfigEntity();
        qBean.setPrdUniqueVal(motherPrdId);
        List<DocScTemplateConfigEntity>motherConfigList= findConfigList(qBean);
        if(motherConfigList!=null){
            if(motherConfigList.size()>0){
                for(DocScTemplateConfigEntity newBean : motherConfigList){
                    newBean.setPrdUniqueVal(newPrdId);
                    newBean.setScId(UUIDUtil.getUUID());
                    Date tmpDate=new Date();
                    newBean.setCreateTime(tmpDate);
                    newBean.setUpdateTime(tmpDate);
                    docScTemplateConfigMapper.insert(newBean);
                }
            }
        }

        result.put("code","0000");
        result.put("msg","拷贝成功");
        return  result;
    }

    /**
     * 删除指定的流程节点模板配置
     * @param delNode{
     *           prdUniqueVal：产品唯一标识
     *      *     flowNo：所属流程
     *      *     nodeNo：节点编号
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  void  delOneTemplateNodeSetting(Map<String,String>delNode)throws Exception{

        try{

            if(delNode.size()>0){

                //获取母体产品所有的模板配置列表
                DocScTemplateConfigEntity scTemplateConfigEntity=new DocScTemplateConfigEntity();

                QueryWrapper<DocScTemplateConfigEntity> queryWrapper = new QueryWrapper<>();
                if(StringUtils.isNotEmpty(delNode.get("prdUniqueVal"))){
                    queryWrapper.eq("prd_unique_val",delNode.get("prdUniqueVal"));
                }
                if(StringUtils.isNotEmpty(delNode.get("flowNo"))){
                    queryWrapper.eq("flow_no",delNode.get("flowNo"));
                }
                if(StringUtils.isNotEmpty(delNode.get("nodeNo"))){
                    queryWrapper.eq("node_no",delNode.get("nodeNo"));
                }
                docScTemplateConfigMapper.delete(queryWrapper);
            }


        }catch (Exception e){

            throw  e;

        }

    }

    /**
     * 获取待选模板
     * @param docScTemplateConfigEntity
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray  getWaitSltTemplateList(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException {
        try{
            JSONArray result=new JSONArray();
            //获取所有的模板
            List<DocTemplateModelEntity> allTemplateList= docTemplateModelService.findDocTemplateList( new DocTemplateModelEntity() );
            //获取已经选择的模板
            List<DocScTemplateConfigEntity>configedList=findConfigList(docScTemplateConfigEntity);
            //移除配置过的模板
            if(configedList!=null){
                int len=configedList.size();
                if(len>0){
                    Map<String ,DocScTemplateConfigEntity>tmpMap=new HashedMap();
                    for(DocScTemplateConfigEntity obj:configedList){
                        tmpMap.put(obj.getTemplateId(),obj);
                    }
                    if(allTemplateList!=null){
                        for(DocTemplateModelEntity bean:allTemplateList){
                            if(tmpMap.containsKey(bean.getTemplateId())){
                                continue;
                            }else{
                                JSONObject json=new JSONObject();
                                json.put("value",bean.getTemplateId());
                                json.put("text",bean.getTemplateName());
                                result.add(json);
                            }
                        }
                    }
                }else{
                    for(DocTemplateModelEntity bean:allTemplateList){

                            JSONObject json=new JSONObject();
                            json.put("value",bean.getTemplateId());
                            json.put("text",bean.getTemplateName());
                            result.add(json);

                    }

                }
            }


            return  result;
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docScTemplateConfigEntity.getScId(),e);
        }
    }

    /**
     * 获取选择的 模板
     * @param docScTemplateConfigEntity
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject  getSltTemplateList(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException {
        try{
            JSONObject result=new JSONObject();
            //获取所有的模板
            List<DocTemplateModelEntity> allTemplateList= docTemplateModelService.findDocTemplateList( new DocTemplateModelEntity() );
            //获取已经选择的模板
            List<DocScTemplateConfigEntity>configedList=findConfigList(docScTemplateConfigEntity);


            //封装所有的模板集合
            JSONArray allTemplateGroup=divideTemplateAry(allTemplateList);
            //封装默认值
            List<List<String>>defVal=new ArrayList<>();
            int groupCnt=allTemplateGroup.size();
            for(int i=0;i<groupCnt;i++){
                List<String>tmpList=new ArrayList<>();
                tmpList.add("");
                defVal.add(tmpList);
            }
            if(configedList!=null){
                if(configedList.size()>0){

                    Map<String,String>tmpMap=new HashedMap();
                    for(DocScTemplateConfigEntity tag:configedList){
                        tmpMap.put(tag.getTemplateId(),tag.getTemplateId());
                    }
                    for(int i=0;i<groupCnt;i++){
                        JSONArray tmpArray=allTemplateGroup.getJSONObject(i).getJSONArray("data");
                        int tmpSubLen=tmpArray.size();
                        for(int j=0;j<tmpSubLen;j++){
                            String keyNo=tmpArray.getJSONObject(j).getString("value");
                            if(tmpMap.containsKey(keyNo)){
                                defVal.get(i).add(keyNo);
                            }
                        }
                    }



                }
            }

            result.put("sltTags",defVal);
            result.put("allTags",allTemplateGroup);

            return  result;
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docScTemplateConfigEntity.getScId(),e);
        }
    }


    /**
     *  把所有的模板按照类型分组
     * @param allTemplateList  所有的模板列表
     * @return
     * @throws ServiceException
     */
    private  JSONArray divideTemplateAry( List<DocTemplateModelEntity> allTemplateList)throws  ServiceException{

        JSONArray result=new JSONArray();

//        let groupData1 = {
//                name: "分组一",
//                data: [
//        { value: "value1", text: "选项1" },
//        { value: "value3", text: "选项3" },
//        { value: "value5", text: "选项5" },
//        { value: "value7", text: "选项7" },
//        { value: "value9", text: "选项9" },
//        ],
        Map<Object,Object> tagGroup= parmCacheUtil.getDicMap("DOC_TEMPLATE_KIND");
        JSONObject tmpResult=new JSONObject();
        if(allTemplateList!=null){
            for(DocTemplateModelEntity tag:allTemplateList){
                String groupNo=tag.getTemplateType();
                JSONObject option=new JSONObject();
                option.put("value",tag.getTemplateId());
                option.put("text",tag.getTemplateName());
                if(tmpResult.containsKey(groupNo)){
                    //如果存在该组号，直接加入组内
                    tmpResult.getJSONObject(groupNo).getJSONArray("data").add(option);
                }else{
                    //如果不存在，创建一个新的分组
                    JSONArray dataArray=new JSONArray();
                    dataArray.add(option);
                    JSONObject groupJson=new JSONObject();
                    groupJson.put("name",tagGroup.get(groupNo));
                    groupJson.put("data",dataArray);
                    tmpResult.put(groupNo,groupJson);


                }
            }

            //把jsonObject转换转化成JsonArray
            Iterator iterator=tmpResult.keySet().iterator();
            while(iterator.hasNext()){
                String groupNo=iterator.next().toString();
                result.add(tmpResult.getJSONObject(groupNo));


            }
        }



        return result;
    }


    /**
     * 插入选择的的模板
     * @param sltTemplate
     * @throws ServiceException
     */
    @Override
    public  void  saveSltTemplate(JSONArray sltTemplate)throws  ServiceException{
        if(sltTemplate!=null){
            int len=sltTemplate.size();
             if(len>0){
                DocScTemplateConfigEntity qBean=new DocScTemplateConfigEntity();
                qBean.setPrdUniqueVal(sltTemplate.getJSONObject(0).getString("prdUniqueVal"));
                qBean.setFlowNo(sltTemplate.getJSONObject(0).getString("flowNo"));
                qBean.setNodeNo(sltTemplate.getJSONObject(0).getString("nodeNo"));
                List<DocScTemplateConfigEntity> tmpList=  findConfigListByObj(qBean);
                if(tmpList!=null){
                    for(DocScTemplateConfigEntity delBean:tmpList){

                        deleteById(delBean.getScId());
                    }
                }
                for(int i=0;i<len;i++){


                    JSONObject object=sltTemplate.getJSONObject(i);
                    DocTemplateModelEntity modelEntity=docTemplateModelService.findById(object.getString("templateId"));
                    DocScTemplateConfigEntity addBean=new DocScTemplateConfigEntity();
                    BeanCopyUtil.copyProperties(modelEntity,addBean);
                    addBean.setPrdUniqueVal(object.getString("prdUniqueVal"));
                    addBean.setFlowNo(object.getString("flowNo"));
                    addBean.setNodeNo(object.getString("nodeNo"));
                    //删除原来模板的配置


                    //插入新的配置
                    insert(addBean);
                }
            }
        }
    }

    @Override
    public DocScTemplateConfigEntity findByEntity(DocScTemplateConfigEntity scTemplateConfigEntity) throws ServiceException {
        try{
            QueryWrapper<DocScTemplateConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("prd_unique_val",scTemplateConfigEntity.getPrdUniqueVal());
            queryWrapper.eq("node_no",scTemplateConfigEntity.getNodeNo());
            queryWrapper.eq("flow_no",scTemplateConfigEntity.getFlowNo());
            return docScTemplateConfigMapper.selectOne(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,scTemplateConfigEntity.getPrdUniqueVal(),e);
        }
    }


    @Override
    public void insert(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException {
        try{

            String tmpId=docScTemplateConfigEntity.getTemplateId();
            DocTemplateModelEntity templateModelEntity=docTemplateModelService.findById(tmpId);

           docScTemplateConfigEntity.setScId(UUIDUtil.getUUID());
            docScTemplateConfigEntity.setMultiFeign(templateModelEntity.getMultiFeign());
            docScTemplateConfigEntity.setMultiFeignMethod(templateModelEntity.getMultiFeignMethod());
            docScTemplateConfigEntity.setParmList(templateModelEntity.getParmList());
           docScTemplateConfigEntity.setCanWrite("0");
           docScTemplateConfigEntity.setIfMustRead("0");
           docScTemplateConfigEntity.setIfMustWrite("0");
            docScTemplateConfigEntity.setIfElectricSign(templateModelEntity.getIfElectricSign());
            docScTemplateConfigEntity.setPdfFlag(templateModelEntity.getPdfFlag());
            docScTemplateConfigEntity.setTemplateType(templateModelEntity.getTemplateType());
            docScTemplateConfigEntity.setUseSealFlag(templateModelEntity.getUseSealFlag());
            Date datetime=new Date();
            docScTemplateConfigEntity.setUpdateTime(datetime);
            docScTemplateConfigEntity.setCreateTime(datetime);
            docScTemplateConfigMapper.insert(docScTemplateConfigEntity);
            //如果有电签，需要同步电签列表同步过来
            if("1".equals(docScTemplateConfigEntity.getIfElectricSign())){
                DocEleSignConfigEntity qBean=new DocEleSignConfigEntity();
                qBean.setTemplateId(tmpId);
                List<DocEleSignConfigEntity> eleSignList=docEleSignConfigService.findListByObj(qBean);
                if(eleSignList!=null){
                    for(DocEleSignConfigEntity config:eleSignList){
                        DocScEleSignConfigEntity addScBean=new DocScEleSignConfigEntity();
                        BeanCopyUtil.copyProperties(config,addScBean);
                        addScBean.setId(UUIDUtil.getUUID());
                        addScBean.setTemplateId(tmpId);
                        addScBean.setPrdUniqueVal(docScTemplateConfigEntity.getPrdUniqueVal());
                        addScBean.setFlowNo(docScTemplateConfigEntity.getFlowNo());
                        addScBean.setNodeNo(docScTemplateConfigEntity.getNodeNo());
                        docScEleSignConfigService.insert(addScBean);
                    }
                }

            }

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docScTemplateConfigEntity.getScId(),e);
        }
    }

    @Override
    public void update(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException {
        try{
            docScTemplateConfigEntity.setUpdateTime(new Date());
            docScTemplateConfigMapper.updateById(docScTemplateConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docScTemplateConfigEntity.getScId(),e);
        }
    }

    @Override
    public DocScTemplateConfigEntity findById(String scId) throws ServiceException {
        try{
            return docScTemplateConfigMapper.selectById(scId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,scId,e);
        }
    }

    @Override
    public void deleteById(String scId) throws ServiceException {
        try{
            DocScTemplateConfigEntity tmpBean=findById(scId);
            docScEleSignConfigService.deleteByTemplateId(tmpBean.getTemplateId());
            docScTemplateConfigMapper.deleteById(scId);


        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,scId,e);
        }
    }

}