/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocTagColumnConfigEntity;
import cn.mftcc.doc.components.mould.service.DocTagColumnConfigService;

/**
 * 标签表格列对应字段配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-16 15:10:40
 */
@RestController
@RequestMapping("mould/docTagColumnConfig")
public class DocTagColumnConfigController {

    @Autowired
    private DocTagColumnConfigService docTagColumnConfigService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocTagColumnConfigEntity docTagColumnConfigEntity) {
        IPage<DocTagColumnConfigEntity> list = this.docTagColumnConfigService.findByPage(docTagColumnConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocTagColumnConfigEntity docTagColumnConfigEntity){
        this.docTagColumnConfigService.insert(docTagColumnConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocTagColumnConfigEntity docTagColumnConfigEntity){
        this.docTagColumnConfigService.update(docTagColumnConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{configId}")
    public R findById(@PathVariable("configId") String configId){
        DocTagColumnConfigEntity docTagColumnConfigEntity = this.docTagColumnConfigService.findById(configId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docTagColumnConfigEntity);
    }

    @RequestMapping("/deleteById/{configId}")
    public R deleteById(@PathVariable("configId") String configId){
        this.docTagColumnConfigService.deleteById(configId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}