/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 电子签章配置流程节点表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-08-28 17:37:58
 */
@Data
@TableName("doc_sc_ele_sign_config")
public class DocScEleSignConfigEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String id;
	/**
	 * 所属模板
	 */
	private String templateId;
	/**
	 * 产品唯一标识
	 */
	private String prdUniqueVal;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;
	/**
	 * 签章定位类型 1-单坐标2-多坐标3单关键字4多关键字
	 */
	private String positionType;
	/**
	 * 关键字
	 */
	private String keyWord;
	/**
	 * 横坐标
	 */
	private String coordinatesX;
	/**
	 * 纵坐标
	 */
	private String coordinatesY;
	/**
	 * 签章人类型 1-机构 2-企业 3-企业法人 4-企业法人配偶 5-企业实际控制人 6-企业实际控制人配偶
	 */
	private String esignerType;
	/**
	 * 签章类型 1-机构章 2-文本章
	 */
	private String esignType;
	/**
	 * 签章人员姓名
	 */
	private String esigner;
	/**
	 * 签章顺序
	 */
	private Integer esignSort;
	/**
	 * 是否必签 1-是 0-否
	 */
	private String mustFlag;
	/**
	 * 接收人姓名
	 */
	private String receiverName;
	/**
	 * 接收人联系方式
	 */
	private String receiverPhone;
	/**
	 * 创建时间时间（yyyy-mm-dd hh:mm:ss）
	 */
	private Date createTime;
	/**
	 * 更新时间（yyyy-mm-dd hh:mm:ss）
	 */
	private Date updateTime;

}
