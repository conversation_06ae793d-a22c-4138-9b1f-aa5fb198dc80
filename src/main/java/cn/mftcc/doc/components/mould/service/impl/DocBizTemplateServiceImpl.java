/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.hutool.core.codec.Base64Encoder;
import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.bizcommon.utils.BeanCopyUtil;
import cn.mftcc.bizcommon.utils.DateUtil;
import cn.mftcc.bizcommon.utils.UUIDUtil;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.MapperUtil;
import cn.mftcc.common.utils.RequestUtil;
import cn.mftcc.common.utils.SpringUtil;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.common.utils.OssConfig;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.service.DocFileInfService;
import cn.mftcc.doc.components.mould.entity.*;
import cn.mftcc.doc.components.mould.mapper.DocBizTemplateMapper;
import cn.mftcc.doc.components.mould.service.*;
import cn.mftcc.doc.feign.client.CreditFeignClient;
import cn.mftcc.doc.feign.client.FactorFeignClient;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;
import cn.mftcc.doc.feign.dto.DocBizEleSignConfigDto;
import cn.mftcc.doc.feign.dto.DocBizTemplateDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.lettuce.core.dynamic.support.ReflectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sun.misc.BASE64Encoder;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 业务模板关联表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-27 14:18:47
 */
@Service("docBizTemplateService")
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DocBizTemplateServiceImpl implements DocBizTemplateService {

    @Autowired
    private DocBizTemplateMapper docBizTemplateMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private DocScTemplateConfigService docScTemplateConfigService;
    @Autowired
    private DocTemplateModelService docTemplateModelService;
    @Autowired
    private CreditFeignClient creditFeignClient;
    @Autowired
    private DocEleSignConfigService docEleSignConfigService;
    @Autowired
    private DocBizEleSignConfigService docBizEleSignConfigService;
    @Autowired
    private DocTemplateEsignerListService docTemplateEsignerListService;
    @Autowired
    private OssConfig ossConfig;
    @Autowired
    private LeaseFeiginClient leaseInterface;
    @Autowired
    private RequestUtil requestUtil;
    @Autowired
    private DocFileInfService docFileInfService;
    @Autowired
    private FactorFeignClient factorFeignClient;
    @Value("${mftcc.template.mould-path:}")
    private String  mouldPath;
    private static String ADVANCE_NOTICE_TEMPLATE = "dc1a97668c2442309def74c014675c58";//垫付通知书模板ID


    @Override
    public IPage<DocBizTemplateEntity> findByPage(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocBizTemplateEntity> page = new Page<>();
            page.setCurrent(docBizTemplateEntity.getPageNo());
            page.setSize(docBizTemplateEntity.getPageSize());
            QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docBizTemplateEntity);
            return docBizTemplateMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docBizTemplateEntity.getBtId(),e);
        }
    }

    @Override
    public IPage<DocBizTemplateEntity> findPactFileByPage(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException {
        //翻页
        IPage<DocBizTemplateEntity> page = new Page<>();
        page.setCurrent(docBizTemplateEntity.getPageNo());
        page.setSize(docBizTemplateEntity.getPageSize());
        QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("constract_related_id",docBizTemplateEntity.getConstractRelatedId());
        queryWrapper.ne("type_no","FH-108");
        mapperUtil.tableQuery(queryWrapper,docBizTemplateEntity);
        IPage<DocBizTemplateEntity> docBizTemplateEntityIPage = docBizTemplateMapper.selectPage(page,queryWrapper);
        for(DocBizTemplateEntity entity:docBizTemplateEntityIPage.getRecords()){
            DocTemplateEsignerListEntity docTemplateEsignerListEntity = new DocTemplateEsignerListEntity();
            docTemplateEsignerListEntity.setTemplateBizId(entity.getBtId());
            List<DocTemplateEsignerListEntity> docTemplateEsignerListEntities = docTemplateEsignerListService.getEsignersList(docTemplateEsignerListEntity);
            if(docTemplateEsignerListEntities.isEmpty()){
                entity.setIfElectricSign("0");
            }else{
                entity.setIfElectricSign("1");
            }
        }
        return docBizTemplateEntityIPage;
    }

    /**
     * 判断 该节点的电签是否已经 全部完成
     * @param parm{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * }
     * @return 1-全部完成电签 0-没有完成
     * @throws ServiceException
     */
    @Override
    public   String ifNodeElesignFinished(JSONObject parm)throws ServiceException{
        String result="1";
        DocBizTemplateEntity docBizTemplateEntity=JSONObject.parseObject(parm.toJSONString(),DocBizTemplateEntity.class);
        docBizTemplateEntity.setEsignFinishFlag("0");
        docBizTemplateEntity.setIfElectricSign("1");
        List<DocBizTemplateEntity> tmpList=findList(docBizTemplateEntity);
        if(tmpList!=null){
            if(tmpList.size()>0){
                result="0";
            }
        }
        return  result;


    }

    /**
     * 获取列表
     * @param docBizTemplateEntity
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocBizTemplateEntity> findList(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException {
        try{
            //翻页

            QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
//            mapperUtil.tableQuery(queryWrapper,docBizTemplateEntity);
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getBizNo()),"biz_no",docBizTemplateEntity.getBizNo());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getSubBizNo()),"sub_biz_no",docBizTemplateEntity.getSubBizNo());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getPrdUniqueVal()),"prd_unique_val",docBizTemplateEntity.getPrdUniqueVal());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getFlowNo()),"flow_no",docBizTemplateEntity.getFlowNo());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getNodeNo()),"node_no",docBizTemplateEntity.getNodeNo());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getTemplateNo()),"template_no",docBizTemplateEntity.getTemplateNo());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getBtId()),"bt_id",docBizTemplateEntity.getBtId());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getIfElectricSign()),"if_electric_sign",docBizTemplateEntity.getIfElectricSign());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getEsignFinishFlag()),"esign_finish_flag",docBizTemplateEntity.getEsignFinishFlag());

            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getIfElectricSign()),"if_electric_sign",docBizTemplateEntity.getIfElectricSign());

            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getTemplateId()),"template_id",docBizTemplateEntity.getTemplateId());
            if(StringUtils.isNotEmpty(docBizTemplateEntity.getShowTemplateIds())){
                String[] idAry=docBizTemplateEntity.getShowTemplateIds().split(",");
//                StringBuilder sb=new StringBuilder();
//                for(String id:idAry){
//                    sb.append(",'");
//                    sb.append(id);
//                    sb.append("'");
//                }
//                String ids=sb.toString();
                if(idAry.length>0){
                    queryWrapper.in("template_id",idAry);
                }

            }

            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getBusRelatedId()),"bus_related_id",docBizTemplateEntity.getBusRelatedId());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getApplyRelatedId()),"apply_related_id",docBizTemplateEntity.getApplyRelatedId());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getConstractRelatedId()),"constract_related_id",docBizTemplateEntity.getConstractRelatedId());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getCusRelatedId()),"cus_related_id",docBizTemplateEntity.getCusRelatedId());
            queryWrapper.eq(StringUtils.isNotBlank(docBizTemplateEntity.getFinRelatedId()),"fin_related_id",docBizTemplateEntity.getFinRelatedId());
            queryWrapper.in(StringUtils.isNotBlank(docBizTemplateEntity.getTypeNo()),"type_no",docBizTemplateEntity.getTypeNo());

            return docBizTemplateMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docBizTemplateEntity.getBtId(),e);
        }
    }


    /**
     * 展示多个地方的模板
     * @param parmArray [{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * }]
     * @return
     * @throws ServiceException
     */
    @Override
    public  List<DocBizTemplateEntity> findMultiMouldList(JSONArray  parmArray)throws ServiceException{
        List<DocBizTemplateEntity> result=new ArrayList<>();

        if(parmArray!=null){
            List<String> bizNoList = new ArrayList<>();
            for(int i=0;i<parmArray.size();i++){
                JSONObject jsonObject=parmArray.getJSONObject(i);
                bizNoList.add(jsonObject.getString("bizNo"));
            }
            QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("biz_no",bizNoList);
            result = docBizTemplateMapper.selectList(queryWrapper);
        }
        return result;
    }

    /**
     * 根据 业务流水号 获取当前待签约的列表
     * @param id
     * @return
     * @throws ServiceException
     */
    @Override
     public  JSONArray  getCurWaitEleSignList(String  id)throws  ServiceException{
         JSONArray result=new JSONArray();
         DocBizTemplateEntity qBean=new DocBizTemplateEntity();
         qBean.setBizNo(id);
         qBean.setIfElectricSign("1");
         qBean.setEsignFinishFlag("0");
        try {
            JSONObject creditInfoByCreditId = creditFeignClient.getCreditInfoByCreditId(id);
            String busNodeNo = creditInfoByCreditId.getString("busNodeNo");
            qBean.setNodeNo(busNodeNo);
        } catch (Exception e) {
            e.printStackTrace();
        }
         List<DocBizTemplateEntity> eSignList= findList(qBean);
         if(eSignList!=null){

             for(DocBizTemplateEntity bean:eSignList){
                String  tmplateId=bean.getTemplateId();
                 DocBizEleSignConfigEntity subQbean=new DocBizEleSignConfigEntity();
                 subQbean.setTemplateId(tmplateId);
                 subQbean.setBizNo(id);
                 subQbean.setFinishFlag("0");
                 List<DocBizEleSignConfigEntity> eleSignConfigEntityList=docBizEleSignConfigService.findListByObj(subQbean);
                 bean.setEleSignConfigEntityList(eleSignConfigEntityList);
                 result.add(bean);
             }
         }
         return result;
     }


//     * @param initParm
//     * {

//                * }

    /**
     * 获取多笔业务不同类型下的模板集合
     * @param parmList
     * [{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * 	"templateNo":pact_001
     * }]
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray findTemplateArray(List<Map<String ,String>> parmList) throws ServiceException {
        try{
            JSONArray result=new JSONArray();
            if(parmList!=null){
                for (Map<String, String> parmMap:parmList){
                    String bizNo=parmMap.get("bizNo");
                    String prdUniqueVal=parmMap.get("prdUniqueVal");
                    String flowNo=parmMap.get("flowNo");
                    String nodeNo=parmMap.get("nodeNo");
                    String templateNo=parmMap.get("templateNo");
                    String busRelatedId=parmMap.get("busRelatedId");
                    String cusRelatedId=parmMap.get("cusRelatedId");
                    String applyRelatedId=parmMap.get("applyRelatedId");
                    String constractRelatedId=parmMap.get("constractRelatedId");
                    String finRelatedId=parmMap.get("finRelatedId");

                    DocBizTemplateEntity quereyBean=new DocBizTemplateEntity();
                    quereyBean.setBizNo(bizNo);
                    quereyBean.setPrdUniqueVal(prdUniqueVal);
                    quereyBean.setFlowNo(flowNo);
                    quereyBean.setNodeNo(nodeNo);
                    quereyBean.setTemplateNo(templateNo);
                    quereyBean.setBusRelatedId(busRelatedId);
                    quereyBean.setCusRelatedId(cusRelatedId);
                    quereyBean.setApplyRelatedId(applyRelatedId);
                    quereyBean.setConstractRelatedId(constractRelatedId);
                    quereyBean.setFinRelatedId(finRelatedId);

                    List<DocBizTemplateEntity> tmpList=findList(quereyBean);
                    if(tmpList !=null){
                        for(DocBizTemplateEntity file:tmpList){
                            result.add(file);
                        }
                    }
                }
            }

            return result;


        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, JSON.toJSONString(parmList),e);
        }
    }



    /**
     * 获取待签约列表
     * @param parmMap{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * }
     * @return
     */
    @Override
    public JSONArray findEsignWatiList(Map<String,String>parmMap )throws ServiceException {
        try{
            JSONArray result=new JSONArray();

                    String bizNo=parmMap.get("bizNo");
                    String prdUniqueVal=parmMap.get("prdUniqueVal");
                    String flowNo=parmMap.get("flowNo");
                    String nodeNo=parmMap.get("nodeNo");
//                    String templateNo=parmMap.get("templateNo");



                    DocBizTemplateEntity quereyBean=new DocBizTemplateEntity();
                    quereyBean.setBizNo(bizNo);
                    quereyBean.setPrdUniqueVal(prdUniqueVal);
                    quereyBean.setFlowNo(flowNo);
                    quereyBean.setNodeNo(nodeNo);
//                    quereyBean.setTemplateNo(templateNo);
                    List<DocBizTemplateEntity> tmpList=findList(quereyBean);
                    if(tmpList !=null){
                        for(DocBizTemplateEntity file:tmpList){
                            result.add(file);
                        }
                    }

            return result;


        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, JSON.toJSONString(parmMap),e);
        }
    }




    /**
     *
     * @param parmMap
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *      * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONArray   findBizMouldList(Map<String ,String>parmMap) throws ServiceException {
        DocBizTemplateEntity docBizTemplateEntity =new DocBizTemplateEntity();
        docBizTemplateEntity.setBizNo(parmMap.get("bizNo"));
        docBizTemplateEntity.setPrdUniqueVal(parmMap.get("prdUniqueVal"));
        docBizTemplateEntity.setFlowNo(parmMap.get("flowNo"));
        docBizTemplateEntity.setNodeNo(parmMap.get("nodeNo"));
        docBizTemplateEntity.setIfElectricSign(parmMap.get("ifElectricSign"));
        List<DocBizTemplateEntity>tmpList=findList(docBizTemplateEntity);

        return JSONArray.parseArray(JSON.toJSONString(tmpList));
    }

    /**
     * 获取渲染过的模板
     * @param parmMap
     * @return
     * @throws ServiceException
     */
    public  JSONArray   findBizMouldRenderList(Map<String ,String>parmMap) throws ServiceException {
        List<DocBizTemplateEntity>resultList=new ArrayList<>();
        DocBizTemplateEntity docBizTemplateEntity =new DocBizTemplateEntity();
        docBizTemplateEntity.setBizNo(parmMap.get("bizNo"));
        docBizTemplateEntity.setPrdUniqueVal(parmMap.get("prdUniqueVal"));
        docBizTemplateEntity.setFlowNo(parmMap.get("flowNo"));
        docBizTemplateEntity.setNodeNo(parmMap.get("nodeNo"));
        docBizTemplateEntity.setIfElectricSign(parmMap.get("ifElectricSign"));
        docBizTemplateEntity.setApplyRelatedId(parmMap.get("applyRelatedId"));
        docBizTemplateEntity.setCusRelatedId(parmMap.get("cusRelatedId"));
        docBizTemplateEntity.setConstractRelatedId(parmMap.get("constractRelatedId"));
        docBizTemplateEntity.setFinRelatedId(parmMap.get("finRelatedId"));
        docBizTemplateEntity.setBusRelatedId(parmMap.get("busRelatedId"));
        List<DocBizTemplateEntity>tmpList=findList(docBizTemplateEntity);
        //去除没有替换过书签的文档
        for(DocBizTemplateEntity obj:tmpList){
            DocTemplateModelEntity tmpTemplate=docTemplateModelService.findById(obj.getTemplateId());
            String fileName=tmpTemplate.getTemplateFileName();
            String  suffixName=new FileUtils().getFileSuffixName(fileName);
            String  saveFileName=obj.getTemplateId()+"_"+obj.getBizNo()+"_"+obj.getTemplateId()+"_"+obj.getTemplateNo()+"."+suffixName;


            if(StringUtils.isNotEmpty(obj.getSubBizNo())){
                 String  subBizNo=obj.getSubBizNo();
                 saveFileName=obj.getTemplateId()+"_"+obj.getTemplateId()+"_"+subBizNo+"_"+obj.getTemplateNo()+"."+suffixName;
             }

            String filePath=mouldPath+"saveMould/"+saveFileName;
            File tmpFile=new File(filePath);
            if(tmpFile.exists()){
                resultList.add(obj);
            }
        }
        return JSONArray.parseArray(JSON.toJSONString(resultList));
    }

    /**
     * 获取渲染过的模板
     * @param parmMaps
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONArray   findBizMouldMultiRenderList(List<Map<String ,String>>parmMaps) throws ServiceException{
        JSONArray result=new JSONArray();
        if(parmMaps!=null){
            for(Map<String ,String> parmMap:parmMaps){
                JSONArray subArray=findBizMouldRenderList(parmMap);
                result.addAll(subArray);
            }
        }
        return result;
    }


    /**
     * 修改电签属性
     * @param updList
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject updateEleSealInf (JSONArray updList)throws  ServiceException{
        JSONObject result=new JSONObject();
        result.put("code","0000");
        if(updList!=null){
            for(int i=0;i<updList.size();i++){
                JSONObject obj=updList.getJSONObject(i);
                DocBizTemplateEntity docBizTemplateEntity=JSONObject.toJavaObject(obj,DocBizTemplateEntity.class);
                update(docBizTemplateEntity);
            }
        }
        return result;

    }




    /**
     *  模板初始化的feign接口
     * @param initParm {prdUniqueVal:"1004_2",flowNo:"bus_Flow",nodeNo:"pactSign",bizNo:"pact_00001",
     *                 createUserNo:"",createUserName:"",createOrgNo:"",createOrgName:""}
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject initTemplate(String initParm)throws ServiceException{
        JSONObject result=new JSONObject();

        try{
            JSONObject parmJson=JSONObject.parseObject(initParm);//入参转化为json对象
            //首先获取该节点配置的所有模板
            DocScTemplateConfigEntity docScTemplateConfigEntity= new DocScTemplateConfigEntity();
            docScTemplateConfigEntity.setPrdUniqueVal(parmJson.getString("prdUniqueVal"));
            docScTemplateConfigEntity.setFlowNo(parmJson.getString("flowNo"));
            docScTemplateConfigEntity.setNodeNo(parmJson.getString("nodeNo"));
            String invoiceCheckFlag = parmJson.getString("invoiceCheckFlag");
            if(CommonConstant.YES_NO_Y.equals(invoiceCheckFlag)){
                docScTemplateConfigEntity.setTemplateType(parmJson.getString("templateType"));
            }

            String parmList=parmJson.getString("parmList");

            String bizNo=parmJson.getString("bizNo");

            MFLogger.info("模板初始化 入参："+JSONObject.toJSONString(docScTemplateConfigEntity));
            DocBizTemplateEntity qBean=new DocBizTemplateEntity();
            qBean.setPrdUniqueVal(parmJson.getString("prdUniqueVal"));
            qBean.setFlowNo(parmJson.getString("flowNo"));
            qBean.setNodeNo(parmJson.getString("nodeNo"));
            qBean.setBizNo(bizNo);
            List<DocBizTemplateEntity>bizList=findList(qBean);
            if(bizList.size()==0){
                //如果还没有配置
                MFLogger.info("模板初始化 入参："+JSONObject.toJSONString(docScTemplateConfigEntity));
//            JSONObject parmList=parmJson.getJSONObject("parmList");
                List<DocScTemplateConfigEntity> configList= docScTemplateConfigService.findConfigList(docScTemplateConfigEntity);

                MFLogger.info("需要初始化的模板："+JSONObject.toJSONString(configList));
                //封装业务的配置，并入库
                if(configList!=null){
                    Date tmpDate=new Date();
                    for(DocScTemplateConfigEntity configBean:configList){
                        DocBizTemplateEntity bizTemplateEntity=new DocBizTemplateEntity();

                        DocTemplateModelEntity tmpObj=docTemplateModelService.findById(configBean.getTemplateId());
                        BeanUtils.copyProperties(configBean,bizTemplateEntity);
                        if("UserTask_0j4wj9e".equals(configBean.getNodeNo())){
                            //项目类电子签
                            bizTemplateEntity.setNodeNo("contractPrint");
                            bizTemplateEntity.setFlowNo("apply_flow_split");
                        }
                        DocTemplateModelEntity modelEntity=docTemplateModelService.findById(configBean.getTemplateId());
                        bizTemplateEntity.setTemplateNo(modelEntity.getTemplateNo());
                        bizTemplateEntity.setTemplateName(modelEntity.getTemplateName());
                        bizTemplateEntity.setVersionNo(modelEntity.getVersionNo());
                        bizTemplateEntity.setBtId(UUIDUtil.getUUID());
                        bizTemplateEntity.setCreateOrgNo(parmJson.getString("createOrgNo"));
                        bizTemplateEntity.setCreateOrgName(parmJson.getString("createOrgName"));
                        bizTemplateEntity.setCreateUserNo(parmJson.getString("createUserNo"));
                        bizTemplateEntity.setCreateUserName(parmJson.getString("createUserName"));
                        bizTemplateEntity.setBizNo(bizNo);
                        bizTemplateEntity.setWriteFlag("0");
                        bizTemplateEntity.setReadFlag("0");
                        bizTemplateEntity.setCreateTime(tmpDate);
                        bizTemplateEntity.setUpdateTime(tmpDate);

                        bizTemplateEntity.setCusRelatedId(parmJson.getString("cusRelatedId"));
                        bizTemplateEntity.setBusRelatedId(parmJson.getString("busRelatedId"));
                        bizTemplateEntity.setApplyRelatedId(parmJson.getString("applyRelatedId"));
                        bizTemplateEntity.setConstractRelatedId(parmJson.getString("constractRelatedId"));
                        bizTemplateEntity.setFinRelatedId(parmJson.getString("finRelatedId"));
                        bizTemplateEntity.setTemplateSuffix(tmpObj.getTemplateSuffix());
                        bizTemplateEntity.setTypeName(tmpObj.getTypeName());
                        bizTemplateEntity.setTypeNo(tmpObj.getTypeNo());


//                    检查是否有子项 ，如果有 需要初始化子项

                        if(StringUtils.isEmpty(bizTemplateEntity.getMultiFeign())){

                            docBizTemplateMapper.insert(bizTemplateEntity);
                            if("1".equals(bizTemplateEntity.getIfElectricSign())){

                                initEsignTemplate(bizTemplateEntity,parmJson.getJSONObject("parmList"));
                            }
                        }else{
                            int subSize=initSubDocTemplate(bizTemplateEntity,parmJson.getJSONObject("parmList"));
                        }
                    }
                }
            }
            result.put("code","0000");
            result.put("msg","模板成功初始化");

        }catch (ServiceException e){
            MFLogger.error("SignConract-error-请求参数:"+initParm+"初始化模板失败",e);
            result.put("code","1111");
            result.put("msg","模板初始化失败");
            result.put("error",e);
            throw e;

        }

        return result;

    }

    @Override
    public JSONObject initTemplateJson(JSONObject initParm)throws ServiceException{
        JSONObject result=new JSONObject();

        try{

            JSONObject parmJson=initParm;//入参转化为json对象

            //首先获取该节点配置的所有模板
            DocScTemplateConfigEntity docScTemplateConfigEntity= new DocScTemplateConfigEntity();
            docScTemplateConfigEntity.setPrdUniqueVal(parmJson.getString("prdUniqueVal"));
            docScTemplateConfigEntity.setFlowNo(parmJson.getString("flowNo"));
            docScTemplateConfigEntity.setNodeNo(parmJson.getString("nodeNo"));

            String parmList=parmJson.getString("parmList");

            String bizNo=parmJson.getString("bizNo");


            DocBizTemplateEntity qBean=new DocBizTemplateEntity();
            qBean.setPrdUniqueVal(parmJson.getString("prdUniqueVal"));
            qBean.setFlowNo(parmJson.getString("flowNo"));
            qBean.setNodeNo(parmJson.getString("nodeNo"));
            qBean.setBizNo(bizNo);
            List<DocBizTemplateEntity>bizList=findList(qBean);
            if(bizList.size()==0){
                //如果还没有配置

                MFLogger.info("模板初始化 入参："+JSONObject.toJSONString(docScTemplateConfigEntity));
//            JSONObject parmList=parmJson.getJSONObject("parmList");
                List<DocScTemplateConfigEntity> configList= docScTemplateConfigService.findConfigList(docScTemplateConfigEntity);

                MFLogger.info("需要初始化的模板："+JSONObject.toJSONString(configList));
                //封装业务的配置，并入库
                if(configList!=null){
                    Date tmpDate=new Date();
                    for(DocScTemplateConfigEntity configBean:configList){
                        DocBizTemplateEntity bizTemplateEntity=new DocBizTemplateEntity();

                        DocTemplateModelEntity tmpObj=docTemplateModelService.findById(configBean.getTemplateId());
                        BeanUtils.copyProperties(configBean,bizTemplateEntity);
                        DocTemplateModelEntity modelEntity=docTemplateModelService.findById(configBean.getTemplateId());
                        bizTemplateEntity.setTemplateNo(modelEntity.getTemplateNo());
                        bizTemplateEntity.setTemplateName(modelEntity.getTemplateName());
                        bizTemplateEntity.setVersionNo(modelEntity.getVersionNo());
                        bizTemplateEntity.setBtId(UUIDUtil.getUUID());
                        bizTemplateEntity.setCreateOrgNo(parmJson.getString("createOrgNo"));
                        bizTemplateEntity.setCreateOrgName(parmJson.getString("createOrgName"));
                        bizTemplateEntity.setCreateUserNo(parmJson.getString("createUserNo"));
                        bizTemplateEntity.setCreateUserName(parmJson.getString("createUserName"));
                        bizTemplateEntity.setBizNo(bizNo);
                        bizTemplateEntity.setWriteFlag("0");
                        bizTemplateEntity.setReadFlag("0");
                        bizTemplateEntity.setCreateTime(tmpDate);
                        bizTemplateEntity.setUpdateTime(tmpDate);

                        bizTemplateEntity.setCusRelatedId(parmJson.getString("cusRelatedId"));
                        bizTemplateEntity.setBusRelatedId(parmJson.getString("busRelatedId"));
                        bizTemplateEntity.setApplyRelatedId(parmJson.getString("applyRelatedId"));
                        bizTemplateEntity.setConstractRelatedId(parmJson.getString("constractRelatedId"));
                        bizTemplateEntity.setFinRelatedId(parmJson.getString("finRelatedId"));
                        bizTemplateEntity.setTemplateSuffix(tmpObj.getTemplateSuffix());
                        bizTemplateEntity.setTypeNo(tmpObj.getTypeNo());
                        bizTemplateEntity.setTypeName(tmpObj.getTypeName());

//                    检查是否有子项 ，如果有 需要初始化子项

                        if(StringUtils.isEmpty(bizTemplateEntity.getMultiFeign())){

                            docBizTemplateMapper.insert(bizTemplateEntity);
                            if("1".equals(bizTemplateEntity.getIfElectricSign())){

                                initEsignTemplate(bizTemplateEntity,parmJson.getJSONObject("parmList"));
                            }
                        }else{
                            int subSize=initSubDocTemplate(bizTemplateEntity,parmJson.getJSONObject("parmList"));

                        }

//                    检查是否需要电签，如果有电签的话初始化电签的属性





                    }
                }
            }
            result.put("code","0000");
            result.put("msg","模板成功初始化");

        }catch (ServiceException e){
            result.put("code","1111");
            result.put("msg","模板初始化失败");
            result.put("error",e);
            throw e;

        }

        return result;

    }



    /**
     * 初始化模板的电签列表
     * @param docTemplateModelEntity
     * @throws ServiceException
     */
    private  void  initEsignTemplate(DocBizTemplateEntity docTemplateModelEntity,JSONObject parmList)throws ServiceException{
        DocEleSignConfigEntity qBean=new DocEleSignConfigEntity();
        qBean.setTemplateId(docTemplateModelEntity.getTemplateId());
        List<DocEleSignConfigEntity> eSignList=docEleSignConfigService.findListByObj(qBean);
        if(eSignList!=null){
            for(DocEleSignConfigEntity bean:eSignList){
                DocBizEleSignConfigEntity eBean=new DocBizEleSignConfigEntity();
                BeanCopyUtil.copyProperties(bean,eBean);
                eBean.setId(UUIDUtil.getUUID());
                eBean.setPrdUniqueVal(docTemplateModelEntity.getPrdUniqueVal());
                eBean.setFlowNo(docTemplateModelEntity.getFlowNo());
                eBean.setNodeNo(docTemplateModelEntity.getNodeNo());
                eBean.setBizNo(docTemplateModelEntity.getBizNo());
                eBean.setSubBizNo(docTemplateModelEntity.getSubBizNo());
                if(docTemplateModelEntity.getSubBizNo()!=null){

                    parmList.put("subBizNo",docTemplateModelEntity.getSubBizNo());
                }
                if(parmList!=null){

                    eBean.setParmList(parmList.toJSONString());//设置本次电签传入的所有的参数
                }
                docBizEleSignConfigService.insert(eBean);

            }
        }
    }
    /**
     * 插入子项配置
     * @param docTemplateModelEntity
     * @param parmList
     * @throws ServiceException
     */
    private int initSubDocTemplate(DocBizTemplateEntity docTemplateModelEntity,JSONObject parmList )throws ServiceException{
            int result=0;
            if(StringUtils.isNotEmpty(docTemplateModelEntity.getMultiFeign())){
                String serviceName = docTemplateModelEntity.getMultiFeign();
                String methodName=docTemplateModelEntity.getMultiFeignMethod();
                parmList.put("templateId",docTemplateModelEntity.getTemplateId());
                MFLogger.info("服务名称:"+serviceName+",方法名称:"+methodName+",参数:"+JSONObject.toJSONString(parmList));
                Method method = ReflectionUtils.findMethod(SpringUtil.getBean(serviceName).getClass(),methodName, JSONObject.class);
                JSONArray queryResult = null;
                if (method!=null){
                     queryResult = (JSONArray) ReflectionUtils.invokeMethod(method,SpringUtil.getBean(serviceName),parmList);
                }
//                JSONArray queryResult = creditFeignClient.getMultiMouldTest(parmList);
                if(queryResult!=null){
                    int subLen=queryResult.size();
                    if(subLen>0){
                        String orignalTemplateName=docTemplateModelEntity.getTemplateName();
                        for(int i=0;i<subLen;i++){
                            JSONObject tmpJson=queryResult.getJSONObject(i);

                            String wid=tmpJson.getString("wid");
                            String name=tmpJson.getString("name");
                            docTemplateModelEntity.setBtId(UUIDUtil.getUUID());
                            docTemplateModelEntity.setSubBizNo(wid);
                            String templateName= orignalTemplateName;
                            if(StringUtils.isNotEmpty(name)){
                                templateName= orignalTemplateName+"("+name+")";
                            }
                            docTemplateModelEntity.setTemplateName(templateName);
                            docTemplateModelEntity.setSubTemplateName(name);
                            insert(docTemplateModelEntity);

                            if("1".equals(docTemplateModelEntity.getIfElectricSign())){
                                initEsignTemplate(docTemplateModelEntity,parmList);
                            }
                            result++;

                        }
                    }
                }


            }
            return result;
    }

    /**
     * 检查节点下所有模板是否读取，填写完毕（是否必填，是否已填， 是否必读，是否已读，是否可编辑。）
     * @param parmMap
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *      * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject checkBizTemplateStatus( Map<String ,String> parmMap)throws  ServiceException{

        DocBizTemplateEntity docBizTemplateEntity=new DocBizTemplateEntity();

        docBizTemplateEntity.setPrdUniqueVal(parmMap.get("prdUniqueVal"));
        docBizTemplateEntity.setFlowNo(parmMap.get("flowNo"));
        docBizTemplateEntity.setNodeNo(parmMap.get("nodeNo"));
        docBizTemplateEntity.setBizNo(parmMap.get("bizNo"));
        JSONObject result=new JSONObject();
        result.put("code","0000");
        result.put("msg","success");
        List<DocBizTemplateEntity> tmplateList=findList(docBizTemplateEntity);
        String readTemplateName="";
        String writeTemplateName="";

        if(tmplateList!=null){
            for(DocBizTemplateEntity obj:tmplateList){
                if("1".equals(obj.getIfMustRead()) && "0".equals(obj.getReadFlag())){
                    //处理必读未读
                    readTemplateName+=","+obj.getTemplateName();
                }

                if("1".equals(obj.getIfMustWrite()) && "0".equals(obj.getWriteFlag())){
                    //处理必填未填
                    writeTemplateName+=","+obj.getTemplateName();
                }
            }
        }
        String  msgInfo="";
        if(StringUtils.isNotEmpty(readTemplateName)){
            msgInfo+=readTemplateName.substring(1)+"未读";

        }
        if(StringUtils.isNotEmpty(writeTemplateName)){
            msgInfo+=writeTemplateName.substring(1)+"未填写";

        }
        if(msgInfo.length()>0){
            result.put("code","1111");
            result.put("msg",msgInfo);
        }

        return result;
    }






    @Override
    public void insert(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException {
        try{
            docBizTemplateMapper.insert(docBizTemplateEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docBizTemplateEntity.getBtId(),e);
        }
    }

    @Override
    public void update(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException {
        try{
            docBizTemplateMapper.updateById(docBizTemplateEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docBizTemplateEntity.getBtId(),e);
        }
    }

    @Override
    public void updateByBean(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException {
        try {
            QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotEmpty(docBizTemplateEntity.getBtId()), "bt_id", docBizTemplateEntity.getBtId());
            queryWrapper.eq(StringUtils.isNotEmpty(docBizTemplateEntity.getTemplateId()), "template_id", docBizTemplateEntity.getTemplateId());
            queryWrapper.eq(StringUtils.isNotEmpty(docBizTemplateEntity.getBizNo()), "biz_no", docBizTemplateEntity.getBizNo());
            queryWrapper.eq(StringUtils.isNotEmpty(docBizTemplateEntity.getSubBizNo()), "sub_biz_no", docBizTemplateEntity.getSubBizNo());
            docBizTemplateMapper.update(docBizTemplateEntity, queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docBizTemplateEntity.getBtId(),e);
        }
    }

    public void updateByPactId(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException {
        try {
            QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotEmpty(docBizTemplateEntity.getConstractRelatedId()), "constract_related_id", docBizTemplateEntity.getConstractRelatedId());
            queryWrapper.ne("oss_remove_status","1");
            docBizTemplateMapper.update(docBizTemplateEntity, queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docBizTemplateEntity.getBtId(),e);
        }
    }
    public void updateByOssStatus(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException {
        try {
            QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
            if("3".equals(docBizTemplateEntity.getOssRemoveStatus())){
                //提前结清/还款计划变更单独生成新的租金支付表，只更新租金支付表状态
                queryWrapper.eq(StringUtils.isNotEmpty(docBizTemplateEntity.getConstractRelatedId()), "constract_related_id", docBizTemplateEntity.getConstractRelatedId());
                queryWrapper.eq("template_id","d8f59c946bed4b6ea062735d21b25c5b");
                docBizTemplateEntity.setOssRemoveStatus("0");
                docBizTemplateMapper.update(docBizTemplateEntity, queryWrapper);
            }else if("4".equals(docBizTemplateEntity.getOssRemoveStatus())){
                //提前结清/还款计划变更单独生成新的租金支付表，只更新租金支付表状态
                queryWrapper.eq(StringUtils.isNotEmpty(docBizTemplateEntity.getConstractRelatedId()), "constract_related_id", docBizTemplateEntity.getConstractRelatedId());
                queryWrapper.eq("template_id","70f322daaed64da5b5f871e828600722");
                docBizTemplateEntity.setOssRemoveStatus("0");
                docBizTemplateMapper.update(docBizTemplateEntity, queryWrapper);
            }else{
                queryWrapper.eq(StringUtils.isNotEmpty(docBizTemplateEntity.getConstractRelatedId()), "constract_related_id", docBizTemplateEntity.getConstractRelatedId());
                queryWrapper.ne(StringUtils.isNotEmpty(docBizTemplateEntity.getOssRemoveStatus()), "oss_remove_status", "1");
                docBizTemplateMapper.update(docBizTemplateEntity, queryWrapper);
            }
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docBizTemplateEntity.getBtId(),e);
        }
    }

    @Override
    public DocBizTemplateEntity findById(String btId) throws ServiceException {
        try{
            return docBizTemplateMapper.selectById(btId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,btId,e);
        }
    }

    @Override
    public void deleteById(String btId) throws ServiceException {
        try{
            docBizTemplateMapper.deleteById(btId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,btId,e);
        }
    }

    /**
     * 删除模板
     * @param object
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *          "subBizNo" :""
     *      * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  void  deleteByBean(JSONObject object)throws  ServiceException{
        MFLogger.info("删除模板信息" + JSONObject.toJSONString(object));
        if(StringUtils.isEmpty(object.getString("bizNo"))){
            return ;
        }
        if(StringUtils.isEmpty(object.getString("ifUseNodeNo"))){
            if(StringUtils.isEmpty(object.getString("nodeNo"))){
                return ;
            }
        }
        QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("prdUniqueVal")),"prd_unique_val",object.getString("prdUniqueVal"));
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("flowNo")),"flow_no",object.getString("flowNo"));
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("nodeNo")),"node_no",object.getString("nodeNo"));
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("bizNo")),"biz_no",object.getString("bizNo"));
        queryWrapper.eq(StringUtils.isNotEmpty(object.getString("subBizNo")),"sub_biz_no",object.getString("subBizNo"));
        docBizTemplateMapper.delete(queryWrapper);

        //删除所有的电签的模板配置
        docBizEleSignConfigService.deleteByBean(object);

    }

    /**
     * 修改和业务模板的属性
     * @param templateInfo
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject updateBizTemplateInfo(JSONObject templateInfo)throws ServiceException{
        JSONObject res=new JSONObject();
        if(templateInfo!=null){
            DocBizTemplateEntity updBean = JSON.toJavaObject(templateInfo, DocBizTemplateEntity.class);
            this.updateByBean(updBean);
            res.put("code","0000");
            res.put("msg","success");
        }else{
            res.put("code","1111");
            res.put("msg","没有要修改的信息");
        }
        return  res;
    }

    @Override
    public JSONObject updateBizTemplateInfoByPactId(JSONObject templateInfo) throws ServiceException {
        JSONObject res=new JSONObject();
        if(templateInfo!=null){
            DocBizTemplateEntity updBean = JSON.toJavaObject(templateInfo, DocBizTemplateEntity.class);
            this.updateByPactId(updBean);
            res.put("code","0000");
            res.put("msg","success");
        }else{
            res.put("code","1111");
            res.put("msg","没有要修改的信息");
        }
        return  res;
    }

    @Override
    public JSONObject updateBizTemplateInfoByOssStatus(JSONObject templateInfo) throws ServiceException {
        JSONObject res=new JSONObject();
        if(templateInfo!=null){
            DocBizTemplateEntity updBean = JSON.toJavaObject(templateInfo, DocBizTemplateEntity.class);
            this.updateByOssStatus(updBean);
            res.put("code","0000");
            res.put("msg","success");
        }else{
            res.put("code","1111");
            res.put("msg","没有要修改的信息");
        }
        return  res;
    }


    /**
     * 获取当前待签约模板数据
     * @param jsonObject{
     *                 templateId-模板流水号
     *                  bizNo-业务编号
     *                  subBizNo-子项业务流水号
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getCurWaitEleSign(JSONObject jsonObject) throws ServiceException {

        String templateId = jsonObject.getString("templateId");
        String bizNo = jsonObject.getString("bizNo");
        String subBizNo = jsonObject.getString("subBizNo");
        DocBizTemplateEntity docBizTemplateEntity = new DocBizTemplateEntity();
        docBizTemplateEntity.setTemplateId(templateId);
        docBizTemplateEntity.setBizNo(bizNo);
        docBizTemplateEntity.setSubBizNo(subBizNo);
        List<DocBizTemplateEntity> docBizTemplateList = findList(docBizTemplateEntity);
        jsonObject.put("docBizTemplateList", docBizTemplateList);
        return jsonObject;
    }
    @Override
    public JSONArray getCurrentSignedUpList(String appId) throws ServiceException {
        QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no",appId);
        queryWrapper.eq("if_electric_sign","1" );// 是否电签0-否; 1-是
        queryWrapper.eq("esign_finish_flag","1" );// 是否电签0-否; 1-是
        List<DocBizTemplateEntity> docBizTemplateEntities = docBizTemplateMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(docBizTemplateEntities)){
            return new JSONArray();
        }
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(docBizTemplateEntities));
        return jsonArray;
    }

    @Override
    public JSONObject isInitTemplate(JSONObject map) {
        JSONObject result=new JSONObject();
        try{
            LambdaQueryWrapper<DocBizTemplateEntity> qw = new LambdaQueryWrapper<>();
            qw.eq(DocBizTemplateEntity::getBizNo,map.getString("bizNo"));
            qw.eq(DocBizTemplateEntity::getPrdUniqueVal,map.getString("prdUniqueVal"));
            qw.eq(DocBizTemplateEntity::getFlowNo,map.getString("flowNo"));
            qw.eq(DocBizTemplateEntity::getNodeNo,map.getString("nodeNo"));
            List<DocBizTemplateEntity> docBizTemplateEntities = docBizTemplateMapper.selectList(qw);
            if (docBizTemplateEntities.size()<1){
                //封装业务的配置，并入库
                result.put("code","1111");
                result.put("msg","未初始化");
            }else {
                //封装业务的配置，并入库
                result.put("code","0000");
                result.put("msg","已经初始化");
            }

        }catch (ServiceException e){
            result.put("code","1111");
            result.put("msg","查询失败");
            result.put("error",e);
            throw e;
        }
        return result;
    }

    /**
     * 根据 业务流水号和 nodeNo 获取当前待签约的列表
     * @param id
     * @return
     * @throws ServiceException
     */
    @Override
    public  List<DocBizTemplateDto>  getCurWaitEleSignList(String  id, String nodeNo)throws  ServiceException{
        List<DocBizTemplateDto> docBizTemplateDtoList = new ArrayList<>();
        DocBizTemplateEntity qBean=new DocBizTemplateEntity();
        qBean.setBizNo(id);
//        qBean.setIfElectricSign("1");
        qBean.setEsignFinishFlag("-1");
        qBean.setNodeNo(nodeNo);
        List<DocBizTemplateEntity> eSignList= findList(qBean);
        if(eSignList!=null){

            for(DocBizTemplateEntity bean:eSignList){
                String  tmplateId=bean.getTemplateId();
                DocBizEleSignConfigEntity subQbean=new DocBizEleSignConfigEntity();
                subQbean.setTemplateId(tmplateId);
                subQbean.setBizNo(id);
                subQbean.setSubBizNo(bean.getSubBizNo());
                subQbean.setFinishFlag("0");
                DocBizTemplateDto docBizTemplateDto = new DocBizTemplateDto();
                BeanUtils.copyProperties(bean,docBizTemplateDto);
                List<DocBizEleSignConfigDto> docBizEleSignConfigDtoList = new ArrayList<>();
                List<DocBizEleSignConfigEntity> eleSignConfigEntityList=docBizEleSignConfigService.findListByObj(subQbean);
                for (int i = 0; i < eleSignConfigEntityList.size(); i++) {
                    DocBizEleSignConfigEntity docBizEleSignConfigEntity =eleSignConfigEntityList.get(i);
                    DocBizEleSignConfigDto docBizEleSignConfigDto = new DocBizEleSignConfigDto();
                    BeanUtils.copyProperties(docBizEleSignConfigEntity,docBizEleSignConfigDto);
                    docBizEleSignConfigDtoList.add(docBizEleSignConfigDto);
                }
                docBizTemplateDto.setEleSignConfigEntityList(docBizEleSignConfigDtoList);
                docBizTemplateDtoList.add(docBizTemplateDto);
            }
        }
        return docBizTemplateDtoList;
    }

    @Override
    public String readTemplateBase64ByBtId(String fileId) throws ServiceException {
        String fieBase64 = "";
        StringBuffer fileUrlPath = new StringBuffer();
        DocFileInfEntity docFileInfEntity = docFileInfService.findById(fileId);
        InputStream inputStream = null;
        try{
            fileUrlPath.append(docFileInfEntity.getFilePath());
            inputStream = ossConfig.readOss(fileUrlPath.toString());
            //得到图片的二进制数据，以二进制封装得到数据，具有通用性
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            //创建一个Buffer字符串
            byte[] buffer = new byte[1024];
            //每次读取的字符串长度，如果为-1，代表全部读取完毕
            int len = 0;
            //使用一个输入流从buffer里把数据读取出来
            while ((len = inputStream.read(buffer)) != -1) {
                //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                outStream.write(buffer, 0, len);
            }
            //关闭输入流
            inputStream.close();
            byte[] data = outStream.toByteArray();
            //对字节数组Base64编码
            fieBase64 = Base64Encoder.encode(data);
        }catch (Exception e){
            MFLogger.error("读取文件Base64信息失败，bizNo：" + docFileInfEntity.getBizNo()+"文件："+docFileInfEntity.getTypeName(), e);
        }finally {
            try{
                inputStream.close();
            }catch (IOException e){
                e.printStackTrace();
            }
        }
        return fieBase64;
    }

    @Override
    public String readEndPactTemplateBase64(String btId) throws ServiceException {
        String fieBase64 = "";
        StringBuffer fileUrlPath = new StringBuffer();
        DocBizTemplateEntity docBizTemplateEntity = findById(btId);
        fileUrlPath.append(docBizTemplateEntity.getSavePath()).append(docBizTemplateEntity.getSaveFileName());
        InputStream inputStream = null;
        try{
            //判断是从obs还是oss下载
            if (!"1".equals(docBizTemplateEntity.getOssRemoveStatus())) {
                fieBase64 = FileUtils.encodeBase64File(fileUrlPath.toString());
            } else {
                inputStream = ossConfig.readOss(fileUrlPath.toString());
                //得到图片的二进制数据，以二进制封装得到数据，具有通用性
                ByteArrayOutputStream outStream = new ByteArrayOutputStream();
                //创建一个Buffer字符串
                byte[] buffer = new byte[1024];
                //每次读取的字符串长度，如果为-1，代表全部读取完毕
                int len = 0;
                //使用一个输入流从buffer里把数据读取出来
                while ((len = inputStream.read(buffer)) != -1) {
                    //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                    outStream.write(buffer, 0, len);
                }
                //关闭输入流
                inputStream.close();
                byte[] data = outStream.toByteArray();
                //对字节数组Base64编码
                fieBase64 = Base64Encoder.encode(data);
            }
        }catch (Exception e){
            throw new ServiceException("下载合同结清通知函失败",btId,e);
        }
        return fieBase64;
    }

    @Override
    public  List<DocBizTemplateEntity> readTemplateBase64(Map<String ,String> map)throws  ServiceException{
        String  fileBase64 = "";
        List<DocBizTemplateEntity> tmpList;
        try{
            DocBizTemplateEntity docBizTemplateEntity =new DocBizTemplateEntity();
            docBizTemplateEntity.setBizNo(map.get("bizNo"));
            docBizTemplateEntity.setPrdUniqueVal(map.get("prdUniqueVal"));
            docBizTemplateEntity.setFlowNo(map.get("flowNo"));
            docBizTemplateEntity.setNodeNo(map.get("nodeNo"));
            docBizTemplateEntity.setIfElectricSign(map.get("ifElectricSign"));
            tmpList=findList(docBizTemplateEntity);

            for (DocBizTemplateEntity bizTemplateEntity : tmpList) {
                fileBase64 = "";
                String fileUrlPath=bizTemplateEntity.getSavePath()+bizTemplateEntity.getSaveFileName();
                fileBase64= FileUtils.encodeBase64File(fileUrlPath);
                bizTemplateEntity.setBase64File(fileBase64);
            }

        }catch (Exception e){
            MFLogger.error("读取模板文件Base64信息失败 map="+map,e);
            throw new ServiceException("读取模板文件Base64信息失败",map,e);
        }
        return tmpList;
    }

    @Override
    public void initAdvanceNoticeTemplate(JSONObject jsonObject) throws ServiceException {
        DocTemplateModelEntity modelEntity = docTemplateModelService.findById(ADVANCE_NOTICE_TEMPLATE);
        DocBizTemplateEntity bizTemplateEntity=new DocBizTemplateEntity();
        BeanUtils.copyProperties(modelEntity,bizTemplateEntity);
        bizTemplateEntity.setBtId(UUIDUtil.getUUID());
        bizTemplateEntity.setNodeNo("advanceNotice");//垫付通知
        bizTemplateEntity.setCreateUserNo((String) requestUtil.getUserInfo("opNo"));
        bizTemplateEntity.setCreateUserName((String) requestUtil.getUserInfo("opName"));
        bizTemplateEntity.setCreateOrgNo((String) requestUtil.getUserInfo("brNo"));
        bizTemplateEntity.setCreateOrgName((String) requestUtil.getUserInfo("brName"));
        bizTemplateEntity.setBizNo(jsonObject.getString("advanceAppId"));// 垫付申请ID
        bizTemplateEntity.setWriteFlag("0");
        bizTemplateEntity.setReadFlag("0");
        bizTemplateEntity.setCreateTime(DateUtil.getCurrDateTime());
        bizTemplateEntity.setUpdateTime(DateUtil.getCurrDateTime());

        docBizTemplateMapper.insert(bizTemplateEntity);
    }

    @Override
    public DocBizTemplateDto getDocBizTemplateDtoBy(DocBizTemplateDto bizTemplateEntity) throws ServiceException {
        DocBizTemplateEntity docBizTemplateEntity = new DocBizTemplateEntity();
        BeanUtils.copyProperties(bizTemplateEntity,docBizTemplateEntity);
        QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("biz_no",docBizTemplateEntity.getBizNo());
        queryWrapper.eq("node_no",docBizTemplateEntity.getNodeNo());
        queryWrapper.last("limit 1");
        DocBizTemplateEntity templateEntity = docBizTemplateMapper.selectOne(queryWrapper);
        if(templateEntity == null){
            return null;
        }
        BeanUtils.copyProperties(templateEntity,bizTemplateEntity);
        return bizTemplateEntity;
    }

    @Override
    public List<DocFileInfEntity> getDocIdFileByContractIdAndTypeNo(String constractRelatedId) throws Exception {
        return docBizTemplateMapper.getDocIdFileByContractIdAndTypeNo(constractRelatedId);
    }

    @Override
    public List<DocBizTemplateEntity> findToDoRemoveFileInfo() throws ServiceException{
        return docBizTemplateMapper.findToDoRemoveFileInfo();
    }

    @Override
    public DocBizTemplateEntity findByFileName(String fileName) throws ServiceException {
        QueryWrapper<DocBizTemplateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("save_file_name", fileName);
        DocBizTemplateEntity docBizTemplateEntitie = docBizTemplateMapper.selectOne(queryWrapper);
        return docBizTemplateEntitie;
    }
}