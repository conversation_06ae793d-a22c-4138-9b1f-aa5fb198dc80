/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 模板标签校验信息表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-10-22 11:13:58
 */
@Data
@TableName("doc_template_tag_check")
public class DocTemplateTagCheckEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String templateCheckNo;
	/**
	 * 模板流水号
	 */
	private String templateId;
	/**
	 * 模板编号
	 */
	private String templateNo;

	/**
	 * 版本号
	 */
	private String versionNo;
	/**
	 * 流水号
	 */
	private String serialId;
	/**
	 * 标签key编号
	 */
	private String keyNo;
	/**
	 * 标签key名称
	 */
	private String tagKeyName;
	/**
	 * 分组标识目前按照01-客户、02-合同、03-押品等分类
	 */
	private String groupFlag;
	/**
	 * 启用标识0-未启用，1-已启用
	 */
	private String useFlag;
	/**
	 * 是否允许为空 0-允许，1-不允许
	 */
	private String checkNullFlag;

	/**
	 * 展示样式 1-文字2-图片，3-表格，4-循环列表 5-循环的文字6-勾选项7-循环的不规则表格
	 */
	private String showStyle;

	/**
	 * 提示语
	 */
	private String prompts;
	/**
	 * 正则表达式校验规则
	 */
	private String checkRule;

	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 修改者号
	 */
	private String updateOpNo;
	/**
	 * 修改者姓名
	 */
	private String updateOpName;

}
