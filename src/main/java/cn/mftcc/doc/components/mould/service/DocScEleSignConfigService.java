/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocScEleSignConfigEntity;

import cn.mftcc.common.exception.ServiceException;

/**
 * 电子签章配置流程节点表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-28 17:37:58
 */
public interface DocScEleSignConfigService {

    IPage<DocScEleSignConfigEntity> findByPage(DocScEleSignConfigEntity docScEleSignConfigEntity) throws ServiceException;

    void insert(DocScEleSignConfigEntity docScEleSignConfigEntity) throws ServiceException;

    void update(DocScEleSignConfigEntity docScEleSignConfigEntity) throws ServiceException;

    DocScEleSignConfigEntity findById(String id) throws ServiceException;

    void deleteById(String id) throws ServiceException;

    void deleteByTemplateId(String templateId) throws ServiceException;
}

