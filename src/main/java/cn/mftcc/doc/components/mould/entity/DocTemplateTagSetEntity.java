/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-15 19:02:36
 */
@Data
@TableName("doc_template_tag_set")
public class DocTemplateTagSetEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String serialNo;
	/**
	 * 模板流水号
	 */
	private String templateId;
	/**
	 * 模板编号
	 */
	private String templateNo;
	/**
	 * 版本号
	 */
	private String versionNo;
	/**
	 * 标签key编号集合
	 */
	private String tagKeyNo;
	/**
	 * 机构编号
	 */
	private String corpId;
	/**
	 * 机构名称
	 */
	private String corpName;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;

}
