/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 电子签章配置业务关联表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-08-28 17:36:42
 */
@Data
@TableName("doc_biz_ele_sign_config")
public class DocBizEleSignConfigEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String id;
	/**
	 * 所属模板
	 */
	private String templateId;
	/**
	 * 业务流水号
	 */
	private String bizNo;
	/**
	 * 子项业务流水号
	 */
	private String subBizNo;
	/**
	 * 产品唯一标识
	 */
	private String prdUniqueVal;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;
	/**
	 * 签章定位类型 1-单坐标2-多坐标3单关键字4多关键字
	 */
	private String positionType;
	/**
	 * 关键字
	 */
	private String keyWord;
	/**
	 * 横坐标
	 */
	private String coordinatesX;
	/**
	 * 纵坐标
	 */
	private String coordinatesY;
	/**
	 * 签章人类型 1-银达信,2-银达信法人,3-保理公司,4-保理公司法人,5-企业法人,6-企业法人配偶,7-企业实际控制人配偶,8-企业,9-企业实际控制人
	 */
	private String esignerType;
	/**
	 * 签章类型 1-机构章 2-文本章
	 */
	private String esignType;
	/**
	 * 签章人员姓名
	 */
	private String esigner;
	/**
	 * 签章顺序
	 */
	private Integer esignSort;
	/**
	 * 是否必签 1-是 0-否
	 */
	private String mustFlag;

	/**
	 * 是否完成电签 1-是 0-否
	 */
	private String finishFlag;

	/**
	 * 电签完成时间（yyyy-mm-dd hh:mm:ss）
	 */
	private Date finishTime;
	/**
	 * 接收人姓名
	 */
	private String receiverName;
	/**
	 * 接收人联系方式
	 */
	private String receiverPhone;
	/**
	 * 创建时间时间（yyyy-mm-dd hh:mm:ss）
	 */
	private Date createTime;
	/**
	 * 更新时间（yyyy-mm-dd hh:mm:ss）
	 */
	private Date updateTime;

	/**
	 * 人脸识别图片OBSID'
	 */
	private String faceObsId;

	/**
	 *人脸识别图片HASH值
	 */
	private String faceHash;
	/**
	 * 外部电签系统签章编号
	 */
	private String extSignNo;
	/**
	 * 签约文档后的obsId
	 */
	private String docObsId;
	/**
	 * 多份模板feigin接口
	 */
	private String multiFeign;
	/**
	 * 多份模板feigin接口调用的方法
	 */
	private String multiFeignMethod;
	/**
	 * 多份模板参数列表
	 */
	private String parmList;

}
