/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import cn.mftcc.bizcommon.utils.BeanCopyUtil;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import cn.mftcc.doc.components.mould.mapper.DocTemplateModelMapper;
import cn.mftcc.doc.components.mould.service.DocTemplateModelService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocScTemplateConfigEntity;
import cn.mftcc.doc.components.mould.service.DocScTemplateConfigService;

import java.util.List;

/**
 * 场景的模板配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-25 17:30:20
 */
@RestController
@RequestMapping("mould/docScTemplateConfig")
public class DocScTemplateConfigController {

    @Autowired
    private DocScTemplateConfigService docScTemplateConfigService;
    @Autowired
    private DocTemplateModelMapper docTemplateModelMapper;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocScTemplateConfigEntity docScTemplateConfigEntity) {
        IPage<DocScTemplateConfigEntity> list = this.docScTemplateConfigService.findByPage(docScTemplateConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/findConfigList")
    public R findConfigList(@RequestBody DocScTemplateConfigEntity docScTemplateConfigEntity) {
        List<DocScTemplateConfigEntity> list = this.docScTemplateConfigService.findConfigList(docScTemplateConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/getWaitSltTemplateList")

    public R getWaitSltTemplateList(@RequestBody DocScTemplateConfigEntity docScTemplateConfigEntity) {
       JSONObject jsonObject = this.docScTemplateConfigService.getSltTemplateList(docScTemplateConfigEntity);
        MFLogger.info(jsonObject.toJSONString());
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("json", jsonObject);
    }



    @RequestMapping("/insert")
    public R insert(@RequestBody DocScTemplateConfigEntity docScTemplateConfigEntity){

        DocTemplateModelEntity modelEntity=docTemplateModelMapper.selectById(docScTemplateConfigEntity.getTemplateId());
        DocScTemplateConfigEntity addBean=new DocScTemplateConfigEntity();
        BeanCopyUtil.copyProperties(modelEntity,addBean);
        addBean.setPrdUniqueVal(docScTemplateConfigEntity.getPrdUniqueVal());
        addBean.setFlowNo(docScTemplateConfigEntity.getFlowNo());
        addBean.setNodeNo(docScTemplateConfigEntity.getNodeNo());
        this.docScTemplateConfigService.insert(addBean);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/saveSltTemplate")
    public R saveSltTemplate(@RequestBody JSONArray sltTemplate){


        this.docScTemplateConfigService.saveSltTemplate(sltTemplate);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }





    @RequestMapping("/update")
    public R update(@RequestBody DocScTemplateConfigEntity docScTemplateConfigEntity){
        this.docScTemplateConfigService.update(docScTemplateConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{scId}")
    public R findById(@PathVariable("scId") String scId){
        DocScTemplateConfigEntity docScTemplateConfigEntity = this.docScTemplateConfigService.findById(scId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docScTemplateConfigEntity);
    }

    @RequestMapping("/deleteById/{scId}")
    public R deleteById(@PathVariable("scId") String scId){
        this.docScTemplateConfigService.deleteById(scId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }

}