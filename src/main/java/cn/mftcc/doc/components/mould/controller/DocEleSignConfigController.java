/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocEleSignConfigEntity;
import cn.mftcc.doc.components.mould.service.DocEleSignConfigService;

/**
 * 电子签章配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-27 11:24:48
 */
@RestController
@RequestMapping("mould/docEleSignConfig")
public class DocEleSignConfigController {

    @Autowired
    private DocEleSignConfigService docEleSignConfigService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocEleSignConfigEntity docEleSignConfigEntity) {
        IPage<DocEleSignConfigEntity> list = this.docEleSignConfigService.findByPage(docEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocEleSignConfigEntity docEleSignConfigEntity){
        this.docEleSignConfigService.insert(docEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocEleSignConfigEntity docEleSignConfigEntity){
        this.docEleSignConfigService.update(docEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{id}")
    public R findById(@PathVariable("id") String id){
        DocEleSignConfigEntity docEleSignConfigEntity = this.docEleSignConfigService.findById(id);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docEleSignConfigEntity);
    }

    @RequestMapping("/deleteById/{id}")
    public R deleteById(@PathVariable("id") String id){
        this.docEleSignConfigService.deleteById(id);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}