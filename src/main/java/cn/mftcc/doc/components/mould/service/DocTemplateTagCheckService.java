/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import cn.mftcc.doc.feign.dto.DocTemplateCheckDto;
import cn.mftcc.doc.feign.dto.DocTemplateTagDto;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagCheckEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 模板标签校验信息表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-10-22 11:13:58
 */
public interface DocTemplateTagCheckService {

    IPage<DocTemplateTagCheckEntity> findByPage(DocTemplateTagCheckEntity docTemplateTagCheckEntity) throws ServiceException;

    void insert(DocTemplateTagCheckEntity docTemplateTagCheckEntity) throws ServiceException;

    void update(DocTemplateTagCheckEntity docTemplateTagCheckEntity) throws ServiceException;

    void updateTagCheckInfos(List<DocTemplateTagCheckEntity> docTemplateTagCheckEntityList) throws ServiceException;

    DocTemplateTagCheckEntity findById(String templateCheckNo) throws ServiceException;

    void deleteById(String templateCheckNo) throws ServiceException;

    void syncTemplateTagCheck(DocTemplateTagDto docTemplateTagCheckEntity) throws ServiceException;

    JSONObject checkTemplateTagValue(DocTemplateCheckDto docTemplateTagCheckEntity) throws ServiceException;

}

