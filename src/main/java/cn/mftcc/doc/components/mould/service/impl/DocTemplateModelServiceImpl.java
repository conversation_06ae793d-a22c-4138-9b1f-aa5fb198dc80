/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.bizcommon.utils.DateUtil;
import cn.mftcc.bizcommon.utils.UUIDUtil;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.MapperUtil;
import cn.mftcc.common.utils.ParmCacheUtil;
import cn.mftcc.common.utils.RequestUtil;
import cn.mftcc.credit.feign.dto.CreditAdjustApplyEntityDTO;
import cn.mftcc.credit.feign.dto.CreditApplyEntityDTO;
import cn.mftcc.cus.feign.dto.CusCustomerDTO;
import cn.mftcc.cus.feign.dto.CusPersBaseInfoDTO;
import cn.mftcc.cus.feign.dto.CusSocietyInfoEntityDTO;
import cn.mftcc.doc.common.constant.DocConstant;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.common.utils.OssConfig;
import cn.mftcc.doc.components.docmanage.entity.DocManageEntity;
import cn.mftcc.doc.components.docmanage.model.entity.Parse;
import cn.mftcc.doc.components.docmanage.service.ParseRenderService;
import cn.mftcc.doc.components.docmanage.web.model.ConvertBody;
import cn.mftcc.doc.components.docmanage.web.model.DocModel;
import cn.mftcc.doc.components.docmanage.web.utils.DocUtil;
import cn.mftcc.doc.components.file.entity.DocFileBizFiletypeConfigEntity;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.entity.DocScFiletypeConfigEntity;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.file.service.DocFileInfService;
import cn.mftcc.doc.components.file.service.DocScFiletypeConfigService;
import cn.mftcc.doc.components.mould.entity.*;
import cn.mftcc.doc.components.mould.mapper.DocScTemplateConfigMapper;
import cn.mftcc.doc.components.mould.mapper.DocTemplateModelMapper;
import cn.mftcc.doc.components.mould.service.*;
import cn.mftcc.doc.components.mould.util.GetTagValueManage;
import cn.mftcc.doc.feign.client.*;
import cn.mftcc.doc.feign.dto.*;
import cn.mftcc.lease.feign.dto.LeaseApplyEntityDTO;
import cn.mftcc.lease.feign.dto.LeasePactDTO;
import cn.mftcc.product.feign.dto.ProductDimensionEntityDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.data.style.Style;
import lombok.extern.slf4j.Slf4j;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import javax.activation.MimetypesFileTypeMap;
import java.io.*;
import java.math.BigDecimal;
import java.nio.channels.FileChannel;
import java.util.*;
import java.util.concurrent.*;

/**
 * 模板表
 *
 * <AUTHOR>
 * @email
 * @date 2021-03-25 14:53:31
 */
@Service("docTemplateModelService")
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class DocTemplateModelServiceImpl implements DocTemplateModelService {

    @Value("${mftcc.template.mould-path:}")
    private String docMouldPath;
    //文档转换的服务器路径
    @Value("${mftcc.template.doc-convert-url:}")
    private String docConvertUrl;
    @Value("${mftcc.template.officeServer-path-inner:}")
    private String  officeServerPathInner;
    //文档服务名称
    @Value("${spring.application.name:}")
    private String applicationName;
    @Value("${mftcc.template.server:}")
    private String   serverPath;
    @Value("${mftcc.template.tagPluginSever:}")
    private String   tagPluginSever;

    @Value("${mftcc.template.watermarkFlag:}")
    private String watermarkFlag;
    //    传到外部服务器方式 obs-obs上传 ftp-ftp上传 oss-oss存储
    @Value("${mftcc.file.upload-outter-function:}")
    private  String uploadOutterFunction;



    @Value("${mftcc.template.server:}")
    private String   server;
    @Autowired
    private ParmCacheUtil parmCacheUtil;
    @Autowired
    private GetTagValueManage getTagValueManage;

    @Autowired
    private OssConfig ossConfig;

    @Value("${mftcc.template.officeServer-path:}")
    private String   officeServer;
    @Value("${mftcc.template.server:}")
    private String  serverUrl;
    @Value("${mftcc.template.jyFilePushOss:}")
    private String jyFilePushOss;

    @Value("${mftcc.upload.leaseInfoTemplatePath:}")
    private String docMouldPaths;
    @Value("${mftcc.template.mould-save-path:}")
    private String targetFileSavePath;

    @Value("${mftcc.template.plugsData-baseUrl:}")
    private String  plugsDataBaseUrl;
    @Value("${mftcc.template.tagPlugin-file-path:}")
    private String  tagPluginFilePath;

    @Value("${mftcc.template.doc-url:}")
    private String  docUrl;

//    @Value("${mftcc.template.plugsData-file-path:}")
//    private String  plugsDataFilePath;

    @Value("${mftcc.template.mould-path:}")
    private String  mouldPath;
    @Autowired
    private ElinkApiFeignClient elinkApiFeignClient;
    @Autowired
    private DocFileInfService docFileInfService;



    @Autowired
    private ParseRenderService parseRenderService;
    @Autowired
    private DocTemplateModelMapper docTemplateModelMapper;
    @Autowired
    private RequestUtil requestUtil;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private DocTemplateTagSetService docTemplateTagSetService;
    @Autowired
    private DocTemplateTagBaseService docTemplateTagBaseService;
    @Autowired
    private DocTagTableConfigService docTagTableConfigService;
    @Autowired
    private DocEleSignConfigService docEleSignConfigService;
    @Autowired
    private DocBizTemplateService docBizTemplateService;
    @Autowired
    private DocScTemplateConfigMapper docScTemplateConfigMapper;
    @Autowired
    private ProductFeignClient productFeignClient;
    @Autowired
    private LeaseFeiginClient leaseInterface;
    @Autowired
    private CustomerFeignClient cusInterface;
    @Autowired
    private CreditFeignClient creditInterface;
    @Autowired
    private DocTemplateTagCheckService docTemplateTagCheckService;
    @Autowired
    private DocScTemplateConfigService docScTemplateConfigService;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private DocScFiletypeConfigService docScFiletypeConfigService;

    @Override
    public IPage<DocTemplateModelEntity> findByPage(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTemplateModelEntity> page = new Page<>();
            page.setCurrent(docTemplateModelEntity.getPageNo());
            page.setSize(docTemplateModelEntity.getPageSize());
            QueryWrapper<DocTemplateModelEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("update_time");
            mapperUtil.tableQuery(queryWrapper,docTemplateModelEntity);
            return docTemplateModelMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateModelEntity.getTemplateNo(),e);
        }
    }



    /**
     *
     * @param docTemplateModelEntity
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocTemplateModelEntity> findList(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException {
        try{
            //翻页
            List<DocTemplateModelEntity> List = new ArrayList<>();
            QueryWrapper<DocTemplateModelEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("update_time");

            return docTemplateModelMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateModelEntity.getTemplateNo(),e);
        }
    }

    /**
     * 获取所有的模板
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray  findTemplateArray() throws ServiceException {
        JSONArray result = new JSONArray();
        DocTemplateModelEntity docTemplateModelEntity = new DocTemplateModelEntity();
        List<DocTemplateModelEntity> tmpList = findList(docTemplateModelEntity);
        if (tmpList != null) {
            for (DocTemplateModelEntity tmplate : tmpList) {
                result.add(tmplate);
            }


        }
        return result;
    }

    /**
     *  获取消息模板列表
     * @return
     * @throws ServiceException
     */
    @Override
    public List<DocTemplateModelDto> findMsgMouldList() throws ServiceException{
        List<DocTemplateModelDto> result=new ArrayList<>();
        DocTemplateModelEntity docTemplateModelEntity =new DocTemplateModelEntity();
        docTemplateModelEntity.setTemplateType("08");//类型指定为消息
        List<DocTemplateModelEntity> mouldList=findDocTemplateList(docTemplateModelEntity);
        if(mouldList!=null){
            if(mouldList.size()>0){
                for(DocTemplateModelEntity bean :mouldList){
                    DocTemplateModelDto msgMould=new DocTemplateModelDto();
                    msgMould.setTemplateId(bean.getTemplateId());
                    msgMould.setTemplateName(bean.getTemplateName());
                    result.add(msgMould);
                }
            }
        }


        return result;


    }




    /**
     * 上传模板文件
     * @param file
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject upLoadModelFile( MultipartFile file)throws  ServiceException {
        JSONObject result=new JSONObject();
        String templateNo=UUIDUtil.getUUID();
        boolean impFlag= importMouldFile(templateNo,file);
        if(impFlag){
            result.put("code","0000");
            result.put("msg","上传成功");
            result.put("templateNo",templateNo);
        }else{
            result.put("code","1111");
            result.put("msg","上传失败");
        }

        return result;
    }

    /**
     * 修改上传模板文件
     * @param file
     * @param templateId
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject updateUpLoadModelFile( MultipartFile file, String templateId)throws  ServiceException {
        JSONObject result=new JSONObject();
        String templateNo = "";
        DocTemplateModelEntity docTemplateModelEntity = docTemplateModelMapper.selectById(templateId);
        if(docTemplateModelEntity != null){
            templateNo=docTemplateModelEntity.getTemplateNo();
            boolean impFlag= importMouldFile(templateNo,file);
            if(impFlag){
                result.put("code","0000");
                result.put("msg","上传成功");
                result.put("templateNo",templateNo);
            }else{
                result.put("code","1111");
                result.put("msg","上传失败");
            }
        }else {
            result.put("code","1111");
            result.put("msg","上传失败");
        }

        return result;
    }

    @Override
    public List<DocTemplateModelEntity> findDocTemplateList(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException {
        try{
            //翻页

            QueryWrapper<DocTemplateModelEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .eq(StringUtils.isNotBlank(docTemplateModelEntity.getTemplateNo()),"template_no",docTemplateModelEntity.getTemplateNo());
            queryWrapper
                    .eq(StringUtils.isNotBlank(docTemplateModelEntity.getTemplateType()),"template_type",docTemplateModelEntity.getTemplateType());

            return docTemplateModelMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateModelEntity.getTemplateNo(),e);
        }
    }

    /**
     * 获取最后一次预览的毫秒数
     * @param parm
     * @return
     * @throws ServiceException
     */
    private long  getLastPreviewTime(JSONObject parm)throws  ServiceException{
        String templateId=parm.getString("templateId");//模板流水号
        String versionNo=parm.getString("versionNo");//模板版本号
        String wid=parm.getString("wid");// 业务流水号
        String filePath=parm.getString("filePath");// 文件名称
//        String suffixName=parm.getString("fie");// 文档的后缀名。
//        String filePath=mouldSavePath+templateId+"_"+wid+"_"+templateId+"_"+versionNo+suffixName;
        File file=new File(filePath);
        if(file.exists()){
            try {

                Calendar cal = Calendar.getInstance();
                long time = file.lastModified();
                return time;
            } catch (Exception e) {
                throw  new ServiceException("","",e);
            }

        }else{
            return 0;
        }
    }

    /**
     * 获取模板的最后一次修改时间的毫秒数
     * @param parm
     * @return
     * @throws ServiceException
     */
    private long getMouldLastUpdTime(JSONObject parm)throws  ServiceException{
        String templateId=parm.getString("templateId");//模板流水号
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        Date updTime=docTemplateModelEntity.getUpdateTime();
        return updTime.getTime();
    }

    /**
     *  获取预览页面的配置
     * @param obj
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject previewMould(JSONObject obj)throws  ServiceException{

        JSONObject reuslt =new JSONObject();
        String templateId=obj.getString("templateId");
        String btId=obj.getString("btId");

        DocBizTemplateEntity docBizTemplateEntity=docBizTemplateService.findById(btId);

        JSONObject parmList=obj.getJSONObject("parmList");
        /**
         * 首先判断 最后一次预览的时间 是否大于文档最后一次修改的时间，如果是 直接贷款文档的时间，如果不是，需要重新生成预览文档。
         */
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        JSONObject parm=new JSONObject();
        parm.put("templateId",templateId);
        String  loadFlag=obj.getString("loadFlag");//是否强制重新加载 0-否 1-是
        parm.put("versionNo",docTemplateModelEntity.getVersionNo());
        parm.put("wid",obj.getString("wid"));
        String fileName=docTemplateModelEntity.getTemplateFileName();

        String  thisMouldFilePath=docMouldPath+docTemplateModelEntity.getTemplateFileName();


        String  suffixName=new FileUtils().getFileSuffixName(fileName);

        String  saveFileName= docBizTemplateEntity.getBtId()+"."+suffixName;

//        String  saveFileName=templateId+"_"+obj.getString("wid")+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
//        if(parmList!=null){
//
//            if(StringUtils.isNotEmpty(parmList.getString("subBizNo"))){
//                String  subBizNo=parmList.getString("subBizNo");
//                saveFileName=templateId+"_"+obj.getString("wid")+"_"+subBizNo+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
//            }
//        }
        String filePath="";
        String fileUrl = "";
        //判断文件模板是否已完成oss迁移
        if("1".equals(docBizTemplateEntity.getOssRemoveStatus())){
            loadFlag="0";
        }
        //存在未迁移则从本地服务器取
        filePath=mouldPath+"saveMould/"+saveFileName;
        fileUrl=docUrl+"saveMould"+File.separator+saveFileName;
        reuslt.put("saveFileName",saveFileName);
        reuslt.put("mode","view");
        reuslt.put("modelName",docTemplateModelEntity.getTemplateName());
        reuslt.put("fileUrl",fileUrl);

        parm.put("filePath",filePath);

        if(StringUtils.isNotEmpty(docBizTemplateEntity.getSaveFileName()) && "0".equals(loadFlag)){

            reuslt.put("saveFileName",docBizTemplateEntity.getSaveFileName());
            suffixName=new FileUtils().getFileSuffixName(docBizTemplateEntity.getSaveFileName());
            reuslt.put("fileType",suffixName);
            return reuslt;
        }
        long  previewTime =getLastPreviewTime(parm);//上次预览时间毫秒数
        long  mouldLastUpdDate=getMouldLastUpdTime(parm);//文档最后调整时间毫秒数

        JSONObject tagData=new JSONObject();
        try{



            boolean  reCreateFileFlag=false;
            if(previewTime==0){
                reCreateFileFlag=true;
            }else{
                if(previewTime<=mouldLastUpdDate) {
                    reCreateFileFlag=true;
                }
            }
            if("1".equals(loadFlag)){
                reCreateFileFlag=true;
            }


            if (reCreateFileFlag) {
                MFLogger.info("标签替换-parmList："+parmList);
                JSONObject busData =getTagData( templateId, parmList );

                tagData= getTagsJson(templateId);

                DocManageEntity docManageEntity = new DocManageEntity();

                String newFilePath = filePath;
                Map map = new HashMap();
//                map.put("docVersion", docVersion);
//                map.put("docNo", docNo);
                Parse parse = new Parse();
                parse.setDocPath(newFilePath);
                parse.setBusData(busData);
                parse.setTagData(tagData);
                String type = "text";
                if("2".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="spreadsheet";
                }
                if("3".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="pdf";
                }

                FileUtil.mkParentDirs(newFilePath);
                new FileUtils().copyFile(thisMouldFilePath, newFilePath);

                if ("text".equals(type)) {
                    parseRenderService.renderWordTemplate(parse, newFilePath);
                } else if ("spreadsheet".equals(type)) {
                    parseRenderService.renderExcelTemplate(parse, newFilePath);
                }
                //
                docBizTemplateEntity.setSavePath(mouldPath+"saveMould/");//设置模板的保存路径
                docBizTemplateEntity.setSaveFileName(saveFileName);//设置模板的保存名称
                docBizTemplateEntity.setWriteFlag("1");
                docBizTemplateService.update(docBizTemplateEntity);//修改保存路径和保存的文件名



            } else {
//                String previewFileNmae = obj.getString("previewFileNmae");
//                DocBizTemplateEntity docBizTemplateEntity=docBizTemplateService.findById(btId);
                if(StringUtils.isNotEmpty(docBizTemplateEntity.getSaveFileName())){

                    reuslt.put("saveFileName",docBizTemplateEntity.getSaveFileName());
                    suffixName=new FileUtils().getFileSuffixName(docBizTemplateEntity.getSaveFileName());
                    reuslt.put("fileType",suffixName);
                }



            }



        }catch (Exception e){
            e.printStackTrace();
        }

        return  reuslt ;
    }

    @Override
    public JSONObject archivePreviewMould(JSONObject obj) throws ServiceException {
        JSONObject reuslt =new JSONObject();

        String fileId=obj.getString("fileId");
        DocFileInfEntity docFileInfEntity = docFileInfService.findById(fileId);
        reuslt.put("saveFileName",docFileInfEntity.getFileName());
        reuslt.put("mode","view");
        reuslt.put("fileUrl",docFileInfEntity.getFilePath());
        return reuslt;
    }

    /**
     *  获取预览页面的配置（不自动生成文档）
     * @param obj
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject onlyPreviewMould(JSONObject obj)throws  ServiceException{

        JSONObject reuslt =new JSONObject();
        String templateId=obj.getString("templateId");
        JSONObject parmList=obj.getJSONObject("parmList");
        /**
         * 首先判断 最后一次预览的时间 是否大于文档最后一次修改的时间，如果是 直接贷款文档的时间，如果不是，需要重新生成预览文档。
         */
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        JSONObject parm=new JSONObject();
        parm.put("templateId",templateId);
        parm.put("versionNo",docTemplateModelEntity.getVersionNo());
        parm.put("wid",obj.getString("wid"));
        String fileName=docTemplateModelEntity.getTemplateFileName();

        String  thisMouldFilePath=docMouldPath+docTemplateModelEntity.getTemplateFileName();
        String  suffixName=new FileUtils().getFileSuffixName(fileName);

        String  saveFileName=templateId+"_"+obj.getString("wid")+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
        if(StringUtils.isNotEmpty(parmList.getString("subBizNo"))){
            String  subBizNo=parmList.getString("subBizNo");
            saveFileName=templateId+"_"+obj.getString("wid")+"_"+subBizNo+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
        }
        String filePath=mouldPath+"saveMould/"+saveFileName;
        MFLogger.info("预览页面路径地址:"+filePath);
        File docFile=new File(filePath);

        if(docFile.exists()){
            reuslt.put("code","0000");
            reuslt.put("saveFileName",saveFileName);
        }else{
            reuslt.put("code","1111");
            reuslt.put("msg","模板文档还未生成");
        }
        reuslt.put("mode","view");



        return  reuslt ;
    }
    /**
     *  预览一个模板文档
     * @param obj{
     *           btId:模板关联业务的编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject previewOneModelFile(JSONObject obj)throws  ServiceException{

//             *           bizNo: 业务流水
//                *           subBizNo:子项业务流水
//                *           parmList：参数列表json格式
//                *           prdUniqueVal：流程唯一号
//                *           flowNo：流程编号
        JSONObject reuslt =new JSONObject();

        String btId=obj.getString("btId");
        DocBizTemplateEntity cusBizObj=docBizTemplateService.findById(btId);

        reuslt.put("fileName",cusBizObj.getTemplateName());
        reuslt.put("officeServie",officeServer);

//        fileUrl:"",
//
//                fileType:"",


        String templateId=cusBizObj.getTemplateId();
        DocTemplateModelEntity modelEntity=findById(templateId);
        /**
         * 首先判断 最后一次预览的时间 是否大于文档最后一次修改的时间，如果是 直接贷款文档的时间，如果不是，需要重新生成预览文档。
         */
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        JSONObject parm=new JSONObject();
        parm.put("templateId",templateId);
        parm.put("versionNo",docTemplateModelEntity.getVersionNo());
        parm.put("wid",obj.getString("wid"));
        String fileName=docTemplateModelEntity.getTemplateFileName();

        String  thisMouldFilePath=docMouldPath+docTemplateModelEntity.getTemplateFileName();


        String  suffixName=new FileUtils().getFileSuffixName(fileName);

        reuslt.put("suffix",suffixName);
        reuslt.put("docKey",UUIDUtil.getUUID());
        reuslt.put("fileType",btId);
        String  saveFileName=templateId+"_"+obj.getString("wid")+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
        if(StringUtils.isNotEmpty(cusBizObj.getSubBizNo())){
            String subBizNoStr=cusBizObj.getSubBizNo();
            saveFileName=templateId+"_"+obj.getString("wid")+"_"+subBizNoStr+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
        }
        String filePath=mouldPath+"saveMould/"+saveFileName;
        reuslt.put("saveFileName",saveFileName);
        reuslt.put("mode","view");
        String fileUrl=docUrl+"saveMould"+File.separator+saveFileName;
        reuslt.put("fileUrl",fileUrl);
        parm.put("filePath",filePath);


        if("1".equals(modelEntity.getTemplateSuffix())){//模板格式1-word 2-excel 3-pdf
            reuslt.put("fileType","2");
        }else
        if("2".equals(modelEntity.getTemplateSuffix())){//模板格式1-word 2-excel 3-pdf
            reuslt.put("fileType","3");
        }
        long  previewTime =getLastPreviewTime(parm);//上次预览时间毫秒数
        long  mouldLastUpdDate=getMouldLastUpdTime(parm);//文档最后调整时间毫秒数


        try{

            boolean  reCreateFileFlag=false;


            //如果是重新加载

//                if(previewTime==0){
//                    reCreateFileFlag=true;
//                }else{
//                    if(previewTime<=mouldLastUpdDate) {
//                        reCreateFileFlag=true;
//                    }else{
//                        reCreateFileFlag=false;
//                    }
//                }
            if("1".equals(obj.getString("reLoadFlag"))){
                reCreateFileFlag=true;
            }else{
                reCreateFileFlag=false;
            }


            if (reCreateFileFlag) {
                JSONObject parmList=obj.getJSONObject("parmList");
                JSONObject busData =getTagData( templateId, parmList );

                JSONObject tagData= getTagsJson(templateId);

                DocManageEntity docManageEntity = new DocManageEntity();



                String newFilePath = filePath;
                Map map = new HashMap();
//                map.put("docVersion", docVersion);
//                map.put("docNo", docNo);
                Parse parse = new Parse();
                parse.setDocPath(newFilePath);
                parse.setBusData(busData);
                parse.setTagData(tagData);
                String type = "text";
                if("2".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="spreadsheet";
                }
                if("3".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="pdf";
                }

                FileUtil.mkParentDirs(newFilePath);
                new FileUtils().copyFile(thisMouldFilePath, newFilePath);
                try {
                    if ("text".equals(type)) {

                        parseRenderService.renderWordTemplate(parse, newFilePath);

                    } else if ("spreadsheet".equals(type)) {
                        parseRenderService.renderExcelTemplate(parse, newFilePath);
                    }
                } catch (IOException e1) {
                    e1.printStackTrace();
                }

            }

            String docFileUrl=serverPath+applicationName+"/file/docFileInf/getMouldStream/2/"+saveFileName;
            MFLogger.info("转换pdf的源文件路径："+docFileUrl);
            reuslt.put("fileUrl",docFileUrl);



        }catch (Exception e){
            MFLogger.error("预览一个模板文档：",e);
            throw  new  ServiceException("预览一个模板文档",parm.toJSONString(),e);
        }

        return  reuslt ;
    }


    /**
     * 根据id获取pdf的物理路径
     * @param btId
     * @return
     * @throws ServiceException
     */
    @Override
    public  String  getPdfFilePath(String btId)throws  ServiceException{

        String tmpPath="";
        DocBizTemplateEntity docBizTemplateEntity=docBizTemplateService.findById(btId);
//        String  templateId=docBizTemplateEntity.getTemplateId();
//        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
//        String bizNo=docBizTemplateEntity.getBizNo();
//        String subBizNo=StrUtil.emptyToDefault(docBizTemplateEntity.getSubBizNo(),"");
//        String  pdfFileName=templateId+"_"+subBizNo+"_"+bizNo+"_"+docTemplateModelEntity.getTemplateNo()+".pdf";
        String  pdfFileName=docBizTemplateEntity.getSaveFileName();
        String pdfFiePath=docBizTemplateEntity.getSavePath();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("pdfFilePath",pdfFiePath);
        jsonObject.put("pdfFileName",pdfFileName);
        return  jsonObject.toJSONString();

    }

    @Override
    public JSONObject findByTemplateModdel(JSONObject jsonObject) {
        String templateType = jsonObject.getString("templateType");
        String prdUniqueVal = jsonObject.getString("prdUniqueVal");
        String bizNo = jsonObject.getString("bizNo");
        String flowNo = jsonObject.getString("flowNo");
        String nodeNo = jsonObject.getString("nodeNo");
        QueryWrapper<DocTemplateModelEntity> qw = new QueryWrapper<>();
        qw.eq("template_type",templateType);
        List<DocTemplateModelEntity> docTemplateModelEntities = docTemplateModelMapper.selectList(qw);
        if (docTemplateModelEntities.size()<1){
            jsonObject.put("code","1111");
            jsonObject.put("msg","未找到模板");
            return jsonObject;
        }else {
            String invoiceCheckFlag = jsonObject.getString("invoiceCheckFlag");
            if(!CommonConstant.YES_NO_Y.equals(invoiceCheckFlag)) {
                //初始化关联要件
                DocScFiletypeConfigEntity docScFiletypeConfigEntity = new DocScFiletypeConfigEntity();
                docScFiletypeConfigEntity.setPrdUniqueVal(prdUniqueVal);
                docScFiletypeConfigEntity.setFlowNo(flowNo);
                docScFiletypeConfigEntity.setNodeNo(nodeNo);
                docScFiletypeConfigEntity.setParentTypeNo("electronicSignConfig");
                List<DocScFiletypeConfigEntity> configList = docScFiletypeConfigService.findList(docScFiletypeConfigEntity);
                if (configList != null && configList.size() > 0) {
                    //如果该业务的产品类型，流程，节点 配置过要件，拿过来作为该业务的初始要件配置
                    for (DocScFiletypeConfigEntity configBean : configList) {
                        DocFileBizFiletypeConfigEntity addBean = new DocFileBizFiletypeConfigEntity();
                        addBean.setBizNo(bizNo);
                        addBean.setPrdUniqueVal(prdUniqueVal);
                        addBean.setFlowNo(flowNo);
                        addBean.setNodeNo(nodeNo);
                        addBean.setTypeNo(configBean.getTypeNo());
                        log.info("请求参数：{}", addBean);
                        List<DocFileBizFiletypeConfigEntity> list = docFileBizFiletypeConfigService.findList(addBean);
                        BeanUtils.copyProperties(configBean, addBean);
                        if (list == null || list.size() < 1) {
                            addBean.setScId(UUIDUtil.getUUID());
                            addBean.setCreateTime(new Date());
                            addBean.setUpdateTime(new Date());
                            docFileBizFiletypeConfigService.insert(addBean);
                        }
                    }
                } else {
                    log.info("要件未初始化，请求参数{}" + docScFiletypeConfigEntity);
                    jsonObject.put("code", "1111");
                    jsonObject.put("msg", "初始化影响资料失败");
                    return jsonObject;
                }
            }
            //初始化场景模板配置
            for (DocTemplateModelEntity docTemplateModelEntity : docTemplateModelEntities) {
                String templateId = docTemplateModelEntity.getTemplateId();
                QueryWrapper<DocScTemplateConfigEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("template_id",templateId).eq("template_type",templateType)
                .eq("flow_no",flowNo).eq("node_no",nodeNo);
                DocScTemplateConfigEntity docScTemplateConfigEntity1 = docScTemplateConfigMapper.selectOne(queryWrapper);
                if (docScTemplateConfigEntity1!=null){
                    jsonObject.put("message","当前场景已经初始化");
                }else {
                    DocScTemplateConfigEntity docScTemplateConfigEntity = new DocScTemplateConfigEntity();
                    docScTemplateConfigEntity.setScId(UUIDUtil.getUUID());
                    docScTemplateConfigEntity.setPrdUniqueVal(prdUniqueVal);
                    docScTemplateConfigEntity.setFlowNo(flowNo);
                    docScTemplateConfigEntity.setNodeNo(nodeNo);
                    docScTemplateConfigEntity.setTemplateId(templateId);
                    docScTemplateConfigEntity.setCanWrite("0");
                    docScTemplateConfigEntity.setIfMustRead("0");
                    docScTemplateConfigEntity.setIfMustWrite("0");
                    BeanUtils.copyProperties(docTemplateModelEntity,docScTemplateConfigEntity);
                    docScTemplateConfigMapper.insert(docScTemplateConfigEntity);
                }
            }
            jsonObject.put("code","0000");
            jsonObject.put("msg","场景初始化成功");
            return jsonObject;
        }
    }

    @Override
    public JSONArray getTemplteDetail(JSONObject jsonObject) throws ServiceException {
        JSONArray jsonArray = new JSONArray();
        String templateId = jsonObject.getString("templateId");
        if(StringUtils.isBlank(templateId)){
            MFLogger.info("模板ID为空");
            return jsonArray;
        }
        try{
            Boolean flag = getIfShowTemplate(jsonObject);
            if(!flag){
                MFLogger.info("模板维度不匹配");
                return jsonArray;
            }
            JSONObject object = new JSONObject();
            object.put("wid",UUIDUtil.getUUID());
            object.put("name","");
            jsonArray.add(object);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_ERROR,templateId,e);
        }

        return jsonArray;
    }

    @Override
    public Boolean getIfShowTemplate(JSONObject jsonObject) throws Exception {
        String templateId = jsonObject.getString("templateId");
        if(StringUtils.isBlank(templateId)){
            MFLogger.info("模板ID为空");
            return false;
        }
        ProductDimensionEntityDTO productDimensionEntityDTO = new ProductDimensionEntityDTO();
        productDimensionEntityDTO.setProductNo(templateId);
        List<ProductDimensionEntityDTO> productDimensionEntityDTOList = productFeignClient.getProductDimensionList(productDimensionEntityDTO);
        if(CollectionUtils.isEmpty(productDimensionEntityDTOList)){
            MFLogger.info("未找到维度配置" + JSONObject.toJSONString(productDimensionEntityDTO));
            return false;
        }
        Boolean flag = true;
        for (ProductDimensionEntityDTO dimensionEntityDTO: productDimensionEntityDTOList ) {
            // 业务维度标识
            String dimensionMark = dimensionEntityDTO.getDimensionMark();
            switch (dimensionMark){
                case "fitLeaseType"://租赁类型
                    flag = getFitLeaseType(jsonObject,dimensionEntityDTO);
                    break;
                case "ifFundBus"://是否资方 是否自营
                    flag = getIfFundBus(jsonObject,dimensionEntityDTO);
                    break;
                case "maritalStatus"://自然人婚姻状况
                    flag = getMaritalStatus(jsonObject,dimensionEntityDTO);
                    break;
//                case "assureMaritalStatus"://担保人自然人婚姻状况
//                    flag = getAssureMaritalStatus(jsonObject,dimensionEntityDTO);
//                    break;
                case "guaranteeType"://担保人类型
                    flag = getGuaranteeType(jsonObject,dimensionEntityDTO);
                    break;
                case "cusType"://客户类型
                    flag = getCusType(jsonObject,dimensionEntityDTO);
                    break;
                case "ifAffiliated"://是否挂靠
                    flag = getIfAffiliated(jsonObject,dimensionEntityDTO);
                    break;
                case "ifCollateralRequire"://是否抵押我司
                    flag = getIfCollateralRequire(jsonObject,dimensionEntityDTO);
                    break;
                case "creditOccType"://授信发生类型
                    flag = getCreditOccType(jsonObject,dimensionEntityDTO);
                    break;
                case "company"://公司
                    flag = getCompany(jsonObject,dimensionEntityDTO);
                    break;
                default:
                    break;
            }
            if(!flag){
                break;
            }
        }
        return flag;
    }
    /**
     * 公司维度
     * @param jsonObject
     * @param dimensionEntityDTO
     * @return
     */
    private Boolean getCompany(JSONObject jsonObject, ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        String pactId = (String)jsonObject.get("pactId");
        MFLogger.info("getCompany匹配维度公司,合同ID=" + pactId);
        if(StringUtils.isBlank(pactId)){
            MFLogger.info("getCompany合同ID为空");
            return false;
        }
        // 申请编号
        LeasePactDTO leasePactDTO = leaseInterface.getLeasePactByPactId(pactId);
        if(leasePactDTO == null){
            MFLogger.info("getCompany合同为空,合同ID=" + pactId);
            return false;
        }
        String company = leasePactDTO.getCompany();
        if(StringUtils.isBlank(company)){
            MFLogger.info("getCompany公司为空,合同ID=" + pactId);
            return false;
        }
        // 公司
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("getCompany公司维度为空");
            return false;
        }
        // 分割租赁类型
        String [] fitLeaseTypeArr = minDimensionValue.split("\\|");
        boolean fitLeaseTypeFlag = false;
        for(int i = 0;i < fitLeaseTypeArr.length;i++){
            if(company.equals(fitLeaseTypeArr[i])){
                fitLeaseTypeFlag = true;
            }
        }
        if(!fitLeaseTypeFlag){
            MFLogger.info("getCompany未匹配到维度,合同ID=" + pactId);
            return false;
        }
        return true;
    }
    @Override
    public DocTemplateModelEntity findByNo(String templateNo) throws ServiceException {
        QueryWrapper<DocTemplateModelEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_no",templateNo);
        return docTemplateModelMapper.selectOne(queryWrapper);
    }

    @Override
    public JSONObject auditFkPdfFile(JSONObject obj) throws ServiceException {
        JSONObject resObj = new JSONObject();
        log.info("StartTaskCall-batchAuditFk-付款审批单-call");
        try {
            //查询配置场景的模板id
            DocScTemplateConfigEntity configEntity = new DocScTemplateConfigEntity();
            configEntity.setPrdUniqueVal("paymentDoc");
            configEntity.setNodeNo("paymentDoc");
            configEntity.setFlowNo("paymentDoc");
            DocScTemplateConfigEntity templateConfigEntity = docScTemplateConfigService.findByEntity(configEntity);
            String templateId = templateConfigEntity.getTemplateId();
            //查询已经初始化的模板
            DocBizTemplateEntity bizTemplateEntity = new DocBizTemplateEntity();
            bizTemplateEntity.setTemplateId(templateId);
            List<DocBizTemplateEntity> bizTemplateEntities = docBizTemplateService.findList(bizTemplateEntity);

            //初始化的模板的灌数和转换pdf
            if (bizTemplateEntities != null && bizTemplateEntities.size() > 0){
                ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("auditFkPdfFile-init-%d").build();
                ExecutorService executorService = new ThreadPoolExecutor(10,10,10L,
                        TimeUnit.MILLISECONDS,new LinkedBlockingQueue<>(),namedThreadFactory);
                Set callables = new HashSet<>();
                //处理总数、失败和成功条数
                int totalCnt = 0;
                int failCnt = 0;
                int okCnt = 0;
                for (DocBizTemplateEntity docBizTemplateEntity : bizTemplateEntities) {
                    //已生成的合同不处理
                    if (!"1".equals(docBizTemplateEntity.getTemplateSuffix())){
                        continue;
                    }

                    callables.add(new Callable() {
                        @Override
                        public Object call() throws Exception {
                            return generatePdfFileToOss(docBizTemplateEntity);
                        }
                    });
                    totalCnt++;
                }

                // 收集线程返回的值
                List<Future<JSONObject>> futures = executorService.invokeAll(callables);
                for(Future<JSONObject> future:futures){
                    JSONObject js = future.get();
                    if (js != null && !"0000".equals(js.getString("code"))){
                        failCnt++;
                    }else{
                        //上传oss服务器
                        okCnt++;
                    }
                }
                log.info("TaskCall-batchAuditFk-付款审批单-本次处理数据条数{}条，成功{}条，失败{}条。",totalCnt,okCnt,failCnt);
                if(failCnt>0){
                    log.info("TaskCall-batchAuditFk-付款审批单-error-本次处理数据条数{}条，成功{}条，失败{}条。",totalCnt,okCnt,failCnt);
                }
            }
            resObj.put("code","0000");
        }catch (Exception e){
            log.info("TaskCall-batchAuditFk-付款审批单-失败原因/异常信息:"+e.getMessage());
            log.error("TaskCall-batchAuditFk-error-付款审批单转换PDF失败:{}",e.getMessage());
            resObj.put("code","1111");
            resObj.put("msg","转换生成pdf文件报错");
        }
        return resObj;
    }


    @Override
    public JSONObject endPactPdfFile(JSONObject obj) throws ServiceException {
        JSONObject resObj = new JSONObject();
        log.info("StartTaskCall-endPactPdfFile-合同结清通知函-call");
        try {
            //查询配置场景的模板id
            DocScTemplateConfigEntity configEntity = new DocScTemplateConfigEntity();
            configEntity.setPrdUniqueVal("prePayDoc");
            configEntity.setNodeNo("prePayDoc");
            configEntity.setFlowNo("prePayDoc");
            DocScTemplateConfigEntity templateConfigEntity = docScTemplateConfigService.findByEntity(configEntity);
            String templateId = templateConfigEntity.getTemplateId();
            //查询已经初始化的模板
            DocBizTemplateEntity bizTemplateEntity = new DocBizTemplateEntity();
            bizTemplateEntity.setTemplateId(templateId);
            bizTemplateEntity.setBizNo(obj.getString("pactId"));
            List<DocBizTemplateEntity> bizTemplateEntities = docBizTemplateService.findList(bizTemplateEntity);

            //初始化的模板的灌数和转换pdf
            if (bizTemplateEntities != null && bizTemplateEntities.size() > 0){
                ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("endPactPdfFile-init-%d").build();
                ExecutorService executorService = new ThreadPoolExecutor(10,10,10L,
                        TimeUnit.MILLISECONDS,new LinkedBlockingQueue<>(),namedThreadFactory);
                Set callables = new HashSet<>();
                //处理总数、失败和成功条数
                int totalCnt = 0;
                int failCnt = 0;
                int okCnt = 0;
                for (DocBizTemplateEntity docBizTemplateEntity : bizTemplateEntities) {
                    docBizTemplateEntity.setDueId(obj.getString("dueId"));
                    docBizTemplateEntity.setPrepayId(obj.getString("prepayId"));
                    docBizTemplateEntity.setDueBeginDate(obj.getString("dueBeginDate"));
                    docBizTemplateEntity.setPrepayDate(obj.getString("prepayDate"));
                    //已生成的合同不处理
                    if (StringUtils.isNotEmpty(docBizTemplateEntity.getSaveFileName())){
                        continue;
                    }

                    callables.add(new Callable() {
                        @Override
                        public Object call() throws Exception {
                            return generatePdfFileToOss(docBizTemplateEntity);
                        }
                    });
                    totalCnt++;
                }

                // 收集线程返回的值
                List<Future<JSONObject>> futures = executorService.invokeAll(callables);
                for(Future<JSONObject> future:futures){
                    JSONObject js = future.get();
                    if (js != null && !"0000".equals(js.getString("code"))){
                        failCnt++;
                    }else{
                        //上传oss服务器
                        okCnt++;
                    }
                }
                log.info("TaskCall-endPactPdfFile-合同结清通知函-本次处理数据条数{}条，成功{}条，失败{}条。",totalCnt,okCnt,failCnt);
                if(failCnt>0){
                    log.info("TaskCall-endPactPdfFile-合同结清通知函-error-本次处理数据条数{}条，成功{}条，失败{}条。",totalCnt,okCnt,failCnt);
                }
            }
            resObj.put("code","0000");
        }catch (Exception e){
            log.info("Taskdetailex-endPactPdfFile-合同结清通知函-失败原因/异常信息:"+e.getMessage());
            log.error("合同结清通知函转换PDF失败");
            resObj.put("code","1111");
            resObj.put("msg","转换生成pdf文件报错");
        }
        return resObj;
    }

    private Boolean getCusType(JSONObject jsonObject, ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        MFLogger.info("匹配维度客户类型");
        if(StringUtils.isBlank(jsonObject.getString("cusId"))){
            MFLogger.info("客户ID为空");
            return false;
        }
        // 客户编号
        CusCustomerDTO customerDTO = cusInterface.getCusCustomerByCusId(jsonObject.getString("cusId"));
        // 客户基本类型
        String cusBaseType = customerDTO.getCusBaseType();
        if(StringUtils.isBlank(cusBaseType)){
            MFLogger.info("客户客户基本类型为空");
            return false;
        }
        // 客户类型
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("匹配维度客户类型为空");
            return false;
        }
        // 分割客户类型
        String [] minDimensionValueArr = minDimensionValue.split("\\|");
        boolean flag = false;
        for(int i = 0;i < minDimensionValueArr.length;i++){
            if(cusBaseType.equals(minDimensionValueArr[i])){
                flag = true;
            }
        }
        if(!flag){
            MFLogger.info("未匹配到维度");
            return false;
        }
        return true;
    }

    private Boolean getCreditOccType(JSONObject jsonObject, ProductDimensionEntityDTO dimensionEntityDTO) {
        MFLogger.info("匹配维度授信发生类型");
        String creditAppId = jsonObject.getString("creditAppId");
        if(StringUtils.isBlank(creditAppId)){
            MFLogger.info("授信ID为空");
            return false;
        }
        // 授信发生类型
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("授信发生类型维度为空");
            return false;
        }
        // 分割授信发生类型
        String [] minDimensionValueArr = minDimensionValue.split("\\|");
        boolean flag = false;
        for(int i = 0;i < minDimensionValueArr.length;i++){
            if("1".equals(minDimensionValueArr[i])){
                CreditApplyEntityDTO creditApplyEntity = creditInterface.selectCreditApplyById(creditAppId);
                if(creditApplyEntity != null ){
                    flag = true;
                }
            }else if("2".equals(minDimensionValueArr[i])){
                CreditAdjustApplyEntityDTO creditAdjustApplyEntity = creditInterface.selectCreditAdjustApplyById(creditAppId);
                if(creditAdjustApplyEntity != null){
                    //授信调整类型  1-续授信 2-授信额度调整
                    String creditAdjustType = creditAdjustApplyEntity.getCreditAdjustType();
                    //如果 授信类型为续授信时 则加载精简版 尽职调查模板
                    if("1".equals(creditAdjustType)){
                        flag = true;
                    }
                }
            }else if("3".equals(minDimensionValueArr[i])){
                CreditAdjustApplyEntityDTO creditAdjustApplyEntity = creditInterface.selectCreditAdjustApplyById(creditAppId);
                if(creditAdjustApplyEntity != null){
                    //授信调整类型  1-续授信 2-授信额度调整
                    String creditAdjustType = creditAdjustApplyEntity.getCreditAdjustType();
                    //如果 授信类型为续授信时 则加载精简版 尽职调查模板
                    if("2".equals(creditAdjustType)){
                        flag = true;
                    }
                }
            }
        }
        if(!flag){
            MFLogger.info("未匹配到维度");
            return false;
        }
        return true;
    }

    private Boolean getIfCollateralRequire(JSONObject jsonObject, ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        MFLogger.info("匹配维度是否抵押我司");
        if(StringUtils.isBlank(jsonObject.getString("applyId"))){
            MFLogger.info("申请ID为空");
            return false;
        }
        // 申请编号
        LeaseApplyEntityDTO applyEntityDTO = leaseInterface.getLeaseApplyEntityById(jsonObject.getString("applyId"));
        String hasControlledKangfu = applyEntityDTO.getHasControlledKangfu();
        if(StringUtils.isBlank(hasControlledKangfu)){
            MFLogger.info("是否抵押我司为空");
            return false;
        }
        // 是否抵押我司
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("是否抵押我司维度为空");
            return false;
        }
        // 分割是否抵押我司
        String [] minDimensionValueArr = minDimensionValue.split("\\|");
        boolean flag = false;
        for(int i = 0;i < minDimensionValueArr.length;i++){
            if(hasControlledKangfu.equals(minDimensionValueArr[i])){
                flag = true;
            }
        }
        if(!flag){
            MFLogger.info("未匹配到维度");
            return false;
        }
        return true;
    }

    private Boolean getIfAffiliated(JSONObject jsonObject, ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        MFLogger.info("匹配维度是否挂靠");
        // 申请编号
        LeaseApplyEntityDTO applyEntityDTO = leaseInterface.getLeaseApplyEntityById(jsonObject.getString("applyId"));
        String affiliatedCompanyId = applyEntityDTO.getAffiliatedCompanyId();
        if(StringUtils.isBlank(affiliatedCompanyId)){
            MFLogger.info("是否挂靠为空");
            return false;
        }
        // 是否挂靠
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("是否挂靠维度为空");
            return false;
        }
        // 分割是否挂靠
        String [] minDimensionValueArr = minDimensionValue.split("\\|");
        boolean flag = false;
        for(int i = 0;i < minDimensionValueArr.length;i++){
            if("1".equals(minDimensionValueArr[i])){//是
                if(StringUtils.isNoneBlank(affiliatedCompanyId)){
                    flag = true;
                }
            }else{//其他
                if(StringUtils.isBlank(affiliatedCompanyId)){
                    flag = true;
                }
            }
        }
        if(!flag){
            MFLogger.info("未匹配到维度");
            return false;
        }
        return true;
    }

    private Boolean getGuaranteeType(JSONObject jsonObject, ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        MFLogger.info("匹配维度担保人类型");
        // 客户编号
        CusCustomerDTO customerDTO = cusInterface.getCusCustomerByCusId(jsonObject.getString("assureCusId"));
        // 客户基本类型
        String cusBaseType = customerDTO.getCusBaseType();
        if(StringUtils.isBlank(cusBaseType)){
            MFLogger.info("担保人客户基本类型为空");
            return false;
        }
        // 担保人类型
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("匹配维度担保人类型为空");
            return false;
        }
        // 分割担保人类型
        String [] minDimensionValueArr = minDimensionValue.split("\\|");
        boolean flag = false;
        for(int i = 0;i < minDimensionValueArr.length;i++){
            if(cusBaseType.equals(minDimensionValueArr[i])){
                flag = true;
            }
        }
        if(!flag){
            MFLogger.info("未匹配到维度");
            return false;
        }
        return true;
    }

    private Boolean getAssureMaritalStatus(JSONObject jsonObject, ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        MFLogger.info("匹配维度担保人自然人婚姻状况");
        // 客户编号
        CusCustomerDTO customerDTO = cusInterface.getCusCustomerByCusId(jsonObject.getString("assureCusId"));
        // 客户基本类型
        String cusBaseType = customerDTO.getCusBaseType();
        if(StringUtils.isBlank(cusBaseType)){
            MFLogger.info("担保人自然人客户基本类型为空");
            return false;
        }
        if(!"2".equals(cusBaseType)){
            MFLogger.info("担保人自然人客户类型不是自然人");
            return false;
        }
        CusPersBaseInfoDTO cusPersBaseInfoDTO = new CusPersBaseInfoDTO();
        cusPersBaseInfoDTO.setCusId(customerDTO.getId());
        CusPersBaseInfoDTO PersBaseInfoDTO = cusInterface.getCusPersBaseInfoByCusId(cusPersBaseInfoDTO);
        String marrige = PersBaseInfoDTO.getMarrige();
        if(StringUtils.isBlank(marrige)){
            MFLogger.info("担保人自然人婚姻状况为空");
            return false;
        }
        // 婚姻状况
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("匹配维度担保人自然人婚姻状况为空");
            return false;
        }
        // 分割租赁类型
        String [] minDimensionValueArr = minDimensionValue.split("\\|");
        boolean flag = false;
        for(int i = 0;i < minDimensionValueArr.length;i++){
            if("1".equals(minDimensionValueArr[i])){//已婚
                if("20".equals(marrige) || "21".equals(marrige) || "22".equals(marrige) || "23".equals(marrige)){
                    CusSocietyInfoEntityDTO cusSocietyInfoEntityDTO = new CusSocietyInfoEntityDTO();
                    cusSocietyInfoEntityDTO.setCusId(customerDTO.getId());
                    cusSocietyInfoEntityDTO.setRelativeType("1");
                    CusSocietyInfoEntityDTO societyInfoEntityDTO = cusInterface.getCusSocietyInfo(cusSocietyInfoEntityDTO);
                    if(societyInfoEntityDTO != null){
                        flag = true;
                    }
                }
            }else{//其他
                if(!"20".equals(marrige) && !"21".equals(marrige) && !"22".equals(marrige) && !"23".equals(marrige)){
                    flag = true;
                }
            }
        }
        if(!flag){
            MFLogger.info("未匹配到维度");
            return false;
        }
        return true;
    }

    private Boolean getMaritalStatus(JSONObject jsonObject, ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        MFLogger.info("匹配维度承租人婚姻状况");
        // 客户编号
        String cusId = jsonObject.getString("cusId");
        if(StringUtils.isBlank(cusId)){
            cusId = jsonObject.getString("cusNO");
        }
        if(StringUtils.isBlank(cusId)){
            MFLogger.info("客户ID为空");
            return false;
        }
        CusCustomerDTO customerDTO = cusInterface.getCusCustomerByCusId(cusId);
        // 客户基本类型
        String cusBaseType = customerDTO.getCusBaseType();
        if(StringUtils.isBlank(cusBaseType)){
            MFLogger.info("客户基本类型为空");
            return false;
        }
        if(!"2".equals(cusBaseType)){
            MFLogger.info("客户类型不是自然人");
            return false;
        }
        CusPersBaseInfoDTO cusPersBaseInfoDTO = new CusPersBaseInfoDTO();
        cusPersBaseInfoDTO.setCusId(customerDTO.getId());
        CusPersBaseInfoDTO PersBaseInfoDTO = cusInterface.getCusPersBaseInfoByCusId(cusPersBaseInfoDTO);
        String marrige = PersBaseInfoDTO.getMarrige();
        if(StringUtils.isBlank(marrige)){
            MFLogger.info("婚姻状况为空");
            return false;
        }
        // 婚姻状况
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("匹配维度婚姻状况为空");
            return false;
        }
        // 分割租赁类型
        String [] minDimensionValueArr = minDimensionValue.split("\\|");
        boolean flag = false;
        for(int i = 0;i < minDimensionValueArr.length;i++){
            if("1".equals(minDimensionValueArr[i])){//已婚
                if("20".equals(marrige) || "21".equals(marrige) || "22".equals(marrige) || "23".equals(marrige)){
                    CusSocietyInfoEntityDTO cusSocietyInfoEntityDTO = new CusSocietyInfoEntityDTO();
                    cusSocietyInfoEntityDTO.setCusId(customerDTO.getId());
                    cusSocietyInfoEntityDTO.setRelativeType("1");
                    CusSocietyInfoEntityDTO societyInfoEntityDTO = cusInterface.getCusSocietyInfo(cusSocietyInfoEntityDTO);
                    if(societyInfoEntityDTO != null){
                        flag = true;
                    }
                }
            }else{//其他
                if(!"20".equals(marrige) && !"21".equals(marrige) && !"22".equals(marrige) && !"23".equals(marrige)){
                    flag = true;
                }
            }
        }
        if(!flag){
            MFLogger.info("未匹配到维度");
            return false;
        }
        return true;
    }

    /**
     * todo 资方业务
     * @参数  jsonObject,dimensionEntityDTO
     * @返回 Boolean
     * @作者 zhangyingjie
     * @日期 2022/11/18 15:33
     **/
    private Boolean getIfFundBus(JSONObject jsonObject,ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        log.info("匹配维度租赁类型");
        if(StringUtils.isBlank(jsonObject.getString("mainId"))){
            log.info("业务主表ID为空");
            return false;
        }
        // 申请编号
        JSONObject selectJson = leaseInterface.getLeaseMainById(jsonObject.getString("mainId"));
        String ifFundBus = selectJson.getString("ifFundBus");
        String ifAccordFund = selectJson.getString("ifAccordFund");
        //是否自营
        String ifSelfSupport = selectJson.getString("ifSelfSupport");
        if(StringUtils.isBlank(ifSelfSupport)){
            log.info("资方业务为空{}",ifSelfSupport);
            return false;
        }
        if(StringUtils.isBlank(ifFundBus)||StringUtils.isBlank(ifAccordFund)){
            log.info("资方业务为空{}",ifFundBus+","+ifAccordFund);
            return false;
        }
        // 租赁类型
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            log.info("资方业务维度为空");
            return false;
        }
        // 分割租赁类型
        String [] fitLeaseTypeArr = minDimensionValue.split("\\|");
        boolean fitLeaseTypeFlag = false;
        for(int i = 0;i < fitLeaseTypeArr.length;i++){
//            if(ifFundBus.equals(fitLeaseTypeArr[i])&&ifAccordFund.equals(fitLeaseTypeArr[i])){
//                fitLeaseTypeFlag = true;
//            }
            if("0".equals(ifSelfSupport)){
                fitLeaseTypeFlag = true;
            }
        }
        if(!fitLeaseTypeFlag){
            log.info("未匹配到维度");
            return false;
        }
        return true;
    }

    /**
     * todo 租赁类型过滤
     * @参数
     * @返回
     * @作者 仇招
     * @日期 2022/8/12 15:33
     **/
    private Boolean getFitLeaseType(JSONObject jsonObject,ProductDimensionEntityDTO dimensionEntityDTO) throws Exception {
        MFLogger.info("匹配维度租赁类型");
        if(StringUtils.isBlank(jsonObject.getString("applyId"))){
            MFLogger.info("申请ID为空");
            return false;
        }
        // 申请编号
        LeaseApplyEntityDTO applyEntityDTO = leaseInterface.getLeaseApplyEntityById(jsonObject.getString("applyId"));
        String leaseType = applyEntityDTO.getLeaseType();
        if(StringUtils.isBlank(leaseType)){
            MFLogger.info("租赁类型为空");
           return false;
        }
        // 租赁类型
        String minDimensionValue = dimensionEntityDTO.getMinDimensionValue();
        if(StringUtils.isBlank(minDimensionValue)){
            MFLogger.info("租赁类型维度为空");
            return false;
        }
        // 分割租赁类型
        String [] fitLeaseTypeArr = minDimensionValue.split("\\|");
        boolean fitLeaseTypeFlag = false;
        for(int i = 0;i < fitLeaseTypeArr.length;i++){
            if(leaseType.equals(fitLeaseTypeArr[i])){
                fitLeaseTypeFlag = true;
            }
        }
        if(!fitLeaseTypeFlag){
            MFLogger.info("未匹配到维度");
            return false;
        }
        return true;
    }

    /**
     *  生成pdf文件
     * @param obj{
     *           templateId:模板编号
     *           bizNo: 业务流水
     *           subBizNo:子项业务流水
     *           parmList：参数列表json格式
     *           prdUniqueVal：流程唯一号
     *           flowNo：流程编号
     *           nodeNo：节点编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     *
     *
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject generatePdfFile(JSONObject obj)throws  ServiceException{
        MFLogger.info("生成pdf文件:传递的参数："+obj.toJSONString());
        JSONObject reuslt =new JSONObject();
        String templateId=obj.getString("templateId");
        String bizNo=obj.getString("bizNo");
        String subBizNo=StrUtil.emptyToDefault(obj.getString("subBizNo"),"");
        String pdfFilePath="";
        JSONObject parmList=obj.getJSONObject("parmList");
        parmList.put("subBizNo",subBizNo);
        parmList.put("bizNo",bizNo);
        DocBizTemplateEntity docBizTemplateEntity=new DocBizTemplateEntity();
        docBizTemplateEntity.setTemplateId(templateId);
        docBizTemplateEntity.setBizNo(bizNo);
        docBizTemplateEntity.setSubBizNo(subBizNo);
//     * 	"prdUniqueVal":"prdId",
//     * 	"flowNo":"flowNo",
//     * 	"nodeNo":"nodeNo",
        docBizTemplateEntity.setPrdUniqueVal(obj.getString("prdUniqueVal"));
        docBizTemplateEntity.setFlowNo(obj.getString("flowNo"));
        docBizTemplateEntity.setNodeNo(obj.getString("nodeNo"));
        List<DocBizTemplateEntity> bizTemplateEntities= docBizTemplateService.findList(docBizTemplateEntity);
        DocBizTemplateEntity cusBizObj=bizTemplateEntities.get(0);
        /**
         * 首先判断 最后一次预览的时间 是否大于文档最后一次修改的时间，如果是 直接贷款文档的时间，如果不是，需要重新生成预览文档。
         */
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        JSONObject parm=new JSONObject();
        parm.put("templateId",templateId);
        parm.put("versionNo",docTemplateModelEntity.getVersionNo());
        parm.put("wid",subBizNo);
        String fileName=docTemplateModelEntity.getTemplateFileName();
        String  thisMouldFilePath=docMouldPath+docTemplateModelEntity.getTemplateFileName();
        String  suffixName=new FileUtils().getFileSuffixName(fileName);
        String  saveFileName= cusBizObj.getBtId()+"."+suffixName;
        String filePath=mouldPath+"saveMould/"+saveFileName;
        reuslt.put("saveFileName",saveFileName);
        reuslt.put("mode","view");
        reuslt.put("btId",cusBizObj.getBtId());
        String fileUrl=docUrl+"saveMould"+File.separator+saveFileName;
//        reuslt.put("fileUrl",fileUrl);
        parm.put("filePath",filePath);
        long  previewTime =getLastPreviewTime(parm);//上次预览时间毫秒数
        long  mouldLastUpdDate=getMouldLastUpdTime(parm);//文档最后调整时间毫秒数
        try{
            long sendTime = 1;
            long elinkTime = System.currentTimeMillis();
            MFLogger.info("替换书签开始时间:"+elinkTime);
            JSONObject busData =getTagData( templateId, parmList );
            DocTemplateCheckDto templateCheckDto = new DocTemplateCheckDto();
            templateCheckDto.setTemplateId(templateId);
            templateCheckDto.setTagKeyInfo(busData);
            JSONObject checkObj = docTemplateTagCheckService.checkTemplateTagValue(templateCheckDto);
            if (!"0000".equals(checkObj.getString("code"))){
                return checkObj;
            }
            JSONObject tagData= getTagsJson(templateId);
            boolean  reCreateFileFlag=false;
            if(previewTime==0){
                reCreateFileFlag=true;
            }else{
                if(previewTime<=mouldLastUpdDate) {
                    reCreateFileFlag=true;
                }
            }
            if("1".equals(obj.getString("reLoadFlag"))){
                //如果是重新加载
                reCreateFileFlag=true;
            }
            if (reCreateFileFlag) {
                DocManageEntity docManageEntity = new DocManageEntity();
                String newFilePath = filePath;
                Map map = new HashMap();
//                map.put("docVersion", docVersion);
//                map.put("docNo", docNo);
                Parse parse = new Parse();
                parse.setDocPath(newFilePath);
                parse.setBusData(busData);
                parse.setTagData(tagData);
                String type = "text";
                if("2".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="spreadsheet";
                }
                if("3".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="pdf";
                }
                FileUtil.mkParentDirs(newFilePath);
                new FileUtils().copyFile(thisMouldFilePath, newFilePath);

                if ("text".equals(type)) {
                    parseRenderService.renderWordTemplate(parse, newFilePath);
                } else if ("spreadsheet".equals(type)) {
                    parseRenderService.renderExcelTemplate(parse, newFilePath);
                }
                long endElink = System.currentTimeMillis();
                long elink = elinkTime-endElink;
                MFLogger.info("替换书签结束时间:"+endElink+"替换书签运行时间"+elink);
//                {
//                    "url":"http://192.168.2.196:7019/mftcc-doc-server-dev/file/docFileInf/previewOfficeFile/c483727a86f24aa68434d54554d0fcb3",
//                        "fileType":"docx",
//                        "outputtype":"pdf"
//
//                }
                sendTime = System.currentTimeMillis();
                MFLogger.info("转换pdf开始时间:"+sendTime);
                MFLogger.info("成功生成word文档："+newFilePath);
                String docFileUrl=serverPath+applicationName+"/file/docFileInf/getMouldStream/2/"+saveFileName;
                MFLogger.info("转换pdf的源文件路径："+docFileUrl);
                String fileType=suffixName;
                String  outputtype="pdf";
                DocConvertDto convertDto=new DocConvertDto();
                convertDto.setUrl(docFileUrl);
                convertDto.setFiletype(fileType);
                convertDto.setOutputtype(outputtype);
                JSONObject convertJson=fileFormatConvert(convertDto);
                MFLogger.info("转换pdf的结果："+convertJson.toJSONString());
                if("0000".equals(convertJson.getString("code"))){
                    String pdfUrl=convertJson.getString("fileUrl");
//                    MFLogger.info("转换pdf后的路径："+pdfUrl);
//                    //判断二级域名
//                    //判断是否有二级域名
//                    int dex_=officeServerPathInner.lastIndexOf("/");
//                    if(dex_>7){
//                        String fromStr=officeServerPathInner.substring(0,dex_);
//                        pdfUrl=pdfUrl.replaceAll(fromStr,officeServerPathInner);
//                    }
//
//                    MFLogger.info("****处理完二级域名后的pdf路径："+pdfUrl);
                    String  pdfFileName=cusBizObj.getBtId()+".pdf";
                    pdfFilePath=mouldPath+"saveMould/"+pdfFileName;
                    File toSave = new File(pdfFilePath);
                    DocUtil.downloadToFile(pdfUrl, toSave);//pdf下载最新保存文档
                    reuslt.put("saveFileName",pdfFilePath);
                    MFLogger.info("转换pdf后的路径 ：成功保存到本地");
                    cusBizObj.setInitQuery(pdfFilePath);//设置pdf的物理路径
                    cusBizObj.setSort(pdfFileName);//设置pdf 的文件名
                    if("obs".equals(uploadOutterFunction)){

                        reuslt=upLoadOBSOtherServer(cusBizObj);
                        MFLogger.info("转换pdf后的路径 ：上传OBS:"+reuslt.toJSONString());
                    }else{
                        DocBizTemplateEntity updBean=new DocBizTemplateEntity();
                        updBean.setBtId(cusBizObj.getBtId());
                        updBean.setTemplateSuffix("3");
                        updBean.setSaveFileName(pdfFileName);
                        updBean.setSavePath(mouldPath+"saveMould/");
                        docBizTemplateService.update(updBean);
                        reuslt.put("code","0000");
                    }
                }else{
                    reuslt=convertJson;
                }
            } else {

                if(StringUtils.isNotEmpty(cusBizObj.getPdfObsId())){
                    String previewFileNmae = obj.getString("previewFileNmae");
                    tagData.getJSONObject("");
                    reuslt=new JSONObject();
                    reuslt.put("code","0000");
                    reuslt.put("pdfObsId",cusBizObj.getPdfObsId());
                }else{
//                    String  pdfFileName=templateId+"_"+obj.getString("wid")+"_"+bizNo+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+".pdf";
                    String  pdfFileName=docBizTemplateEntity.getBtId()+".pdf";
//                    pdfFilePath=mouldPath+"saveMould/"+pdfFileName;
                    pdfFilePath=docBizTemplateEntity.getSavePath()+docBizTemplateEntity.getSaveFileName();
                    reuslt.put("saveFileName",pdfFilePath);
                    cusBizObj.setInitQuery(pdfFilePath);//设置pdf的物理路径
                    cusBizObj.setSort(pdfFileName);//设置pdf 的文件名
                    if("obs".equals(uploadOutterFunction)){
                        reuslt=upLoadOBSOtherServer(cusBizObj);

                    }

                }

            }
            long endTime = System.currentTimeMillis();
            long runTime = endTime-sendTime;
            MFLogger.info("替换书签结束时间:"+endTime+"替换书签运行时间"+runTime);


        }catch (Exception e){
            MFLogger.error("SignConract-error-请求参数："+obj.toJSONString()+"转换生成pdf文件报错",e);
        }
        reuslt.put("btId",cusBizObj.getBtId());
        return  reuslt ;
    }
    /**
     *  生成替换书签后的文件
     * @param obj{
     *           templateId:模板编号
     *           bizNo: 业务流水
     *           subBizNo:子项业务流水
     *           parmList：参数列表json格式
     *           prdUniqueVal：流程唯一号
     *           flowNo：流程编号
     *           nodeNo：节点编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     *
     *
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject getReplaceTagFile(JSONObject obj)throws  ServiceException{

        JSONObject reuslt =new JSONObject();
        String templateId=obj.getString("templateId");
        String bizNo=obj.getString("bizNo");
        String subBizNo=obj.getString("subBizNo");
        JSONObject parmList=obj.getJSONObject("parmList");
        parmList.put("subBizNo",subBizNo);

        DocBizTemplateEntity docBizTemplateEntity=new DocBizTemplateEntity();
        docBizTemplateEntity.setTemplateId(templateId);
        docBizTemplateEntity.setBizNo(bizNo);
        docBizTemplateEntity.setSubBizNo(subBizNo);
//     * 	"prdUniqueVal":"prdId",
//     * 	"flowNo":"flowNo",
//     * 	"nodeNo":"nodeNo",
        docBizTemplateEntity.setPrdUniqueVal(obj.getString("prdUniqueVal"));
        docBizTemplateEntity.setFlowNo(obj.getString("flowNo"));
        docBizTemplateEntity.setNodeNo(obj.getString("nodeNo"));
        if(obj.containsKey("btId")){
            docBizTemplateEntity.setBtId(obj.getString("btId"));
        }

        List<DocBizTemplateEntity> bizTemplateEntities= docBizTemplateService.findList(docBizTemplateEntity);
        DocBizTemplateEntity cusBizObj=bizTemplateEntities.get(0);

        /**
         * 首先判断 最后一次预览的时间 是否大于文档最后一次修改的时间，如果是 直接贷款文档的时间，如果不是，需要重新生成预览文档。
         */
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        JSONObject parm=new JSONObject();
        parm.put("templateId",templateId);
        parm.put("versionNo",docTemplateModelEntity.getVersionNo());
        parm.put("wid",obj.getString("wid"));
        String fileName=docTemplateModelEntity.getTemplateFileName();

        String  thisMouldFilePath=docMouldPath+docTemplateModelEntity.getTemplateFileName();


        String  suffixName=new FileUtils().getFileSuffixName(fileName);

        String  saveFileName=templateId+"_"+obj.getString("wid")+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
        if(StringUtils.isNotEmpty(parmList.getString("subBizNo"))){
            String subBizNoStr=parmList.getString("subBizNo");
            saveFileName=templateId+"_"+obj.getString("wid")+"_"+subBizNoStr+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
        }
        String filePath=mouldPath+"saveMould/"+saveFileName;
        reuslt.put("saveFileName",saveFileName);
        reuslt.put("mode","view");
        String fileUrl=docUrl+"saveMould"+File.separator+saveFileName;
        reuslt.put("fileUrl",fileUrl);
        reuslt.put("filePath",filePath);
        parm.put("filePath",filePath);
        long  previewTime =getLastPreviewTime(parm);//上次预览时间毫秒数
        long  mouldLastUpdDate=getMouldLastUpdTime(parm);//文档最后调整时间毫秒数


        try{

            JSONObject busData =getTagData( templateId, parmList );

            JSONObject tagData= getTagsJson(templateId);

            boolean  reCreateFileFlag=false;
            if(previewTime==0){
                reCreateFileFlag=true;
            }else{
                if(previewTime<=mouldLastUpdDate) {
                    reCreateFileFlag=true;
                }
            }

            if("1".equals(obj.getString("reLoadFlag"))){
                //如果是重新加载
                reCreateFileFlag=true;
            }


            if (reCreateFileFlag) {

                DocManageEntity docManageEntity = new DocManageEntity();

                String newFilePath = filePath;
                Map map = new HashMap();
//                map.put("docVersion", docVersion);
//                map.put("docNo", docNo);
                Parse parse = new Parse();
                parse.setDocPath(newFilePath);
                parse.setBusData(busData);
                parse.setTagData(tagData);
                String type = "text";
                if("2".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="spreadsheet";
                }
                if("3".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="pdf";
                }

                FileUtil.mkParentDirs(newFilePath);
                new FileUtils().copyFile(thisMouldFilePath, newFilePath);

                if ("text".equals(type)) {
                    parseRenderService.renderWordTemplate(parse, newFilePath);
                } else if ("spreadsheet".equals(type)) {
                    parseRenderService.renderExcelTemplate(parse, newFilePath);
                }

                //
                docBizTemplateEntity.setSavePath(mouldPath+"saveMould/");//设置模板的保存路径
                docBizTemplateEntity.setSaveFileName(saveFileName);//设置模板的保存名称
                docBizTemplateEntity.setWriteFlag("1");
                docBizTemplateService.update(docBizTemplateEntity);//修改保存路径和保存的文件名

                reuslt.put("code","success");

            }



        }catch (Exception e){
            MFLogger.error("SignConract-error-请求参数："+obj.toJSONString()+"生成替换书签后的文件报错",e);
        }

        return  reuslt ;
    }
    /**
     *  生成pdf文件
     * @param obj{
     *           templateId:模板编号
     *           bizNo: 业务流水
     *           subBizNo:子项业务流水
     *           parmList：参数列表json格式
     *           prdUniqueVal：流程唯一号
     *           flowNo：流程编号
     *           nodeNo：节点编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     *
     *
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject getPdfFile(JSONObject obj)throws  ServiceException{

        JSONObject reuslt =new JSONObject();
        String templateId=obj.getString("templateId");
        String bizNo=obj.getString("bizNo");
        String subBizNo=obj.getString("subBizNo");
        JSONObject parmList=obj.getJSONObject("parmList");
        parmList.put("subBizNo",subBizNo);

        DocBizTemplateEntity docBizTemplateEntity=new DocBizTemplateEntity();
        docBizTemplateEntity.setTemplateId(templateId);
        docBizTemplateEntity.setBizNo(bizNo);
        docBizTemplateEntity.setSubBizNo(subBizNo);
//     * 	"prdUniqueVal":"prdId",
//     * 	"flowNo":"flowNo",
//     * 	"nodeNo":"nodeNo",
        docBizTemplateEntity.setPrdUniqueVal(obj.getString("prdUniqueVal"));
        docBizTemplateEntity.setFlowNo(obj.getString("flowNo"));
        docBizTemplateEntity.setNodeNo(obj.getString("nodeNo"));
        List<DocBizTemplateEntity> bizTemplateEntities= docBizTemplateService.findList(docBizTemplateEntity);
        DocBizTemplateEntity cusBizObj=bizTemplateEntities.get(0);

        /**
         * 首先判断 最后一次预览的时间 是否大于文档最后一次修改的时间，如果是 直接贷款文档的时间，如果不是，需要重新生成预览文档。
         */
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        JSONObject parm=new JSONObject();
        parm.put("templateId",templateId);
        parm.put("versionNo",docTemplateModelEntity.getVersionNo());
        parm.put("wid",obj.getString("wid"));
        String fileName=docTemplateModelEntity.getTemplateFileName();

        String  thisMouldFilePath=docMouldPath+docTemplateModelEntity.getTemplateFileName();


        String  suffixName=new FileUtils().getFileSuffixName(fileName);

//        String  saveFileName=templateId+"_"+obj.getString("wid")+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
//        if(StringUtils.isNotEmpty(parmList.getString("subBizNo"))){
//            String subBizNoStr=parmList.getString("subBizNo");
//            saveFileName=templateId+"_"+obj.getString("wid")+"_"+subBizNoStr+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
//        }
        String  saveFileName=docBizTemplateEntity.getBtId()+"."+suffixName;
        String filePath=mouldPath+"saveMould/"+saveFileName;
        reuslt.put("saveFileName",saveFileName);
        reuslt.put("mode","view");
        String fileUrl=docUrl+"saveMould"+File.separator+saveFileName;
        reuslt.put("fileUrl",fileUrl);
        parm.put("filePath",filePath);
        long  previewTime =getLastPreviewTime(parm);//上次预览时间毫秒数
        long  mouldLastUpdDate=getMouldLastUpdTime(parm);//文档最后调整时间毫秒数


        try{

            JSONObject busData =getTagData( templateId, parmList );

            JSONObject tagData= getTagsJson(templateId);

            boolean  reCreateFileFlag=false;
            if(previewTime==0){
                reCreateFileFlag=true;
            }else{
                if(previewTime<=mouldLastUpdDate) {
                    reCreateFileFlag=true;
                }
            }

            if("1".equals(obj.getString("reLoadFlag"))){
                //如果是重新加载
                reCreateFileFlag=true;
            }


            if (reCreateFileFlag) {

                DocManageEntity docManageEntity = new DocManageEntity();

                String newFilePath = filePath;
                Map map = new HashMap();
//                map.put("docVersion", docVersion);
//                map.put("docNo", docNo);
                Parse parse = new Parse();
                parse.setDocPath(newFilePath);
                parse.setBusData(busData);
                parse.setTagData(tagData);
                String type = "text";
                if("2".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="spreadsheet";
                }
                if("3".equals(docTemplateModelEntity.getTemplateSuffix())){
                    type="pdf";
                }

                FileUtil.mkParentDirs(newFilePath);
                new FileUtils().copyFile(thisMouldFilePath, newFilePath);

                if ("text".equals(type)) {
                    parseRenderService.renderWordTemplate(parse, newFilePath);
                } else if ("spreadsheet".equals(type)) {
                    parseRenderService.renderExcelTemplate(parse, newFilePath);
                }
//                {
//                    "url":"http://192.168.2.196:7019/mftcc-doc-server-dev/file/docFileInf/previewOfficeFile/c483727a86f24aa68434d54554d0fcb3",
//                        "fileType":"docx",
//                        "outputtype":"pdf"
//
//                }
                String docFileUrl=serverPath+applicationName+"/file/docFileInf/getMouldStream/2/"+saveFileName;
                MFLogger.info("转换pdf的源文件路径："+docFileUrl);
                String fileType=suffixName;
                String  outputtype="pdf";
                DocConvertDto convertDto=new DocConvertDto();
                convertDto.setUrl(docFileUrl);
                convertDto.setFiletype(fileType);
                convertDto.setOutputtype(outputtype);
                JSONObject convertJson=fileFormatConvert(convertDto);
                MFLogger.info("转换pdf的结果："+convertJson.toJSONString());
                if("0000".equals(convertJson.getString("code"))){
                    String pdfUrl=convertJson.getString("fileUrl");

//                    MFLogger.info("转换pdf后的路径："+pdfUrl);
//                    //判断二级域名
//                    //判断是否有二级域名
//                    int dex_=officeServerPathInner.lastIndexOf("/");
//                    if(dex_>7){
//                        String fromStr=officeServerPathInner.substring(0,dex_);
//                        pdfUrl=pdfUrl.replaceAll(fromStr,officeServerPathInner);
//                    }
//
//                    MFLogger.info("****处理完二级域名后的pdf路径："+pdfUrl);

//                    String  pdfFileName=templateId+"_"+obj.getString("wid")+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+".pdf";
                    String  pdfFileName=docBizTemplateEntity.getBtId()+".pdf";
                    String pdfFilePath=mouldPath+"saveMould/"+pdfFileName;
                    File toSave = new File(pdfFilePath);
                    DocUtil.downloadToFile(pdfUrl, toSave);//pdf下载最新保存文档
                    MFLogger.info("转换pdf后的路径 ：成功保存到本地");
                    cusBizObj.setInitQuery(pdfFilePath);//设置pdf的物理路径
                    cusBizObj.setSort(pdfFileName);//设置pdf 的文件名

                    docBizTemplateEntity.setSavePath(mouldPath+"saveMould/");//设置模板的保存路径
                    docBizTemplateEntity.setSaveFileName(pdfFileName);//设置模板的保存名称
                    docBizTemplateService.update(docBizTemplateEntity);//修改保存路径和保存的文件名

                    if("obs".equals(uploadOutterFunction)){

                        reuslt=upLoadOBSOtherServer(cusBizObj);
                        MFLogger.info("转换pdf后的路径 ：上传OBS:"+reuslt.toJSONString());
                    }
                }else{
                    reuslt=convertJson;
                }




            } else {

                if(StringUtils.isNotEmpty(cusBizObj.getPdfObsId())){

                    String previewFileNmae = obj.getString("previewFileNmae");
                    tagData.getJSONObject("");
                    reuslt=new JSONObject();
                    reuslt.put("code","0000");
                    reuslt.put("pdfObsId",cusBizObj.getPdfObsId());
                }else{

//                    String  pdfFileName=templateId+"_"+obj.getString("wid")+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+".pdf";
//                    String pdfFilePath=mouldPath+"saveMould/"+pdfFileName;
                    String  pdfFileName=docBizTemplateEntity.getSaveFileName();
                    String pdfFilePath=docBizTemplateEntity.getSavePath();
                    cusBizObj.setInitQuery(pdfFilePath);//设置pdf的物理路径
                    cusBizObj.setSort(pdfFileName);//设置pdf 的文件名
                    if("obs".equals(uploadOutterFunction)){

                        reuslt=upLoadOBSOtherServer(cusBizObj);

                    }
                }

            }



        }catch (Exception e){
            MFLogger.error("SignConract-error-请求参数："+obj.toJSONString()+"生成pdf文件报错",e);
        }

        return  reuslt ;
    }


    /**
     * 通过obs上传到外网服务
     * @param docBizTemplateEntity
     * @return
     * @throws ServiceException
     */
    private JSONObject  upLoadOBSOtherServer(DocBizTemplateEntity docBizTemplateEntity)throws  ServiceException{

        JSONObject resultData=new JSONObject();

        String elinkReqNo="";
        try{


            JSONObject requestJson = new JSONObject();

            requestJson.put("callNo", "OBS0005");

            JSONObject paramData=new JSONObject();
            paramData.put("appId",docBizTemplateEntity.getBizNo()); //项目编号
            paramData.put("busReqNo",docBizTemplateEntity.getBtId()); //项目编号
            paramData.put("tag",docBizTemplateEntity.getBizNo());//业务标记（建议上传与业务相关联的标识）
            String opNo= (String) requestUtil.getUserInfo("opNo");
            if(StringUtils.isBlank(opNo)){   //没获取到操作员 默认0000
                opNo="0000";
            }
            paramData.put("createdBy",opNo);//创建人

            String savePath=docBizTemplateEntity.getInitQuery();//最终的物理路径
            File f = new File(savePath);
            String  mimeType=new MimetypesFileTypeMap().getContentType(f);
            paramData.put("mimeType",mimeType);//Mime类型

            String originalName=docBizTemplateEntity.getSort();

            paramData.put("originalName",originalName);//原文件名
            String base64Str=FileUtils.encodeBase64File(savePath);
            paramData.put("base64File",base64Str);//base64后的文件流


            //paramData是三方接口的入参必传,如果没有参数传new JSONObject()

            requestJson.put("paramData", paramData);


            String result = elinkApiFeignClient.itoData(requestJson);


            JSONObject resultJson = JSONObject.parseObject(result);
            if("1".equals(resultJson.getString("code"))){
//                如果上传成功
                JSONObject returnData=resultJson.getJSONObject("data");
                if(returnData.getString("code").equals("SUCCESS")){
                    resultData.put("code","0000");
                    DocBizTemplateEntity updBean=new DocBizTemplateEntity();
                    updBean.setBtId(docBizTemplateEntity.getBtId());
                    String obsId=returnData.getJSONObject("data").getString("id");

                    updBean.setPdfObsId(obsId);
                    docBizTemplateService.update(updBean);
                    docBizTemplateEntity.setPdfObsId(obsId);
                    resultData.put("pdfObsId",obsId);
                    resultData.put("templateObj",docBizTemplateEntity);
//                    resultData.put("data",returnData.getJSONObject("data"));
                }else{
                    resultData.put("code","1111");
                    resultData.put("msg","obs上传失败");
                }

            }else{
                resultData.put("code","1111");
                resultData.put("msg","obs上传失败");
            }
            //elink的调用编号

            elinkReqNo = resultJson.getString("mftccReqNo");
        }catch (Exception e){
            throw  new ServiceException("向其他服务上传要件",elinkReqNo,e);
        }
        return  resultData;
    }

    /**
     *  获取消息模板内容
     * @param obj
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject getMsgContent(Map<String,Object> obj)throws  ServiceException{
        JSONObject reuslt =new JSONObject();
        String templateId=(String) obj.get("templateId");
        Map<String,String> parmList=(Map)obj.get("parmList");

        /**
         * 首先判断 最后一次预览的时间 是否大于文档最后一次修改的时间，如果是 直接贷款文档的时间，如果不是，需要重新生成预览文档。
         */
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        JSONObject parm=new JSONObject();
        parm.put("templateId",templateId);
        parm.put("versionNo",docTemplateModelEntity.getVersionNo());
        String  wid=(String)obj.get("wid");
        if(StringUtils.isEmpty(wid)){
            wid="wid";

        }
//        parm.put("wid",StringUtils.obj.get("wid"));
        String fileName=docTemplateModelEntity.getTemplateFileName();

        String  thisMouldFilePath=docMouldPath+docTemplateModelEntity.getTemplateFileName();

        String  suffixName=new FileUtils().getFileSuffixName(fileName);;

        String  saveFileName=templateId+"_"+wid+"_"+templateId+"_"+docTemplateModelEntity.getTemplateNo()+"."+suffixName;
        String filePath=mouldPath+"saveMould/"+saveFileName;
        reuslt.put("saveFileName",saveFileName);
        reuslt.put("mode","view");
        String fileUrl=docUrl+"saveMould"+File.separator+saveFileName;
        reuslt.put("fileUrl",fileUrl);
        parm.put("filePath",filePath);
        long  previewTime =getLastPreviewTime(parm);//上次预览时间毫秒数
        long  mouldLastUpdDate=getMouldLastUpdTime(parm);//文档最后调整时间毫秒数


        try{

            JSONObject tmpPram=JSONObject.parseObject(JSONObject.toJSONString(parmList));
            JSONObject busData =getTagData( templateId, tmpPram );

            JSONObject tagData= getTagsJson(templateId);



//            if(previewTime<=mouldLastUpdDate){
//            if(true){

            DocManageEntity docManageEntity = new DocManageEntity();

            String newFilePath =filePath;
            Map map = new HashMap();
//                map.put("docVersion", docVersion);
//                map.put("docNo", docNo);
            Parse parse = new Parse();
            parse.setDocPath(newFilePath);
            parse.setBusData(busData);
            parse.setTagData(tagData);
            String type ="text";

            FileUtil.mkParentDirs(newFilePath);
            new FileUtils().copyFile(thisMouldFilePath,newFilePath);

            if ("text".equals(type)){
                parseRenderService.renderWordTemplate(parse, newFilePath);
            }else if("spreadsheet".equals(type)){
                parseRenderService.renderExcelTemplate(parse, newFilePath);
            }





//            }else{
////                String  previewFileNmae=obj.getString("previewFileNmae");
//                tagData.getJSONObject("");
//
//            }



            //读取文本内容

            ConvertBody convertBody = new ConvertBody();
            convertBody.setAsync(false);
            String docSuffix=new FileUtils().getFileSuffixName(filePath);
            convertBody.setFiletype(docSuffix);
            convertBody.setKey(RandomUtil.randomString(20));
            convertBody.setOutputtype("txt");
            convertBody.setUrl(fileUrl);
            //调用weboffice转换接口
            String accessResult = HttpRequest.post(docConvertUrl).body(JSON.toJSONString(convertBody)).execute().body();
            JSONObject resultJSON = JSONObject.parseObject(accessResult);
            String txtPath = filePath.split("\\.")[0]+".txt";
            //下载转换好的PDF到本地
            HttpUtil.downloadFile(resultJSON.getString("fileUrl"), txtPath);

            String  txtConetent=new  FileUtils().readFileContent(txtPath);
            txtConetent=txtConetent.replaceAll("\\ufeff","");
            reuslt.put("code","0000");
            reuslt.put("content",txtConetent);






        }catch (Exception e){
            MFLogger.error("SignConract-error-请求参数："+obj+"获取消息模板内容报错",e);
            reuslt.put("code","1111");
            reuslt.put("content","");
            reuslt.put("error",e);
            e.printStackTrace();
        }

        return  reuslt ;
    }


    /**
     * 文档格式转化
     * @param docConvertDto
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject fileFormatConvert(@RequestBody DocConvertDto docConvertDto)throws Exception{

        JSONObject result=new JSONObject();
        try{

            if(StringUtils.isEmpty(docConvertDto.getUrl())){
                result.put("code","1111");
                result.put("msg","被转换文件路径为空");
                return  result;
            }
            if(StringUtils.isEmpty(docConvertDto.getFiletype())){
                result.put("code","2222");
                result.put("msg","被转换文件类型为空");
                return  result;
            }
            if(StringUtils.isEmpty(docConvertDto.getOutputtype())){
                result.put("code","3333");
                result.put("msg","要转换的文件类型为空");
                return  result;
            }
            docConvertDto.setAsync(false);
            docConvertDto.setKey(RandomUtil.randomString(20));
            String parmJson=JSONObject.toJSONString(docConvertDto);
            //调用weboffice转换接口
            String convertResult = HttpRequest.post(docConvertUrl).body(parmJson).execute().body();
            JSONObject resultJSON = JSONObject.parseObject(convertResult);
            if(resultJSON.containsKey("fileUrl")){
                result.put("code","0000");
                result.put("msg","转换成功");
                result.put("fileUrl",resultJSON.getString("fileUrl"));
            }

//            String pdfPath = "D:/lisittpng.pdf";
//            //下载转换好的PDF到本地
//            HttpUtil.downloadFile(resultJSON.getString("fileUrl"), pdfPath);

        }catch (Exception e){
            MFLogger.error("SignConract-error-文档格式转化",e);
            throw  e;
        }
        return result;
    }


    /**
     * 文档格式转化（对外）
     * @param converParm{
     *
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject fileFormatConvertExternal(@RequestBody Map<String ,String >converParm){

        JSONObject result=new JSONObject() ;
        try{

            DocConvertDto docConvertDto=new DocConvertDto();
            docConvertDto.setUrl(converParm.get("url"));
            docConvertDto.setFiletype(converParm.get("fileType"));
            docConvertDto.setOutputtype(converParm.get("outputtype"));
            result=  fileFormatConvert(docConvertDto);
        }catch (Exception e){
            result.put("code","4444");
            result.put("msg","文档格式转化（对外）报错："+e.getMessage());
            MFLogger.error("文档格式转化（对外）",e);

        }
        return result;


    }





    /**
     *  获取模板的保存路径
     * @return
     */
    @Override
    public  String getDocMouldPath(){
        return docMouldPath;
    }
    @Override
    public void insert(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException {
        try{
            Date dateTime=new Date();
            if(StringUtils.isEmpty(docTemplateModelEntity.getTemplateNo())){

                if(StringUtils.isBlank(docTemplateModelEntity.getTemplateFile())){
                    //如果不是导入文件，需要生成新的模板编号
                    docTemplateModelEntity.setTemplateNo(UUIDUtil.getUUID());
                }
            }
            docTemplateModelEntity.setTemplateId(UUIDUtil.getUUID());
            docTemplateModelEntity.setIfPublic("0");
            docTemplateModelEntity.setCreateTime(dateTime);
            docTemplateModelEntity.setUpdateTime(dateTime);

            String  newVersionNo="1";
            docTemplateModelEntity.setVersionNo(newVersionNo);
            docTemplateModelEntity.setCorpId((String)requestUtil.getUserInfo("corpId"));
            String  docName="";
            if(DocConstant.TEMPLATE_SUFFIX_WORD.equals(docTemplateModelEntity.getTemplateSuffix())){
                docName=docTemplateModelEntity.getTemplateNo()+DocConstant.TEMPLATE_SUFFIX_WORD_SUFFIX_NAME;
            }
            if(DocConstant.TEMPLATE_SUFFIX_EXCEL.equals(docTemplateModelEntity.getTemplateSuffix())){
                docName=docTemplateModelEntity.getTemplateNo()+DocConstant.TEMPLATE_SUFFIX_EXCEL_SUFFIX_NAME;
            }
            docTemplateModelEntity.setTemplateFileName(docName);
            docTemplateModelEntity.setDocEditSts("0");

            //如果没有引入模板文件 且  没有copy其他的模板文件 ，需要新创建一个模板文件template_source
            if(StringUtils.isBlank(docTemplateModelEntity.getTemplateFile()) && StringUtils.isBlank(docTemplateModelEntity.getTemplateSource()) ){
                makeNewMouldFile(docTemplateModelEntity);
            }
            //创建对应的组件配置文件config.json
//            createPluginsConfigInfo(docTemplateModelEntity.getTemplateId());
            docTemplateModelMapper.insert(docTemplateModelEntity);

            //插入书签配置信息
            DocTemplateTagSetEntity  docTemplateTagBaseEntity=new DocTemplateTagSetEntity();
            BeanUtils.copyProperties(docTemplateModelEntity,docTemplateTagBaseEntity);
            docTemplateTagBaseEntity.setSerialNo(UUIDUtil.getUUID());
            Date tmpDate=new Date();
            docTemplateTagBaseEntity.setCreateTime(tmpDate);
            docTemplateTagBaseEntity.setUpdateTime(tmpDate);
            this.docTemplateTagSetService.insert(docTemplateTagBaseEntity);





        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTemplateModelEntity.getTemplateNo(),e);
        }


    }
    @Override
    public JSONObject clone(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException {
        JSONObject result=new JSONObject();
        try{
            if(StringUtils.isEmpty(docTemplateModelEntity.getTemplateSource())){
                result.put("code","1111");
                result.put("msg","母体模板不存在");

            }else{
                DocTemplateModelEntity motherBean=findById(docTemplateModelEntity.getTemplateSource());

                docTemplateModelEntity.setTemplateSourceName(motherBean.getTemplateName());

                Date dateTime=new Date();

                if(StringUtils.isEmpty(docTemplateModelEntity.getTemplateNo())){

                    docTemplateModelEntity.setTemplateNo(UUIDUtil.getUUID());
                }

                docTemplateModelEntity.setIfPublic("0");
                docTemplateModelEntity.setCreateTime(dateTime);
                docTemplateModelEntity.setUpdateTime(dateTime);

                String  newVersionNo="1";
                docTemplateModelEntity.setVersionNo(newVersionNo);
                docTemplateModelEntity.setCorpId((String)requestUtil.getUserInfo("corpId"));
                String  docName="";
                if(DocConstant.TEMPLATE_SUFFIX_WORD.equals(docTemplateModelEntity.getTemplateSuffix())){
                    docName=docTemplateModelEntity.getTemplateNo()+DocConstant.TEMPLATE_SUFFIX_WORD_SUFFIX_NAME;
                }
                if(DocConstant.TEMPLATE_SUFFIX_EXCEL.equals(docTemplateModelEntity.getTemplateSuffix())){
                    docName=docTemplateModelEntity.getTemplateNo()+DocConstant.TEMPLATE_SUFFIX_EXCEL_SUFFIX_NAME;
                }
                docTemplateModelEntity.setTemplateFileName(docName);
                docTemplateModelEntity.setDocEditSts("0");

                cloneNewMouldFile(docTemplateModelEntity,motherBean);//克隆模板文件
                //创建对应的组件配置文件config.json
//                createPluginsConfigInfo(docTemplateModelEntity.getTemplateId());
                docTemplateModelMapper.insert(docTemplateModelEntity);

                //克隆书签配置信息
                DocTemplateTagSetEntity  motherTagConfig=docTemplateTagSetService.findOneObject(motherBean.getTemplateId());//获取母体书签配置
                motherTagConfig.setSerialNo(UUIDUtil.getUUID());
                motherTagConfig.setTemplateId(docTemplateModelEntity.getTemplateId());
                motherTagConfig.setTemplateNo(docTemplateModelEntity.getTemplateNo());
                motherTagConfig.setVersionNo(docTemplateModelEntity.getVersionNo());
                Date tmpDate=new Date();
                motherTagConfig.setCreateTime(tmpDate);
                motherTagConfig.setUpdateTime(tmpDate);
                this.docTemplateTagSetService.insert(motherTagConfig);
                result.put("code","0000");
                result.put("msg","克隆成功");


            }


        }catch (Exception e){
            result.put("code","2222");
            result.put("msg","克隆模板报错");
            MFLogger.error("克隆模板报错",e);
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTemplateModelEntity.getTemplateNo(),e);
        }
        return result;

    }

    /**
     * 导入模板文件
     * @param file
     * @param templateNo
     * @return
     * @throws ServiceException
     */
    private  boolean importMouldFile(String templateNo,MultipartFile file)throws  ServiceException{
        boolean result=false;
        String fileName=file.getOriginalFilename();
        String fileType="."+new FileUtils().getFileSuffixName(fileName);
        String finalName="";
        String newFilePath="";
        if(DocConstant.TEMPLATE_SUFFIX_WORD_SUFFIX_NAME.equals(fileType)){
            //如果是word
            finalName=templateNo+DocConstant.TEMPLATE_SUFFIX_WORD_SUFFIX_NAME;;//文件名称
        }else{
            if(DocConstant.TEMPLATE_SUFFIX_EXCEL_SUFFIX_NAME.equals(fileType)){

                finalName=templateNo+DocConstant.TEMPLATE_SUFFIX_EXCEL_SUFFIX_NAME;;//文件名称
            }
        }
        newFilePath=docMouldPath+finalName;
        File dest0 = new File(docMouldPath);
        File dest = new File(dest0, finalName);
        if (!dest0.getParentFile().exists()) {
            dest0.getParentFile().mkdirs();
            //检测文件是否存在
        }
        if (!dest.exists()) {
            dest.mkdirs();
        }
        try{
            file.transferTo(dest);

        }catch ( Exception e){
            throw  new ServiceException("导入模板文件","",e);
        }
        result =true;
        return  result;
    }



    /**
     * 创建一个新的模板
     * @param docTemplateModelEntity
     * @return
     * @throws ServiceException
     */
    private boolean makeNewMouldFile(DocTemplateModelEntity docTemplateModelEntity)throws  ServiceException{
        boolean result=false;
        FileUtils fileUtils=new FileUtils();
        String fileName="";
        String filePath=docMouldPath;
        String originalFileName="";
        String  newFilePath="";
        String  orginalPath=new FileUtils().getOrignalFilePath();
        if(DocConstant.TEMPLATE_SUFFIX_WORD.equals(docTemplateModelEntity.getTemplateSuffix())){
            //如果是word
            fileName=docTemplateModelEntity.getTemplateNo()+DocConstant.TEMPLATE_SUFFIX_WORD_SUFFIX_NAME;;//文件名称

//            originalFileName= orginalPath+"newmould"+File.separator+DocConstant.TEMPLATE_ORIGINAL_WORD_NAME;
            originalFileName= "static/template/newmould"+File.separator+DocConstant.TEMPLATE_ORIGINAL_WORD_NAME;
            newFilePath=filePath+ fileName;

        }else{
            if(DocConstant.TEMPLATE_SUFFIX_EXCEL.equals(docTemplateModelEntity.getTemplateSuffix())){
                //如果是Excel
                fileName=docTemplateModelEntity.getTemplateNo()+DocConstant.TEMPLATE_SUFFIX_EXCEL_SUFFIX_NAME;;//文件名称
//                originalFileName= orginalPath+"newmould"+File.separator+DocConstant.TEMPLATE_ORIGINALEXCEL_NAME;
                originalFileName= "static/template/newmould"+File.separator+DocConstant.TEMPLATE_ORIGINALEXCEL_NAME;
                newFilePath=filePath+ fileName;
            }else{
                MFLogger.error(originalFileName,"不支持文件格式",new Exception("不支持文件格式"));
                throw new ServiceException("不支持文件格式","","");
            }

        }

        MFLogger.info("新的文件路径是："+newFilePath+" 原始的文件路径是："+originalFileName);

        if(StringUtils.isNotBlank(originalFileName) && StringUtils.isNotBlank(filePath)){

//            fileUtils.copyFile(originalFileName,newFilePath);
            InputStream inputStream=fileUtils.getResourceStream(originalFileName);
            fileUtils.copyFileByInputStream(inputStream,newFilePath);
            result=true;
        }else{
            MFLogger.error(originalFileName,"原始文件名或新的文件名称不能为空",new Exception("原始文件名或新的文件名称不能为空"));
            throw new ServiceException("原始文件名或新的文件名称不能为空","","");
        }
        return result ;

    }
    /**
     * 克隆一个新的模板
     * @param docTemplateModelEntity
     * @param montherBean 母体模板
     * @return
     * @throws ServiceException
     */
    private boolean cloneNewMouldFile(DocTemplateModelEntity docTemplateModelEntity,DocTemplateModelEntity montherBean)throws  ServiceException{
        boolean result=false;
        FileUtils fileUtils=new FileUtils();
        String fileName="";
        String filePath=docMouldPath;
        String  newFilePath="";
        String originalFileName= filePath+File.separator+montherBean.getTemplateFileName();//母体模板文件
        if(DocConstant.TEMPLATE_SUFFIX_WORD.equals(docTemplateModelEntity.getTemplateSuffix())){
            //如果是word
            fileName=docTemplateModelEntity.getTemplateNo()+DocConstant.TEMPLATE_SUFFIX_WORD_SUFFIX_NAME;;//文件名称
            newFilePath=filePath+ File.separator+fileName;

        }else{
            if(DocConstant.TEMPLATE_SUFFIX_EXCEL.equals(docTemplateModelEntity.getTemplateSuffix())){
                //如果是Excel
                fileName=docTemplateModelEntity.getTemplateNo()+DocConstant.TEMPLATE_SUFFIX_EXCEL_SUFFIX_NAME;;//文件名称
//                originalFileName= docMouldPath+ File.separator+"newmould"+File.separator+DocConstant.TEMPLATE_ORIGINALEXCEL_NAME;
                newFilePath=filePath+ File.separator+fileName;
            }else{
                MFLogger.error(originalFileName,"不支持文件格式",new Exception("不支持文件格式"));
                throw new ServiceException("不支持文件格式","","");
            }

        }

        if(StringUtils.isNotBlank(originalFileName) && StringUtils.isNotBlank(filePath)){

            fileUtils.copyFile(originalFileName,newFilePath);
            result=true;
        }else{
            MFLogger.error(originalFileName,"原始文件名或新的文件名称不能为空",new Exception("原始文件名或新的文件名称不能为空"));
            throw new ServiceException("原始文件名或新的文件名称不能为空","","");
        }
        return result ;

    }

    /**
     * 获取 新的版本编号
     * @param templateNo
     * @return
     * @throws ServiceException
     */
    private   String    getDocTemplateVersionNo(String templateNo )throws  ServiceException{
        int result=0;
        DocTemplateModelEntity qBean=new DocTemplateModelEntity();
        qBean.setTemplateNo(templateNo);
        List<DocTemplateModelEntity>currList=findDocTemplateList(qBean);
        if(currList!=null){
            int len=currList.size();
            int curNo=0;
            if(len>0){
                for(int i=0;i<len;i++){
                    curNo=Integer.getInteger(currList.get(i).getVersionNo());
                    if(result<curNo){
                        result=curNo;

                    }
                }
            }
        }
        result++;

        return  String.valueOf(result);
    }

    /**
     *   读取组件配置文件
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getPluginsData(String  templateId)throws  ServiceException{
        String plugsConfigFilePath=serverPath+applicationName+"/config.json"+templateId;
        JSONObject result=null;
        try{
            String fileContent=new FileUtils().readFileContent(plugsConfigFilePath);
            result =JSON.parseObject(fileContent);
            result.put("baseUrl",plugsDataBaseUrl);

            String filePrefix="../../";
            //修改组件路径
            JSONArray variations=result.getJSONArray("variations");
            String tmpUrl=filePrefix+variations.getJSONObject(0).getString("url");
            tmpUrl+="?templateId="+templateId;
            //组件图标路径
            JSONArray icons=variations.getJSONObject(0).getJSONArray("icons");
            if(icons!=null){
                int len=icons.size();

                for(int i=0;i<len;i++){

                    String name=icons.getString(i);
                    icons.set(i,filePrefix+name);
                }
            }
            variations.getJSONObject(0).put("url",tmpUrl);
            variations.getJSONObject(0).put("icons",icons);
            result.put("variations",variations);


        }catch ( Exception e){
            MFLogger.error("读取组件配置文件错误",e);
        }
        return  result;
    }








    @Override
    public void update(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException {
        try{
            docTemplateModelMapper.updateById(docTemplateModelEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTemplateModelEntity.getTemplateNo(),e);
        }
    }

    @Override
    public DocTemplateModelEntity findById(String templateNo) throws ServiceException {
        try{
            return docTemplateModelMapper.selectById(templateNo);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,templateNo,e);
        }
    }




    /**
     * 获取该模板的配置信息
     * @param parmJson
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getMouldConfigInfo(JSONObject parmJson) throws ServiceException {

        JSONObject result=null;
        try{
            String templateId=parmJson.getString("templateId");//模板编号
            String mode=parmJson.getString("mode");//打开方式 编辑 edit或者预览 view
            String previewFileName=parmJson.getString("fileName");//如果是预览，预览文件的名称

            DocTemplateModelEntity  mouldObj=docTemplateModelMapper.selectById(templateId);
            JSONObject jsonObject=new JSONObject();
            String docName=mouldObj.getTemplateFileName();//模板文件名称
            jsonObject.put("docName",docName);
            String  docPath=docMouldPath+docName;
            jsonObject.put("docPath",docPath);


            String docFileUrl=serverPath+applicationName+"/file/docFileInf/getMouldStream/1/"+docName;
            if("view".equals(mode)){
                docFileUrl=serverPath+applicationName+"/file/docFileInf/getMouldStream/2/"+previewFileName;
                if("1".equals(parmJson.getString("canWrite"))){
                    mode="eidt";
                }
            }



            jsonObject.put("docUrl",docFileUrl);

            String  callBackUrlStr=server+applicationName+"/doc/docmanage/track/";
            jsonObject.put("docShowName",mouldObj.getTemplateName());
            String docType=new FileUtils().getFileSuffixName(docName);
//            if(DocConstant.TEMPLATE_SUFFIX_WORD.equals(mouldObj.getTemplateSuffix())){

                jsonObject.put("docType",docType);
//            }
            jsonObject.put("officeServer",officeServer);//onlineOffice文件服务器

            if(StringUtils.isNotEmpty(previewFileName)){
                jsonObject.put("callbackUrl",callBackUrlStr+templateId+"/"+previewFileName);//回调路径
            }else{

                jsonObject.put("callbackUrl",callBackUrlStr+templateId+"/no");//回调路径
            }
            jsonObject.put("goBackUrl",serverUrl+"mftcc-doc-web/mould/docTemplateModel/findByPage");//报错后的返回路径
            jsonObject.put("openType",mode);//打开方式 编辑 edit或者预览 view
            jsonObject.put("clientType","desktop");//打开方式 访问设备类型 desktop-pc展示 mobile-手机端

            String []pluginsUrlAry=null;
            if("edit".equals(mode) && !"1".equals(parmJson.getString("canWrite"))){

//                String configFile=tagPluginFilePath+"puginsConfig/"+templateId+"/config.json";
//                File configInfo=new File(configFile);
//                if(!configInfo.exists()){
//                    createPluginsConfigInfo(templateId);
//                }
//                http://192.168.2.196:7019/mftcc-doc-server-dev-rjq/config.json/84013c4e1bf94f47a2a7422a1fa2c31c"
                String pluginUrl=tagPluginSever+applicationName+"/config.json/"+templateId;
                //组件
                pluginsUrlAry=new String[1];
                pluginsUrlAry[0]= pluginUrl;
//                String watermarkUrl=serverPath+applicationName+"/watermarkConfig.json/"+templateId;
//                pluginsUrlAry[1]= watermarkUrl;
//                //组件
//                pluginsUrlAry=new String[2];
//                pluginsUrlAry[0]= pluginUrl;
//                String watermarkUrl=serverPath+applicationName+"/config.json/watermark";
//                pluginsUrlAry[1]= watermarkUrl;

                jsonObject.put("pluginsUrlAry",pluginsUrlAry);

            }else{

                if("1".equals(watermarkFlag)){

                    pluginsUrlAry=new String[1];

                    String opNo= (String) requestUtil.getUserInfo("opNo");
                    String watermarkUrl=tagPluginSever+applicationName+"/config.json/watermark-"+opNo;
                    pluginsUrlAry[0]= watermarkUrl;
                    jsonObject.put("pluginsUrlAry",pluginsUrlAry);
                }
            }
            DocModel docModel=new DocModel(jsonObject);


            if("1".equals(parmJson.getString("canWrite"))){
                mode="edit";
            }
            //判断当前是否已经在编辑
            if("edit".equals(mode)){

                if ("0".equals(mouldObj.getDocEditSts())) {
                    //edit为编辑状态
                    docModel.changeType("edit", null);
                } else {
                    //如果已经在编辑状态，改为查看状态。
                    //view为查看状态
                    docModel.changeType("view", null);
                }
            }



//            String jsonStr=JSONObject.toJSONString(docModel);
//            result=JSONObject.toJSON(docModel);
            result=(JSONObject) JSONObject.toJSON(docModel);
//            if("edit".equals(mode) && !"1".equals(parmJson.getString("canWrite"))){
                result.getJSONObject("editorConfig").getJSONObject("plugins").put("pluginsData",pluginsUrlAry);
//            }

        }catch (Exception e){

            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, parmJson.toJSONString(),e);
        }
        MFLogger.info(JSON.toJSONString(result));
        return  result;
    }


    /**
     * 获取模板的书签配置信息格式
     *
     * {
     *     cus:{
     *          desc:"客户信息",
     *          tags:{
     *              cusName:{
     *                  typpe:"text",tag:"客户名称"
     *              },
     *              idNum:{
     *                  typpe:"text",tag:"证件号码"
     *              },
     *              cusName:{
     *                  typpe:"text",tag:"家庭住址"
     *              },
     *              idPhoto:{
     *                  type:"img",tag:"证件照片"，width:"60",height:"60"
     *              }

     *          }
     *
     *     },
     *     apply:{
     *          desc:"申请信息"
     *          tags:{
     *              applyNo:{
     *                  typpe:"text",tag:"申请号"
     *              }
     *              applyAmt:{
     *                  typpe:"text",tag:"申请金额"
     *              },
     *              pledgeInfo:{
     *                  type:"table",tag:"押品列表"
     *                  value:{
     *                      pleName:{
     *                          type:"text",
     *                          tag:"押品名称"
     *                      }，
     *                      plePrice:{
     *                          type:"text",
     *                          tag:"评估价值"
     *                      }，
     *                      pleBlonger:{
     *                          type:"text",
     *                          tag:"所有权人"
     *                      }，
     *                      pleClass:{
     *                          type:"text",
     *                          tag:"押品类别"
     *                      }，
     *                      pleRatio:{
     *                          type:"text",
     *                          tag:"抵质押率"
     *                      }
     *                  }
     *              }
     *
     *
     *          }
     *
     *     },
     *     pact:{
     *          desc:"合同信息",
     *          tags{
     *              pactNo:{
     *                  typpe:"text",tag:"合同号"
     *              },
     *              pactSignDate:{
     *                  typpe:"text",tag:"合同签订日期"
     *              },
     *              pactAmt:{
     *                  typpe:"text",tag:"合同金额"
     *              },
     *          }
     *
     *     }
     *
     * }
     *
     *
     * @param templateId
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject getTagsConfig(String templateId)throws  ServiceException{
        JSONObject result=null;
//        String tags=new FileUtils().readFileContent("D:/json/tmpTag.json");
        //获取改模板的配置实体
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        if(docTemplateModelEntity==null){
            return new JSONObject();
        }
        DocTemplateTagSetEntity tmpObj=docTemplateTagSetService.getDocTemplateTagSetEntity(templateId,docTemplateModelEntity.getVersionNo());
        if(tmpObj!=null){
            String tagKeyNos=tmpObj.getTagKeyNo();//获取标签的集合key值
            List<DocTemplateTagBaseEntity> tagList=docTemplateTagBaseService.findListByTagkeynos(tagKeyNos);
            if(tagList!=null){
                result=getTagKeySetJSON(tagList);
            }
        }

//        result=JSONObject.parseObject(tags);
        if(result!=null){

            MFLogger.info(result.toJSONString());
        }
        return result;
    }

    /**
     * 获取模板配置的书签集合
     * @param templateId
     * @return
     * @throws ServiceException
     */
    private JSONObject getTagsJson(String templateId)throws  ServiceException{
        JSONObject result=new JSONObject();
        JSONObject tagConfigJson=getTagsConfig(templateId);
        if(tagConfigJson!=null){
            for (String key:tagConfigJson.keySet()){
                result.putAll(tagConfigJson.getJSONObject(key).getJSONObject("tags"));;
            }
        }
        return result;

    }

    /**
     * 通过配置的书签 生成JSONOBject对象
     * @param tagList
     * @return
     * @throws ServiceException
     */
    private JSONObject getTagKeySetJSON(List<DocTemplateTagBaseEntity> tagList)throws ServiceException{
        JSONObject result=null;
        Map<Object,Object> tagGroup= parmCacheUtil.getDicMap("DOC_TAG_GROUP");
        if(tagList!=null){
            result=new JSONObject();
            for(DocTemplateTagBaseEntity tag:tagList){
//                展示样式 1-文字2-图片，3-表格，4-循环列表 5-循环的文字6-勾选项7-循环的不规则表格
//                JSONObject oneObj=new JSONObject();
                JSONObject tmpObj=new JSONObject();
                if("1".equals(tag.getShowStyle())){
                    tmpObj.put("type","text");
                    tmpObj.put("tag",tag.getTagKeyName());
                    if(StringUtils.isNotEmpty(tag.getTextLink())){
                        tmpObj.put("url",tag.getTextLink());
                        Style style=new Style();
                        style.setUnderLine(true);
                        tmpObj.put("style",style);
                    }
                }else  if("2".equals(tag.getShowStyle())){
                    tmpObj.put("type","img");
                    tmpObj.put("tag",tag.getTagKeyName());
                    tmpObj.put("width",tag.getLineNum());
                    tmpObj.put("height",tag.getColumnNum());
                }else if("4".equals(tag.getShowStyle())){
                    tmpObj.put("type","table");
                    tmpObj.put("tag",tag.getTagKeyName());
                    tmpObj.put("listLoopPartten",tag.getListLoopPartten());
                    JSONObject valJson=getTabTagValues(tag);
                    tmpObj.put("value",valJson);
                }else if("5".equals(tag.getShowStyle())){
                    tmpObj.put("type","loop");
                    tmpObj.put("tag",tag.getTagKeyName());
                    JSONObject valJson=getTabTagValues(tag);
                    tmpObj.put("value",valJson);
                }else if("6".equals(tag.getShowStyle())){
                    tmpObj.put("type","checkbox");
                    tmpObj.put("tag",tag.getTagKeyName());
                }


                if(result.containsKey(tag.getGroupFlag())){
                    result.getJSONObject(tag.getGroupFlag()).getJSONObject("tags").put(tag.getKeyNo(),tmpObj);

                }else{
                    JSONObject oneObj=new JSONObject();
                    oneObj.put(tag.getKeyNo(),tmpObj);

                    JSONObject tmpTags=new JSONObject();
                    tmpTags.put("desc",tagGroup.get(tag.getGroupFlag()));
                    tmpTags.put("tags",oneObj);
                    result.put(tag.getGroupFlag(),tmpTags);
                }
            }
        }


        return result;
    }

    /**
     * 获取模板的标签配置信息（包含待选的和已选的）
     * @param templateId
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject getTemplateTagConfigJson(String templateId)throws ServiceException{
        JSONObject result=new JSONObject();
        JSONArray allTag=docTemplateTagBaseService.getAllTagSltJson();//获取所有的待选书签
        DocTemplateTagSetEntity currConfig=docTemplateTagSetService.findOneObject(templateId);
        String  tagKeyNo=currConfig.getTagKeyNo();

        //封装默认值
        List<List<String>>defVal=new ArrayList<>();
        int groupCnt=allTag.size();
        for(int i=0;i<groupCnt;i++){
            List<String>tmpList=new ArrayList<>();
            tmpList.add("");
            defVal.add(tmpList);
        }
        if(tagKeyNo!=null){
            if(tagKeyNo.length()>0){
                String sltTags[]=tagKeyNo.split("@");
                Map<String,String>tmpMap=new HashedMap();
                for(String tag:sltTags){
                    tmpMap.put(tag,tag);
                }
                for(int i=0;i<groupCnt;i++){
                    JSONArray tmpArray=allTag.getJSONObject(i).getJSONArray("data");
                    int tmpSubLen=tmpArray.size();
                    for(int j=0;j<tmpSubLen;j++){
                        String keyNo=tmpArray.getJSONObject(j).getString("value");
                        if(tmpMap.containsKey(keyNo)){
                            defVal.get(i).add(keyNo);
                        }
                    }
                }



            }
        }
        result.put("sltTags",defVal);
        result.put("allTags",allTag);

        MFLogger.info(result.toJSONString());
        return result;
    }



    /**
     * 修改模板的书签配置
     * @param updBean
     * @return
     * @throws ServiceException
     */
    @Override
    public int setTemplateTags(DocTemplateTagSetEntity updBean)throws  ServiceException{
        int res=0;
        DocTemplateTagSetEntity currObj=this.docTemplateTagSetService.findOneObject(updBean.getTemplateId());

        DocTemplateTagSetEntity updateBean=new DocTemplateTagSetEntity();
        updateBean.setSerialNo(currObj.getSerialNo());
        updateBean.setTagKeyNo(updBean.getTagKeyNo());
        this.docTemplateTagSetService.update(updateBean);
        //同步更新标签规则信息信息
        DocTemplateTagDto docTemplateTagDto = new DocTemplateTagDto();
        docTemplateTagDto.setTemplateId(currObj.getTemplateId());
        docTemplateTagDto.setTemplateNo(currObj.getTemplateNo());
        docTemplateTagDto.setVersionNo(currObj.getVersionNo());
        docTemplateTagDto.setTagKeyNos(updBean.getTagKeyNo());
        docTemplateTagCheckService.syncTemplateTagCheck(docTemplateTagDto);
        res=1;
        return res;
    }


    /**
     * 获取标签的取值
     * @param templateId 模板管理
     * @param parmList
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getTagData(String templateId,JSONObject parmList )throws ServiceException{
        JSONObject result=new JSONObject();

        //获取标签列表
        List<DocTemplateTagBaseEntity> tagList;
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        DocTemplateTagSetEntity tmpObj=docTemplateTagSetService.getDocTemplateTagSetEntity(templateId,docTemplateModelEntity.getVersionNo());
        if(tmpObj!=null){
            String tagKeyNos=tmpObj.getTagKeyNo();//获取标签的集合key值
            tagList=docTemplateTagBaseService.findListByTagkeynos(tagKeyNos);
            //获取sql数据源的值
            if(tagList!=null){

                result=getTagValueManage.getAllTagVal(tagList,parmList);
            }

            //处理格式化

        }

        return result;

    }

    /**
     * 获取标签的取值(key值为汉语)
     * @param templateId 模板管理
     * @param parmList
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getChnTagData(String templateId,JSONObject parmList )throws ServiceException{
        JSONObject result=new JSONObject();
        JSONObject tmpResult=new JSONObject();

        //获取标签列表
        List<DocTemplateTagBaseEntity> tagList;
        DocTemplateModelEntity docTemplateModelEntity=findById(templateId);
        DocTemplateTagSetEntity tmpObj=docTemplateTagSetService.getDocTemplateTagSetEntity(templateId,docTemplateModelEntity.getVersionNo());
        if(tmpObj!=null){
            String tagKeyNos=tmpObj.getTagKeyNo();//获取标签的集合key值
            tagList=docTemplateTagBaseService.findListByTagkeynos(tagKeyNos);
            //获取sql数据源的值
            tmpResult=getTagValueManage.getAllTagVal(tagList,parmList);

            //中文key值替换
            Map<String,String >chnKey=new HashedMap();
            for(DocTemplateTagBaseEntity tag:tagList){
                chnKey.put(tag.getKeyNo(),tag.getTagKeyName());
            }
            //开始替换
            if(tmpResult.size()>0){
                for(String key:tmpResult.keySet()){
                    result.put(chnKey.get(key),tmpResult.get(key));
                }
            }



        }

        return result;

    }
    /**
     * 获取标签的取值(key值为汉语)
     * @param parmJson{
     *     tagKeyAry:书签数组
     *     parmList:所需参数
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getAllTagData(JSONObject parmJson )throws ServiceException{
        JSONObject tmpResult=new JSONObject();

        //获取标签列表
        List<DocTemplateTagBaseEntity> tagList;

        JSONArray tagKeyAry=parmJson.getJSONArray("tagKeyAry");
        JSONObject parmList=parmJson.getJSONObject("parmList");
        String tagKeyNos="";
        if(tagKeyAry!=null){
              for(int i=0;i<tagKeyAry.size();i++){
                  tagKeyNos+="@"+tagKeyAry.getString(i);
              }
              if(tagKeyNos.length()>0){
                  tagKeyNos=tagKeyNos.substring(1);
              }
//            String tagKeyNos=tmpObj.getTagKeyNo();//获取标签的集合key值
            tagList=docTemplateTagBaseService.findListByTagkeynos(tagKeyNos);
            //获取sql数据源的值
            tmpResult=getTagValueManage.getAllTagVal(tagList,parmList);





        }

        return tmpResult;

    }






    /**
     * 获取列表类书签的列值属性
     * @param docTemplateTagBaseEntity
     * @return
     * @throws ServiceException
     */
    private JSONObject getTabTagValues(DocTemplateTagBaseEntity docTemplateTagBaseEntity )throws ServiceException{
        JSONObject result=new JSONObject();
        List<DocTagTableConfigEntity>configEntityList=docTagTableConfigService.findListByKeyno(docTemplateTagBaseEntity.getKeyNo());
        if(configEntityList!=null){
            for(DocTagTableConfigEntity column:configEntityList){
                JSONObject colJson=new JSONObject();
                colJson.put("type","text");
                colJson.put("tag",column.getTableColumnName());
                result.put(column.getTableColumnId(),colJson);
            }
        }
        return result;

    }

    /**
     *  创建模板的组件的config文件
     * @param templateId
     * @throws ServiceException
     */
    public  void createPluginsConfigInfo(String templateId)throws  ServiceException{


        String configFile=tagPluginFilePath+"puginsConfig/"+templateId+"/config.json";
        File configInfo=new File(configFile);
        JSONObject pluginsConfigJson=getPluginsData(templateId);

        if (!configInfo.getParentFile().exists())
        { // 如果父目录不存在，创建父目录
            configInfo.getParentFile().mkdirs();
        }
        try{
            configInfo.createNewFile();
        }catch (Exception e){
            throw  new ServiceException("创建模板的组件的config文件："+templateId,"",e);
        }

        String content=pluginsConfigJson.toJSONString();
        new FileUtils().writeFileInfo(configFile,content);
    }



    @Override
    public void deleteById(String templateNo) throws ServiceException {
        try{

            docTemplateModelMapper.deleteById(templateNo);
            //删除电签的配置
            docEleSignConfigService.deleteByTemplateId(templateNo);

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,templateNo,e);
        }
    }

    /**
    * @功能描述: 文件模板替换标签转换PDF，上传oss
    * <AUTHOR>
    * @date 2022/12/14 16:23
    * @param: [obj]
    * @return: com.alibaba.fastjson.JSONObject
    */
    public  JSONObject generatePdfFileToOss(DocBizTemplateEntity docBizTemplateEntity)throws  ServiceException{
        JSONObject reuslt = new JSONObject();
        JSONObject pdfObj = new JSONObject();
        try{
            JSONObject jsonObject = leaseInterface.getLeasePact(docBizTemplateEntity.getBizNo());

            JSONObject parmList= new JSONObject();
            parmList.put("applyId",jsonObject.getString("applyId"));
            parmList.put("dueId",docBizTemplateEntity.getDueId());
            parmList.put("fincId",jsonObject.getString("dueId"));
            parmList.put("bizNo",docBizTemplateEntity.getBizNo());
            parmList.put("cusId",jsonObject.getString("cusId"));
            parmList.put("pactId",jsonObject.getString("pactId"));
            parmList.put("mainId",jsonObject.getString("mainId"));
            parmList.put("prepayId",docBizTemplateEntity.getPrepayId());
            parmList.put("dueBeginDate",docBizTemplateEntity.getDueBeginDate());
            parmList.put("prepayDate",docBizTemplateEntity.getPrepayDate());
            pdfObj.put("templateId",docBizTemplateEntity.getTemplateId());
            pdfObj.put("pactId",jsonObject.getString("pactId"));
            pdfObj.put("bizNo",docBizTemplateEntity.getBizNo());
            pdfObj.put("parmList",parmList);
            pdfObj.put("reLoadFlag","1");
            pdfObj.put("typeNo",docBizTemplateEntity.getTypeNo());
            pdfObj.put("templateName",docBizTemplateEntity.getTemplateName());
            JSONObject pdfRes = this.generatePdfFile(pdfObj);
            //pdf转化成功上传文件到oss
            if (pdfRes != null && "0000".equals(pdfRes.getString("code"))){
                //提前结清通知函
                if(!StringUtils.isEmpty(docBizTemplateEntity.getPrepayId())){
                    //更新通知函待迁移oss状态
                    docBizTemplateEntity.setOssRemoveStatus("0");
                    docBizTemplateService.update(docBizTemplateEntity);
                    reuslt.put("code","0000");
                    return reuslt;
                }
                DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();
                docFileBizFiletypeConfigEntity.setBizNo(jsonObject.getString("pactId"));
                docFileBizFiletypeConfigEntity.setFlowNo(jsonObject.getString("flowNo"));
                docFileBizFiletypeConfigEntity.setTypeNo(docBizTemplateEntity.getTypeNo());
                docFileBizFiletypeConfigEntity.setNodeNo("contractSign");
                DocFileBizFiletypeConfigEntity folder = this.docFileBizFiletypeConfigService.selectOne(docFileBizFiletypeConfigEntity);
                String saveFileName = pdfRes.getString("saveFileName");
                File uploadFile = new File(saveFileName);
                MultipartFile file = this.getMultipartFile(uploadFile,docBizTemplateEntity.getTemplateName());
                JSONObject uploadParmJson = new JSONObject();
                uploadParmJson.put("constractRelatedId", jsonObject.getString("pactId"));
                JSONObject gson = this.docFileBizFiletypeConfigService.upLoadFile(folder, file, "lease", JSONObject.toJSONString(uploadParmJson));
                if (!"0000".equals(gson.getString("code"))) {
                    log.error("TaskCall-batchAuditFk-error-付款审批上传OSS失败.失败合同id{},失败参数={}",jsonObject.getString("pactId"),pdfObj.toString());
                    reuslt.put("code","9999");
                }else{
                    reuslt.put("code","0000");
                }
            }else{
                log.error("TaskCall-batchAuditFk-error-付款审批/结清合同通知函转换PDF失败.失败bizNo={},失败参数={}",jsonObject.getString("pactId"),pdfObj.toString());
                reuslt.put("code","9999");
            }
        }catch (Exception e){
            log.error("TaskCall-batchAuditFk-error-付款审批/结清合同通知函转换PDF上传OSS失败.失败bizNo={},失败原因={}",docBizTemplateEntity.getBizNo(),e);
            reuslt.put("code","9999");
        }
        return  reuslt ;
    }


    /**
    * @功能描述: 文件转换MultipartFile，并指定新的文件名称
    * <AUTHOR>
    * @date 2022/12/14 16:22
    * @param: [file, newFileName]
    * @return: org.springframework.web.multipart.MultipartFile
    */
    private MultipartFile getMultipartFile(File file,String newFileName){
        FileInputStream fileInputStream = null;
        MultipartFile multipartFile = null;
        try {
            String origName = file.getName();
            String type = origName.split("\\.")[1];
            String origPath = file.getPath();
            newFileName = newFileName+"."+type;
            String newpath = origPath.substring(0,origPath.lastIndexOf("/")+1);
            File fileTemp=new File(newpath+newFileName);
            this.copyFileUsingFileChannels(file,fileTemp);
            fileInputStream = new FileInputStream(fileTemp);
            multipartFile = new MockMultipartFile(fileTemp.getName(),fileTemp.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(),fileInputStream);
            fileTemp.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                if (fileInputStream != null){
                    fileInputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return multipartFile;
    }

    private  void copyFileUsingFileChannels(File source, File dest) throws IOException {
        FileChannel inputChannel = null;
        FileChannel outputChannel = null;
        try {
            inputChannel = new FileInputStream(source).getChannel();
            outputChannel = new FileOutputStream(dest).getChannel();
            outputChannel.transferFrom(inputChannel, 0, inputChannel.size());
        } finally {
            if (inputChannel != null){
                inputChannel.close();
            }
            if (outputChannel != null){
                outputChannel.close();
            }
        }
    }


    /**
     * 模板渲染
     * @param jsonObject
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject renderTemplate(JSONObject jsonObject) throws ServiceException {
        JSONObject returnJson = new JSONObject();
        //获取模板Id
        String templateId = jsonObject.getString("templateId");
        //渲染模板参数
        JSONObject parmList = jsonObject.getJSONObject("parmList");
        //目标文件夹
        String targetFilePath = jsonObject.getString("targetFilePath");
        String pactNo = jsonObject.getString("pactNo");
        try {
            DocTemplateModelEntity templateModel = this.findById(templateId);
            if (null == templateModel) {
                MFLogger.error("模板主键templateId=" + templateId + "未找到，请配置模板", new RuntimeException(templateId + "模板未找到"));
                throw new RuntimeException(templateId + "模板未找到");
            }
            //找到模板的路径
            String fileName = templateModel.getTemplateFileName();
            String suffixName = new FileUtils().getFileSuffixName(fileName);
            String filePath = targetFilePath + File.separator + pactNo + "." + suffixName;
            String thisMouldFilePath = docMouldPath + templateModel.getTemplateFileName();

            long elinkTime = System.currentTimeMillis();
            MFLogger.info("替换书签开始时间:" + elinkTime);
            JSONObject busData = getTagData(templateId, parmList);
            DocTemplateCheckDto templateCheckDto = new DocTemplateCheckDto();
            templateCheckDto.setTemplateId(templateId);
            templateCheckDto.setTagKeyInfo(busData);
            JSONObject checkObj = docTemplateTagCheckService.checkTemplateTagValue(templateCheckDto);
            if (!"0000".equals(checkObj.getString("code"))) {
                return checkObj;
            }
            JSONObject tagData = getTagsJson(templateId);
            Parse parse = new Parse();
            parse.setDocPath(thisMouldFilePath);
            parse.setBusData(busData);
            parse.setTagData(tagData);
            MFLogger.info("renderTemplate模板路径:" +thisMouldFilePath);
            MFLogger.info("renderTemplate生成文件路径:" +filePath);
            parseRenderService.renderWordTemplate(parse,filePath);
            long endElink = System.currentTimeMillis();
            long elink = elinkTime - endElink;
            MFLogger.info("替换书签结束时间:" + endElink + "替换书签运行时间" + elink);
            returnJson.put("path",filePath);
            returnJson.put("fileName",pactNo + "." + suffixName);
        } catch (Exception e) {
            MFLogger.error("模板主键templateId=" + templateId + "标签替换失败，请配置模板", e);
            throw new RuntimeException(e);
        }
        return returnJson;
    }

    @Override
    public void renderTemplateForExcel(JSONObject jsonObject) throws ServiceException {
        //获取模板Id
        String templateId = jsonObject.getString("templateId");
        //渲染模板参数
        JSONObject parmList = jsonObject.getJSONObject("parmList");
        //目标文件夹
        String targetFilePath = jsonObject.getString("targetFilePath");
        String bizNo = jsonObject.getString("bizNo");
        try {
            DocTemplateModelEntity templateModel = this.findById(templateId);
            if (null == templateModel) {
                MFLogger.error("模板主键templateId=" + templateId + "未找到，请配置模板", new RuntimeException(templateId + "模板未找到"));
                throw new RuntimeException(templateId + "模板未找到");
            }
            //找到模板的路径
            String fileName = templateModel.getTemplateFileName();
            String suffixName = new FileUtils().getFileSuffixName(fileName);
            String filePath = targetFilePath + File.separator +  bizNo +File.separator +  bizNo + "." + suffixName;
            String thisMouldFilePath = docMouldPath + templateModel.getTemplateFileName();

            long elinkTime = System.currentTimeMillis();
            MFLogger.info("替换书签开始时间:" + elinkTime);
            JSONObject busData = getTagData(templateId, parmList);
            DocTemplateCheckDto templateCheckDto = new DocTemplateCheckDto();
            templateCheckDto.setTemplateId(templateId);
            templateCheckDto.setTagKeyInfo(busData);
            JSONObject checkObj = docTemplateTagCheckService.checkTemplateTagValue(templateCheckDto);
            if (!"0000".equals(checkObj.getString("code"))) {
                throw new RuntimeException("模板主键templateId=" + templateId + "标签替换失败");
            }
            JSONObject tagData = getTagsJson(templateId);
            Parse parse = new Parse();
            parse.setDocPath(thisMouldFilePath);
            parse.setBusData(busData);
            parse.setTagData(tagData);
            MFLogger.info("renderTemplate模板路径:" +thisMouldFilePath);
            MFLogger.info("renderTemplate生成文件路径:" +filePath);
            parseRenderService.renderExcelTemplate(parse,filePath);
            DocBizTemplateEntity docBizTemplateEntity = new DocBizTemplateEntity();
            docBizTemplateEntity.setBtId(jsonObject.getString("btId"));
            docBizTemplateEntity.setSavePath(targetFilePath + File.separator +  bizNo +File.separator);
            docBizTemplateEntity.setSaveFileName(bizNo + "." + suffixName);
            docBizTemplateService.update(docBizTemplateEntity);
            long endElink = System.currentTimeMillis();
            long elink = elinkTime - endElink;
            MFLogger.info("替换书签结束时间:" + endElink + "替换书签运行时间" + elink);
        } catch (Exception e) {
            MFLogger.error("模板主键templateId=" + templateId + "标签替换失败，请配置模板", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public JSONObject renderTemplateWrod(JSONObject parmTempJson) {
        JSONObject returnJson = new JSONObject();
        //获取模板Id
        String templateId = parmTempJson.getString("templateId");
        //渲染模板参数
        JSONObject busData = parmTempJson.getJSONObject("parmList");
        String liftId = parmTempJson.getString("liftId");
        //目标文件夹
//        String targetFilePath = parmTempJson.getString("targetFilePath");
        String uuid = UUIDUtil.getUUID();
        String targetFilePath = targetFileSavePath;
        String pactNo = parmTempJson.getString("pactNo");
        try {
            DocTemplateModelEntity templateModel = this.findById(templateId);
            if (null == templateModel) {
                MFLogger.error("模板主键templateId=" + templateId + "未找到，请配置模板", new RuntimeException(templateId + "模板未找到"));
                throw new RuntimeException(templateId + "模板未找到");
            }
            //找到模板的路径
            String fileName = templateModel.getTemplateFileName();
            String suffixName = new FileUtils().getFileSuffixName(fileName);
//            String filePath = targetFilePath + File.separator + uuid + "." + suffixName;
            String filePath = targetFilePath + uuid + "." + suffixName;
            String thisMouldFilePath = docMouldPaths +"/"+ templateModel.getTemplateFileName();

            long elinkTime = System.currentTimeMillis();
            MFLogger.info("替换书签开始时间:" + elinkTime);
//            JSONObject busData = getTagData(templateId, parmList);
            DocTemplateCheckDto templateCheckDto = new DocTemplateCheckDto();
            templateCheckDto.setTemplateId(templateId);
            templateCheckDto.setTagKeyInfo(busData);
            JSONObject checkObj = docTemplateTagCheckService.checkTemplateTagValue(templateCheckDto);
            if (!"0000".equals(checkObj.getString("code"))) {
                return checkObj;
            }
           JSONObject tagData = getTagsJson(templateId);

            Parse parse = new Parse();
            parse.setDocPath(thisMouldFilePath);
            parse.setBusData(busData);
            parse.setTagData(tagData);
            MFLogger.info("renderTemplate模板路径:" +thisMouldFilePath);
            MFLogger.info("renderTemplate生成文件路径:" +filePath);
            parseRenderService.renderWordTemplate(parse,filePath);
            long endElink = System.currentTimeMillis();
            long elink = elinkTime - endElink;
            MFLogger.info("替换书签结束时间:" + endElink + "替换书签运行时间" + elink);
            //成功后上传oss
            String substring = filePath.substring(filePath.lastIndexOf("."));
            FileInputStream inputStream = new FileInputStream(filePath);
            JSONObject writeOss = ossConfig.writeOss(jyFilePushOss + "/" + pactNo + "/" + liftId + "/" + uuid + substring, inputStream);
            if (!"0000".equals(writeOss.get("code"))) {
                log.error(liftId+"解押材料上传oss失败:" + writeOss.get("msg"));
                throw new RuntimeException(liftId+"解押材料上传oss失败");
            }
            String writeOssPath = writeOss.getString("filePath");
//            String writeOssPath = filePath;
            //初始化模板
            DocBizTemplateEntity docBizTemplateEntity = new DocBizTemplateEntity();
            docBizTemplateEntity.setBtId(uuid);
            docBizTemplateEntity.setBizNo(liftId);
            //查询场景模板配置表
            DocScTemplateConfigEntity docScTemplateConfigEntity = new DocScTemplateConfigEntity();
            docScTemplateConfigEntity.setPrdUniqueVal("d7e1a0f1740d400695527fbd295f0b32");
            docScTemplateConfigEntity.setFlowNo("archive_refund");
            docScTemplateConfigEntity.setNodeNo("archive_refund");
            DocScTemplateConfigEntity byEntity = docScTemplateConfigService.findByEntity(docScTemplateConfigEntity);
            BeanUtils.copyProperties(byEntity, docBizTemplateEntity);
            docBizTemplateEntity.setSavePath(filePath.substring(0,filePath.lastIndexOf("/") + 1));
            docBizTemplateEntity.setSaveFileName(filePath.substring(filePath.lastIndexOf("/") + 1));
            docBizTemplateEntity.setConstractRelatedId(parmTempJson.getString("pactId"));//合同id
            docBizTemplateEntity.setApplyRelatedId(liftId);//申请id
            docBizTemplateEntity.setCusRelatedId(parmTempJson.getString("cusId"));//客户id
            docBizTemplateEntity.setTypeNo("releasedMortgageApplication");
            docBizTemplateEntity.setTypeName("解押材料");
            docBizTemplateEntity.setTemplateNo("89af23af8976eefaf7a5f7da0149fee");
            docBizTemplateEntity.setCanWrite("0");
            docBizTemplateEntity.setPdfFlag("0");
            docBizTemplateEntity.setTemplateSuffix("1");
            docBizTemplateEntity.setEsignFinishFlag("-1");
            docBizTemplateEntity.setPledgeInfoFlag("-1");
            docBizTemplateEntity.setVersionNo("1");
            docBizTemplateEntity.setIfMustRead("0");
            docBizTemplateEntity.setReadFlag("0");
            docBizTemplateEntity.setIfMustWrite("0");
            docBizTemplateEntity.setCreateUserNo((String) requestUtil.getUserInfo("opNo"));
            docBizTemplateEntity.setCreateUserName((String) requestUtil.getUserInfo("opName"));
            docBizTemplateEntity.setCreateOrgNo((String) requestUtil.getUserInfo("createOrgNo"));
            docBizTemplateEntity.setCreateOrgName((String) requestUtil.getUserInfo("createOrgName"));
            docBizTemplateEntity.setIfElectricSign("0");
            docBizTemplateEntity.setUseSealFlag("0");
            docBizTemplateEntity.setOssRemoveStatus("0");
            docBizTemplateService.insert(docBizTemplateEntity);
            //存放至解押材料下
            DocFileInfEntity docFileInfEntity = new DocFileInfEntity();
            docFileInfEntity.setFileName(pactNo + "解押材料");
            docFileInfEntity.setBizNo(liftId);
            docFileInfEntity.setPrdUniqueVal("d7e1a0f1740d400695527fbd295f0b32");
            docFileInfEntity.setFlowNo("archive_refund");
            docFileInfEntity.setNodeNo("archive_refund");
            docFileInfEntity.setTypeNo("releasedMortgageApplication");
            docFileInfEntity.setTypeName("解押材料");
            docFileInfEntity.setFilePath(writeOssPath);
            docFileInfEntity.setCreateTime(DateUtil.getCurrDateTime());
            File file = new File(filePath);
            docFileInfEntity.setFileSize(BigDecimal.valueOf(file.length()));
            docFileInfEntity.setFileId(UUIDUtil.getUUID());
            docFileInfService.insert(docFileInfEntity);
//            file.delete();
        } catch (Exception e) {
            MFLogger.error("模板主键templateId=" + templateId + "标签替换失败，请配置模板", e);
            throw new RuntimeException(e);
        }
        return returnJson;
    }

    public static void main(String[] args) {
        System.out.println("/l/q/g/lwq.sql".substring(0,"/l/q/g/lwq.sql".lastIndexOf("/")+1));
    }
}