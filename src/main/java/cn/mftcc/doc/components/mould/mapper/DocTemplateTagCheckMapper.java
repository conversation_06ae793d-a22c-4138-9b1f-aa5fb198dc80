/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.mapper;

import cn.mftcc.doc.components.mould.entity.DocTemplateTagCheckEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模板标签校验信息表
 * 
 * <AUTHOR>
 * @email 
 * @date 2022-10-22 11:13:58
 */
@Mapper
public interface DocTemplateTagCheckMapper extends BaseMapper<DocTemplateTagCheckEntity> {

    void insertBatch(@Param("list") List<DocTemplateTagCheckEntity> list);

    void updateBatchById(@Param("list") List<DocTemplateTagCheckEntity> list);

}
