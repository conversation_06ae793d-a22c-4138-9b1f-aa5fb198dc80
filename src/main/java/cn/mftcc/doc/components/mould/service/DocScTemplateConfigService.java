/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocScTemplateConfigEntity;

import cn.mftcc.common.exception.ServiceException;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 场景的模板配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-25 17:30:20
 */
public interface DocScTemplateConfigService {

    IPage<DocScTemplateConfigEntity> findByPage(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException;

    List<DocScTemplateConfigEntity> findConfigList(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException;

    JSONArray getWaitSltTemplateList(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException;

    void insert(DocScTemplateConfigEntity docScTemplateConfigEntrtity) throws ServiceException;

    void update(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException;

    DocScTemplateConfigEntity findById(String scId) throws ServiceException;

    void deleteById(String scId) throws ServiceException;

    JSONObject copyTemplateConfigOfPrd( String newPrdId,  String motherPrdId)throws ServiceException;

    void  delOneTemplateNodeSetting(Map<String,String> delNode)throws Exception;

    /**
     * 获取某个场景下配置的所有模板
     * @param parmObj{
     *     "prdUniqueVal":"prdId",
     *     flowNo":"flowNo",
     *     "nodeNo":"nodeNo",
     * }
     * @return
     * @throws ServiceException
     */

     List<DocScTemplateConfigEntity> findScTemplateList(JSONObject parmObj) throws ServiceException;

    /**
     * 获取选择的 模板
     * @param docScTemplateConfigEntity
     * @return
     * @throws ServiceException
     */
    JSONObject  getSltTemplateList(DocScTemplateConfigEntity docScTemplateConfigEntity) throws ServiceException;

    /**
     * 插入选择的的模板
     * @param sltTemplate
     * @throws ServiceException
     */
      void  saveSltTemplate(JSONArray sltTemplate)throws  ServiceException;

    DocScTemplateConfigEntity findByEntity(DocScTemplateConfigEntity scTemplateConfigEntity) throws ServiceException;
}

