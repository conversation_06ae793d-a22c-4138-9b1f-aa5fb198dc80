/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocTagTableConfigEntity;
import cn.mftcc.doc.components.mould.service.DocTagTableConfigService;

import java.util.List;

/**
 * 标签表格类配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-16 15:07:29
 */
@RestController
@RequestMapping("mould/docTagTableConfig")
public class DocTagTableConfigController {

    @Autowired
    private DocTagTableConfigService docTagTableConfigService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocTagTableConfigEntity docTagTableConfigEntity) {
        IPage<DocTagTableConfigEntity> list = this.docTagTableConfigService.findByPage(docTagTableConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }
    @RequestMapping("/findListByKeyno")
    public R findListByKeyno(@RequestBody DocTagTableConfigEntity docTagTableConfigEntity) {
        List<DocTagTableConfigEntity> list = this.docTagTableConfigService.findListByKeyno(docTagTableConfigEntity.getKeyNo());
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocTagTableConfigEntity docTagTableConfigEntity){
        this.docTagTableConfigService.insert(docTagTableConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocTagTableConfigEntity docTagTableConfigEntity){
        this.docTagTableConfigService.update(docTagTableConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{configId}")
    public R findById(@PathVariable("configId") String configId){
        DocTagTableConfigEntity docTagTableConfigEntity = this.docTagTableConfigService.findById(configId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docTagTableConfigEntity);
    }

    @RequestMapping("/deleteById/{configId}")
    public R deleteById(@PathVariable("configId") String configId){
        this.docTagTableConfigService.deleteById(configId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}