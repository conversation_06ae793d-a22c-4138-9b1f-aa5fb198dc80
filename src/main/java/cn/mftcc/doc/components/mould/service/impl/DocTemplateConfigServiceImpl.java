/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.components.docmanage.web.model.DocModel;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateConfigService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service("docTemplateConfigService")
public class DocTemplateConfigServiceImpl implements DocTemplateConfigService {

    //文档服务名称
    @Value("${spring.application.name:}")
    private String applicationName;
    @Value("${mftcc.template.server:}")
    private String   serverPath;
    @Value("${mftcc.template.officeServer-path:}")
    private String   officeServer;
    @Value("${mftcc.template.watermarkFlag:}")
    private String watermarkFlag;


    /**
     * 获取模板配置Json
     * @param parmJson
     * {
     *     templateId:模板编号
     *     mode:  edit- 编辑  view-预览
     *     fileName: 如果是预览，预览文件的名称
     *     docShowName: 模板的展示名称
     *     docName: 模板文件的名称
     *     errorPage: 报错后展示的页面
     *     canWrite: 在编辑模式下是否 强制允许编辑 1-是 0-否 默认 0
     *     curOpNo: 当前登录人编号
     *     docCurStaus: 文档的当前状态  1-编辑状态  0-只读状态 （用于处理如果该文档是否正被其他人打开 ，如果是，强制改为只读）
     *
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject getMouldConfigInfo(JSONObject parmJson) throws ServiceException {

        JSONObject result=null;
        try{
            String templateId=parmJson.getString("templateId");//模板编号
            String mode=parmJson.getString("mode");//打开方式 编辑 edit或者预览 view
            String previewFileName=parmJson.getString("fileName");//如果是预览，预览文件的名称

//            DocTemplateModelEntity mouldObj=docTemplateModelMapper.selectById(templateId);
            JSONObject jsonObject=new JSONObject();
            String docName=parmJson.getString("docName");
            jsonObject.put("docName",docName);
//            String  docPath=docMouldPath+docName;
//            jsonObject.put("docPath",docPath);


            String docFileUrl=serverPath+applicationName+"/file/docFileInf/getMouldStream/1/"+docName;
            if("view".equals(mode)){
                docFileUrl=serverPath+applicationName+"/file/docFileInf/getMouldStream/2/"+previewFileName;
                if("1".equals(parmJson.getString("canWrite"))){
                    mode="eidt";
                }
            }



            jsonObject.put("docUrl",docFileUrl);

            String  callBackUrlStr=serverPath+applicationName+"/doc/docmanage/track/";
            jsonObject.put("docShowName", parmJson.getString("docShowName"));
            String docType=new FileUtils().getFileSuffixName(docName);
//            if(DocConstant.TEMPLATE_SUFFIX_WORD.equals(mouldObj.getTemplateSuffix())){

            jsonObject.put("docType",docType);
//            }
            jsonObject.put("officeServer",officeServer);//onlineOffice文件服务器

            if(StringUtils.isNotEmpty(previewFileName)){
                jsonObject.put("callbackUrl",callBackUrlStr+templateId+"/"+previewFileName);//回调路径
            }else{

                jsonObject.put("callbackUrl",callBackUrlStr+templateId+"/no");//回调路径
            }
            jsonObject.put("goBackUrl",parmJson.getString("errorPage"));//报错后的返回路径
            jsonObject.put("openType",mode);//打开方式 编辑 edit或者预览 view
            jsonObject.put("clientType","desktop");//打开方式 访问设备类型 desktop-pc展示 mobile-手机端

            String []pluginsUrlAry=null;
            if("edit".equals(mode) && !"1".equals(parmJson.getString("canWrite"))){

                String pluginUrl=serverPath+applicationName+"/config.json/"+templateId;
                //组件
                pluginsUrlAry=new String[1];
                pluginsUrlAry[0]= pluginUrl;

                jsonObject.put("pluginsUrlAry",pluginsUrlAry);

            }else{

                if("1".equals(watermarkFlag)){

                    pluginsUrlAry=new String[1];

                    String opNo= parmJson.getString("curOpNo");
                    String watermarkUrl=serverPath+applicationName+"/config.json/watermark-"+opNo;
                    pluginsUrlAry[0]= watermarkUrl;
                    jsonObject.put("pluginsUrlAry",pluginsUrlAry);
                }
            }
            DocModel docModel=new DocModel(jsonObject);


            if("1".equals(parmJson.getString("canWrite"))){
                mode="edit";
            }
            //判断当前是否已经在编辑
            if("edit".equals(mode)){

                if ("0".equals(parmJson.getString("docCurStaus"))) {
                    //edit为编辑状态
                    docModel.changeType("edit", null);
                } else {
                    //如果已经在编辑状态，改为查看状态。
                    //view为查看状态
                    docModel.changeType("view", null);
                }
            }



//            String jsonStr=JSONObject.toJSONString(docModel);
//            result=JSONObject.toJSON(docModel);
            result=(JSONObject) JSONObject.toJSON(docModel);
//            if("edit".equals(mode) && !"1".equals(parmJson.getString("canWrite"))){
            result.getJSONObject("editorConfig").getJSONObject("plugins").put("pluginsData",pluginsUrlAry);
//            }

        }catch (Exception e){

            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR, parmJson.toJSONString(),e);
        }
        MFLogger.info(JSON.toJSONString(result));
        return  result;
    }
}
