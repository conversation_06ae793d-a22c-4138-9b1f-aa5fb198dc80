/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.bizcommon.utils.UUIDUtil;
import cn.mftcc.doc.common.constant.DocConstant;
import cn.mftcc.doc.feign.dto.DocConvertDto;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocEleSignConfigEntity;
import cn.mftcc.doc.components.mould.mapper.DocEleSignConfigMapper;
import cn.mftcc.doc.components.mould.service.DocEleSignConfigService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

import java.util.List;

/**
 * 电子签章配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-27 11:24:48
 */
@Service("docEleSignConfigService")
@Transactional(rollbackFor = Exception.class)
public class DocEleSignConfigServiceImpl implements DocEleSignConfigService {

    @Autowired
    private DocEleSignConfigMapper docEleSignConfigMapper;
    @Autowired
    private MapperUtil mapperUtil;

    @Override
    public IPage<DocEleSignConfigEntity> findByPage(DocEleSignConfigEntity docEleSignConfigEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocEleSignConfigEntity> page = new Page<>();
            page.setCurrent(docEleSignConfigEntity.getPageNo());
            page.setSize(docEleSignConfigEntity.getPageSize());
            QueryWrapper<DocEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByAsc("esign_sort");
            mapperUtil.tableQuery(queryWrapper,docEleSignConfigEntity);

            return docEleSignConfigMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docEleSignConfigEntity.getId(),e);
        }
    }
    @Override
    public List<DocEleSignConfigEntity> findListByObj(DocEleSignConfigEntity docEleSignConfigEntity) throws ServiceException {
        try{
            //翻页

            QueryWrapper<DocEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotEmpty(docEleSignConfigEntity.getTemplateId()),"template_id",docEleSignConfigEntity.getTemplateId());
            return docEleSignConfigMapper.selectList(queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docEleSignConfigEntity.getId(),e);
        }
    }

    /**
     * 根据模板编号获取电签顺序
     * @param templateId
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray findListByTemplateId(String templateId)throws ServiceException {
        DocEleSignConfigEntity docEleSignConfigEntity =new DocEleSignConfigEntity();
        docEleSignConfigEntity.setTemplateId(templateId);
        List<DocEleSignConfigEntity>res= findListByObj(docEleSignConfigEntity);
        JSONArray resAry=new JSONArray();
        if(res!=null){
            for(DocEleSignConfigEntity bean:res){
                resAry.add(bean);
            }
        }
        return resAry;
    }


        @Override
    public void insert(DocEleSignConfigEntity docEleSignConfigEntity) throws ServiceException {
        try{
            docEleSignConfigEntity.setId(UUIDUtil.getUUID());
            docEleSignConfigMapper.insert(docEleSignConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docEleSignConfigEntity.getId(),e);
        }
    }

    @Override
    public void update(DocEleSignConfigEntity docEleSignConfigEntity) throws ServiceException {
        try{
            docEleSignConfigMapper.updateById(docEleSignConfigEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docEleSignConfigEntity.getId(),e);
        }
    }

    @Override
    public DocEleSignConfigEntity findById(String id) throws ServiceException {
        try{
            return docEleSignConfigMapper.selectById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,id,e);
        }
    }





    @Override
    public void deleteById(String id) throws ServiceException {
        try{
            docEleSignConfigMapper.deleteById(id);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,id,e);
        }
    }

    @Override
    public void deleteByTemplateId(String templateId) throws ServiceException {
        try{

            if(StringUtils.isNotEmpty(templateId)){

                DocEleSignConfigEntity delBean=new DocEleSignConfigEntity();
                QueryWrapper<DocEleSignConfigEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("template_id",templateId);

                docEleSignConfigMapper.delete(queryWrapper);
            }
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,templateId,e);
        }
    }

}