/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import lombok.Data;

/**
 * 标签表格类配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-16 15:07:29
 */
@Data
@TableName("doc_tag_table_config")
public class DocTagTableConfigEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 表格配置编号
	 */
	@TableId
	private String configId;
	/**
	 * 标签编号
	 */
	private String keyNo;
	/**
	 * 表格列编号
	 */
	private String tableColumnId;
	/**
	 * 表格列名称
	 */
	private String tableColumnName;
	/**
	 * 列顺序
	 */
	private Integer tabSort;
	/**
	 * 行号
	 */
	private Integer lineNum;
	/**
	 * 列号
	 */
	private Integer columnNum;
	/**
	 * 表头方向。1横向2纵向
	 */
	private String headerDirection;
	/**
	 * 单元列类型1常规2合并
	 */
	private String columnType;
	/**
	 * 合并单元格类型1合并列2合并行3复合
	 */
	private String mergeType;
	/**
	 * 合并开始行号
	 */
	private Integer mergeStartLineNum;
	/**
	 * 合并开始列号
	 */
	private Integer mergeStartColumnNum;
	/**
	 * 合并结束行号
	 */
	private Integer mergeEndLineNum;
	/**
	 * 合并结束列号
	 */
	private Integer mergeEndColumnNum;
	/**
	 * 复合单元格子列标识。0否1是
	 */
	private String subFlag;
	/**
	 * 父列
	 */
	private String parentColumnId;
	/**
	 * 动态类型1数据字典2以天为单位统计当前月3以月为单位统计当前年4指定列
	 */
	private String dynamicType;
	/**
	 * 动态列参数
	 */
	private String dynamicParam;
	/**
	 * 启用标识。0否1是
	 */
	private String useFlag;

}
