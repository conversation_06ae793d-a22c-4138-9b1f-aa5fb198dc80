/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.feign.dto.DocBizTemplateDto;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;

import cn.mftcc.common.exception.ServiceException;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 业务模板关联表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-27 14:18:47
 */
public interface DocBizTemplateService {

    IPage<DocBizTemplateEntity> findByPage(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException;

    IPage<DocBizTemplateEntity> findPactFileByPage(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException;


    JSONObject initTemplateJson(JSONObject initParm)throws ServiceException;

    void insert(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException;

    List<DocBizTemplateEntity> findList(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException;

    JSONObject initTemplate(String initParm)throws ServiceException;

    JSONObject checkBizTemplateStatus(Map<String ,String> parmMap)throws  ServiceException;

    void update(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException;

    DocBizTemplateEntity findById(String btId) throws ServiceException;

    void deleteById(String btId) throws ServiceException;

    JSONArray  findBizMouldList(Map<String ,String>parmMap) throws ServiceException;

    JSONObject updateEleSealInf (JSONArray updList)throws  ServiceException;

    JSONArray findTemplateArray(List<Map<String ,String>> parmList) throws ServiceException;

    JSONArray findEsignWatiList(Map<String,String>parmMap) throws ServiceException;

    JSONArray   findBizMouldMultiRenderList(List<Map<String ,String>>parmMaps) throws ServiceException;

    List<DocBizTemplateEntity> findToDoRemoveFileInfo()throws ServiceException;
    /**
     * 依据文件名称查询
     * @param fileName
     * @return
     * @throws ServiceException
     */
    DocBizTemplateEntity findByFileName(String fileName) throws ServiceException;

    /**
     * 获取渲染过的模板
     * @param parmMap
     * @return
     * @throws ServiceException
     */
    JSONArray   findBizMouldRenderList(Map<String ,String>parmMap) throws ServiceException;

    /**
     * 根据 业务流水号 获取当前待签约的列表
     * @param id
     * @return
     * @throws ServiceException
     */
      JSONArray  getCurWaitEleSignList(String  id)throws  ServiceException;

    JSONObject updateBizTemplateInfo(JSONObject templateInfo)throws ServiceException;

    JSONObject updateBizTemplateInfoByPactId(JSONObject templateInfo)throws ServiceException;

    JSONObject updateBizTemplateInfoByOssStatus(JSONObject templateInfo)throws ServiceException;

    void updateByBean(DocBizTemplateEntity docBizTemplateEntity) throws ServiceException;
    /**
     * 获取当前待签约模板数据
     * @param jsonObject{
     *           templateId-模板流水号
     *            bizNo-业务编号
     *            subBizNo-子项业务流水号
     *      }
     * @return
     * @throws ServiceException
     */
    JSONObject  getCurWaitEleSign(JSONObject  jsonObject)throws  ServiceException;

    /**
     * 展示多个地方的模板
     * @param parmArray [{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * }]
     * @return
     * @throws ServiceException
     */
    List<DocBizTemplateEntity> findMultiMouldList(JSONArray  parmArray)throws ServiceException;
    /**
     * 判断 该节点的电签是否已经 全部完成
     * @param parm{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * }
     * @return 1-全部完成电签 0-没有完成
     * @throws ServiceException
     */
   String ifNodeElesignFinished(JSONObject parm)throws ServiceException;
    /**
     * 删除模板
     * @param object
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *          "subBizNo" :""
     *      * }
     * @return
     * @throws ServiceException
     */
    void  deleteByBean(JSONObject object)throws  ServiceException;
    /**
     * 根据 业务流水号 获取当前已签约合同列表
     * @param appId
     * @return
     * @throws ServiceException
     */
    JSONArray  getCurrentSignedUpList(String appId)throws  ServiceException;

    List<DocBizTemplateDto>  getCurWaitEleSignList(String  id, String nodeNo)throws  ServiceException;

    JSONObject isInitTemplate(JSONObject map)throws  ServiceException;

    String readTemplateBase64ByBtId(String fileId)throws  ServiceException;

    String readEndPactTemplateBase64(String btId)throws  ServiceException;

    /**
     * 根据btId获取模板文件base64流
     * @param map
     * @return
     * @throws ServiceException
     */
    List<DocBizTemplateEntity> readTemplateBase64(Map<String ,String> map)throws  ServiceException;

    void initAdvanceNoticeTemplate(JSONObject jsonObject)throws  ServiceException;

    DocBizTemplateDto getDocBizTemplateDtoBy(DocBizTemplateDto bizTemplateEntity)throws  ServiceException;

    List<DocFileInfEntity> getDocIdFileByContractIdAndTypeNo(String constractRelatedId)throws Exception;

}

