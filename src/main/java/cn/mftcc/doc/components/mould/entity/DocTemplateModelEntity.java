/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 模板表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-03-25 14:53:31
 */
@Data
@TableName("doc_template_model")
public class DocTemplateModelEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * 流水号
	 */
	@TableId(type = IdType.UUID)
	private String templateId;

	/**
	 * 模板编号
	 */
	private String templateNo;
	/**
	 * 模板名称
	 */
	private String templateName;
	/**
	 * 模板文件
	 */
	private String templateFile;
	/**
	 * 模板类型
	 */
	private String templateType;
	/**
	 * 模板格式1-word 2-excel 3-pdf
	 */
	private String templateSuffix;
	/**
	 * 启用标示
	 */
	private String useFlag;
	/**
	 * '文件转换类型1-转换成pdf
	 */
	private String changeType;
	/**
	 * 系统标示:1-基础数据 0-客户新增
	 */
	private String systemFlag;
	/**
	 * 是否允许编辑0不允许1允许
	 */
	private String editableFlag;
	/**
	 * 克隆模板来源
	 */
	private String templateSource;
	/**
	 * 克隆模板来源名称
	 */
	private String templateSourceName;
	/**
	 * 是否电签0-否; 1-是
	 */
	private String ifElectricSign;
	/**
	 * 是否用印0-否; 1-是
	 */
	private String useSealFlag;
	/**
	 * 是否视频面审 0-否; 1-是
	 */
	private String videoInterview;
	/**
	 * 前端是否显示  0-否; 1-是
	 */
	private String frontView;


	/**
	 * 多份模板feigin接口
	 */
	private String multiFeign;
	/**
	 * 多份模板feigin接口调用的方法
	 */
	private String multiFeignMethod;

	/**
	 * 多份模板参数列表
	 */
	private String parmList;
	/**
	 * 机构编号
	 */
	private String corpId;
	/**
	 * 签约顺序
	 */
	private Integer esignSort;
	/**
	 * 版本号
	 */
	private String versionNo;
	/**
	 * 模板版本展示编号
	 */
	private String versionShowNo;
	/**
	 * 模板文件名称
	 */
	private String templateFileName;

	/**
	 * 模板是否正处于编辑状态 0-否 1-是
	 */
	private String  docEditSts;
	/**
	 * 是否已发布 0-否; 1-是
	 */
	private String ifPublic;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;

	/**
	 * 是否生成pdf 0-否; 1-是
	 */
	private String  pdfFlag;

	/**
	 * 要件类型编号
	 */
	private String typeNo;
	/**
	 * 要件类型名称
	 */
	private String typeName;


	@ApiModelProperty(value = "是否可提供设备编号/车架号")
	private String pledgeInfoFlag;

}
