/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import lombok.Data;

/**
 * 标签表格列对应字段配置表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-04-16 15:10:40
 */
@Data
@TableName("doc_tag_column_config")
public class DocTagColumnConfigEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@TableId
	private String configId;
	/**
	 * 表格列编号
	 */
	private String tableColumnId;
	/**
	 * 标签编号
	 */
	private String keyNo;
	/**
	 * 对应字段
	 */
	private String columnId;
	/**
	 * 对应字段名称
	 */
	private String columnName;
	/**
	 * 合并单元格标识。0不合并1合并
	 */
	private String mergeFlag;
	/**
	 * 合并开始行号
	 */
	private Integer mergeStartLineNum;
	/**
	 * 合并开始列号
	 */
	private Integer mergeStartColumnNum;
	/**
	 * 合并结束行号
	 */
	private Integer mergeEndLineNum;
	/**
	 * 合并结束列号
	 */
	private Integer mergeEndColumnNum;
	/**
	 * 是否突出显示
	 */
	private String highlightFlag;
	/**
	 * 突出显示背景颜色
	 */
	private String highlightColor;
	/**
	 * 格式化类型01金额小写02金额大写03日期格式化05金额拆分06保留小数
	 */
	private String formatType;
	/**
	 * 格式化参数。金额拆分格式参数、保留小数位、日期格式化类型
	 */
	private String formatParam;
	/**
	 * 单位。元、万元、%等
	 */
	private String columnUnit;
	/**
	 * 字典项编号
	 */
	private String sysDictionryVal;
	/**
	 * 字段组编号,适用于两个字段拼接，后者列编号column_id(end_date)。例如起止日期
	 */
	private String groupId;
	/**
	 * 字段组分割符号
	 */
	private String splitStr;
	/**
	 * 顺序
	 */
	private Integer colSort;
	/**
	 * 前缀
	 */
	private String prefix;

}
