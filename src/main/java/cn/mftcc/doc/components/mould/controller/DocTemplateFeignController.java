/*
 * Copyright © 2020 北京微金时代科技有限公司 <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.bizcommon.utils.BeanCopyUtil;
import cn.mftcc.bizcommon.utils.DateUtil;
import cn.mftcc.bizcommon.utils.UUIDUtil;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.StringUtil;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.components.file.entity.*;
import cn.mftcc.doc.components.file.mapper.DocFileBizFiletypeConfigMapper;
import cn.mftcc.doc.components.file.service.*;
import cn.mftcc.doc.components.mould.entity.*;
import cn.mftcc.doc.components.mould.mapper.DocTemplateEsignerListMapper;
import cn.mftcc.doc.components.mould.mapper.DocTemplateModelMapper;
import cn.mftcc.doc.components.mould.service.*;
import cn.mftcc.doc.feign.api.DocInterface;
import cn.mftcc.doc.feign.dto.DocBizTemplateDto;
import cn.mftcc.doc.feign.dto.DocConvertDto;
import cn.mftcc.doc.feign.dto.DocTemplateEsignerListDto;
import cn.mftcc.doc.feign.dto.DocTemplateModelDto;
import cn.mftcc.doc.feign.client.CustomerFeignClient;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;
import cn.mftcc.doc.feign.dto.*;
import cn.mftcc.lease.feign.dto.LeaseApplyEntityDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;

import static java.lang.Thread.sleep;

@RestController
@RequestMapping({"/docInterface"})
@Slf4j
public class DocTemplateFeignController implements DocInterface {

    @Autowired
    private DocBizTemplateService docBizTemplateService;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private DocScTemplateConfigService docScTemplateConfigService;
    @Autowired
    private DocScFiletypeConfigService docScFiletypeConfigService;

    @Autowired
    private DocTemplateModelService docTemplateModelService;
    @Autowired
    private DocTemplateModelMapper docTemplateModelMapper;
    @Autowired
    private DocFileInfService docFileInfService;
    @Autowired
    private DocFileTypeService docFileTypeService;
    @Autowired
    private DocEleSignConfigService docEleSignConfigService;
    @Autowired
    private DocBizEleSignConfigService docBizEleSignConfigService;
    @Autowired
    private DocTemplateEsignerListService docTemplateEsignerListService;
    @Autowired
    private DocTemplateEsignerListMapper docTemplateEsignerListMapper;
    @Autowired
    private DocFileBizFiletypeConfigMapper docFileBizFiletypeConfigMapper;


    @Autowired
    private BatchVoucherPushService batchVoucherPushService;




    /**
     *  模板初始化的feign接口
     * @param initParm 初始化参数 json字符串:{prdUniqueVal:"1004_2",flowNo:"bus_Flow",nodeNo:"pactSign",bizNo:"pact_00001"
     *                  createUserNo:"",createUserName:"",createOrgNo:"",createOrgName:""}
     * @return  json对象
     * @throws Exception
     */
    @Override
    public JSONObject initTemplate(@RequestParam("initParm") String initParm) throws Exception {
        JSONObject result=new JSONObject();
        result =docBizTemplateService.initTemplate(initParm);
        return  result;
    }

    @Override
    public JSONObject initTemplateJson(JSONObject parmObj) throws Exception {

        JSONObject result =docBizTemplateService.initTemplateJson(parmObj);
        return  result;
    }

    /**
     * 获取某个场景下配置的所有模板
     * @param parmObj{
     *     "prdUniqueVal":"prdId",
     *     flowNo":"flowNo",
     *     "nodeNo":"nodeNo",
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray findScTemplateList(JSONObject parmObj) throws Exception {
        JSONArray result=new JSONArray();
        List<DocScTemplateConfigEntity> resultList=docScTemplateConfigService.findScTemplateList(parmObj);
        if(resultList!=null){
            for(DocScTemplateConfigEntity template:resultList){
                result.add(template);
            }
        }
        return  result;
    }


    /**
     *获取模板列表
     * @param parmMap
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *      * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONArray   findBizMouldList(Map<String ,String>parmMap) throws Exception{
        return  docBizTemplateService.findBizMouldList(parmMap);
    }

    /**
     *获取渲染过的模板
     * @param parmMap
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *      * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONArray   findBizMouldRenderList(List<Map<String ,String>>parmMap) throws Exception{


        return  docBizTemplateService.findBizMouldMultiRenderList(parmMap);
    }


    @Override
    public JSONObject  isInitTemplate(JSONObject map) throws Exception{
        return  docBizTemplateService.isInitTemplate(map);
    }

    @Override
    public JSONArray   findTemplateTypeArray() throws Exception{
        return  docTemplateModelService.findTemplateArray();
    }

    /**
     * 修改电签的属性
     * @param updList
     * [
     *  {
     *      btId:业务模板流水号
     *      elecSealDt：电签时间
     *      elecSealFlag：是否已电签
     *      elecSealPersons：电签人员，用逗号隔开
     *  },{
     *   btId:业务模板流水号
     *   elecSealDt：电签时间
     *   elecSealFlag：是否已电签
     *   elecSealPersons：电签人员，用逗号隔开
     *  }
     * ]
     * return  {code:"0000" 代表成功}
     * */
    @Override
    public  JSONObject updateEleSealInf (JSONArray updList)throws  Exception{
        return docBizTemplateService.updateEleSealInf(updList);
    }

    /**
     * 根据模板编号获取电签顺序
     * @param templateId
     * @return
     * @throws Exception
     */
    @Override
    public  JSONArray findListByTemplateId (String templateId )throws  Exception{
        return  docEleSignConfigService.findListByTemplateId(templateId);
    }
    @Override
    public JSONObject  getTemplateTagData(JSONObject parmJson) throws  Exception{
        String templateId=parmJson.getString("templateId");
        return  docTemplateModelService.getTagData(templateId,parmJson);
    }


    @Override
    public JSONObject  getChnTagData(JSONObject parmJson) throws  Exception{
        String templateId=parmJson.getString("templateId");
        return  docTemplateModelService.getChnTagData(templateId,parmJson);
    }

    /**
     * 获取标签的取值(key值为汉语)
     * @param parmJson{
     *     tagKeyAry:书签数组
     *     parmList:所需参数
     * }
     * @throws ServiceException
     */
    @Override
    public JSONObject  getAllTagData(JSONObject parmJson) throws  Exception{

        return  docTemplateModelService.getAllTagData(parmJson);
    }

    @Override
    public  JSONObject initBizFileTypeConfig(@RequestParam("initParm") String initParm)throws  Exception{
        return docFileBizFiletypeConfigService.initBizFileTypeConfig(initParm);
    }

    @Override
    public JSONObject insertBatchFile(List<JSONObject> parmMapList) throws Exception {
        return docFileInfService.insertBatchFile(parmMapList);
    }

    @Override
    public  JSONArray getCurWaitEleSignList( String id)throws  Exception{
        return docBizTemplateService.getCurWaitEleSignList(id);
    }


    @Override
    public JSONArray findDocBizTemplateList(JSONObject parmObj) throws Exception{
        JSONArray result=new JSONArray();
        DocBizTemplateEntity docBizTemplateEntity=JSONObject.toJavaObject(parmObj,DocBizTemplateEntity.class);
        List<DocBizTemplateEntity>tmpList=docBizTemplateService.findList(docBizTemplateEntity);

        if(tmpList!=null){
            for(DocBizTemplateEntity obj:tmpList){
                result.add(obj);
            }
        }
        return  result ;
    }

    @Override
    public JSONArray findDocFileInfoList(JSONObject pamJson) throws Exception {
        JSONArray result=new JSONArray();
        String constractRelatedId = pamJson.get("constractRelatedId").toString();
        List<DocFileInfEntity> docFileInfEntities= docBizTemplateService.getDocIdFileByContractIdAndTypeNo(constractRelatedId);
        if(docFileInfEntities!=null){
            for(DocFileInfEntity obj:docFileInfEntities){
                result.add(obj);
            }
        }
        return result;
    }

    @Override
    public JSONArray findDocFileInfoListByBizNo(List<String> bizNos) throws Exception {
        JSONArray result=new JSONArray();
        List<DocFileInfEntity> docFileInfEntities= docFileInfService.findFilesInfoByBizNo(bizNos);
        if(docFileInfEntities!=null){
            for(DocFileInfEntity obj:docFileInfEntities){
                result.add(obj);
            }
        }
        return result;
    }

    @Override
    public void insertDocFileInfo(DocFileInfDTO docFileInfDTO) throws Exception {
        DocFileInfEntity docFileInfEntity = new DocFileInfEntity();
        BeanUtils.copyProperties(docFileInfDTO,docFileInfEntity);
        docFileInfService.insert(docFileInfEntity);
    }

    @Override
    public void updateDocFileInfo(DocFileInfDTO docFileInfDTO) throws Exception {
        DocFileInfEntity docFileInfEntity = new DocFileInfEntity();
        docFileInfEntity.setBizNo(docFileInfDTO.getBizNo());
        docFileInfEntity.setTypeNo(docFileInfDTO.getTypeNo());
        docFileInfEntity.setFlowNo(docFileInfDTO.getFlowNo());
        docFileInfEntity.setPrdUniqueVal(docFileInfDTO.getPrdUniqueVal());
        docFileInfEntity.setOrderBy("desc");
        List<DocFileInfEntity> entitys = docFileInfService.findFileInfList(docFileInfEntity);
        DocFileInfEntity infEntity = new DocFileInfEntity();
        BeanUtils.copyProperties(entitys.get(0),infEntity);
        infEntity.setDeleteFlag(docFileInfDTO.getDeleteFlag());
        docFileInfService.updateByBizNo(infEntity);
    }

    /**
     * 检查节点下所有模板是否读取，填写完毕（是否必填，是否已填， 是否必读，是否已读，是否可编辑。）
     * @param parmMap
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *      * }
     * @return
     * @throws Exception
     */

    @Override
    public  JSONObject checkBizTemplateStatus(Map<String ,String>parmMap)throws  Exception{

        return docBizTemplateService.checkBizTemplateStatus(parmMap);
    }
    /**
     *  检查节点下是否已上传（是否必填，是否已填， 是否必读，是否已读，是否可编辑。）
     * @param parmMap
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *      * }
     * @return
     * @throws Exception
     */

    @Override
    public  JSONObject checkBizFileStatus(Map<String ,String>parmMap)throws  Exception{

        return docFileBizFiletypeConfigService.checkBizFileStatus(parmMap);
    }

    @Override
    public  JSONObject checkBizFileStatusAccess(Map<String ,String>parmMap)throws  Exception{

        return docFileBizFiletypeConfigService.checkBizFileStatusAccess(parmMap);
    }

    @Override
    public  String ifFileConfig(@RequestParam("initParm") String initParm) throws  Exception{
        return docFileBizFiletypeConfigService.ifFileConfig(initParm);
    }

    /**
     *  拷贝一个产品的模板配置到另外一个产品中
     * {
     *   newPrdId  新的产品唯一标识
     *   motherPrdId 母体产品唯一标识
     * }
     * @return
     *   {
     *       code:"0000" --成功
     *       msg：""  --错误消息
     *   }
     * @throws Exception
     */
    @Override
    public JSONObject copyTemplateConfigOfPrd(@RequestParam("newPrdId") String newPrdId,@RequestParam("motherPrdId") String motherPrdId)throws Exception{

        return  docScTemplateConfigService.copyTemplateConfigOfPrd(newPrdId,motherPrdId);
    }
    /**
     *  拷贝一个产品的要件配置到另外一个产品中
     * {
     *   newPrdId  新的产品唯一标识
     *   motherPrdId 母体产品唯一标识
     * }
     * @return
     *   {
     *       code:"0000" --成功  "1111" -- 报错
     *       msg：""  --错误消息
     *   }
     * @throws Exception
     */
    @Override
    public JSONObject copyFilesConfigOfPrd(@RequestParam("newPrdId") String newPrdId,@RequestParam("motherPrdId") String motherPrdId)throws Exception{

        return  docScFiletypeConfigService.copyFilesConfigOfPrd(newPrdId,motherPrdId);
    }

    /**
     *  获取所有的消息模板
     * @return
     * @throws Exception
     */
    @Override
    public List<DocTemplateModelDto> findMsgMouldList()throws Exception{

        return docTemplateModelService.findMsgMouldList();

    }

    /**
     * 获取所有的消息模板
     * @param obj   格式
     * 格式{
     *            templateId:
     *            parmList:{
     *                  cusId:"0200000",
     *                  dueId:"32231231"
     *            }
     * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject getMsgContent(Map<String,Object> obj)throws  Exception{

        return docTemplateModelService.getMsgContent(obj);

    }

    /**
     *  文件格式转换
     * @param docConvertDto{
     *
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject fileFormatConvert(@RequestBody DocConvertDto docConvertDto)throws Exception{

        return docTemplateModelService.fileFormatConvert(docConvertDto);

    }

    /**
     *  删除指定的流程节点配置
     * @param delNode{
     *     prdUniqueVal：产品唯一标识
     *     flowNo：所属流程
     *     nodeNo：节点编号
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject delFileNodeSetting(@RequestBody List<Map<String,String>>delNode)throws Exception{
        return docScFiletypeConfigService.delFileNodeSetting(delNode);
    }

    @Override
    public String getDocFileIfType(JSONObject jsonObject) throws Exception {
        return docFileInfService.getDocFileIfType(jsonObject);
    }

    /**
     *  删除指定的流程模板配置
     * @param delNode{
     *     prdUniqueVal：产品唯一标识
     *     flowNo：所属流程
     *     nodeNo：节点编号
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject delTemplateNodeSetting(@RequestBody List<Map<String,String>>delNode)throws Exception{
        return docScFiletypeConfigService.delTemplateNodeSetting(delNode);
    }

    /**
     * 判断 节点下是否配置过要件
     * @param nodeMap{
     *      prdUniqueVal：产品唯一标识
     *      flowNo：所属流程
     *      nodeNo：节点编号
     * }
     * @return{
     *     code:"0000",
     *     data:{
     *         settedFlag:1    1-是 0-否
     *     }
     * @throws Exception
     */
    @Override
    public JSONObject ifDocSetted(Map<String,String>nodeMap)throws  Exception{
        JSONObject result=new JSONObject();
        result.put("code","0000");
        JSONObject data=new JSONObject();
        data.put("settedFlag","1");
        result.put("data",data);

        return result;
    }



    /**
     * 判断 节点下是否配置过模板
     * @param nodeMap{
     *      prdUniqueVal：产品唯一标识
     *      flowNo：所属流程
     *      nodeNo：节点编号
     * }
     * @return{
     *     code:"0000",
     *     data:{
     *         settedFlag:1    1-是 0-否
     *     }
     * }
     * @throws Exception
     */
     @Override
    public JSONObject ifTemplateSetted(Map<String,String>nodeMap)throws  Exception{
         JSONObject result=new JSONObject();
         result.put("code","0000");
         JSONObject data=new JSONObject();
         data.put("settedFlag","0");
         result.put("data",data);
        return result;
    }

    /**
     * 删除模板和要件的配置
     * @param nodeMap{
     *      prdUniqueVal：产品唯一标识
     *      flowNo：所属流程
     *      nodeNo：节点编号
     *   }
     * @return
     * @throws Exception
     */
    @Override
    public void  deleteDocAndTemplateSetting(Map<String,String>nodeMap)throws  Exception{

            docScFiletypeConfigService.delOneFileNodeSetting(nodeMap);
            docScTemplateConfigService.delOneTemplateNodeSetting(nodeMap);


    }
    /**
     * 删除模板和要件的配置
     * @param delJson{
     *                bizNo - 业务流水号
     *                prdUniqueVal - 产品编号
     *                flowNo -流程编号
     *                nodeNo - 节点编号
     *                typeNo - 资料类型编号
     *   }
     * @return
     * @throws Exception
     */
    @Override
    public void  deleteByJson( JSONObject delJson)throws  Exception{
            if(StringUtils.isNotEmpty(delJson.getString("bizNo"))){

                docFileBizFiletypeConfigService.deleteByJson(delJson);
            }

    }

    @Override
    public JSONObject findByTemplateModdel(JSONObject jsonObject) throws Exception {
        return docTemplateModelService.findByTemplateModdel(jsonObject);
    }

    @Override
    public void deleteByBizNo(String bizNo) throws Exception {
        docFileBizFiletypeConfigService.deleteByBizNo(bizNo);
        docFileInfService.deleteByBizNo(bizNo);
    }

    @Override
    public void deleteEsiger(DocTemplateEsignerListDto docTemplateEsignerListDto) throws Exception {

        DocTemplateEsignerListEntity docTemplateEsignerListEntity=new DocTemplateEsignerListEntity();
        BeanCopyUtil.copyProperties(docTemplateEsignerListDto,docTemplateEsignerListEntity);
        docTemplateEsignerListService.deleteEsiger(docTemplateEsignerListEntity);
    }


    /**
     *  读取orc信息
     * @param parmMap{
     *     fileId：要件流水号
     *     busReqNo：业务系统请求编号(可不传)
     *     category：识别的影像类型 id_card-身份证 bank_card-银行卡影像 vehicle_invoice-发票（机动车统一发票，增值税专用发票、普票、电子发票）
     *     side：需有有正反面参数时使用，可用值 front/back
     * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject readOCRInfo(Map<String ,Object>parmMap)throws Exception{
        JSONObject resultJson=docFileInfService.readOCRInfo(parmMap);

        return resultJson;

    }
    /**
     * 获取某个节点下面的所有要件base64Str
     * @param parm{
     *        bizNo:业务编号
     *       prdUniqueId:产品编号
     *       flowNo:流程编号
     *       nodeNo:节点编号
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray getFilesBase64Str(JSONObject parm)throws Exception{
        JSONArray resultJson=docFileInfService.getFilesBase64Str(parm);

        return resultJson;

    }

    /**
     * 通过base64串 生成要件
     * @param fileInf{
     *              uploadFileName：文件名称
     *              uploadParm：{
     *                  cusRelatedId:
     *                  busRelatedId
     *              }
     *                appName  微服务名称
     *                corpId  机构编号
     *                prdUniqueVal 产品唯一编号
     *                flowNo  流程编号
     *                nodeNo 节点编号
     *                bizNo 业务编号
     *                typeNo 要件二级类型编号
     *                fileName 文件名称
     *
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject addFileByBase64(JSONObject fileInf)throws Exception{
        JSONObject resultJson=docFileInfService.addFileByBase64(fileInf);

        return resultJson;

    }

    /**
     * 把一个业务的要件拷贝到另外一个业务中（两个业务的层级必须保持一直）
     * @param parmMap{
     *       originalBizNo:原始业务编号
     *       originalPrdUniqueId:原始产品编号
     *       originalFlowNo:原始流程编号
     *       originalNodeNo:原始节点编号
     *       targetBizNo:目标业务编号
     *
     * }
     * @return
     * @throws Exception
     */

    @Override
    public JSONObject copyFile(Map<String ,String >parmMap)throws Exception{
        JSONObject resultJson=docFileInfService.copyFile(parmMap);

        return resultJson;

    }

    /**
     * 把一个业务的要件拷贝到另外一个业务中 根据prdUniqueVal（两个业务的层级必须保持一直）
     * @param parmMap{
     *       originalBizNo:原始业务编号
     *       originalprdUniqueVal:原始产品编号
     *       originalFlowNo:原始流程编号
     *       originalNodeNo:原始节点编号
     *       originalTypeNo:原始类型编号
     *       targetPrdUniqueVal:目标产品编号
     *
     * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject copyFileByPrdUniqueVal(Map<String ,String >parmMap)throws Exception{
        JSONObject resultJson=docFileInfService.copyFileByPrdUniqueVal(parmMap);
        return resultJson;
    }

    /**
     * 把一个业务的要件拷贝到另外一个业务中 根据参数
     * @param parmMap{
     *       originalBizNo:原始业务编号
     *       originalprdUniqueVal:原始产品编号
     *       originalFlowNo:原始流程编号
     *       originalNodeNo:原始节点编号
     *       originalTypeNo:原始类型编号
     *       targetBizNo:目标业务编号
     *       targetPrdUniqueVal:目标产品编号
     *       targetFlowNo:目标流程编号
     *       targetNodeNo:目标节点编号
     *       targetTypeNo:目标产品编号
     * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject copyFileByParam(Map<String ,String >parmMap)throws Exception{
        JSONObject resultJson=docFileInfService.copyFileByParam(parmMap);
        return resultJson;
    }
    /**
     * 要件拷贝到其他目录下
     * @param parmJson{
     *
     *     orignalBizNo: 源流水号
     *     orignalPrdId:源产品编号
     *     orignalFlowNo:源流程编号
     *     orignalNodeNo:源节点编号
     *     targetBizNo: 目标流水号
     *     targetPrdId:目标产品编号
     *     targetFlowNo:目标流程编号
     *     targetNodeNo:目标节点编号

     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject copyFileToOtherFolder(JSONArray parmJson)throws Exception{
        JSONObject resultJson=docFileInfService.copyFileToOtherFolders(parmJson);

        return resultJson;
    }

    @Override
    public JSONObject moveFilesToOtherFolder(JSONObject parmJson)throws Exception{
        JSONObject resultJson=docFileInfService.moveFilesToOtherFolder(parmJson);

        return resultJson;
    }

    @Override
    public void fileAutoInitFun(@RequestBody JSONObject parmJson)throws Exception{
        DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = JSONObject.toJavaObject(parmJson,DocFileBizFiletypeConfigEntity.class);
        this.docFileBizFiletypeConfigService.fileAutoInitFun(docFileBizFiletypeConfigEntity);
    };

    @Override
    public JSONArray getRootListByTypeBean(JSONObject parmJson)throws Exception{
        DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = JSONObject.toJavaObject(parmJson,DocFileBizFiletypeConfigEntity.class);
        return docFileBizFiletypeConfigService.getRootListByTypeBean(docFileBizFiletypeConfigEntity);
    }

    @Override
    public JSONObject uploadFile(MultipartFile file,String folderId, String appName, String  uploadParm)throws Exception{
        /**
         * 获取要保存的文件的目标路径
         */
        DocFileBizFiletypeConfigEntity folder = this.docFileBizFiletypeConfigService.findById(folderId);

        return this.docFileBizFiletypeConfigService.upLoadFile(folder,file,appName,uploadParm);
    }

    @Override
    public JSONArray getTemplteDetail(JSONObject jsonObject) throws ServiceException {
        return docTemplateModelService.getTemplteDetail(jsonObject);
    }

    @Override
    public Boolean getIfShowTemplate(JSONObject jsonObject) throws Exception {
        return docTemplateModelService.getIfShowTemplate(jsonObject);
    }

    /**
     * 要件拷贝到其他目录下
     * @param parmJson{
     *
     *     orignalBizNo: 源流水号
     *     orignalPrdId:源产品编号
     *     orignalFlowNo:源流程编号
     *     orignalNodeNo:源节点编号
     *     targetBizNo: 目标流水号
     *     targetPrdId:目标产品编号
     *     targetFlowNo:目标流程编号
     *     targetNodeNo:目标节点编号

     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject copyFileToOtherFolderIncludeFolder(JSONArray parmJson)throws Exception{
        JSONObject resultJson=docFileInfService.copyFileToOtherFolderIncludeFolder(parmJson);

        return resultJson;
    }
    /**
     * 把一个业务的要件拷贝到另外一个业务中（两个业务的层级必须保持一直）
     * @param parmMapList{
     *       originalBizNo:原始业务编号
     *       originalPrdUniqueId:原始产品编号
     *       originalFlowNo:原始流程编号
     *       originalNodeNo:原始节点编号
     *       targetBizNo:目标业务编号
     *       relMark:标记是否复制关联信息（页面根据关联信息会查出来对应节点信息）1：是 ，0：否
     * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject copyFileByNodes( List<Map<String ,String>>parmMapList)throws Exception{
        JSONObject resultJson=docFileInfService.copyFileByNodes(parmMapList);

        return resultJson;

    }
    /**
     * 批量删除要件
     * @param parmMap{
     *      bizNo:关联业务主键
     *      prdUniqueVal:产品唯一标识
     *      flowNo:流程标识
     *      nodeNo:节点标识
     *      typeNo:要件类型标识
     *
     * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject deleteBatch(Map<String ,String>parmMap) throws Exception{
        JSONObject resultJson=docFileInfService.deleteBatch(parmMap);

        return resultJson;

    }
    /**
     * 批量删除要件
     * @param parmMapList{
     *      bizNo:关联业务主键
     *      prdUniqueVal:产品唯一标识
     *      flowNo:流程标识
     *      nodeNo:节点标识
     *      typeNo:要件类型标识
     *
     * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject deleteBatchByNodes(List<Map<String ,String>>parmMapList) throws Exception{
        JSONObject resultJson=docFileInfService.deleteBatchByNodes(parmMapList);

        return resultJson;

    }
    /**
     * 获取某一业务某个类型下是否有要件
     * @param parmMap{
     *      bizNo:关联业务主键
     *      prdUniqueVal:产品唯一标识
     *      flowNo:流程标识
     *      nodeNo:节点标识
     *      typeNo:要件类型标识
     *
     * }
     * @return {
     *     code：0000
     *      fileCnt：文件的数量
     *     * }
     * @throws ServiceException
     */
    @Override
    public JSONObject ifHasFiles(Map<String ,String>parmMap) throws Exception{
        JSONObject resultJson=docFileInfService.ifHasFiles(parmMap);

        return resultJson;

    }

    /** 获取某一业务某个类型下的所有要件
     * @param parmMap{
     *      bizNo:关联业务主键
     *      prdUniqueVal:产品唯一标识
     *      flowNo:流程标识
     *      nodeNo:节点标识
     *      typeNo:要件类型标识
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray getAllFilesByNode(JSONObject parmMap) throws Exception{
        JSONArray resultJson=docFileInfService.getAllFilesByNode(parmMap);

        return resultJson;

    }
    /**
     *  根据id读取文件base64信息
     * @param fileId：要件流水号
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject readFileBase64(String fileId)throws Exception{
        JSONObject result=new JSONObject();
        result.put("code","0000");
        String base64=docFileInfService.readFileBase64(fileId);
        result.put("data",base64);
        return result;
    }

    @Override
    public JSONObject ordinaryReadFileBase64(String fileId)throws Exception{
        JSONObject result=new JSONObject();
        result.put("code","0000");
        String base64=docFileInfService.ordinaryReadFileBase64(fileId);
        result.put("data",base64);
        return result;
    }
    @Override
    public JSONObject readTemplateBase64ByBtId(String fileId)throws Exception{
        JSONObject result=new JSONObject();
        result.put("code","0000");
        String list =docBizTemplateService.readTemplateBase64ByBtId(fileId);
        result.put("data",JSONArray.toJSONString(list));
        return result;
    }
    @Override
    public JSONObject readEndPactTemplateBase64(String btId)throws Exception{
        JSONObject result=new JSONObject();
        result.put("code","0000");
        String list =docBizTemplateService.readEndPactTemplateBase64(btId);
        result.put("data",JSONArray.toJSONString(list));
        return result;
    }

    @Override
    public JSONObject readTemplateBase64(@RequestBody Map<String ,String> parmMap)throws Exception{
        JSONObject result=new JSONObject();
        result.put("code","0000");
        List<DocBizTemplateEntity> list =docBizTemplateService.readTemplateBase64(parmMap);
        result.put("data",JSONArray.toJSONString(list));
        return result;
    }


    @PostMapping({"/readTemplateFile"})
    public void readTemplateFile(@RequestBody Map<String ,String> parmMap, HttpServletResponse response) throws Exception {
        FileInputStream inputStream = null;
        ByteArrayOutputStream os=null;
        try{
            JSONArray  jsonArray= docBizTemplateService.findBizMouldList(parmMap);
            if(jsonArray!=null&&jsonArray.size()>0){
                JSONObject object=jsonArray.getJSONObject(0);
                File file = new File(object.getString("savePath")+object.getString("saveFileName"));
                inputStream = new FileInputStream(file);
                os = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) != -1) {
                    os.write(buffer, 0, length);
                }
                byte[] data = os.toByteArray();
                inputStream.close();
                os.close();
                os.flush();
                response.resetBuffer();
                response.setHeader("Content-Disposition", "attachment");
                response.addHeader("file-name", URLEncoder.encode(file.getName(), "UTF-8"));
                response.addHeader("Content-Length", "" + data.length);
                response.setContentType("application/octet-stream; charset=UTF-8");
                IOUtils.write(data,response.getOutputStream());
            }
        } catch (Exception e) {
            MFLogger.error(e);
            throw e;
        } finally {
            if(inputStream != null)
            {
                inputStream.close();
            }
            if(os != null)
            {
                os.close();
            }
        }

    }

    @Override
    public JSONObject getFileByFileId(String fileId,String docType) throws Exception {
        JSONObject jsonObject = new JSONObject();
        if(StringUtils.equals(docType,CommonConstant.YES_NO_Y)){
            DocFileInfEntity entity = docFileInfService.findById(fileId);
            if (entity != null) {
                jsonObject = JSONObject.parseObject(JSONObject.toJSONString(entity));
                String obsId = entity.getObsId();
                if(StringUtils.isNotEmpty(obsId)){
                    String url = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                    jsonObject.put("showUrl",url);
                }
            }
        }else{
            DocBizTemplateEntity entity = docBizTemplateService.findById(fileId);
            if (entity != null) {
                jsonObject = JSONObject.parseObject(JSONObject.toJSONString(entity));
                String obsId = entity.getDocObsId();
                if(StringUtils.isNotEmpty(obsId)){
                    String url = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                    jsonObject.put("showUrl",url);
                }
            }
        }
        return jsonObject;
    }

    /**
     * 是否节点下所有的要件类型都已上传要件
     * @param parmMap{
     *      *      bizNo:关联业务主键
     *      *      prdUniqueVal:产品唯一标识
     *      *      flowNo:流程标识
     *      *      nodeNo:节点标识
     * }
     * @return{
     *     {
     *         code: 0000 都有了 1111 存在没上传的
     *         msg:
     *     }
     * }
     *
     * */
    @Override
    public JSONObject ifEachTypeHasFileOfNode(Map<String ,String>parmMap)throws  Exception{
        return  docFileInfService.ifEachTypeHasFileOfNode(parmMap);
    }
    @Override
    public JSONArray findTypeAndList(JSONObject jsonObject)throws  Exception{
        return  docFileBizFiletypeConfigService.findTypeAndList(jsonObject);
    }
    @Override
    public JSONArray findTypeScList(JSONObject jsonObject)throws  Exception{
        return  docFileBizFiletypeConfigService.findTypeScList(jsonObject);
    }
    /**
     * 获取某一业务某个类型下的所有要件
     * @param parmMap{
     *      bizNo:关联业务主键
     *      prdUniqueVal:产品唯一标识
     *      flowNo:流程标识
     *      nodeNo:节点标识
     *      typeNo:要件类型标识
     *
     * }
     * @return jsonArray
     * @throws Exception
     */
    @Override
    public JSONArray getFileList(Map<String ,String>parmMap) throws Exception{
        JSONArray resultJson=docFileInfService.getFileList(parmMap);
        return resultJson;
    }
    /**
     * 获取要件类型的最新上传的一个要件
     * @param parmJson{
     *      bizNo:关联业务主键
     *      prdUniqueVal:产品唯一标识
     *      flowNo:流程标识
     *      nodeNo:节点标识
     *      typeNo:要件类型标识
     * }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject getLastFileOfOneType(JSONObject parmJson) throws Exception{
        JSONObject resultJson=docFileInfService.getLastFileOfOneType(parmJson);
        return resultJson;
    }

    /**
     * 获取某一场景下的所有要件
     * @param parmMap{
     *      prdUniqueVal:产品唯一标识
     *      flowNo:流程标识
     *      nodeNo:节点标识
     *      typeNo:要件类型标识
     * }
     * @return jsonArray
     * @throws Exception
     */
    @Override
    public JSONArray getScFiletypeList(Map<String ,String>parmMap) throws Exception{
        //获取文件类型所属大类的map
        List<DocFileTypeEntity> fileTypeList= docFileTypeService.findList(new DocFileTypeEntity());
        Map<String ,String >fileTypeMap=new HashMap<>();
        for(DocFileTypeEntity type:fileTypeList){
            fileTypeMap.put(type.getTypeNo(),type.getTypeClass());
        }
        JSONArray jsonArray=new JSONArray();
        DocScFiletypeConfigEntity docScFiletypeConfigEntity=JSONObject.toJavaObject(JSONObject.parseObject(JSONObject.toJSONString(parmMap)),DocScFiletypeConfigEntity.class);
        List<DocScFiletypeConfigEntity> list=docScFiletypeConfigService.findList(docScFiletypeConfigEntity);
        for(DocScFiletypeConfigEntity entity:list){
            entity.setParentTypeNo(fileTypeMap.get(entity.getTypeNo()));
            jsonArray.add(entity);
        }
        return jsonArray;
    }



    /**
     * 获取所有的要件类型
     * @return
     * @throws Exception
     */
    @Override
    public JSONArray fileTypeArray() throws Exception{
        JSONArray resultJson=docFileTypeService.fileTypeArray();
        return resultJson;
    }

    @Override
    public JSONArray getFileIdArray(List<Map<String ,String>> parmList) throws Exception{
        JSONArray resultJson=docFileInfService.getFileIdArray(parmList);
        return resultJson;
    }

    @Override
    public JSONArray getFileListArray(List<Map<String ,String>> parmList) throws Exception{
        JSONArray resultJson=docFileInfService.getFileListArray(parmList);
        return resultJson;
    }
    @Override
    public JSONArray findTemplateArray(List<Map<String ,String>> parmList) throws Exception{
        JSONArray resultJson=docBizTemplateService.findTemplateArray(parmList);
        return resultJson;
    }
    /**
     * 获取待签约列表
     * @param parmMap{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * }
     * @return
     */
    @Override
    public JSONArray findEsignWatiList(Map<String,String>parmMap )throws Exception{
        JSONArray resultJson=docBizTemplateService.findEsignWatiList(parmMap);
        return resultJson;
    }
    /**
     * 通过签章顺序主键ID获取签章配置业务
     * @param signOrderId
     *          签章顺序signOrderId
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject findBySignOrderId(String signOrderId) throws Exception {
        JSONObject jsonObject = docBizEleSignConfigService.findBySignOrderId(signOrderId);
        return jsonObject;
    }

    @Override
    public String lastStepFlag(String templateId,String bizNo,String subBizNo,String curSignId) throws Exception {
        String res = docBizEleSignConfigService.lastStepFlag(templateId,bizNo,subBizNo,curSignId);
        return res;
    }

    /**
     * 获取需要电签的模板
     * @param object{
     *      "prdUniqueVal":"prdId",
     *      "flowNo":"flowNo",
     *      "nodeNo":"nodeNo",
     *      "bizNo":"pact_00001"
     *      "subBizNo" :""
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray  getNeedEsignTemplate(JSONObject object) throws Exception{
        JSONArray result=docBizEleSignConfigService.getNeedEsignTemplate(object);
        return result;
    }
    @Override
    public  JSONObject addDocFromObs(JSONObject parmJson)throws Exception{
        return  docFileBizFiletypeConfigService.addDocFromObs(parmJson);
    }



    /**
     * 根据obsId获取obs展示路径
     * @param obsId
     * @return
     * @throws ServiceException
     */
    @Override
    public  String  getShowUrlByObsid(String obsId)throws Exception{
        return  docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
    }


    /**
     * 通过签章顺序主键ID获取签章配置业务
     * @param updJson
     *          签章顺序signOrderId
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject updateBizSignInfo(JSONObject updJson) throws Exception {
        JSONObject jsonObject = docBizEleSignConfigService.updateBizSignInfo(updJson);
        return jsonObject;
    }
    /**
     * 修改和业务模板的属性
     * @param templateInfo
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONObject updateBizTemplateInfo(JSONObject templateInfo)throws Exception{
        JSONObject jsonObject = docBizTemplateService.updateBizTemplateInfo(templateInfo);
        return jsonObject;
    }

    @Override
    public JSONObject updateBizTemplateInfoByPactId(JSONObject templateInfo) throws Exception {
        JSONObject jsonObject = docBizTemplateService.updateBizTemplateInfoByPactId(templateInfo);
        return jsonObject;
    }

    @Override
    public JSONObject updateBizTemplateInfoByOssStatus(JSONObject templateInfo) throws Exception {
        JSONObject jsonObject = docBizTemplateService.updateBizTemplateInfoByOssStatus(templateInfo);
        return jsonObject;
    }

    /**
     * 获取当前待签约模板数据
     * @param jsonObject{
     *           templateId-模板流水号
     *            bizNo-业务编号
     *            subBizNo-子项业务流水号
     *                  }
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject getCurWaitEleSign(JSONObject jsonObject) throws Exception {
        return docBizTemplateService.getCurWaitEleSign(jsonObject);
    }



    @Override
    public JSONArray findListByObj(JSONObject parmJson) throws Exception {
        DocBizEleSignConfigEntity docBizEleSignConfigEntity = new DocBizEleSignConfigEntity();
        String templateId = parmJson.getString("templateId");
        String bizNo = parmJson.getString("bizNo");
        String subBizNo = parmJson.getString("subBizNo");
        docBizEleSignConfigEntity.setTemplateId(templateId);
        docBizEleSignConfigEntity.setBizNo(bizNo);
        docBizEleSignConfigEntity.setSubBizNo(subBizNo);
        List<DocBizEleSignConfigEntity> listByObj = docBizEleSignConfigService.findListByObj(docBizEleSignConfigEntity);

        return JSONArray.parseArray(JSON.toJSONString(listByObj));
    }
    /**
     * 判断 该节点的电签是否已经 全部完成
     * @param parm{
     * 	"prdUniqueVal":"prdId",
     * 	"flowNo":"flowNo",
     * 	"nodeNo":"nodeNo",
     * 	"bizNo":"pact_00001"
     * }
     * @return 1-全部完成电签 0-没有完成
     * @throws ServiceException
     */
    @Override
    public   String ifNodeElesignFinished(JSONObject parm)throws Exception{
        return  docBizTemplateService.ifNodeElesignFinished(parm);
    }

    /**
     *  删除
     * @param parm
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *          "subBizNo" :""
     *      * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject deletBizTemplate(@RequestBody JSONObject parm)throws Exception{
        JSONObject result=new JSONObject();
        try{
            docBizTemplateService.deleteByBean(parm);
            result.put("code","0000");
            result.put("msg","success");
        }catch (Exception e){
            MFLogger.error("",e);
            result.put("code","1111");
            result.put("msg","shagn");
            throw e;
        }
        return  result;
    }

    /**
     *  生成pdf文件
     * @param parm{
     *           templateId:模板编号
     *           bizNo: 业务流水
     *           subBizNo:子项业务流水
     *           parmList：参数列表json格式
     *           prdUniqueVal：流程唯一号
     *           flowNo：流程编号
     *           nodeNo：节点编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     *
     *
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject getPdfFile(@RequestBody JSONObject parm)throws Exception{
        JSONObject result=new JSONObject();
        try{
            result=docTemplateModelService.getPdfFile(parm);

        }catch (Exception e){
            MFLogger.error("",e);
            result.put("code","1111");
            result.put("msg","shagn");
        }
        return  result;
    }
    /**
     *  生成替换书签后的文件
     * @param parm{
     *           templateId:模板编号
     *           bizNo: 业务流水
     *           subBizNo:子项业务流水
     *           parmList：参数列表json格式
     *           prdUniqueVal：流程唯一号
     *           flowNo：流程编号
     *           nodeNo：节点编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     *
     *
     * }
     * @return
     * @throws ServiceException
     */
    @Override
    public  JSONObject getReplaceTagFile(@RequestBody JSONObject parm)throws Exception{
        JSONObject result=new JSONObject();
        try{
            result=docTemplateModelService.getReplaceTagFile(parm);

        }catch (Exception e){
            MFLogger.error("",e);
            result.put("code","1111");
            result.put("msg","shagn");
        }
        return  result;
    }
    /**
     *  生成pdf文件
     * @param parm{
     *           templateId:模板编号
     *           bizNo: 业务流水
     *           subBizNo:子项业务流水
     *           parmList：参数列表json格式
     *           prdUniqueVal：流程唯一号
     *           flowNo：流程编号
     *           nodeNo：节点编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     *
     *
     * }
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject generatePdfFile(@RequestBody JSONObject parm)throws Exception{
        JSONObject result = null;
        try{
            result=docTemplateModelService.generatePdfFile(parm);

        }catch (Exception e){
            result = new JSONObject();
            MFLogger.error("CreateContract-error-请求参数："+parm+"转换生成pdf文件报错"+e.getMessage(),e);
            result.put("code","1111");
            result.put("msg","转换生成pdf文件报错");
        }
        return  result;
    }


    /**
     * 根据id获取pdf的物理路径
     * @param btId
     * @return
     * @throws ServiceException
     */
    @Override
    public  String  getPdfFilePath(String btId)throws Exception{
        String  result="";
        try{
            result=docTemplateModelService.getPdfFilePath(btId);

        }catch (Exception e){
            MFLogger.error("",e);

        }
        return  result;
    }

    /**
     * 获取一个业务配置的所有要件类型
     * @param parm
     * @return
     * @throws Exception
     */
    @Override
    public  JSONObject getAllFileTypeOfOneBuseness(@RequestBody JSONObject parm)throws Exception{
        JSONObject result=new JSONObject();
        try{
            result=docScFiletypeConfigService.getAllFileTypeOfOneBuseness(parm);

        }catch (Exception e){
            MFLogger.error("",e);
            result.put("code","1111");
            result.put("msg","shagn");
        }
        return  result;
    }
    @Override
    public JSONObject getSignSortId(JSONObject paramJson) throws Exception {
        return docBizEleSignConfigService.getSignSortId(paramJson);
    }

    @Override
    public JSONArray getCurrentSignedUpList(String appId) throws Exception {
        return docBizTemplateService.getCurrentSignedUpList(appId);
    }

    @Override
    public JSONObject getSsqEsignCorp(JSONObject jsonObject) throws Exception {
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new DocTemplateEsignerListEntity();
        String bizNo = jsonObject.getString("bizNo");
        String esignerType = jsonObject.getString("esignerType");
        QueryWrapper<DocTemplateEsignerListEntity> qw = new QueryWrapper<>();
        qw.eq("biz_no",bizNo);
        qw.ne("esigner_type","13");
        int max = 0;
        List<DocTemplateEsignerListEntity> docTemplateEsignerListEntities = docTemplateEsignerListMapper.selectList(qw);
        for (DocTemplateEsignerListEntity templateEsignerListEntity : docTemplateEsignerListEntities) {
            String ifEsigned = templateEsignerListEntity.getIfEsigned();
            if (!"2".equals(ifEsigned)){
                max++;
            }
        }
        if (max>0){
            jsonObject.put("code","0");//还有未签署
        }else {
            jsonObject.put("code","1");//只剩下平台
        }
        jsonObject.put("data",docTemplateEsignerListEntities);
        return jsonObject;
    }

    @Override
    public  List<DocBizTemplateDto> getCurWaitEleSignListByNode(String id, String nodeNo)throws  Exception{
        return docBizTemplateService.getCurWaitEleSignList(id,nodeNo);
    }

    @Override
    public  DocBizTemplateDto getDocBizTemplateDtoByBtId(String btId)throws  Exception{

        DocBizTemplateDto result=new DocBizTemplateDto();
        DocBizTemplateEntity tmpBean=docBizTemplateService.findById(btId);
        BeanCopyUtil.copyProperties(tmpBean,result);
        return result;
    }
    @Override
    public  void insertEsiger(DocTemplateEsignerListDto docTemplateEsignerListDto)throws  Exception{
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new DocTemplateEsignerListEntity();
        BeanCopyUtil.copyProperties(docTemplateEsignerListDto,docTemplateEsignerListEntity);
        docTemplateEsignerListDto.setCreateTime(new Date());
        docTemplateEsignerListDto.setUpdateTime(new Date());
        docTemplateEsignerListService.insert(docTemplateEsignerListEntity);
    }
    @Override
    public  List<DocTemplateEsignerListDto> getEsignersList(DocTemplateEsignerListDto docTemplateEsignerListDto)throws  Exception{
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new  DocTemplateEsignerListEntity();
        BeanCopyUtil.copyProperties(docTemplateEsignerListDto,docTemplateEsignerListEntity);
        List<DocTemplateEsignerListDto> docTemplateEsignerListDtoList = new ArrayList<>();
        List<DocTemplateEsignerListEntity> docTemplateEsignerListEntityList =docTemplateEsignerListService.getEsignersList(docTemplateEsignerListEntity);
        for (DocTemplateEsignerListEntity templateEsignerListEntity : docTemplateEsignerListEntityList) {
            QueryWrapper<DocTemplateModelEntity> qw = new QueryWrapper();
            qw.eq("template_no",templateEsignerListEntity.getTemplateNo());
            DocTemplateModelEntity docTemplateModelEntity = docTemplateModelMapper.selectOne(qw);
            templateEsignerListEntity.setTypeNo(docTemplateModelEntity.getTypeNo());
            templateEsignerListEntity.setTypeName(docTemplateModelEntity.getTypeName());
            docTemplateEsignerListService.update(templateEsignerListEntity);
        }
        for (int i = 0; i < docTemplateEsignerListEntityList.size(); i++) {
            DocTemplateEsignerListDto templateEsignerListDto = new DocTemplateEsignerListDto();
            BeanCopyUtil.copyProperties(docTemplateEsignerListEntityList.get(i),templateEsignerListDto);
            docTemplateEsignerListDtoList.add(templateEsignerListDto);
        }
        return docTemplateEsignerListDtoList;
    }

    @Override
    public  List<DocTemplateEsignerListDto> queryCusSsqEsignPage(DocTemplateEsignerListDto docTemplateEsignerListDto)throws  Exception{
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new  DocTemplateEsignerListEntity();
        BeanCopyUtil.copyProperties(docTemplateEsignerListDto,docTemplateEsignerListEntity);
        List<DocTemplateEsignerListDto> docTemplateEsignerListDtoList = new ArrayList<>();
        List<DocTemplateEsignerListEntity> docTemplateEsignerListEntityList = docTemplateEsignerListService.findCusSsqEsignList(docTemplateEsignerListEntity);
        for (int i = 0; i < docTemplateEsignerListEntityList.size(); i++) {
            DocTemplateEsignerListDto templateEsignerListDto = new DocTemplateEsignerListDto();
            BeanCopyUtil.copyProperties(docTemplateEsignerListEntityList.get(i),templateEsignerListDto);
            docTemplateEsignerListDtoList.add(templateEsignerListDto);
        }
        return docTemplateEsignerListDtoList;
    }
    @Override
    public byte[] getFileToByte(String filePath) {
        try {
            File file = new File(filePath);
            FileInputStream fileInputStream;
            fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[(int) file.length()];
            fileInputStream.read(buffer);
            fileInputStream.close();
            return buffer;
        } catch (Exception e) {
            return null;
        }

    }

    @Override
    public List<DocTemplateEsignerListDto> getAppEsignersAll(DocTemplateEsignerListDto docTemplateEsignerListDto) throws Exception {
        String cusTel = docTemplateEsignerListDto.getReceiverPhone();
        QueryWrapper<DocTemplateEsignerListEntity> qw = new QueryWrapper<>();
        qw.eq("receiver_phone",cusTel);
        qw.in("esigner_type","1","2");
        qw.orderByDesc("create_time");
        qw.groupBy("biz_no");
        List<DocTemplateEsignerListEntity> docTemplateEsignerListEntities =  docTemplateEsignerListMapper.selectList(qw);
        List<DocTemplateEsignerListDto> docTemplateEsignerListDtoList = new ArrayList<>();
        for (int i = 0; i < docTemplateEsignerListEntities.size(); i++) {
            DocTemplateEsignerListDto templateEsignerListDto = new DocTemplateEsignerListDto();
            BeanCopyUtil.copyProperties(docTemplateEsignerListEntities.get(i),templateEsignerListDto);
            docTemplateEsignerListDtoList.add(templateEsignerListDto);
        }
        return docTemplateEsignerListDtoList;
    }

    @Override
    public void updateFileByFileId(JSONObject jsonObject) throws Exception {
        DocFileInfEntity docFileInfEntity = JSONObject.toJavaObject(jsonObject, DocFileInfEntity.class);
        docFileInfService.update(docFileInfEntity);
    }

    @Override
    public void updateEsigerSts(DocTemplateEsignerListDto docTemplateEsignerListDto) throws Exception {
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new  DocTemplateEsignerListEntity();
        BeanCopyUtil.copyProperties(docTemplateEsignerListDto,docTemplateEsignerListEntity);
        docTemplateEsignerListService.update(docTemplateEsignerListEntity);
    }

    @Override
    public void updateSSQEsigerSts(DocTemplateEsignerListDto docTemplateEsignerListDto) throws Exception {
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new  DocTemplateEsignerListEntity();
        QueryWrapper<DocTemplateEsignerListEntity> qw = new QueryWrapper<>();
        if (StringUtils.isNotEmpty(docTemplateEsignerListDto.getIfEsigned()) && StringUtils.isNotEmpty(docTemplateEsignerListDto.getReceiverPhone())){
            qw.lambda().eq(DocTemplateEsignerListEntity::getBizNo,docTemplateEsignerListDto.getBizNo());
            qw.lambda().eq(DocTemplateEsignerListEntity::getIfEsigned,"0");
            qw.lambda().eq(StringUtils.isNotEmpty(docTemplateEsignerListDto.getRoleName()),DocTemplateEsignerListEntity::getRoleName,docTemplateEsignerListDto.getRoleName());//签约人角色名称
            qw.lambda().eq(DocTemplateEsignerListEntity::getReceiverPhone,docTemplateEsignerListDto.getReceiverPhone());//签约人手机号
            docTemplateEsignerListEntity.setIfEsigned(docTemplateEsignerListDto.getIfEsigned());
            docTemplateEsignerListEntity.setEsignTime(docTemplateEsignerListDto.getEsignTime());
        }
        if (StringUtils.isNotEmpty(docTemplateEsignerListDto.getEsigner()) && StringUtils.isNotEmpty(docTemplateEsignerListDto.getSignShortUrl())){
            qw.lambda().eq(DocTemplateEsignerListEntity::getBizNo,docTemplateEsignerListDto.getBizNo());
            qw.lambda().eq(DocTemplateEsignerListEntity::getIfEsigned,"0");
//            qw.lambda().eq(StringUtils.isNotEmpty(docTemplateEsignerListDto.getEsigner()),DocTemplateEsignerListEntity::getEsigner,docTemplateEsignerListDto.getEsigner());//签约人
            qw.lambda().eq(StringUtils.isNotEmpty(docTemplateEsignerListDto.getRoleName()),DocTemplateEsignerListEntity::getRoleName,docTemplateEsignerListDto.getRoleName());//签约人
            docTemplateEsignerListEntity.setContractId(docTemplateEsignerListDto.getContractId());//上上签合同编号
            docTemplateEsignerListEntity.setReturnUrl(docTemplateEsignerListDto.getReturnUrl());
            docTemplateEsignerListEntity.setSignShortUrl(docTemplateEsignerListDto.getSignShortUrl());
        }else {
            qw.lambda().eq(DocTemplateEsignerListEntity::getBizNo,docTemplateEsignerListDto.getBizNo());
            qw.lambda().eq(DocTemplateEsignerListEntity::getIfEsigned,"0");
            qw.lambda().eq(StringUtils.isNotEmpty(docTemplateEsignerListDto.getTemplateName()),DocTemplateEsignerListEntity::getTemplateName,docTemplateEsignerListDto.getTemplateName());
            if (StringUtils.isNotEmpty(docTemplateEsignerListDto.getIfEsigned())){
                docTemplateEsignerListEntity.setIfEsigned(docTemplateEsignerListDto.getIfEsigned());
            }
            if (StringUtils.isNotEmpty(docTemplateEsignerListDto.getSubContractId())){
                docTemplateEsignerListEntity.setSubmitTime(docTemplateEsignerListDto.getSubmitTime());
                docTemplateEsignerListEntity.setContractId(docTemplateEsignerListDto.getContractId());
                docTemplateEsignerListEntity.setSubContractId(docTemplateEsignerListDto.getSubContractId());
            }
        }
        if (docTemplateEsignerListDto!=null){
            docTemplateEsignerListMapper.update(docTemplateEsignerListEntity,qw);
        }else {
            MFLogger.info("请求参数为空");
        }
    }

    @Override
    public void updateContractId(DocTemplateEsignerListDto docTemplateEsignerListDto) throws Exception {
        QueryWrapper<DocTemplateEsignerListEntity> qw = new QueryWrapper<>();
        qw.eq("biz_no",docTemplateEsignerListDto.getBizNo());
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new DocTemplateEsignerListEntity();
        docTemplateEsignerListEntity.setContractId(docTemplateEsignerListDto.getContractId());
        docTemplateEsignerListEntity.setSubContractId(docTemplateEsignerListDto.getSubContractId());
        docTemplateEsignerListMapper.update(docTemplateEsignerListEntity,qw);
    }


    /**
     * 根据Id删除文件
     * @param fileId:文件Id
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject deleteById(String fileId) throws Exception{
        JSONObject object = null;
        try {
            object = new JSONObject();
            object.put("code", SysConstant.MSG_CONFIG_DELETE_SUCCESS) ;
            object.put("msg", "文件删除成功") ;
            docFileInfService.deleteById(fileId);
        } catch (ServiceException e) {
            e.printStackTrace();
            object.put("code", SysConstant.MSG_CONFIG_DELETE_ERROR) ;
            object.put("msg", "文件删除成功") ;
        }

        return object ;
    }

    @Override
    public void batchDownloadEsignPactFile() throws Exception {
        int success = 0;
        int error = 0;
        List<DocTemplateEsignerListEntity> docTemplateEsignerListEntityList = docTemplateEsignerListService.batchDownloadEsignPactFile();
        MFLogger.info("下载电签合同方法 查询签约人合同列表" + JSONObject.toJSONString(docTemplateEsignerListEntityList));
        if(CollectionUtils.isNotEmpty(docTemplateEsignerListEntityList)){
            log.info("StartTaskCall-esignPactDownloadConfig-电签合同下载");
            for (DocTemplateEsignerListEntity docTemplateEsignerListEntity: docTemplateEsignerListEntityList) {
                try {
                    MFLogger.info("下载电签合同 开始" + JSONObject.toJSONString(docTemplateEsignerListEntity));
                    docTemplateEsignerListService.downloadEsignPactFile(docTemplateEsignerListEntity);
                    success = success + 1;
                    MFLogger.info("下载电签合同 结束" + JSONObject.toJSONString(docTemplateEsignerListEntity));
                }catch(Exception e){
                    error = error + 1;
                    MFLogger.error("下载电签合同失败" + JSONObject.toJSONString(docTemplateEsignerListEntity),e);
                    log.info("Taskdetailex-esignPactDownloadConfig-电签合同下载-流水号："+docTemplateEsignerListEntity.getBizNo()+"-异常信息："+e.getMessage());
                }
            }
            if(error>0){
                log.info("TaskCall-esignPactDownloadConfig-电签合同下载-error-本次处理数据条数:"+docTemplateEsignerListEntityList.size()+"条，成功"+success+"条，失败"+error+"条");
            }else{
                log.info("TaskCall-esignPactDownloadConfig-电签合同下载-本次处理数据条数:"+docTemplateEsignerListEntityList.size()+"条，成功"+success+"条，失败"+error+"条");
            }
        }
    }

    @Override
    public JSONObject insertInvoiceFile(JSONObject jsonObject) throws Exception{
        JSONObject jsonObjectA = new JSONObject();
        try {
            JSONObject jsonObject1=new JSONObject();
            jsonObject1.put("bizId",jsonObject.getString("bizId"));
            JSONObject jsonObject2= this.docFileBizFiletypeConfigService.getFileConfigByBizId(jsonObject1);
            String scId = (String)jsonObject2.get("scId");
            //Base 64 转化上传
            String base64 = (String)jsonObject.get("pdfContent");
            String fileName = (String)jsonObject.get("fileName");
            String savePath = (String)jsonObject.get("savePath");
            /**
             * MultipartFile file,String folderId, String appName, String  uploadParm
             */

            String uploadParm = (String)jsonObject.get("uploadParm");
            //base64转文件并保存
            FileUtils.base64ToFile(base64,fileName,savePath);
            //上传要件
            jsonObjectA = this.uploadFile(FileUtils.base64ToMultipartFileFh(fileName,savePath),scId,"lease",uploadParm);
        } catch (ServiceException e) {
            e.printStackTrace();
        }
        return jsonObjectA;
    }
    @Override
    public void processCallbackData(JSONObject jsonObject) throws Exception {
        DocTemplateEsignerListEntity query = new DocTemplateEsignerListEntity();
        query.setBizNo(jsonObject.getString("bizNo"));
        List<DocTemplateEsignerListEntity> esignerListEntities = docTemplateEsignerListService.findNonSignListByBizNo(query);
        if(esignerListEntities == null){
            // 最后一个签约人不下载模板
            MFLogger.info("最后一个签约人不下载模板" + jsonObject.toJSONString());
            return;
        }
        MFLogger.info("下载电签合同方法 进入" + jsonObject.toJSONString());
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new DocTemplateEsignerListEntity();
        docTemplateEsignerListEntity.setBizNo(jsonObject.getString("bizNo"));
        docTemplateEsignerListEntity.setReceiverPhone(jsonObject.getString("receiverPhone"));
        List<DocTemplateEsignerListEntity> docTemplateEsignerListEntityList = docTemplateEsignerListService.processCallbackData(docTemplateEsignerListEntity);
        MFLogger.info("下载电签合同方法 查询签约人合同列表" + JSONObject.toJSONString(docTemplateEsignerListEntityList));
        if(CollectionUtils.isNotEmpty(docTemplateEsignerListEntityList)){
            for (DocTemplateEsignerListEntity templateEsignerListEntity: docTemplateEsignerListEntityList) {
                try {
                    MFLogger.info("下载电签合同 开始" + JSONObject.toJSONString(templateEsignerListEntity));
                    docTemplateEsignerListService.downloadEsignPactFile(templateEsignerListEntity);
                    MFLogger.info("下载电签合同 结束" + JSONObject.toJSONString(templateEsignerListEntity));
                }catch(Exception e){
                    MFLogger.error("SignConract-error-合同id："+jsonObject.getString("bizNo")+"下载电签合同失败" + JSONObject.toJSONString(docTemplateEsignerListEntity),e);
                }
            }
        }
    }

    @Override
    public void ssqProcessCallbackDataAll(JSONObject jsonObject) throws Exception {
        MFLogger.info("下载电签合同方法 进入" + jsonObject.toJSONString());
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new DocTemplateEsignerListEntity();
        docTemplateEsignerListEntity.setBizNo(jsonObject.getString("bizNo"));
        List<DocTemplateEsignerListEntity> docTemplateEsignerListEntityList = docTemplateEsignerListService.processCallbackData(docTemplateEsignerListEntity);
        MFLogger.info("下载电签合同方法 查询签约人合同列表" + JSONObject.toJSONString(docTemplateEsignerListEntityList));
        if(CollectionUtils.isNotEmpty(docTemplateEsignerListEntityList)){
            for (DocTemplateEsignerListEntity templateEsignerListEntity: docTemplateEsignerListEntityList) {
                try {
                    //延缓elink调用上上签，优化电子签下载资源占用问题
                    sleep(30000);
                    MFLogger.info("下载电签合同 开始" + JSONObject.toJSONString(templateEsignerListEntity));
                    docTemplateEsignerListService.downloadEsignPactFile(templateEsignerListEntity);
                    MFLogger.info("下载电签合同 结束" + JSONObject.toJSONString(templateEsignerListEntity));
                    //更新为已完成下载
                    if("CONTRACT_COMPLETE".equals(jsonObject.getString("signFlag"))){
                        DocTemplateEsignerListDto esignerListDto = new DocTemplateEsignerListDto();
                        esignerListDto.setId(templateEsignerListEntity.getId());
                        esignerListDto.setIfDownload("2");
                        updateEsigerSts(esignerListDto);
                    }
                }catch(Exception e){
                    MFLogger.error("SignConract-error-合同下载失败" + JSONObject.toJSONString(docTemplateEsignerListEntity),e);
                }
            }
        }
    }

    /**
     * 根据bizId 获取DocFileBizFiletypeConfigEntity
     * @param parmJson
     * @throws Exception
     */
    @Override
    public JSONObject getFileConfigByBizId(@RequestBody JSONObject parmJson)throws Exception{
        return this.docFileBizFiletypeConfigService.getFileConfigByBizId(parmJson);
    };

    @Override
    public JSONObject getFileByBizNoList(@RequestBody JSONObject parmJson)throws Exception{
        return this.docFileBizFiletypeConfigService.getFileByBizNoList(parmJson);
    };

    @Override
    public JSONObject getFileListByBizNo(@RequestBody JSONObject parmJson)throws Exception{
        return this.docFileBizFiletypeConfigService.getFileListByBizNo(parmJson);
    }

    /**
     * 根据bizNo,封装打包需要的参数对象
     * @param parmJson
     * @return
     * @throws Exception
     */
    @Override
    public JSONArray getDocFileBizFiletypeConfig(@RequestBody JSONObject parmJson)throws Exception{
        DocFileBizFiletypeConfigEntity configEntity = new DocFileBizFiletypeConfigEntity() ;
        configEntity.setBizNo(parmJson.getString("bizNo"));
        configEntity.setPrdUniqueVal(parmJson.getString("prdUniqueVal"));
        configEntity.setFlowNo(parmJson.getString("flowNo"));
        configEntity.setNodeNo(parmJson.getString("nodeNo"));
        if(!StringUtil.isBlank(parmJson.getString("typeNo"))){
            configEntity.setTypeNo(parmJson.getString("typeNo"));
        }
        List<DocFileBizFiletypeConfigEntity> list = docFileBizFiletypeConfigService.findList(configEntity);
        JSONArray array = new JSONArray() ;
        JSONObject jsonObject = new JSONObject() ;
        if(CollectionUtils.isNotEmpty(list)){
            for (DocFileBizFiletypeConfigEntity entity:list){
                jsonObject = new JSONObject() ;
                jsonObject.put("scId",entity.getScId()) ;
                jsonObject.put("typeNo",entity.getTypeNo()) ;
                array.add(jsonObject) ;
            }
        }
        return array ;
    }

    @Override
    public void initAdvanceNoticeTemplate(JSONObject jsonObject) throws Exception {
        docBizTemplateService.initAdvanceNoticeTemplate(jsonObject);
    }

    @Override
    public DocBizTemplateDto getDocBizTemplateDtoBy(DocBizTemplateDto bizTemplateEntity) throws Exception {
        try{
            return docBizTemplateService.getDocBizTemplateDtoBy(bizTemplateEntity);
        }catch(Exception e){
            throw e;
        }

    }
    @Override
    public JSONObject  downSSQFile(DocTemplateEsignerListDto docTemplateEsignerListDto) throws Exception{
        try{
            DocTemplateEsignerListEntity docTemplateEsignerListEntity = new DocTemplateEsignerListEntity();
            BeanCopyUtil.copyProperties(docTemplateEsignerListDto,docTemplateEsignerListEntity);
            return  docTemplateEsignerListService.downSSQFile(docTemplateEsignerListEntity);
        }catch(Exception e){
            throw e;
        }

    }

    @Override
    public void updateSSQSubContractId(DocTemplateEsignerListDto docTemplateEsignerListDto) throws Exception {
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new  DocTemplateEsignerListEntity();
        QueryWrapper<DocTemplateEsignerListEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(DocTemplateEsignerListEntity::getBizNo,docTemplateEsignerListDto.getBizNo());
        qw.lambda().eq(StringUtils.isNotEmpty(docTemplateEsignerListDto.getTemplateName()),DocTemplateEsignerListEntity::getTemplateName,docTemplateEsignerListDto.getTemplateName());
        if (StringUtils.isEmpty(docTemplateEsignerListDto.getSubContractId())){
            MFLogger.info("子合同ID为空");
            return ;
        }
        docTemplateEsignerListEntity.setContractId(docTemplateEsignerListDto.getContractId());
        docTemplateEsignerListEntity.setSubContractId(docTemplateEsignerListDto.getSubContractId());
        if (docTemplateEsignerListDto!=null){
            docTemplateEsignerListMapper.update(docTemplateEsignerListEntity,qw);
        }else {
            MFLogger.info("请求参数为空");
        }
    }

    @Override
    public void updateSSQSignUrl(DocTemplateEsignerListDto docTemplateEsignerListDto) throws Exception {
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = new  DocTemplateEsignerListEntity();
        QueryWrapper<DocTemplateEsignerListEntity> qw = new QueryWrapper<>();
        qw.lambda().eq(DocTemplateEsignerListEntity::getBizNo,docTemplateEsignerListDto.getBizNo());
        qw.lambda().eq(StringUtils.isNotEmpty(docTemplateEsignerListDto.getRoleName()),DocTemplateEsignerListEntity::getRoleName,docTemplateEsignerListDto.getRoleName());//签约人
        docTemplateEsignerListEntity.setContractId(docTemplateEsignerListDto.getContractId());//上上签合同编号
        docTemplateEsignerListEntity.setReturnUrl(docTemplateEsignerListDto.getReturnUrl());
        docTemplateEsignerListEntity.setSignShortUrl(docTemplateEsignerListDto.getSignShortUrl());
        if (docTemplateEsignerListDto!=null){
            docTemplateEsignerListMapper.update(docTemplateEsignerListEntity,qw);
        }else {
            MFLogger.info("请求参数为空");
        }
    }

    @Override
    public void copyAssureFile(JSONObject jsonObject) throws Exception {
        docFileInfService.copyAssureFile(jsonObject);
    }

    /**
     *  批量删除文件信息
     * @param fileIds
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject deleteByIds(List<String> fileIds) throws Exception{
        JSONObject resultJson = new JSONObject();
        resultJson.put("code",SysConstant.MSG_CONFIG_SUCCESS) ;
        resultJson.put("msg","删除成功") ;
        try {
            docFileInfService.deleteByIds(fileIds);
        } catch (ServiceException e) {
            resultJson.put("code",SysConstant.MSG_CONFIG_ERROR) ;
            MFLogger.error("文件删除失败",e);
        }
        return resultJson ;
    }

    @Override
    public void deleteEsignerList(JSONObject jsonObject) throws Exception {
        docTemplateEsignerListService.deleteByBizNo(jsonObject);
    }

    @Override
    public  JSONObject auditFkPdfFile(@RequestBody JSONObject parm)throws Exception{
        JSONObject result=new JSONObject();
        try{
            result=docTemplateModelService.auditFkPdfFile(parm);
        }catch (Exception e){
            log.error("转换生成pdf文件报错",e);
            result.put("code","1111");
            result.put("msg","转换生成pdf文件报错");
        }
        return  result;
    }

    @Override
    public  JSONObject endPactPdfFile(@RequestBody JSONObject parm)throws Exception{
        JSONObject result=new JSONObject();
        try{
            result=docTemplateModelService.endPactPdfFile(parm);
        }catch (Exception e){
            log.error("转换生成pdf文件报错",e);
            result.put("code","1111");
            result.put("msg","转换生成pdf文件报错");
        }
        return  result;
    }

    @Override
    public void uploadFileForZd(String fileName, String savePathStr, String registerId) throws Exception {
        docFileInfService.uploadFileForZd( fileName,  savePathStr,  registerId);
    }

    @Override
    public JSONObject ordinaryReadFileBase64ForCompress(String fileId) throws Exception {
        JSONObject result=new JSONObject();
        result.put("code","0000");
        String base64=docFileInfService.ordinaryReadFileBase64ForCompress(fileId);
        result.put("data",base64);
        return result;
    }

    @Override
    public JSONArray getDocIdFileByCusId(String cusId) throws Exception {
        return docFileInfService.getDocIdFileByCusId(cusId);
    }

    @Override
    public JSONArray getDocIdFileByApplyId(String applyId) throws Exception {
        return docFileInfService.getDocIdFileByApplyId(applyId);
    }

    @Override
    public void batchFileMove(){
        docTemplateEsignerListService.batchFileMove();
    }

    @Override
    public JSONObject sanyAddFileByBase64(JSONObject fileInf) throws Exception {
        String  appName=fileInf.getString("appName");
        JSONObject uploadParmObj = fileInf.getJSONObject("uploadParm");
        String  uploadParm="";
        if (uploadParmObj != null){
            uploadParm = uploadParmObj.toString();
        }
        JSONArray tmpArray = fileInf.getJSONArray("file");
        List<DocFileInfBase64>  file=JSONObject.parseArray(tmpArray.toJSONString(),DocFileInfBase64.class);
        DocFileBizFiletypeConfigEntity folder = this.getOneObj(fileInf);
        JSONObject gson = this.docFileBizFiletypeConfigService.upLoadFileBase64Multi(folder,file,appName,uploadParm);
        return gson;
    }

    /**
     * 获取一个实体
     * @param fileInf
     * @return
     * @throws ServiceException
     */
    private  DocFileBizFiletypeConfigEntity getOneObj(JSONObject fileInf)throws  ServiceException{
        QueryWrapper<DocFileBizFiletypeConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(fileInf.getString("prdUniqueVal")),"prd_unique_val",fileInf.getString("prdUniqueVal"));
        queryWrapper.eq(StringUtils.isNotBlank(fileInf.getString("flowNo")),"flow_no",fileInf.getString("flowNo"));
        queryWrapper.eq(StringUtils.isNotBlank(fileInf.getString("nodeNo")),"node_no",fileInf.getString("nodeNo"));
        queryWrapper.eq(StringUtils.isNotBlank(fileInf.getString("typeNo")),"type_no",fileInf.getString("typeNo"));
        queryWrapper.eq(StringUtils.isNotBlank(fileInf.getString("bizNo")),"biz_no",fileInf.getString("bizNo"));
        queryWrapper.last("limit 1");
        return docFileBizFiletypeConfigMapper.selectOne(queryWrapper);

    }

    @PostMapping({"/getOssFile"})
    public void getOssFile(@RequestParam("fileId") String fileId, HttpServletResponse response) throws Exception {
        InputStream inputStream = null;
        ByteArrayOutputStream os=null;
        try {
            inputStream=this.docFileInfService.getFileStream(fileId);
            DocFileInfEntity tmpObj=this.docFileInfService.findById(fileId);
            os = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                os.write(buffer, 0, length);
            }
            byte[] data = os.toByteArray();
            inputStream.close();
            os.close();
            os.flush();
            response.resetBuffer();
            response.setHeader("Content-Disposition", "attachment");
            response.addHeader("file-name", URLEncoder.encode(tmpObj.getFileName(), "UTF-8"));
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream; charset=UTF-8");
            IOUtils.write(data,response.getOutputStream());
        } catch (Exception  e) {
            log.error("资方附件下载失败",e);

        }finally {
            if (null !=inputStream){
                try {
                    inputStream.close();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
            if (null !=os){
                try {
                    os.close();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }

    }
    @Override
    public JSONArray getDocIdFileSocByCusId(String cusId) throws Exception {
        return docFileInfService.getDocIdFileSocByCusId(cusId);
    }

    @Override
    public void copyAssureFileInexistence(JSONObject jsonObject) throws Exception {
        docFileInfService.copyAssureFileInexistence(jsonObject);
    }

    @Override
    public void insertDocBizTemplat(DocBizTemplateDto docBizTemplateDto) throws Exception {
        DocBizTemplateEntity docBizTemplate = new DocBizTemplateEntity();
        BeanUtils.copyProperties(docBizTemplateDto,docBizTemplate);
        docBizTemplateService.insert(docBizTemplate);
    }

    @Override
    public DocTemplateModelDto getDocTemplateModel(String tempalteId) throws Exception {
        DocTemplateModelDto docTemplateModelDto = new DocTemplateModelDto();
        DocTemplateModelEntity entity = docTemplateModelService.findById(tempalteId);
        BeanUtils.copyProperties(entity,docTemplateModelDto);
        return docTemplateModelDto;
    }

    @Override
    public List<DocScFiletypeConfigDTO> findDocScFileTypeConfig(DocScFiletypeConfigDTO docScFiletypeConfigDTO) throws Exception {
        List<DocScFiletypeConfigDTO> list = new ArrayList<>();
        DocScFiletypeConfigEntity docScFiletypeConfigEntity = new DocScFiletypeConfigEntity();
        BeanUtils.copyProperties(docScFiletypeConfigDTO,docScFiletypeConfigEntity);
        List<DocScFiletypeConfigEntity> entities = docScFiletypeConfigService.findList(docScFiletypeConfigEntity);
        if(null!= entities && !entities.isEmpty()){
            for(DocScFiletypeConfigEntity entity:entities){
                DocScFiletypeConfigDTO dto = new DocScFiletypeConfigDTO();
                BeanUtils.copyProperties(entity,dto);
                list.add(dto);
            }
        }
        return list;
    }

    @Override
    public void insertDocFileBizFileTypeConfig(DocFileBizFiletypeConfigDTO docFileBizFiletypeConfigDTO) throws Exception {
        DocFileBizFiletypeConfigEntity entity = new DocFileBizFiletypeConfigEntity();
        BeanUtils.copyProperties(docFileBizFiletypeConfigDTO,entity);
        docFileBizFiletypeConfigService.insert(entity);
    }

    @Override
    public void renderTemplate(JSONObject parmTempJson) throws Exception {
        docFileBizFiletypeConfigService.renderTemplateForExcel(parmTempJson);
    }

    @Override
    public void intitDocFileBizFileTypeConfig(DocScFiletypeConfigDTO docScFiletypeConfigDTO) throws Exception {
        DocScFiletypeConfigEntity docScFiletypeConfigEntity = new DocScFiletypeConfigEntity();
        List<DocFileBizFiletypeConfigEntity> docFileBizFiletypeConfigEntities = new ArrayList<>();
        docScFiletypeConfigEntity.setFlowNo(docScFiletypeConfigDTO.getFlowNo());
        List<DocScFiletypeConfigEntity>  docScFiletypeConfigEntities = docScFiletypeConfigService.findList(docScFiletypeConfigEntity);
        for(DocScFiletypeConfigEntity configEntity:docScFiletypeConfigEntities){
            DocFileBizFiletypeConfigEntity docFileBizFiletypeConfigEntity = new DocFileBizFiletypeConfigEntity();
            BeanUtils.copyProperties(configEntity,docFileBizFiletypeConfigEntity);
            docFileBizFiletypeConfigEntity.setScId(UUIDUtil.getUUID());
            docFileBizFiletypeConfigEntity.setBizNo(docScFiletypeConfigDTO.getPactId());
            docFileBizFiletypeConfigEntity.setCreateTime(DateUtil.getCurrDateTime());
            docFileBizFiletypeConfigEntity.setUpdateTime(DateUtil.getCurrDateTime());
            docFileBizFiletypeConfigEntities.add(docFileBizFiletypeConfigEntity);
        }
        docFileBizFiletypeConfigService.insertBatch(docFileBizFiletypeConfigEntities);
    }

    /**
     * 批量推送凭证附件至金蝶
     */
    @Override
    public void batchVoucherPush() throws Exception {
        //调用批量生成凭证附件
         batchVoucherPushService.batchVoucherPush();
        //执行完成之后调用推送金蝶接口
         batchVoucherPushService.batchVoucherPushJD();
    }

    /**
     * 批量删除凭证附件
     * @throws Exception
     */
    @Override
    public void deleteVoucherFile() throws Exception {
        batchVoucherPushService.deleteVoucherFile();
    }

    /**
     * 模板渲染
     * @param parmTempJson
     * @throws Exception
     */
//    @Override
    @RequestMapping("/renderTemplateWrod")
    public JSONObject renderTemplateWrod(JSONObject parmTempJson) throws Exception {
       return docTemplateModelService.renderTemplateWrod(parmTempJson);
    }


}
