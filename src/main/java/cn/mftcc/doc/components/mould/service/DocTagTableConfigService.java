/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocTagTableConfigEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 标签表格类配置表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-16 15:07:29
 */
public interface DocTagTableConfigService {

    IPage<DocTagTableConfigEntity> findByPage(DocTagTableConfigEntity docTagTableConfigEntity) throws ServiceException;

    void insert(DocTagTableConfigEntity docTagTableConfigEntity) throws ServiceException;

    List<DocTagTableConfigEntity> findList<PERSON>y<PERSON><PERSON><PERSON>(String keyNo) throws ServiceException;

    void update(DocTagTableConfigEntity docTagTableConfigEntity) throws ServiceException;

    DocTagTableConfigEntity findById(String configId) throws ServiceException;

    void deleteById(String configId) throws ServiceException;
}

