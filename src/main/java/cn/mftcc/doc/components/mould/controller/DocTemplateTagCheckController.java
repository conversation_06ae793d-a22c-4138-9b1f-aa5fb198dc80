/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagCheckEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateTagCheckService;

import java.util.List;

/**
 * 模板标签校验信息表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-10-22 11:13:58
 */
@RestController
@RequestMapping("mould/docTemplateTagCheck")
public class DocTemplateTagCheckController {

    @Autowired
    private DocTemplateTagCheckService docTemplateTagCheckService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocTemplateTagCheckEntity docTemplateTagCheckEntity) {
        IPage<DocTemplateTagCheckEntity> list = this.docTemplateTagCheckService.findByPage(docTemplateTagCheckEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocTemplateTagCheckEntity docTemplateTagCheckEntity){
        this.docTemplateTagCheckService.insert(docTemplateTagCheckEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocTemplateTagCheckEntity docTemplateTagCheckEntity){
        this.docTemplateTagCheckService.update(docTemplateTagCheckEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/updateTagCheckInfos")
    public R updateTagCheckInfos(@RequestBody List<DocTemplateTagCheckEntity> docTemplateTagCheckEntityList){
        this.docTemplateTagCheckService.updateTagCheckInfos(docTemplateTagCheckEntityList);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }


    @RequestMapping("/findById/{templateCheckNo}")
    public R findById(@PathVariable("templateCheckNo") String templateCheckNo){
        DocTemplateTagCheckEntity docTemplateTagCheckEntity = this.docTemplateTagCheckService.findById(templateCheckNo);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docTemplateTagCheckEntity);
    }

    @RequestMapping("/deleteById/{templateCheckNo}")
    public R deleteById(@PathVariable("templateCheckNo") String templateCheckNo){
        this.docTemplateTagCheckService.deleteById(templateCheckNo);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}