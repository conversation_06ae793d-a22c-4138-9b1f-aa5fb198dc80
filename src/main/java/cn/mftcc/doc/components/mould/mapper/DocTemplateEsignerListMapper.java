/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.mapper;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.doc.components.mould.entity.DocTemplateEsignerListEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 电子签章配置业务关联表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-11-25 15:47:45
 */
@Mapper
public interface DocTemplateEsignerListMapper extends BaseMapper<DocTemplateEsignerListEntity> {
    /**
     *
     * @return
     * @throws ServiceException
     */
    List<String> getFileName( )throws ServiceException;
}
