/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.common.utils.ParmCacheUtil;
import cn.mftcc.doc.common.constant.DocConstant;
import cn.mftcc.doc.components.docmanage.web.utils.StringUtil;
import cn.mftcc.doc.components.mould.entity.DocTagColumnConfigEntity;
import cn.mftcc.doc.components.mould.service.DocTagColumnConfigService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocTemplateTagBaseEntity;
import cn.mftcc.doc.components.mould.mapper.DocTemplateTagBaseMapper;
import cn.mftcc.doc.components.mould.service.DocTemplateTagBaseService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 标签表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-03-27 10:39:22
 */
@Service("docTemplateTagBaseService")
@Transactional(rollbackFor = Exception.class)
public class DocTemplateTagBaseServiceImpl implements DocTemplateTagBaseService {

    @Autowired
    private DocTemplateTagBaseMapper docTemplateTagBaseMapper;
    @Autowired
    private MapperUtil mapperUtil;
    @Autowired
    private ParmCacheUtil parmCacheUtil;


    @Override
    public IPage<DocTemplateTagBaseEntity> findByPage(DocTemplateTagBaseEntity docTemplateTagBaseEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTemplateTagBaseEntity> page = new Page<>();
            page.setCurrent(docTemplateTagBaseEntity.getPageNo());
            page.setSize(docTemplateTagBaseEntity.getPageSize());
            QueryWrapper<DocTemplateTagBaseEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("created_at");
            mapperUtil.tableQuery(queryWrapper,docTemplateTagBaseEntity);

            return docTemplateTagBaseMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateTagBaseEntity.getSerialId(),e);
        }
    }


    /**
     * 获取所有书签
     * @param docTemplateTagBaseEntity
     * @return
     * @throws ServiceException
     */
    public  List<DocTemplateTagBaseEntity>findByList(DocTemplateTagBaseEntity docTemplateTagBaseEntity)throws  ServiceException{
        List<DocTemplateTagBaseEntity>result=null;
        QueryWrapper<DocTemplateTagBaseEntity> queryWrapper = new QueryWrapper<>();
//        mapperUtil.tableQuery(queryWrapper,docTemplateTagBaseEntity);
        result=docTemplateTagBaseMapper.selectList(queryWrapper);
        return result;
    }

    /**
     * 获取书签
     * @param docTemplateTagBaseEntity
     * @return
     * @throws ServiceException
     */
    public  List<DocTemplateTagBaseEntity>findList(DocTemplateTagBaseEntity docTemplateTagBaseEntity)throws  ServiceException{
        List<DocTemplateTagBaseEntity>result=null;
        QueryWrapper<DocTemplateTagBaseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(docTemplateTagBaseEntity);
        result=docTemplateTagBaseMapper.selectList(queryWrapper);
        return result;
    }
    /**
     * 获取所有的待选模版书签（分组展示）
     * @return
     * @throws ServiceException
     */
    @Override
    public JSONArray getAllTagSltJson()throws  ServiceException{
        JSONArray result=new JSONArray();
        DocTemplateTagBaseEntity docTemplateTagBaseEntity=new DocTemplateTagBaseEntity();
        //获取模版书签
        docTemplateTagBaseEntity.setTagCategory(DocConstant.TAG_CATEGORY_1);
        List<DocTemplateTagBaseEntity> allTagList=findList(docTemplateTagBaseEntity);
//        let groupData1 = {
//                name: "分组一",
//                data: [
//        { value: "value1", text: "选项1" },
//        { value: "value3", text: "选项3" },
//        { value: "value5", text: "选项5" },
//        { value: "value7", text: "选项7" },
//        { value: "value9", text: "选项9" },
//        ],
        Map<Object,Object> tagGroup= parmCacheUtil.getDicMap("DOC_TAG_GROUP");
        JSONObject tmpResult=new JSONObject();
        if(allTagList!=null){
            for(DocTemplateTagBaseEntity tag:allTagList){
                String groupNo=tag.getGroupFlag();
                JSONObject option=new JSONObject();
                option.put("value",tag.getKeyNo());
                option.put("text",tag.getTagKeyName());
                if(tmpResult.containsKey(groupNo)){
                    //如果存在该组号，直接加入组内
                    tmpResult.getJSONObject(groupNo).getJSONArray("data").add(option);
                }else{
                    //如果不存在，创建一个新的分组
                    JSONArray dataArray=new JSONArray();
                    dataArray.add(option);
                    JSONObject groupJson=new JSONObject();
                    groupJson.put("name",tagGroup.get(groupNo));
                    groupJson.put("data",dataArray);
                    tmpResult.put(groupNo,groupJson);


                }
            }

            //把jsonObject转换转化成JsonArray
            Iterator iterator=tmpResult.keySet().iterator();
            while(iterator.hasNext()){
                String groupNo=iterator.next().toString();
                result.add(tmpResult.getJSONObject(groupNo));


            }
        }



        return result;
    }
    @Override
    public List<DocTemplateTagBaseEntity> findListByTagkeynos(String tagKeyNos) throws ServiceException {
        try{
            if(StringUtils.isNotBlank(tagKeyNos)){
                String[]tagAry=tagKeyNos.split("@");
                QueryWrapper<DocTemplateTagBaseEntity> queryWrapper = new QueryWrapper<>();
//                mapperUtil.tableQuery(queryWrapper,new DocTemplateTagBaseEntity());
                queryWrapper.in("key_no",tagAry);
                return docTemplateTagBaseMapper.selectList(queryWrapper);
            }else{
                return null;
            }
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,tagKeyNos,e);
        }
    }



    @Override
    public void insert(DocTemplateTagBaseEntity docTemplateTagBaseEntity) throws ServiceException {
        try{
            //默认为模板书签
            if (StringUtils.isEmpty(docTemplateTagBaseEntity.getTagCategory())){
                docTemplateTagBaseEntity.setTagCategory(DocConstant.TAG_CATEGORY_1);
            }
            docTemplateTagBaseMapper.insert(docTemplateTagBaseEntity);//插入数据库表

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTemplateTagBaseEntity.getSerialId(),e);
        }
    }


    @Override
    public void update(DocTemplateTagBaseEntity docTemplateTagBaseEntity) throws ServiceException {
        try{
            docTemplateTagBaseMapper.updateById(docTemplateTagBaseEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTemplateTagBaseEntity.getSerialId(),e);
        }
    }

    @Override
    public DocTemplateTagBaseEntity findById(String serialId) throws ServiceException {
        try{
            return docTemplateTagBaseMapper.selectById(serialId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,serialId,e);
        }
    }

    @Override
    public void deleteById(String serialId) throws ServiceException {
        try{
            docTemplateTagBaseMapper.deleteById(serialId);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,serialId,e);
        }
    }
    @Override
    public IPage<DocTemplateTagBaseEntity> selectTagByPage(DocTemplateTagBaseEntity docTemplateTagBaseEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTemplateTagBaseEntity> page = new Page<>();
            page.setCurrent(docTemplateTagBaseEntity.getPageNo());
            page.setSize(docTemplateTagBaseEntity.getPageSize());
            QueryWrapper<DocTemplateTagBaseEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docTemplateTagBaseEntity);

            return docTemplateTagBaseMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateTagBaseEntity.getSerialId(),e);
        }
    }
}