/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocScEleSignConfigEntity;
import cn.mftcc.doc.components.mould.service.DocScEleSignConfigService;

/**
 * 电子签章配置流程节点表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-28 17:37:58
 */
@RestController
@RequestMapping("mould/docScEleSignConfig")
public class DocScEleSignConfigController {

    @Autowired
    private DocScEleSignConfigService docScEleSignConfigService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocScEleSignConfigEntity docScEleSignConfigEntity) {
        IPage<DocScEleSignConfigEntity> list = this.docScEleSignConfigService.findByPage(docScEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocScEleSignConfigEntity docScEleSignConfigEntity){
        this.docScEleSignConfigService.insert(docScEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocScEleSignConfigEntity docScEleSignConfigEntity){
        this.docScEleSignConfigService.update(docScEleSignConfigEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{id}")
    public R findById(@PathVariable("id") String id){
        DocScEleSignConfigEntity docScEleSignConfigEntity = this.docScEleSignConfigService.findById(id);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docScEleSignConfigEntity);
    }

    @RequestMapping("/deleteById/{id}")
    public R deleteById(@PathVariable("id") String id){
        this.docScEleSignConfigService.deleteById(id);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
}