/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagBaseEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateTagBaseService;

/**
 * 标签表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-03-27 10:39:22
 */
@RestController
@RequestMapping("mould/docTemplateTagBase")
public class DocTemplateTagBaseController {

    @Autowired
    private DocTemplateTagBaseService docTemplateTagBaseService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocTemplateTagBaseEntity docTemplateTagBaseEntity) {
        IPage<DocTemplateTagBaseEntity> list = this.docTemplateTagBaseService.findByPage(docTemplateTagBaseEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocTemplateTagBaseEntity docTemplateTagBaseEntity){
        this.docTemplateTagBaseService.insert(docTemplateTagBaseEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocTemplateTagBaseEntity docTemplateTagBaseEntity){

        this.docTemplateTagBaseService.update(docTemplateTagBaseEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{serialId}")
    public R findById(@PathVariable("serialId") String serialId){
        DocTemplateTagBaseEntity docTemplateTagBaseEntity = this.docTemplateTagBaseService.findById(serialId);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docTemplateTagBaseEntity);
    }

    @RequestMapping("/deleteById/{serialId}")
    public R deleteById(@PathVariable("serialId") String serialId){
        this.docTemplateTagBaseService.deleteById(serialId);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
    /**
    * @Description: 选择标签
    * @Param: [docTemplateTagBaseEntity]
    * @returns: cn.mftcc.common.R
    * <AUTHOR>
    * @Date   2021/10/20 17:49
    */
    @RequestMapping("/selectTagByPage")
    public R selectTagByPage(@RequestBody DocTemplateTagBaseEntity docTemplateTagBaseEntity) {
        IPage<DocTemplateTagBaseEntity> list = this.docTemplateTagBaseService.selectTagByPage(docTemplateTagBaseEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }
}