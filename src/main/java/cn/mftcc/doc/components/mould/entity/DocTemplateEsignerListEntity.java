/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 电子签章配置业务关联表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-11-25 15:47:45
 */
@Data
@TableName("doc_template_esigner_list")
public class DocTemplateEsignerListEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId
	private String id;
	/**
	 * 所属模板
	 */
	private String templateBizId;
	/**
	 * 模板编号
	 */
	private String templateNo;
	/**
	 * 模板名称
	 */
	private String templateName;
	/**
	 * 子项模板名称
	 */
	private String subTemplateName;
	/**
	 * 签约人角色
	 */
	private String roleName;
	/**
	 * 角色id
	 */
	private String receiverId;
	/**
	 * 上上签合同id
	 */
	private String contractId;
	/**
	 * 返回签署连接
	 */
	private String signShortUrl;
	/**
	 * 返回签署短连接
	 */
	private String returnUrl;
	/**
	 * 子合同id
	 */
	private String subContractId;
	/**
	 * 附件标识
	 */
	private String annexFlag;
	/**
	 * 签署人 签署人SIGNER或抄送人CC_USER
	 */
	private String receiverType;
	/**
	 * 文档存储路径
	 */
	private String savePath;
	/**
	 * 保存后的文件名称
	 */
	private String saveFileName;
	/**
	 * 业务流水号
	 */
	private String bizNo;
	/**
	 * 子项业务流水号
	 */
	private String subBizNo;
	/**
	 * 产品唯一标识
	 */
	private String prdUniqueVal;
	/**
	 * 流程标识
	 */
	private String flowNo;
	/**
	 * 节点标识/场景
	 */
	private String nodeNo;
	/**
	 * 签章定位类型 1-单坐标2-多坐标3单关键字4多关键字
	 */
	private String positionType;
	/**
	 * 关键字
	 */
	private String keyWord;
	/**
	 * 横坐标
	 */
	private String coordinatesX;
	/**
	 * 纵坐标
	 */
	private String coordinatesY;
	/**
	 * 签章人类型 1-机构 2-企业 3-企业法人 4-企业法人配偶 5-企业实际控制人 6-企业实际控制人配偶
	 */
	private String esignerType;
	/**
	 * 签章类型 1-机构章 2-文本章
	 */
	private String esignType;
	/**
	 * 签章人员姓名
	 */
	private String esigner;
	/**
	 * 签章顺序
	 */
	private Integer esignSort;
	/**
	 * 是否阅读1-是0-否
	 */
	private String ifRead;
	/**
	 * 签章状态0-待签约1-已提交2-已签约
	 */
	private String ifEsigned;
	/**
	 * 合同内容阅读时间
	 */
	private Date readTime;
	/**
	 * 提交时间
	 */
	private Date submitTime;
	/**
	 * 签章时间
	 */
	private Date esignTime;
	/**
	 * 是否必签 1-是 0-否
	 */
	private String mustFlag;
	/**
	 * 客户类型 1-企业 0-自然人
	 */
	private String cusBaseType;
	/**
	 * 接收人姓名
	 */
	private String receiverName;
	/**
	 * 接收人联系方式
	 */
	private String receiverPhone;
	/**
	 * 签约人员编号
	 */
	private String esignerNo;

	/**
	 * 身份证号或者统一社会信用代码
	 */
	private String idNo;
	/**
	 * 创建时间时间（yyyy-mm-dd hh:mm:ss）
	 */
	private Date createTime;
	/**
	 * 更新时间（yyyy-mm-dd hh:mm:ss）
	 */
	private Date updateTime;

	/**
	 * 要件类型编号
	 */
	private String typeNo;
	/**
	 * 要件类型名称
	 */
	private String typeName;
	/**
	 * 是否已下载电签合同
	 */
	private String ifDownload;

}
