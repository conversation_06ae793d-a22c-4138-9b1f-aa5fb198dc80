/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.util;


import cn.mftcc.bizcommon.utils.DateUtil;
import cn.mftcc.bizcommon.utils.MathExtend;
import cn.mftcc.bizcommon.utils.MoneyUtil;
import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.ParmCacheUtil;
import cn.mftcc.common.utils.SpringUtil;
import cn.mftcc.doc.common.multids.MultiDataSourceManage;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.common.utils.MoneyFormatUtil;
import cn.mftcc.doc.components.docmanage.model.enums.ImgSourceType;
import cn.mftcc.doc.components.mould.entity.DocTagColumnConfigEntity;
import cn.mftcc.doc.components.mould.entity.DocTagTableConfigEntity;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagBaseEntity;
import cn.mftcc.doc.components.mould.service.DocTagColumnConfigService;
import cn.mftcc.doc.components.mould.service.DocTagTableConfigService;
import cn.mftcc.doc.feign.client.PledgeFeignClient;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import io.lettuce.core.dynamic.support.ReflectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.*;

@Repository
/**
 *
 */
@Component
public class GetTagValueManage {

    @Autowired
    private SqlDataSourceUtil sqlDataSourceUtil;
    @Autowired
    private PledgeFeignClient pledgeFeignClient  ;
    @Autowired
    private DocTagTableConfigService docTagTableConfigService  ;
    @Autowired
    private DocTagColumnConfigService docTagColumnConfigService  ;



    @Autowired
    private ParmCacheUtil parmCacheUtil;
//    @Value("${mftcc.template.plugsData-file-path:}")
//    private String  plugsDataFilePath;

    @Value("${mftcc.template.mould-path:}")
    private String docMouldPath;

    /**
     *  根据书签和参数返回所有的书签对应值。
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    public JSONObject getAllTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();
//        获取1-文字
        JSONObject word=getWordTagVal(tagList,parmList);
        result.putAll(word);
//  2-图片，
        JSONObject image=getImgTagVal(tagList,parmList);
        result.putAll(image);
//   3-表格，
        JSONObject table=getTableTagVal(tagList,parmList);
        result.putAll(table);
//    4-循环列表
        JSONObject loopTab=getLoopTabTagVal(tagList,parmList);
        result.putAll(loopTab);
//    5-循环的文字
        JSONObject loopWord=getLoopWordTagVal(tagList,parmList);
        result.putAll(loopWord);
//    6-勾选项
        JSONObject checkbox=getCheckBoxTagVal(tagList,parmList);
        result.putAll(checkbox);
//    7-循环的不规则表格
        JSONObject loopUnurualTab=getLoopUnurualTabVal(tagList,parmList);
        result.putAll(loopUnurualTab);



        return result;
    }

    /**
     * 获取文字类书签的值
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getWordTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();
        JSONObject sqlWord=getSqlTagVal(tagList,parmList);//获取sql数据源文字类标签
        JSONObject javaWord=getJavaTagVal(tagList,parmList);//获取sql数据源文字类标签
        result.putAll(sqlWord);
        result.putAll(javaWord);
        return result ;
    }

    private JSONObject getJavaTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();
        Map<String ,DocTemplateTagBaseEntity> formatMap;//需要格式化的书签
        for(DocTemplateTagBaseEntity tag : tagList){
            //如果是文本类型，且数据来源是feign接口
            if("1".equals(tag.getShowStyle()) && "1".equals(tag.getDataSourceType())) {
                formatMap=new HashMap<>();

                if (StringUtils.isNotEmpty(tag.getFormatType())) {
                    formatMap.put(tag.getKeyNo(), tag);
                }

                String serviceName = tag.getTableName();
                String methodName=tag.getColumnName();

                Method method = ReflectionUtils.findMethod(SpringUtil.getBean(serviceName).getClass(),methodName, String.class);
                MFLogger.info("tag_base:"+tag.toString()+"serviceName:"+serviceName+"-----methodName:"+methodName);
                MFLogger.info("parmList:"+parmList.toString());
                String queryResult = String.valueOf(ReflectionUtils.invokeMethod(method,SpringUtil.getBean(serviceName),parmList.toJSONString()));
                if (StringUtils.isEmpty(queryResult)||queryResult=="null"){
                    queryResult="";
                }
                result.put(tag.getKeyNo(),queryResult);

                //格式化内容
                formatVal(result,formatMap);
            }
        }
        return result;
    }


    /**
     * 获取图片类书签的值
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getImgTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();

        String key="";
        //第一步：找到相同查询条件（数据库，表，查询条件，参数列表）的书签
        JSONObject group=new JSONObject();
        for(DocTemplateTagBaseEntity tag : tagList){
            //如果是图片类型，且数据来源是feign接口
            if("2".equals(tag.getShowStyle()) && "1".equals(tag.getDataSourceType())) {

                String serviceName = tag.getTableName();
                String methodName=tag.getColumnName();

                Method method = ReflectionUtils.findMethod(SpringUtil.getBean(serviceName).getClass(),methodName, String.class);
                JSONObject queryResult = (JSONObject) ReflectionUtils.invokeMethod(method,SpringUtil.getBean(serviceName),parmList.toJSONString());
//                result.put("sourceType","local");
//                result.put("imgPath","D:\\work\\microservices\\prdnew\\web\\mftcc-doc-web\\static\\mySon.jpg");
                JSONObject imgTag=new JSONObject();
                if("local".equals(queryResult.getString("sourceType"))){
                    imgTag.put("source", ImgSourceType.LOCAL);
                }else{
                    imgTag.put("source", ImgSourceType.URL);
                }

                String imgPath=queryResult.getString("imgPath");
                imgTag.put("url",imgPath);
                imgTag.put("imgType", PictureType.suggestFileType(imgPath));
                result.put(tag.getKeyNo(),imgTag);

            }
        }
        return result ;
    }
    /**
     * 获取表格类书签的值
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getTableTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();
        return result ;
    }
    /**
     * 获取循环列表类书签的值
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getLoopTabTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();

        //数据源是sql的循环列表类
        JSONObject loopTabBySql=getLoopTabValBySql(tagList,parmList);
        result.putAll(loopTabBySql);
        //数据源是feign接口的循环列表类
        JSONObject loopTabValByFeign=getLoopTabValByFeign(tagList,parmList);
        result.putAll(loopTabValByFeign);



        return result ;
    }
    /**
     * 获取循环文字类书签的值
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getLoopWordTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();
        //数据源是sql的循环列表类
        JSONObject loopWordBySql=getLoopWordValBySql(tagList,parmList);
        result.putAll(loopWordBySql);
        //数据源是feign接口的循环列表类
        JSONObject loopTabValByFeign=getLoopWordValByFeign(tagList,parmList);
        result.putAll(loopTabValByFeign);

        return result ;
    }
    /**
     * 获取循环的不规则表格类书签的值
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getCheckBoxTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();


        String key="";


        try{
            for(DocTemplateTagBaseEntity tag : tagList){
                //如果是勾选类型，且数据来源是feign接口
                if("6".equals(tag.getShowStyle()) && "1".equals(tag.getDataSourceType())) {

                    String serviceName = tag.getTableName();
                    String methodName=tag.getColumnName();

                    Method method = ReflectionUtils.findMethod(SpringUtil.getBean(serviceName).getClass(),methodName, String.class);
                    JSONArray   queryResult = (JSONArray) ReflectionUtils.invokeMethod(method,SpringUtil.getBean(serviceName),parmList.toJSONString());
//                result.put("sourceType","local");
//                result.put("imgPath","D:\\work\\microservices\\prdnew\\web\\mftcc-doc-web\\static\\mySon.jpg");
                    if(queryResult!=null){
                        int len=queryResult.size();
                        if(len>0){
                            JSONArray resJson=new JSONArray();
                            int width=15;
                            int height=15;
                            if(tag.getLineNum()!=null){
                                width=Integer.parseInt(tag.getLineNum());
                            }
                            if(tag.getColumnNum()!=null){
                                height=Integer.parseInt(tag.getColumnNum());
                            }
                            String  orignalPath=new FileUtils().getOrignalFilePath();
                            String imgPath=orignalPath+ "img"+File.separator;
                            for(int i=0;i<len;i++){
//                            option2.put("checkValue","质押");
//                            option2.put("checked",false);
                                JSONObject json=queryResult.getJSONObject(i);
                                JSONObject option=new JSONObject();
                                PictureRenderData sltPic=null;
                                if(json.getBooleanValue("checked")){
                                    String sltImg=imgPath+"slt.png";
//                                    sltPic=Pictures.ofLocal(sltImg).size(width,height).create();
                                    // 图片流 D:\work\microservices\prdnew\server\mftcc-doc-server\src\main\resources\static\template\img\noSlt.png
                                    ClassPathResource  res = new ClassPathResource("/static/template/img/slt.png", this.getClass());
                                    InputStream impStream=res.getInputStream();

                                    sltPic=Pictures.ofStream(impStream, PictureType.PNG)
                                                    .size(width,height).create();
                                    MFLogger.info("图片流的路径是："+res.getURL());
                                }else{
                                    String sltImg=imgPath+"noSlt.png";
//                                    sltPic= Pictures.ofLocal(sltImg).size(width,height).create();
//                                    sltPic=Pictures.ofStream(new FileInputStream(sltImg), PictureType.PNG)
//                                            .size(width,height).create();
                                    ClassPathResource  res = new ClassPathResource("/static/template/img/noSlt.png", this.getClass());
                                    MFLogger.info("图片流的路径是："+res.getURL());
                                    InputStream impStream=res.getInputStream();

                                    sltPic=Pictures.ofStream(impStream, PictureType.PNG)
                                            .size(width,height).create();
                                }
                                option.put("checkBoxKey",sltPic);
                                option.put("checkBoxValue",json.getString("checkValue"));

                                resJson.add(option);
                            }


                            result.put(tag.getKeyNo(),resJson);
                        }
                    }

                }
            }
        }catch (Exception e){
            MFLogger.error("获取循环的不规则表格类书签的值",e);
            throw  new ServiceException("获取循环的不规则表格类书签的值" ,"",e);
        }


        return result ;
    }
    /**
     * 获取循环的不规则表格类书签的值
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getLoopUnurualTabVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{
        JSONObject result=new JSONObject();
        return result ;
    }


    /**
     *
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getLoopTabValBySql(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{



        JSONObject result=new JSONObject();
        String key="";
        //第一步：找到相同查询条件（数据库，表，查询条件，参数列表）的书签
        JSONObject group=new JSONObject();
        for(DocTemplateTagBaseEntity tag : tagList){
            //如果是文字类型，且数据来源是数据库
            if("4".equals(tag.getShowStyle()) && "0".equals(tag.getDataSourceType())) {

                //如果不存在，重新创建
                JSONObject queryJson = new JSONObject();
                queryJson.put("database", tag.getDatabaseName());
                queryJson.put("tableName", tag.getTableName());
                queryJson.put("columnName", tag.getColumnName());
                queryJson.put("queryCondation", tag.getQueryCondition());
                queryJson.put("parmList", tag.getParamType());
                group.put(key, queryJson);
                JSONArray queryRes=queryResutlBySql(queryJson,parmList);
                queryRes = this.processListResDicVal(queryRes,tag.getKeyNo());//处理数据字典
                queryRes=processListFormatVal(queryRes,tag.getKeyNo());//处理数据的格式化
                result.put(tag.getKeyNo(),queryRes);

            }
        }
        return  result;

    }

    /**
     * 替换列表标签结果字典项值
     * @param queryRes 查询结果
     * @param keyNo 标签名称
     * @return
     * @throws ServiceException
     */
    private JSONArray processListResDicVal(JSONArray queryRes,String keyNo)throws ServiceException{
        JSONArray resJsonArray = new JSONArray();
        for(Object object : queryRes){
            JSONObject jsonObject = (JSONObject) JSON.toJSON(object);
            List<DocTagTableConfigEntity> docTagTableConfigList = docTagTableConfigService.findListByKeyno(keyNo);
            for(DocTagTableConfigEntity tagTableConfig : docTagTableConfigList){
                String key = tagTableConfig.getTableColumnId();
                String value = jsonObject.get(key)!=null?jsonObject.get(key).toString():"";
                if(StringUtils.isNotEmpty(tagTableConfig.getDynamicParam())&&StringUtils.isNotEmpty(value)){
                    value =parmCacheUtil.getOptName(tagTableConfig.getDynamicParam(),value);
                    jsonObject.put(key,value);
                }
            }
            resJsonArray.add(jsonObject);
        }
        return resJsonArray;
    }

    /**
     *  获取循环列表的的
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getLoopTabValByFeign(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{



        JSONObject result=new JSONObject();
        String key="";
        //第一步：找到相同查询条件（数据库，表，查询条件，参数列表）的书签
        JSONObject group=new JSONObject();
        for(DocTemplateTagBaseEntity tag : tagList){
            //如果是文字类型，且数据来源是数据库
            if("4".equals(tag.getShowStyle()) && "1".equals(tag.getDataSourceType())) {



                String serviceName = tag.getTableName();
                String methodName=tag.getColumnName();

                Method method = ReflectionUtils.findMethod(SpringUtil.getBean(serviceName).getClass(),methodName, String.class);
                JSONArray queryResult = (JSONArray) ReflectionUtils.invokeMethod(method,SpringUtil.getBean(serviceName),parmList.toJSONString());
                queryResult = this.processListResDicVal(queryResult,tag.getKeyNo());//处理数据字典
                queryResult=processListFormatVal(queryResult,tag.getKeyNo());//处理数据的格式化
                result.put(tag.getKeyNo(),queryResult);

            }
        }
        return  result;

    }
    /**
     *  获取循环列表的的
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getLoopWordValByFeign(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{



        JSONObject result=new JSONObject();
        String key="";
        //第一步：找到相同查询条件（数据库，表，查询条件，参数列表）的书签
        JSONObject group=new JSONObject();
        for(DocTemplateTagBaseEntity tag : tagList){
            //如果是文字类型，且数据来源是数据库
            if("5".equals(tag.getShowStyle()) && "1".equals(tag.getDataSourceType())) {



                String serviceName = tag.getTableName();
                String methodName=tag.getColumnName();

                MFLogger.info("服务名："+serviceName+"方法名："+methodName+",参数："+parmList.toJSONString());
                Method method = ReflectionUtils.findMethod(SpringUtil.getBean(serviceName).getClass(),methodName, String.class);
                JSONArray queryResult = (JSONArray) ReflectionUtils.invokeMethod(method,SpringUtil.getBean(serviceName),parmList.toJSONString());

                result.put(tag.getKeyNo(),queryResult);

            }
        }
        return  result;

    }





    /**
     *   循环的文字类sql数据源
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getLoopWordValBySql(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{



        JSONObject result=new JSONObject();
        String key="";
        //第一步：找到相同查询条件（数据库，表，查询条件，参数列表）的书签
        JSONObject group=new JSONObject();
        for(DocTemplateTagBaseEntity tag : tagList){
            //如果是文字类型，且数据来源是数据库
            if("5".equals(tag.getShowStyle()) && "0".equals(tag.getDataSourceType())) {

                //如果不存在，重新创建
                JSONObject queryJson = new JSONObject();
                queryJson.put("database", tag.getDatabaseName());
                queryJson.put("tableName", tag.getTableName());
                queryJson.put("columnName", tag.getColumnName());
                queryJson.put("queryCondation", tag.getQueryCondition());
                queryJson.put("parmList", tag.getParamType());
                group.put(key, queryJson);
                JSONArray queryRes=queryResutlBySql(queryJson,parmList);
                result.put(tag.getKeyNo(),queryJson);

            }
        }
        return  result;

    }


    /**
     *  格式化值
     * @param resData
     * @param formaterMap
     * @throws ServiceException
     */
    private void formatVal(JSONObject resData, Map<String ,DocTemplateTagBaseEntity> formaterMap)throws ServiceException{
        if(formaterMap!=null){
            if(formaterMap.size()>0){
                for(String key :formaterMap.keySet()){
                    if(!resData.containsKey(key)){
                        continue;
                    }
                    DocTemplateTagBaseEntity tag=formaterMap.get(key);
                    String resVal=getFormatVal(resData.get(key),tag);

                    resData.put(key,resVal);
                }
            }
        }
    }

    /**
     * 处理数据字典的取值
     * @param resData
     * @param dicMap
     * @throws ServiceException
     */
    private void  processDicVal(JSONObject resData, Map<String ,DocTemplateTagBaseEntity> dicMap)throws ServiceException{
        if(dicMap!=null){
            if(dicMap.size()>0){
                for(String key :dicMap.keySet()){
                    if(!resData.containsKey(key)){
                        continue;
                    }
                    DocTemplateTagBaseEntity tag=dicMap.get(key);
                    if(null != resData.get(key)){
                        String keyval=resData.get(key).toString();
                        String value =parmCacheUtil.getOptName(tag.getSysDictionryVal(),keyval);
                        resData.put(key,value);
                    }
                }
            }
        }
    }
    /**
     * 格式化值
     * @param originalVal
     * @param docTemplateTagBaseEntity
     * @return
     * @throws ServiceException
     */
    private  String   getFormatVal(Object originalVal,DocTemplateTagBaseEntity docTemplateTagBaseEntity  )throws ServiceException{
        String result="";
        if(originalVal==null){
            return result;
        }
        if(StringUtils.isNotEmpty(String.valueOf(originalVal))){
            String  formatType=docTemplateTagBaseEntity.getFormatType();
            if("01".equals(formatType)){
                //金额小写
                result= MoneyUtil.moneyStr(originalVal.toString());
            }
            if("02".equals(formatType)){
                //金额大写
                result= MoneyFormatUtil.numberToChinese(Double.parseDouble(originalVal.toString()));
            }
            if("03".equals(formatType)){
                //日期格式化(年月日)
                Date tmp=DateUtil.strToDate(String.valueOf(originalVal),"yyyy-MM-dd");
                result= DateUtil.DateToStr(tmp,"yyyy年MM月dd日");

            }
            if("04".equals(formatType)){
                //金额拆分
                result= MoneyUtil.moneyStr(originalVal.toString());

            }
            if("05".equals(formatType)){
                //保留两位小数
                result = String.format("%.2f",Double.valueOf(originalVal.toString()));
            }
            if("06".equals(formatType)){
                //日期时间格式化
                result= DateUtil.DateToStr((Date)originalVal,docTemplateTagBaseEntity.getFormatParam());
            }
            if("07".equals(formatType)){
                //时间格式化
                result= DateUtil.DateToStr((Date)originalVal,docTemplateTagBaseEntity.getFormatParam());

            }
        }else{
            result="";
        }
        return result;
    }



    /**
     * 获取标签中所有sql数据源的书签的取值
     * @param tagList
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private JSONObject getSqlTagVal(List<DocTemplateTagBaseEntity> tagList, JSONObject parmList)throws  ServiceException{


        JSONObject result=new JSONObject();
        if(tagList==null){
            //如果没配配置书签直接返回
            return  result;
        }
        String key="";
        //第一步：找到相同查询条件（数据库，表，查询条件，参数列表）的书签
        JSONObject group=new JSONObject();


        JSONObject keyColRel = new JSONObject();//列名和key值的对应guan'xi

        JSONObject keyUrl=new JSONObject();//有超链接的书签。

        Map<String ,DocTemplateTagBaseEntity> formaterMap=new HashMap<String ,DocTemplateTagBaseEntity>();//需要格式化的书签
        Map<String ,DocTemplateTagBaseEntity> dicMap=new HashMap<String ,DocTemplateTagBaseEntity>();//包含数据字典的书签
        for(DocTemplateTagBaseEntity tag : tagList){
            if(StringUtils.isNotEmpty(tag.getFormatType())){

                formaterMap.put(tag.getKeyNo(),tag);
            }

            if(StringUtils.isNotEmpty(tag.getSysDictionryVal())){

                dicMap.put(tag.getKeyNo(),tag);
            }

            //如果是文字类型，且数据来源是数据库
            if("1".equals(tag.getShowStyle()) && "0".equals(tag.getDataSourceType())){
                key=tag.getDatabaseName()+"_"+tag.getTableName()+"_"+tag.getQueryCondition()+"_"+tag.getParamType();
                if(StringUtils.isNotEmpty(tag.getTextLink())){
                    keyUrl.put(tag.getKeyNo(),tag.getTextLink());
                }

                String tagName=tag.getColumnName();
                if(tagName.contains("as")){
                    String ary[]=tagName.split(" as ");
                    tagName=ary[ary.length-1];
                    tagName=tagName.replaceAll(" ","");
                }
                if(group.containsKey(key)){
                    //如果存在，在原来的列名追加列名
                    String orginalVal= group.getJSONObject(key).getString("columnName");
                    orginalVal+=","+tag.getColumnName();
                    group.getJSONObject(key).put("columnName",orginalVal);
                    keyColRel.put(tagName,tag.getKeyNo());
                }else{
                    //如果不存在，重新创建
                    keyColRel.put(tagName,tag.getKeyNo());
                    JSONObject queryJson=new JSONObject();
                    queryJson.put("database",tag.getDatabaseName());
                    queryJson.put("tableName",tag.getTableName());
                    queryJson.put("columnName",tag.getColumnName());
                    queryJson.put("queryCondation",tag.getQueryCondition());
                    queryJson.put("parmList",tag.getParamType());
                    group.put(key,queryJson);
                }
            }
        }
        //第二步：遍历查询结果
        if(group.size()>0){
            Iterator iterator = group.keySet().iterator();
            while(iterator.hasNext()){
                key = iterator.next().toString();
                JSONObject queryObj = group.getJSONObject(key);
                JSONObject queryRes=queryOneObjBySql(queryObj,parmList);
                //放入结果中
                Iterator subIterator=queryRes.keySet().iterator();
                while(subIterator.hasNext()){
                    String subKey = subIterator.next().toString();
                    String temTagKey=keyColRel.getString(subKey);

                    result.put(temTagKey,queryRes.get(subKey));
                }


            }
        }
        //格式化内容
        formatVal(result,formaterMap);
        //处理数据字典
        processDicVal(result,dicMap);

        return  result;

    }



    /**
     *  封装查询的结果实体
     * @param queryParts
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private  JSONObject queryOneObjBySql(JSONObject queryParts,JSONObject parmList)  throws  ServiceException{
        JSONObject result=new JSONObject();
        String database=queryParts.getString("database");
        String tableName=queryParts.getString("tableName");
        String  columName=queryParts.getString("columnName");
        String  queryCondation=queryParts.getString("queryCondation");
        String  parms=queryParts.getString("parmList");
        String  querySql="select "+columName +" from "+tableName+"  "+queryCondation;

        String[] paramsNameAry=parms.split("\\|");
        int paramCnt=paramsNameAry.length;
        String parmAry[]=new String[paramCnt];
        for(int i=0;i<paramCnt;i++){
            parmAry[i]=parmList.getString(paramsNameAry[i]);
        }


        result=sqlDataSourceUtil.getQueryObject(querySql,database,parmAry);
        return result;

    }




    /**
     *  封装查询的结果实体
     * @param queryParts
     * @param parmList
     * @return
     * @throws ServiceException
     */
    private  JSONArray queryResutlBySql(JSONObject queryParts,JSONObject parmList)  throws  ServiceException{
        JSONArray result=new JSONArray();
        String database=queryParts.getString("database");
        String tableName=queryParts.getString("tableName");
        String  columName=queryParts.getString("columnName");
        String  queryCondation=queryParts.getString("queryCondation");
        String  parms=queryParts.getString("parmList");
        String  querySql="select "+columName +" from "+tableName+"  "+queryCondation;

        String[] paramsNameAry=parms.split(",");
        int paramCnt=paramsNameAry.length;
        String parmAry[]=new String[paramCnt];
        for(int i=0;i<paramCnt;i++){
            parmAry[i]=parmList.getString(paramsNameAry[i]);
        }


        result=sqlDataSourceUtil.getQueryResult(querySql,database,parmAry);


        return result;

    }

    /**
     * 替换列表中的格式化
     * @param queryRes 查询结果
     * @param keyNo 标签名称
     * @return
     * @throws ServiceException
     */
    private JSONArray processListFormatVal(JSONArray queryRes,String keyNo)throws ServiceException{
        JSONArray resJsonArray = new JSONArray();
        DocTagColumnConfigEntity qBean=new DocTagColumnConfigEntity();
        qBean.setKeyNo(keyNo);
        List<DocTagColumnConfigEntity> docTagTableConfigList = docTagColumnConfigService.findList(qBean);
        Map<String ,DocTagColumnConfigEntity>formatMap=new HashMap<>();
        if(docTagTableConfigList!=null){
            if(docTagTableConfigList.size()>0) {

                for(DocTagColumnConfigEntity bean:docTagTableConfigList){
                    formatMap.put(bean.getTableColumnId(),bean);
                }

            }
        }
        if(formatMap.size()>0){
            for(Object object : queryRes) {
                JSONObject jsonObject = (JSONObject) object;


                for(String columnId:formatMap.keySet()){

                    DocTagColumnConfigEntity tagTableConfig=formatMap.get(columnId);
                    String value = jsonObject.get(columnId)!=null?jsonObject.get(columnId).toString():"";
                    String patten=tagTableConfig.getFormatParam();
                    String afterVal=getFormatValByFormat(value,tagTableConfig.getFormatType(),patten);
                    jsonObject.put(columnId,afterVal);

                }
                resJsonArray.add(jsonObject);

            }




        }else{
            resJsonArray=queryRes;
        }
        return resJsonArray;
    }

    /**
     * 对结果的格式化
     * @param originalVal
     * @param formatType
     * @param patten
     * @return
     * @throws ServiceException
     */
    private  String  getFormatValByFormat(Object originalVal,String formatType,String patten )throws ServiceException{
        String result="";
        if(originalVal==null){
            return result;
        }
        result = originalVal.toString();
        if(StringUtils.isNotEmpty(String.valueOf(originalVal))){

            if("01".equals(formatType)){
                //金额小写
                result= MoneyUtil.moneyStr(originalVal.toString());
            }
            if("02".equals(formatType)){
                //金额大写
                result= MoneyFormatUtil.numberToChinese(Double.parseDouble(originalVal.toString()));
            }
            if("03".equals(formatType)){
                //日期格式化(年月日)
                Date tmp=DateUtil.strToDate(String.valueOf(originalVal),"yyyyMMdd");
                result= DateUtil.DateToStr(tmp,"yyyy年MM月dd日");

            }
            if("04".equals(formatType)){
                //金额拆分
                result= MoneyUtil.moneyStr(originalVal.toString());

            }
            if("05".equals(formatType)){
                //保留两位小数
                result = String.format("%.2f",Double.valueOf(originalVal.toString()));
            }
            if("06".equals(formatType)){
                //日期时间格式化
                result = DateUtil.getShowDateTime(String.valueOf(originalVal));
            }
            if("07".equals(formatType)){
                //时间格式化
                if(StringUtils.isNotEmpty(patten))
                    result= DateUtil.DateToStr((Date)originalVal,patten);

            }
        }else{
            result="";
        }
        return result;
    }





}
