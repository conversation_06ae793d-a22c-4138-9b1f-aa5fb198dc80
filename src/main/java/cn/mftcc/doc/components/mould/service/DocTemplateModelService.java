/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import cn.mftcc.doc.components.mould.entity.DocTemplateTagSetEntity;
import cn.mftcc.doc.feign.dto.DocConvertDto;
import cn.mftcc.doc.feign.dto.DocTemplateModelDto;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;

import cn.mftcc.common.exception.ServiceException;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 模板表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-03-25 14:53:31
 */
public interface DocTemplateModelService {

    IPage<DocTemplateModelEntity> findByPage(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException;

    public List<DocTemplateModelEntity> findDocTemplateList(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException;

    public  String getDocMouldPath();

    List<DocTemplateModelEntity> findList(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException;

    List<DocTemplateModelDto> findMsgMouldList() throws ServiceException;

    JSONObject getMsgContent(Map<String,Object> obj)throws  ServiceException;

   JSONObject fileFormatConvert(@RequestBody DocConvertDto docConvertDto)throws Exception;

    JSONObject fileFormatConvertExternal(@RequestBody Map<String ,String >converParm);

    public  JSONObject previewMould(JSONObject obj)throws  ServiceException;
    public  JSONObject archivePreviewMould(JSONObject obj)throws  ServiceException;

    public  JSONObject getTagsConfig(String templateId)throws  ServiceException;

    public JSONObject getMouldConfigInfo(JSONObject parmJson) throws ServiceException;

    public JSONObject getPluginsData(String templateId)throws  ServiceException;

    JSONObject getTemplateTagConfigJson(String templateId)throws ServiceException;

    int setTemplateTags(DocTemplateTagSetEntity updBean)throws  ServiceException;

    void insert(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException;

    JSONObject clone(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException;

    void update(DocTemplateModelEntity docTemplateModelEntity) throws ServiceException;

    DocTemplateModelEntity findById(String templateNo) throws ServiceException;

    void deleteById(String templateNo) throws ServiceException;

    JSONObject upLoadModelFile( MultipartFile file)throws  ServiceException;

    JSONObject updateUpLoadModelFile( MultipartFile file,String templateId)throws  ServiceException;

    JSONArray findTemplateArray() throws ServiceException;

    JSONObject getTagData(String templateId,JSONObject parmList )throws ServiceException;

    /**
     * 获取标签的取值(key值为汉语)
     * @param templateId 模板管理
     * @param parmList
     * @return
     * @throws ServiceException
     */
    JSONObject getChnTagData(String templateId,JSONObject parmList )throws ServiceException;


    /**
     *  生成替换书签后的文件
     * @param obj{
     *           templateId:模板编号
     *           bizNo: 业务流水
     *           subBizNo:子项业务流水
     *           parmList：参数列表json格式
     *           prdUniqueVal：流程唯一号
     *           flowNo：流程编号
     *           nodeNo：节点编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     *
     *
     * }
     * @return
     * @throws ServiceException
     */
      JSONObject getReplaceTagFile(JSONObject obj)throws  ServiceException;

    /**
     *  生成pdf文件
     * @param obj
     * @return
     * @throws ServiceException
     */

      JSONObject getPdfFile(JSONObject obj)throws  ServiceException;

    /**
     *  获取预览页面的配置（不自动生成文档）
     * @param obj
     * @return
     * @throws ServiceException
     */
      JSONObject onlyPreviewMould(JSONObject obj)throws  ServiceException;

    /**
     * 获取标签的取值(key值为汉语)
     * @param parmJson{
     *     tagKeyAry:书签数组
     *     parmList:所需参数
     * }
     * @throws ServiceException
     */

     JSONObject  getAllTagData(JSONObject parmJson )throws ServiceException;

    /**
     *  预览一个模板文档
     * @param obj{
     *           btId:模板关联业务的编号
     *           reLoadFlag:是否重新替换标签 0-否 1-是
     * }
     * @return
     * @throws ServiceException
     */

    public  JSONObject previewOneModelFile(JSONObject obj)throws  ServiceException;


    public  JSONObject generatePdfFile(JSONObject obj)throws  ServiceException;

    String  getPdfFilePath(String btId)throws  ServiceException;

    JSONObject findByTemplateModdel(JSONObject jsonObject);

    JSONArray getTemplteDetail(JSONObject jsonObject)throws  ServiceException;

    Boolean getIfShowTemplate(JSONObject jsonObject)throws  ServiceException, Exception;

    DocTemplateModelEntity findByNo(String templateNo)throws  ServiceException;

    JSONObject auditFkPdfFile(JSONObject obj)throws  ServiceException;

    /**
     * 生成合同结清通知涵
     * @param parm
     * @throws ServiceException
     */
    JSONObject endPactPdfFile(JSONObject parm)throws  ServiceException;

    /**
     *  模板渲染
     * @param parm
     * @return
     * @throws ServiceException
     */
    JSONObject renderTemplate(JSONObject parm)throws  ServiceException;

    void renderTemplateForExcel(JSONObject parmTempJson)throws  ServiceException;

    JSONObject renderTemplateWrod(JSONObject parmTempJson);
}

