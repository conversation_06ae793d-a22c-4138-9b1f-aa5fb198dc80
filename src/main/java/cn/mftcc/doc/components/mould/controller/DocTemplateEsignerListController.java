/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocTemplateEsignerListEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateEsignerListService;

/**
 * 电子签章配置业务关联表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-11-25 15:47:45
 */
@RestController
@RequestMapping("mould/docTemplateEsignerList")
public class DocTemplateEsignerListController {

    @Autowired
    private DocTemplateEsignerListService docTemplateEsignerListService;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocTemplateEsignerListEntity docTemplateEsignerListEntity) {
        IPage<DocTemplateEsignerListEntity> list = this.docTemplateEsignerListService.findByPage(docTemplateEsignerListEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocTemplateEsignerListEntity docTemplateEsignerListEntity){
        this.docTemplateEsignerListService.insert(docTemplateEsignerListEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }

    @RequestMapping("/update")
    public R update(@RequestBody DocTemplateEsignerListEntity docTemplateEsignerListEntity){
        this.docTemplateEsignerListService.update(docTemplateEsignerListEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }

    @RequestMapping("/findById/{id}")
    public R findById(@PathVariable("id") String id){
        DocTemplateEsignerListEntity docTemplateEsignerListEntity = this.docTemplateEsignerListService.findById(id);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docTemplateEsignerListEntity);
    }

    @RequestMapping("/deleteById/{id}")
    public R deleteById(@PathVariable("id") String id){
        this.docTemplateEsignerListService.deleteById(id);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }
    /**
     * @Description: 催签
     * @Param: [bizNo]
     * @returns: cn.mftcc.common.R
     * <AUTHOR>
     * @Date  2022/1/19 14:57
     */
    @RequestMapping("/reNoticeSign/{bizNo}")
    public R reNoticeSign(@PathVariable("bizNo") String bizNo){
        this.docTemplateEsignerListService.reNoticeSign(bizNo);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }
    /**
     * @Description: 电签记录查询
     * @Param: [bizNo]
     * @returns: cn.mftcc.common.R
     * <AUTHOR>
     * @Date  2022/1/19 14:57
     */
    @RequestMapping("/querySsqEsignPage")
    public R querySsqEsignPage(@RequestBody DocTemplateEsignerListEntity docTemplateEsignerListEntity) {
        IPage<DocTemplateEsignerListEntity> list = this.docTemplateEsignerListService.querySsqEsignPage(docTemplateEsignerListEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    /**
     * @Description: 电签记录查询
     * @Param: [bizNo]
     * @returns: cn.mftcc.common.R
     * <AUTHOR>
     * @Date  2022/1/19 14:57
     */
    @RequestMapping("/queryCusSsqEsignPage")
    public R queryCusSsqEsignPage(@RequestBody DocTemplateEsignerListEntity docTemplateEsignerListEntity) {
        IPage<DocTemplateEsignerListEntity> list = this.docTemplateEsignerListService.queryCusSsqEsignPage(docTemplateEsignerListEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }
}