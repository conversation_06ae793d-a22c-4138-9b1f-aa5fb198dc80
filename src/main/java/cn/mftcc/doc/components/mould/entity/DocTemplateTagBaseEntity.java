/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import cn.mftcc.common.MftccEntity;

import java.io.Serializable;
import lombok.Data;

/**
 * 标签表
 * 
 * <AUTHOR>
 * @email 
 * @date 2021-03-27 10:39:22
 */
@Data
@TableName("doc_template_tag_base")
public class DocTemplateTagBaseEntity extends MftccEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 流水号
	 */
	@TableId(type = IdType.UUID)
	private String serialId;
	/**
	 * 标签key编号
	 */
	private String keyNo;
	/**
	 * 标签key名称
	 */
	private String tagKeyName;
	/**
	 * 标签取值来源类型0-来自数据库单个字段，1-计算字段，2-规范的表格类，3-不规范的表格类，9-其他
	 */
	private String dataSourceType;

	/**
	 * 展示样式 1-文字2-图片，3-表格，4-循环列表 5-循环的文字6-勾选项7-循环的不规则表格
	 */
	private String showStyle;
	/**
	 * 数据库名称
	 */
	private String databaseName;
	/**
	 * 取值数据库表，该标签直接取值的数据库表
	 */
	private String tableName;
	/**
	 * 标签sql字段标签取值来源类型=0-来自数据库字段，该字段存储的是直接取值的数据库表那个字段。
 标签取值来源类型=1-计算字段，该字段存储的是计算的方法名称，标签取值来源类型=2
	 */
	private String columnName;

	/**
	 * 超链路径
	 */
	private String  textLink;
	/**
	 * 标签计量单位元
	 */
	private String tagUnit;
	/**
	 * 查询条件
	 */
	private String queryCondition;
	/**
	 * 分组标识目前按照01-客户、02-合同、03-押品等分类
	 */
	private String groupFlag;
	/**
	 * 启用标识0-未启用，1-已启用
	 */
	private String useFlag;
	/**
	 * 格式化类型
	 */
	private String formatType;
	/**
	 * 标签说明
	 */
	private String remark;
	/**
	 * 字典项编号
	 */
	private String sysDictionryVal;
	/**
	 * 参数类型
	 */
	private String paramType;
	/**
	 * 表格类替换字段的key值
	 */
	private String poName;
	/**
	 * 表格类对象的表头
	 */
	private String titleName;
	/**
	 * 标签编号
	 */
	private String bookGroup;
	/**
	 * 顺序
	 */
	private String  tagSort;
	/**
	 * 是否使用表头配置定义表头0否1是
	 */
	private String tableHeaderFlag;
	/**
	 * 标签替换开始行号
	 */
	private String lineNum;
	/**
	 * 标签替换开始列号
	 */
	private String columnNum;
	/**
	 * 标签值替换类型1横向2纵向。暂时支持Excel纵向替换
	 */
	private String tagReplaceType;
	/**
	 * 格式化参数
	 */
	private String formatParam;

	/**
	 * 列表的循环方式 h-横向，v-纵向
	 */
	private String listLoopPartten;
	/**
	 * 标签分类1-模板书签；2-评级指标
	 */
	private String tagCategory;
}
