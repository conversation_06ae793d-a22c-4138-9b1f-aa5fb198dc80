/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;

import cn.mftcc.doc.components.mould.service.DocTemplateConfigService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
public class DocTemplateConfigController {

    @Autowired
    private DocTemplateConfigService docTemplateConfigService;

    /**
     * 获取模板配置Json
     * @param parmJson
     * {
     *     templateId:模板编号
     *     mode:  edit- 编辑  view-预览
     *     fileName: 如果是预览，预览文件的名称
     *     docShowName: 模板的展示名称
     *     docName: 模板文件的名称
     *     errorPage: 报错后展示的页面
     *     canWrite: 在编辑模式下是否 强制允许编辑 1-是 0-否 默认 0
     *     curOpNo: 当前登录人编号
     *     docCurStaus: 文档的当前状态  1-编辑状态  0-只读状态 （用于处理如果该文档是否正被其他人打开 ，如果是，强制改为只读）
     *
     * }
     * @param response
     * @param request
     * @param model
     * @return
     */
    @RequestMapping("/templateConfig")
    public String templateConfig(@RequestBody JSONObject parmJson, HttpServletResponse response, HttpServletRequest request, Model model){

        JSONObject configJson=  docTemplateConfigService.getMouldConfigInfo(parmJson);
        model.addAttribute("config",configJson.toJSONString());
        return "templateConfig";
    }
}
