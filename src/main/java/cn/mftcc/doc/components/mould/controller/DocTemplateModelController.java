/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.controller;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.doc.common.multids.MultiDataSourceBean;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.common.utils.OssConfig;
import cn.mftcc.doc.components.docmanage.web.utils.StringUtil;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagSetEntity;
import cn.mftcc.doc.components.mould.service.DocBizTemplateService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import cn.mftcc.common.R;
import cn.mftcc.common.constant.SysConstant;
import cn.mftcc.doc.components.mould.entity.DocTemplateModelEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateModelService;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * 模板表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-03-25 14:53:31
 */
@RestController
@RequestMapping("mould/docTemplateModel")

public class DocTemplateModelController {

    @Autowired
    private DocTemplateModelService docTemplateModelService;

    private static final String CONTENT_TYPE_DEF = "application/x-download";
    private static final String HEADER_VALUE_DEFAULT = "attchement;filename=";
    public static final String HEADER_NAME_DEFAULT = "Content-Disposition";
    private static final String CONTENT_TYPE_DEF1 = "application/octet-stream";
    private static final String CHARSET_NAME_DEF = "iso-8859-1";
    private static final String STR6 = "alert(\"not find the file\")";
    private static final String SCRIPT_HEAD = "<script>";
    private static final String SCRIPT_TAIL = "</script>";

    @Autowired
    private MultiDataSourceBean multiDataSourceBean;
    @Autowired
    private DocBizTemplateService docBizTemplateService;
    @Autowired
    private OssConfig ossConfig;

    @Value("${mftcc.template.mould-path:}")
    private String docMouldPath;

    @RequestMapping("/findByPage")
    public R findByPage(@RequestBody DocTemplateModelEntity docTemplateModelEntity) {

        Map<String,Map<String,String>> setting=multiDataSourceBean.getSetting();
        IPage<DocTemplateModelEntity> list = this.docTemplateModelService.findByPage(docTemplateModelEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }

    @RequestMapping("/findList")
    public R findList(@RequestBody DocTemplateModelEntity docTemplateModelEntity) {
        List<DocTemplateModelEntity> list = this.docTemplateModelService.findList(docTemplateModelEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("list", list);
    }
    @RequestMapping("/previewOneModelFile")
    public R previewOneModelFile(@RequestBody JSONObject docTemplateModelEntity) {
        JSONObject res = this.docTemplateModelService.previewOneModelFile(docTemplateModelEntity);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", res);
    }


    @RequestMapping("/upLoadModelFile")
    public R upLoadModelFile( @RequestParam("file") MultipartFile file )
            throws Exception {
        JSONObject gson = this.docTemplateModelService.upLoadModelFile(file);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS).put("data",gson);

    }

    @RequestMapping("/updateUpLoadModelFile")
    @ApiOperation(value="根据templateId 修改模板配置信息", notes="根据templateId 修改模板配置信息")
    @ApiImplicitParam(name="templateId",value="templateId",required=true,dataType="String")
    public R updateUpLoadModelFile(@RequestParam("file") MultipartFile file ,@RequestParam("templateId") String templateId )
            throws Exception {
        JSONObject gson = this.docTemplateModelService.updateUpLoadModelFile(file,templateId);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS).put("data",gson);

    }

    @RequestMapping("/downLoadTemplate")
    public void downLoadTemplate(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
        // 开始下载文件
        InputStream fis =null;
        try {
            // 1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            OutputStream toClient =null;
            // 2.设置文件头：最后一个参数是设置下载文件名
            String previewFileName=ajaxData.getString("fileName");//如果是预览，预览文件的名称
            String fileSuffix=new FileUtils().getFileSuffixName(previewFileName);//文件后缀名
            String modelName=ajaxData.getString("modelName");
            modelName=modelName+"."+fileSuffix;
            String fileUrlPath=docMouldPath+"saveMould"+File.separator+previewFileName;
            File file = null;
            String templateName = "";
            long len = 0L;
            //依据文件名称查询文件路径
            String btId = previewFileName.split("\\.")[0];
            MFLogger.info("合同模板-oss-btId："+btId);
            DocBizTemplateEntity docBizTemplateEntity = docBizTemplateService.findById(btId);
            if("1".equals(docBizTemplateEntity.getOssRemoveStatus())){
                //已迁移模板从oss下载
                String fileUrl = docBizTemplateEntity.getSavePath()+docBizTemplateEntity.getSaveFileName();
                MFLogger.info("合同模板-oss-fileUrl："+fileUrl);
                if(StringUtils.isEmpty(fileUrl)){
                    response.sendError(0,"文件路径不存在！");
                    return;
                }
                fis = ossConfig.readOss(fileUrl);
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                response.addHeader("file-name*", URLEncoder.encode(docBizTemplateEntity.getTemplateName()+".pdf", "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();
            }else{
                file=new File(fileUrlPath);
                if (file.exists()) {
                    len = file.length();
//                    String dfileName = file.getName();
                    //下载文件名称
                    templateName = ajaxData.getString("templateName");
                    if (StringUtils.isEmpty(templateName)){
                        templateName = modelName;
                    }else{
                        templateName = templateName+"."+fileSuffix;
                    }
                    fis = new BufferedInputStream(new FileInputStream( file));
                    response.reset();
                    response.setContentType(CONTENT_TYPE_DEF);
                    response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                    response.addHeader("Content-Length", "" + len);
                    response.addHeader("file-name*", URLEncoder.encode(templateName, "UTF-8"));
                    toClient = new BufferedOutputStream(response.getOutputStream());
                    response.setContentType(CONTENT_TYPE_DEF1);
                    byte[] buffer = new byte[1024 * 1024 * 4];
                    int i = -1;
                    while ((i = fis.read(buffer)) != -1) {
                        toClient.write(buffer, 0, i);
                    }
                    //fis.close();
                    toClient.flush();
                    toClient.close();
                } else {
                    PrintWriter out = response.getWriter();
                    out.print(SCRIPT_HEAD);
                    out.print(STR6);
                    out.print(SCRIPT_TAIL);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (null !=fis){
                try {
                    fis.close();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
    }

    @RequestMapping("/insert")
    public R insert(@RequestBody DocTemplateModelEntity docTemplateModelEntity){
        this.docTemplateModelService.insert(docTemplateModelEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS);
    }
    @RequestMapping("/clone")
    public R clone(@RequestBody DocTemplateModelEntity docTemplateModelEntity){
        JSONObject result=this.docTemplateModelService.clone(docTemplateModelEntity);
        return R.ok(SysConstant.MSG_CONFIG_SAVE_SUCCESS).put("data",result);
    }

    /**
     * 获取该模板的配置信息
     * @param parmJson
     * @return

     */
    @RequestMapping(value = {"/getMouldConfigInfo"})
    public R getMouldConfigInfo(@RequestBody JSONObject parmJson){
        JSONObject configInfo = this.docTemplateModelService.getMouldConfigInfo(parmJson);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", configInfo);
    }

    /**
     * 获取该模板的配置信息
     * @param btId
     * @param response
     * @return

     */
    @RequestMapping(value = {"/getEleSignedMould/{btId}"})
    public void getEleSignedMould(@PathVariable("btId")String btId,HttpServletResponse response){


        try {

            DocBizTemplateEntity docBizTemplateEntity=docBizTemplateService.findById(btId);

            String  tmpplatePath=docBizTemplateEntity.getSavePath()+docBizTemplateEntity.getSaveFileName();
            File downLoadFile=new File(tmpplatePath);

            if(downLoadFile.exists()){

                InputStream instream=new FileInputStream(downLoadFile);


                String tmpAry[]=docBizTemplateEntity.getSaveFileName().split("\\.");

                String  suffix=tmpAry[tmpAry.length-1];//后缀名
                response.reset();
                response.setContentType(CONTENT_TYPE_DEF);
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
//            response.addHeader("Content-Length", "" + file.length());
                response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(docBizTemplateEntity.getTemplateName()+"."+suffix, "UTF-8"));
//            response.addHeader("file-name*", URLEncoder.encode(tmpObj.getFileName()+"."+suffix, "UTF-8"));
                byte[] b=new byte[1024];
                BufferedInputStream buf=new BufferedInputStream(instream);
                ServletOutputStream out=response.getOutputStream();
                BufferedOutputStream bot=new BufferedOutputStream(out);

                int readLength;
                while ((readLength = buf.read(b)) != -1) {
                    bot.write(b, 0, readLength);
                }
                bot.flush();
            }else{
                ServletOutputStream out=response.getOutputStream();
                BufferedOutputStream bot=new BufferedOutputStream(out);
                bot.write("没有对应的文档".getBytes());

                bot.flush();
            }
        } catch (Exception  e) {
            // TODO Auto-generated catch block
            MFLogger.error("",e);

        }

    }





    @RequestMapping("/update")
    public R update(@RequestBody DocTemplateModelEntity docTemplateModelEntity){
        this.docTemplateModelService.update(docTemplateModelEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }
    @RequestMapping(value = {"/templatetags/config.json"})
    public JSONObject getPlugsData( HttpServletRequest request) {

//        String templateId=request.getParameter("templateId");
        String templateId="28748be89ea5a6156a5257dab7fa48de";
        return this.docTemplateModelService.getPluginsData(templateId);
    }

    @RequestMapping(value = {"/fileFormatConvert"})
    @ResponseBody
    public cn.mftcc.doc.common.utils.R fileFormatConvert(@RequestBody Map<String,String> parmMap) {
        /**
         * {templateId,versionNo,wid ,parmList}
         */
        JSONObject previewObj=this.docTemplateModelService.fileFormatConvertExternal(parmMap);
        return cn.mftcc.doc.common.utils.R.ok(previewObj);
    }

    @RequestMapping(value = {"/previewMould"})
    public cn.mftcc.doc.common.utils.R previewMould(@RequestBody JSONObject jsonObject) {
        /**
         * {templateId,versionNo,wid ,parmList}
         */
        JSONObject previewObj=this.docTemplateModelService.previewMould(jsonObject);
        return cn.mftcc.doc.common.utils.R.ok(previewObj);
    }
    @RequestMapping(value = {"/archivePreviewMould"})
    public cn.mftcc.doc.common.utils.R archivePreviewMould(@RequestBody JSONObject jsonObject) {
        /**
         * {templateId,versionNo,wid ,parmList}
         */
        JSONObject previewObj=this.docTemplateModelService.archivePreviewMould(jsonObject);
        return cn.mftcc.doc.common.utils.R.ok(previewObj);
    }
    @RequestMapping(value = {"/onlyPreviewMould"})
    public cn.mftcc.doc.common.utils.R onlyPreviewMould(@RequestBody JSONObject jsonObject) {
        /**
         * {templateId,versionNo,wid ,parmList}
         */
        JSONObject previewObj=this.docTemplateModelService.onlyPreviewMould(jsonObject);
        return cn.mftcc.doc.common.utils.R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data",previewObj);
    }
    @RequestMapping(value = {"/getTemplateTagConfigJson/{templateId}"})
    public  cn.mftcc.doc.common.utils.R getTemplateTagConfigJson(@PathVariable("templateId")String templateId){
        JSONObject configTags=this.docTemplateModelService.getTemplateTagConfigJson(templateId);
        return cn.mftcc.doc.common.utils.R.ok(configTags);
    }


    @RequestMapping("/setTemplateTags")
    public R setTemplateTags(@RequestBody DocTemplateTagSetEntity  docTemplateTagSetEntity){
        this.docTemplateModelService.setTemplateTags(docTemplateTagSetEntity);
        return R.ok(SysConstant.MSG_CONFIG_UPDATE_SUCCESS);
    }




    @RequestMapping(value = {"/getTagsConfig/{templateId}"})
    public R getTagsConfig(@PathVariable("templateId") String templateId){
        JSONObject configInfo = this.docTemplateModelService.getTagsConfig(templateId);


        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", configInfo);
    }


    @RequestMapping("/findById/{templateNo}")
    public R findById(@PathVariable("templateNo") String templateNo){
        DocTemplateModelEntity docTemplateModelEntity = this.docTemplateModelService.findById(templateNo);
        return R.ok(SysConstant.MSG_CONFIG_SELECT_SUCCESS).put("data", docTemplateModelEntity);
    }

    @RequestMapping("/deleteById/{templateNo}")
    public R deleteById(@PathVariable("templateNo") String templateNo){
        this.docTemplateModelService.deleteById(templateNo);
        return R.ok(SysConstant.MSG_CONFIG_DELETE_SUCCESS);
    }


}