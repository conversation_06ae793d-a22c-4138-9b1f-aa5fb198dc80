/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocBizEleSignConfigEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 电子签章配置业务关联表
 *
 * <AUTHOR>
 * @email 
 * @date 2021-08-28 17:36:42
 */
public interface DocBizEleSignConfigService {

    IPage<DocBizEleSignConfigEntity> findByPage(DocBizEleSignConfigEntity docBizEleSignConfigEntity) throws ServiceException;

    List<DocBizEleSignConfigEntity> findListByObj(DocBizEleSignConfigEntity docEleSignConfigEntity) throws ServiceException;

    void insert(DocBizEleSignConfigEntity docBizEleSignConfigEntity) throws ServiceException;

    void update(DocBizEleSignConfigEntity docBizEleSignConfigEntity) throws ServiceException;

    DocBizEleSignConfigEntity findById(String id) throws ServiceException;

    void deleteById(String id) throws ServiceException;

    JSONObject findBySignOrderId(String signOrderId) throws ServiceException;

    JSONObject updateBizSignInfo(JSONObject signInf)throws ServiceException;
    String  lastStepFlag(String templateId,String bizNo,String subBizNo,String curSignId)throws ServiceException;
    /**
     * 删除模板
     * @param object
     *  {
     *      * 	"prdUniqueVal":"prdId",
     *      * 	"flowNo":"flowNo",
     *      * 	"nodeNo":"nodeNo",
     *      * 	"bizNo":"pact_00001"
     *          "subBizNo" :""
     *      * }
     * @return
     * @throws ServiceException
     */

  void  deleteByBean(JSONObject object)throws  ServiceException;
    /**
     * 获取需要电签的模板
     * @param object{
     *      "prdUniqueVal":"prdId",
     *      "flowNo":"flowNo",
     *      "nodeNo":"nodeNo",
     *      "bizNo":"pact_00001"
     *      "subBizNo" :""
     * }
     * @return
     * @throws ServiceException
     */
     JSONArray getNeedEsignTemplate(JSONObject object) throws ServiceException;

    JSONObject getSignSortId(JSONObject paramJson)throws ServiceException;

}

