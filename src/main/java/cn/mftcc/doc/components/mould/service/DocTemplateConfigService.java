/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import cn.mftcc.common.exception.ServiceException;
import com.alibaba.fastjson.JSONObject;

public interface DocTemplateConfigService {

    /**
     * 获取模板配置Json
     * @param parmJson
     * {
     *     templateId:模板编号
     *     mode:  edit- 编辑  view-预览
     *     fileName: 如果是预览，预览文件的名称
     *     docShowName: 模板的展示名称
     *     docName: 模板文件的名称
     *     errorPage: 报错后展示的页面
     *     canWrite: 在编辑模式下是否 强制允许编辑 1-是 0-否 默认 0
     *     curOpNo: 当前登录人编号
     *     docCurStaus: 文档的当前状态  1-编辑状态  0-只读状态 （用于处理如果该文档是否正被其他人打开 ，如果是，强制改为只读）
     *
     * }
     * @return
     * @throws ServiceException
     */
     JSONObject getMouldConfigInfo(JSONObject parmJson) throws ServiceException;

}
