/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service.impl;

import cn.mftcc.bizcommon.constant.CommonConstant;
import cn.mftcc.bizcommon.utils.BeanCopyUtil;
import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.DateUtil;
import cn.mftcc.common.utils.RequestUtil;
import cn.mftcc.common.utils.UUIDUtil;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagBaseEntity;
import cn.mftcc.doc.components.mould.service.DocTemplateTagBaseService;
import cn.mftcc.doc.feign.dto.DocTemplateCheckDto;
import cn.mftcc.doc.feign.dto.DocTemplateTagDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import cn.mftcc.common.utils.MapperUtil;
import org.springframework.transaction.annotation.Transactional;

import cn.mftcc.doc.components.mould.entity.DocTemplateTagCheckEntity;
import cn.mftcc.doc.components.mould.mapper.DocTemplateTagCheckMapper;
import cn.mftcc.doc.components.mould.service.DocTemplateTagCheckService;

import cn.mftcc.common.exception.ServiceException;
import cn.mftcc.common.constant.SysConstant;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 模板标签校验信息表
 *
 * <AUTHOR>
 * @email 
 * @date 2022-10-22 11:13:58
 */
@Service("docTemplateTagCheckService")
@Transactional(rollbackFor = Exception.class)
public class DocTemplateTagCheckServiceImpl implements DocTemplateTagCheckService {

    @Autowired
    private DocTemplateTagCheckMapper docTemplateTagCheckMapper;
    @Autowired
    private MapperUtil mapperUtil;

    @Autowired
    private RequestUtil requestUtil;

    @Autowired
    private DocTemplateTagBaseService docTemplateTagBaseService;

    @Override
    public IPage<DocTemplateTagCheckEntity> findByPage(DocTemplateTagCheckEntity docTemplateTagCheckEntity) throws ServiceException {
        try{
            //翻页
            IPage<DocTemplateTagCheckEntity> page = new Page<>();
            page.setCurrent(docTemplateTagCheckEntity.getPageNo());
            page.setSize(docTemplateTagCheckEntity.getPageSize());
            QueryWrapper<DocTemplateTagCheckEntity> queryWrapper = new QueryWrapper<>();
            mapperUtil.tableQuery(queryWrapper,docTemplateTagCheckEntity);
            return docTemplateTagCheckMapper.selectPage(page,queryWrapper);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,docTemplateTagCheckEntity.getTemplateCheckNo(),e);
        }
    }

    @Override
    public void insert(DocTemplateTagCheckEntity docTemplateTagCheckEntity) throws ServiceException {
        try{
            docTemplateTagCheckMapper.insert(docTemplateTagCheckEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTemplateTagCheckEntity.getTemplateCheckNo(),e);
        }
    }

    @Override
    public void update(DocTemplateTagCheckEntity docTemplateTagCheckEntity) throws ServiceException {
        try{
            docTemplateTagCheckEntity.setUpdateOpNo((String) requestUtil.getUserInfo("opNo"));
            docTemplateTagCheckEntity.setUpdateOpName((String) requestUtil.getUserInfo("opName"));
            docTemplateTagCheckEntity.setUpdateTime(DateUtil.getCurrDate());
            docTemplateTagCheckMapper.updateById(docTemplateTagCheckEntity);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTemplateTagCheckEntity.getTemplateCheckNo(),e);
        }
    }

    @Override
    public void updateTagCheckInfos(List<DocTemplateTagCheckEntity> docTemplateTagCheckEntityList)  throws ServiceException {
        try{
            for (DocTemplateTagCheckEntity docTemplateTagCheckEntity : docTemplateTagCheckEntityList) {
                DocTemplateTagCheckEntity updateEntity=new DocTemplateTagCheckEntity();
                updateEntity.setTemplateCheckNo(docTemplateTagCheckEntity.getTemplateCheckNo());
                updateEntity.setUseFlag(docTemplateTagCheckEntity.getUseFlag());
                updateEntity.setCheckNullFlag(docTemplateTagCheckEntity.getCheckNullFlag());
                updateEntity.setCheckRule(docTemplateTagCheckEntity.getCheckRule());
                updateEntity.setPrompts(docTemplateTagCheckEntity.getPrompts());
                updateEntity.setUpdateOpNo((String) requestUtil.getUserInfo("opNo"));
                updateEntity.setUpdateOpName((String) requestUtil.getUserInfo("opName"));
                updateEntity.setUpdateTime(Calendar.getInstance().getTime());
                docTemplateTagCheckMapper.updateById(updateEntity);
            }

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_UPDATE_ERROR,docTemplateTagCheckEntityList,e);
        }
    }

    @Override
    public DocTemplateTagCheckEntity findById(String templateCheckNo) throws ServiceException {
        try{
            return docTemplateTagCheckMapper.selectById(templateCheckNo);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SELECT_ERROR,templateCheckNo,e);
        }
    }

    @Override
    public void deleteById(String templateCheckNo) throws ServiceException {
        try{
            docTemplateTagCheckMapper.deleteById(templateCheckNo);
        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_DELETE_ERROR,templateCheckNo,e);
        }
    }

    /**
     * 同步更新标签规则信息信息
     * @param docTemplateTagCheckEntity
     * @throws ServiceException
     */
    @Override
    public void syncTemplateTagCheck(DocTemplateTagDto docTemplateTagCheckEntity) throws ServiceException {
        try{
            if(StringUtils.isBlank(docTemplateTagCheckEntity.getTemplateId())){
                throw new Exception("模版ID为空");
            }
            QueryWrapper<DocTemplateTagCheckEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("template_id",docTemplateTagCheckEntity.getTemplateId());
            List<DocTemplateTagCheckEntity> list=docTemplateTagCheckMapper.selectList(queryWrapper);
            List<DocTemplateTagBaseEntity> templateTagBases =docTemplateTagBaseService.findListByTagkeynos(docTemplateTagCheckEntity.getTagKeyNos());
            //删除标签
            List<String> delList =new ArrayList<>();
            //新增标签
            List<DocTemplateTagCheckEntity> addList = new ArrayList<>();
            //更新标签
            List<DocTemplateTagCheckEntity> updateList = new ArrayList<>();

            if(!list.isEmpty()) {
                Map<String, DocTemplateTagCheckEntity> oldMap=list.stream().collect(Collectors.toMap(DocTemplateTagCheckEntity::getKeyNo, Function.identity()));
                Map<String, DocTemplateTagBaseEntity> newMap=templateTagBases.stream().collect(Collectors.toMap(DocTemplateTagBaseEntity::getKeyNo, Function.identity()));
                for (DocTemplateTagCheckEntity templateTagCheckEntity : list) {
                    if(!newMap.containsKey(templateTagCheckEntity.getKeyNo())){
                        delList.add(templateTagCheckEntity.getKeyNo());
                    }
                }
                for (DocTemplateTagBaseEntity templateTagBase : templateTagBases) {
                    if(oldMap.containsKey(templateTagBase.getKeyNo())){
                        DocTemplateTagCheckEntity oldEntity=oldMap.get(templateTagBase.getKeyNo());
                        if(!oldEntity.getTagKeyName().equals(templateTagBase.getTagKeyName())
                                ||!oldEntity.getShowStyle().equals(templateTagBase.getShowStyle())
                                ||!oldEntity.getGroupFlag().equals(templateTagBase.getGroupFlag())){
                            DocTemplateTagCheckEntity updateEntity=new DocTemplateTagCheckEntity();
                            updateEntity.setTemplateCheckNo(oldEntity.getTemplateCheckNo());
                            updateEntity.setTagKeyName(templateTagBase.getTagKeyName());
                            updateEntity.setShowStyle(templateTagBase.getShowStyle());
                            updateEntity.setGroupFlag(templateTagBase.getGroupFlag());
                            updateEntity.setUpdateTime(Calendar.getInstance().getTime());
                            updateList.add(updateEntity);
                        }
                    }else{
                        DocTemplateTagCheckEntity newEntity= new DocTemplateTagCheckEntity();
                        BeanCopyUtil.copyProperties(docTemplateTagCheckEntity,newEntity);
                        BeanCopyUtil.copyProperties(templateTagBase,newEntity);
                        newEntity.setTemplateCheckNo(UUIDUtil.getUUID());
                        newEntity.setUseFlag(CommonConstant.YES_NO_N);
                        newEntity.setCreateTime(Calendar.getInstance().getTime());
                        addList.add(newEntity);
                    }
                }
            }else{
                for (DocTemplateTagBaseEntity templateTagDto : templateTagBases) {
                    DocTemplateTagCheckEntity newEntity= new DocTemplateTagCheckEntity();
                    BeanCopyUtil.copyProperties(docTemplateTagCheckEntity,newEntity);
                    BeanCopyUtil.copyProperties(templateTagDto,newEntity);
                    newEntity.setTemplateCheckNo(UUIDUtil.getUUID());
                    newEntity.setUseFlag(CommonConstant.YES_NO_N);
                    newEntity.setCreateTime(Calendar.getInstance().getTime());
                    addList.add(newEntity);
                }
            }

            if(!addList.isEmpty()){
                docTemplateTagCheckMapper.insertBatch(addList);
            }
            if(!delList.isEmpty()){
                QueryWrapper<DocTemplateTagCheckEntity> delQuery = new QueryWrapper<>();
                delQuery.eq("template_id",docTemplateTagCheckEntity.getTemplateId());
                delQuery.in("key_no",delList);
                docTemplateTagCheckMapper.delete(delQuery);
            }
            if(!updateList.isEmpty()){
                docTemplateTagCheckMapper.updateBatchById(updateList);
            }

        }catch (Exception e){
            throw new ServiceException(SysConstant.MSG_CONFIG_SAVE_ERROR,docTemplateTagCheckEntity,e);
        }

    }

    @Override
    public JSONObject checkTemplateTagValue(DocTemplateCheckDto docTemplateTagCheckEntity) throws ServiceException {

        JSONObject resultObject= new JSONObject();
        StringBuffer msg = new StringBuffer();
        try {
            if (StringUtils.isBlank(docTemplateTagCheckEntity.getTemplateId()) || docTemplateTagCheckEntity.getTagKeyInfo().isEmpty()) {
                resultObject.put("code", "1111");
                resultObject.put("msg", "模板检验信息未传值");
                return resultObject;
            }
            QueryWrapper<DocTemplateTagCheckEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("template_id", docTemplateTagCheckEntity.getTemplateId());
            queryWrapper.eq("use_flag", "1");
            List<DocTemplateTagCheckEntity> list = docTemplateTagCheckMapper.selectList(queryWrapper);
            JSONObject object = docTemplateTagCheckEntity.getTagKeyInfo();
            for (DocTemplateTagCheckEntity templateTagCheckEntity : list) {
                if (CommonConstant.YES_NO_Y.equals(templateTagCheckEntity.getCheckNullFlag())) {
                    if (object.get(templateTagCheckEntity.getKeyNo())!=null) {
                        if("1".equals(templateTagCheckEntity.getShowStyle())){
                            //文本类
                            if (StringUtils.isNotBlank(object.getString(templateTagCheckEntity.getKeyNo()))) {
                                continue;
                            }
                        }else if("4".equals(templateTagCheckEntity.getShowStyle())){
                            try{

                                JSONArray jsonArray=JSONArray.parseArray(JSON.toJSONString(object.get(templateTagCheckEntity.getKeyNo())));
                                if (jsonArray!=null &&jsonArray.size()>0) {
                                    continue;
                                }
                            }catch (Exception e){
                                MFLogger.error("模板校验异常:{}",templateTagCheckEntity.toString()+object.get(templateTagCheckEntity.getKeyNo()).toString(),e);
                            }

                        }else{
                            continue;
                        }

                    }
                    msg.append(":").append(templateTagCheckEntity.getTagKeyName()).append(templateTagCheckEntity.getPrompts());
                }
            }
            if (StringUtils.isNotBlank(msg)) {
                resultObject.put("code", "1111");
                resultObject.put("msg", "错误信息"+msg.toString());
                return resultObject;
            }
        }catch (Exception e){
            MFLogger.error("模板校验异常{}",docTemplateTagCheckEntity.toString(),e);
            resultObject.put("code", "1111");
            resultObject.put("msg", "模板值校验异常");
            return resultObject;
        }
        resultObject.put("code","0000");
        resultObject.put("msg","模板校验成功");
        return resultObject;
    }

}