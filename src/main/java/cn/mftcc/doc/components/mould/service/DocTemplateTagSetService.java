/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.mould.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.mftcc.doc.components.mould.entity.DocTemplateTagSetEntity;

import cn.mftcc.common.exception.ServiceException;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @email 
 * @date 2021-04-15 19:02:36
 */
public interface DocTemplateTagSetService {

    IPage<DocTemplateTagSetEntity> findByPage(DocTemplateTagSetEntity docTemplateTagSetEntity) throws ServiceException;

    public DocTemplateTagSetEntity getDocTemplateTagSetEntity(String  templateId,String templateVersionNo)throws  ServiceException;

    DocTemplateTagSetEntity findOneObject(String templateId) throws ServiceException;

    public List<DocTemplateTagSetEntity> findByList(DocTemplateTagSetEntity docTemplateTagSetEntity) throws ServiceException;

    void insert(DocTemplateTagSetEntity docTemplateTagSetEntity) throws ServiceException;

    void update(DocTemplateTagSetEntity docTemplateTagSetEntity) throws ServiceException;

    DocTemplateTagSetEntity findById(String serialNo) throws ServiceException;

    void deleteById(String serialNo) throws ServiceException;
}

