package cn.mftcc.doc.components.rbmq.listener;
import cn.mftcc.common.rb.BaseMap;
import cn.mftcc.common.rb.RabbitComponent;
import cn.mftcc.common.utils.DateUtil;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.file.service.DocFileInfService;
import cn.mftcc.doc.components.rbmq.constant.CloudConstant;
import cn.mftcc.doc.components.rbmq.service.MsgInvoiceService;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;
import cn.mftcc.lease.feign.dto.*;
import cn.mftcc.rabbitmq.core.BaseRabbiMqHandler;
import cn.mftcc.rabbitmq.listenter.MqListener;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.web.client.RestTemplate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 定义接收者（可以定义N个接受者，消息会均匀的发送到N个接收者中）
 * <p>
 * RabbitMq接受者1
 * （@RabbitListener声明类上，一个类只能监听一个队列）
 *
 * @author: hh
 * @date: 2022/04/21
 */
@Slf4j
@RabbitListener(queues = CloudConstant.MQ_LEASE_VOUCHER_FILE_DOWN)
@RabbitComponent(value = "leaseBatchDownVoucherFile")
public class LeaseBatchDownVoucherFile extends BaseRabbiMqHandler<BaseMap> {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private LeaseFeiginClient leaseFeiginClient;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private MsgInvoiceService msgInvoiceService;
    @Autowired
    private DocFileInfService docFileInfService;


    @RabbitHandler
    public void onMessage(BaseMap baseMap, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.onMessage(baseMap, deliveryTag, channel, new MqListener<BaseMap>() {
            @Override
            public void handler(BaseMap map, Channel channel) {
                log.info("批量下载消费开始invoiceDownload map:{}",map);

                String busIds = map.get("busIds") ;
                String busType = map.get("busType") ;

                //付款投放
                if(CloudConstant.VOUCHER_FILE_01.equals(busType)){
                   voucherFileDown01(busIds,baseMap) ;
                }

                //正常结清
                if(CloudConstant.VOUCHER_FILE_02.equals(busType)){
                    voucherFileDown02(busIds,baseMap) ;
                }
                //提前结清
                if(CloudConstant.VOUCHER_FILE_03.equals(busType)){
                    voucherFileDown03(busIds,baseMap) ;
                }

                //费用抵扣
                if(CloudConstant.VOUCHER_FILE_05.equals(busType)){
                    voucherFileDown05(busIds,baseMap) ;
                }

                //付分润，sp费用
                if(CloudConstant.VOUCHER_FILE_06.equals(busType)){
                    voucherFileDown06(busIds,baseMap) ;
                }
                //退款
                if(CloudConstant.VOUCHER_FILE_08.equals(busType)){
                    voucherFileDown08(busIds,baseMap) ;
                }

                //合同变更（承租人变更）
                if(CloudConstant.VOUCHER_FILE_10.equals(busType)){
                    voucherFileDown10(busIds,baseMap) ;
                }

                //合同变更（还款计划）
                if(CloudConstant.VOUCHER_FILE_11.equals(busType)){
                    voucherFileDown11(busIds,baseMap) ;
                }

            }
        });
    }

    /**
     * 付款投放凭证附件下载
     * @param busIds
     * @param map
     */
    public void voucherFileDown01(String busIds,BaseMap map){
        log.info("付款投放下载消费开始voucherFileDownload map:{}",busIds);
        JSONObject sendObject = new JSONObject();
        String opNo = map.get("opNo") ;
        String opName = map.get("opName") ;
        String batchNo = map.get("batchNo") ;

        try {
            sendObject.put("opNo", opNo);
            sendObject.put("opName", opName);
            sendObject.put("batchNo", batchNo);
            sendObject.put("tmplType", "lease_voucher_down");
            String fileName=opNo.concat("_").concat(batchNo).concat(".zip");
            sendObject.put("busKeyId", fileName);

            List<String> pactNos = Arrays.stream(busIds.split(",")).collect(Collectors.toList()) ;
            List<String> fileNames = new ArrayList<>() ;
            if(CollectionUtils.isNotEmpty(pactNos)){
                for (String pactNo:pactNos){
                    fileNames.add("付款审批单("+pactNo+")") ;
                }
            }

            log.info("付款投放下载消费voucherFileDownload fileNames:{}",JSONObject.toJSONString(fileNames));
            List<String> tempLateFileNames = new ArrayList<>() ;
            List<DocFileInfEntity> fileInfList = docFileInfService.findFileInfListByFileName(fileNames);
            log.info("付款投放下载消费voucherFileDownload fileInfList:{}",JSONObject.toJSONString(fileInfList));
            List<String> fileIds = new ArrayList();
            if(CollectionUtils.isNotEmpty(fileInfList)){
                for (DocFileInfEntity entity:fileInfList){
                    if(!fileNames.contains(entity.getFileName())){
                        tempLateFileNames.add(entity.getFileName());
                    }else{
                        fileIds.add(entity.getFileId()) ;
                    }
                }
            }
//            String resultFileName=fileName.replace(".zip",".txt");
            JSONObject ajaxData = new JSONObject();
            ajaxData.put("fileIds", fileIds);
            ajaxData.put("fileName",fileName);
            ajaxData.put("tempLateFileNames",tempLateFileNames);
//            ajaxData.put("resultFileName",resultFileName);

            long startTime = System.currentTimeMillis();
            log.info("付款投放凭证附件下载-批号:"+batchNo+"，打包开始时间:" + startTime);
            Map<String, Object> map1 = docFileBizFiletypeConfigService.downloadFilesZipV1(ajaxData);

            long endTime = System.currentTimeMillis();
            log.info("付款投放凭证附件下载-批号:"+batchNo+"，打包结束时间:" + endTime);
            long duration = calcTime(startTime,endTime) ;
            log.info("付款投放凭证附件下载-批号:"+batchNo+"，总耗时:" + duration +"秒");

            if (map1 != null && "success".equals(map1.get("msg"))) {
                sendObject.put("statusInfo", "成功。文件名为" + ajaxData.getString("fileName"));
            } else {
                sendObject.put("statusInfo", "失败，请重新操作");
                log.error("MQCreatVoucherFile-凭证附件批量下载失败,批号："+batchNo+"，参数信息：{}，异常信息："+new Exception("文件未生成"), sendObject);
            }
        } catch (Exception e) {
            log.error("MQCreatVoucherFile-凭证附件批量下载失败,批号："+batchNo+"，参数信息：{}，异常信息：{}", sendObject, e.getMessage());
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }


    /**
     * 提前结清凭证附件下载
     * @param busIds
     * @param map
     */
    public void voucherFileDown03(String busIds,BaseMap map){
        log.info("提前结清凭证下载消费开始voucherFileDownload map:{}",busIds);
        String opNo = map.get("opNo") ;
        String batchNo = map.get("batchNo") ;
        JSONObject sendObject = createSendMsg(map);
        try {
            //流程中的附件
            List<String> prepayIds = Arrays.stream(busIds.split(",")).collect(Collectors.toList()) ;
            JSONObject parmJson = new JSONObject() ;
            parmJson.put("prepayIds",prepayIds) ;
            //获取提前结清信息
            List<LeasePrepRepayApplyDTO> leasePrepRepayApplyDTOS = leaseFeiginClient.leasePrepRepayApplyByPrepayIds(parmJson);
            Map<String,LeasePrepRepayApplyDTO> dtoMap = new HashMap<>() ;

            if(CollectionUtils.isNotEmpty(leasePrepRepayApplyDTOS)){
                for(LeasePrepRepayApplyDTO dto: leasePrepRepayApplyDTOS){
                    if(dtoMap.get(dto.getPrepayId())==null ){
                        dtoMap.put(dto.getPrepayId(),dto) ;
                    }
                }
            }

            List<DocFileInfEntity> fileList = null ;
            //合同模板渲染，上传附件下载
            LeasePrepRepayApplyDTO dto = null ;
            String pactNo = "" ;
            JSONObject parmList = null ;
            //没有上传文件的合同号
            List<String> pactNosNoFiles =  new ArrayList<>() ;
            pactNosNoFiles.add("以下合同流程中未上传附件") ;

            long startTime = System.currentTimeMillis();
            log.info("提前结清凭证附件下载-批号:"+batchNo+"，文件生成开始时间:" + startTime);

            if(null != dtoMap && !dtoMap.isEmpty()){
                JSONObject  parmTempJson = new JSONObject();
                parmTempJson.put("batchNo",batchNo) ;
                parmTempJson.put("opNo",opNo) ;
                DocFileInfEntity docFileInf = new DocFileInfEntity() ;

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key) ;
                    pactNo = dto.getPactNo()  ;
                    parmTempJson.put("pactNo",pactNo) ;
                    //如果是部分提前结清
                    if("2".equals(dto.getPrepayType())){
                        parmTempJson.put("templateId","31c334daccf443eba7832f12f2ca483b") ;
                    }else {
                        parmTempJson.put("templateId","71cb5892bf944732b0dabb17a1c9b7ae") ;
                    }


                    //生成合同套打参数
                    parmList = getLeasePrepRepayParmList(dto, key);
                    parmTempJson.put("parmList",parmList);
                    //生成合同模板
                    docFileBizFiletypeConfigService.renderTemplate(parmTempJson);
                    docFileInf.setBizNo(key);
                    fileList = docFileInfService.findFileInfList(docFileInf);
                    //如果没有附件，则给出提示
                    if(CollectionUtils.isEmpty(fileList)){
                        pactNosNoFiles.add(pactNo) ;
                        log.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"流程中没有上传附件");
                        continue;
                    }
                    //生成文件到指定位置
                    docFileBizFiletypeConfigService.generateFile(opNo,batchNo, pactNo,fileList) ;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("提前结清凭证附件下载-批号:"+batchNo+"，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime,endTime) ;
            log.info("提前结清凭证附件下载-批号:"+batchNo+"，总耗时:" + duration +"秒");

            //没有上传文件的合同记录到文件
            docFileBizFiletypeConfigService.writInfosToFile(opNo,batchNo,pactNosNoFiles) ;

            long startTime1 = System.currentTimeMillis();
            log.info("提前结清凭证附件下载-批号:"+batchNo+"，打包开始时间:" + startTime1);
            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo,batchNo) ;

            long endTime1 = System.currentTimeMillis();
            log.info("提前结清凭证附件下载-批号:"+batchNo+"，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1,endTime1) ;
            log.info("提前结清凭证附件下载-批号:"+batchNo+"，打包总耗时:" + duration1 +"秒");

            sendObject.put("statusInfo", "成功。文件名为" + sendObject.getString("busKeyId"));
        } catch (Exception e) {
            log.error("MQCreatVoucherFile--Name(提前结清凭证附件下载)-批号：("+batchNo+")-生成附件失败参数：{}-异常信息：{}", sendObject, e);
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }

    /**
     *  提前结清合同套打参数
     * @param dto
     * @param key
     * @return
     */
    private  JSONObject getLeasePrepRepayParmList(LeasePrepRepayApplyDTO dto, String key) {
        JSONObject parmList = new JSONObject();
        parmList.put("prepayId", key);
        parmList.put("dueId", dto.getDueId());
        parmList.put("pactId", dto.getPactId());
        parmList.put("busId", key);
        parmList.put("applyId", dto.getApplyId());
        JSONObject leaseDue = leaseFeiginClient.getLeaseDueId(dto.getDueId());

        if(null != leaseDue
                && null != leaseDue.getDate("dueBeginDate")
                && null != leaseDue.getDate("dueEndDate")){
            parmList.put("dueBeginDate",DateUtil.DateToStr(leaseDue.getDate("dueBeginDate"),"yyyy-MM-dd"));
            parmList.put("dueEndDate", DateUtil.DateToStr(leaseDue.getDate("dueEndDate"),"yyyy-MM-dd"));
        }else{
            parmList.put("dueBeginDate", DateUtil.DateToStr(leaseDue.getDate("dueBeginDate"),"yyyy-MM-dd"));
            parmList.put("dueEndDate", DateUtil.DateToStr(leaseDue.getDate("dueEndDate"),"yyyy-MM-dd"));
        }

        return parmList;
    }

    private JSONObject  createSendMsg(BaseMap map) {
        String opNo = map.get("opNo") ;
        String opName = map.get("opName") ;
        String batchNo = map.get("batchNo") ;

        JSONObject sendObject = new JSONObject();
        sendObject.put("opNo", opNo);
        sendObject.put("opName", opName);
        sendObject.put("batchNo", batchNo);
        sendObject.put("tmplType", "lease_voucher_down");
        String fileName= opNo.concat("_").concat(batchNo).concat(".zip");
        sendObject.put("busKeyId", fileName);
        return sendObject;
    }


    /**
     * 合同变更凭证附件下载
     * @param busIds
     * @param map
     */
    public void voucherFileDown11(String busIds,BaseMap map){
        log.info("合同变更凭证附件下载消费开始voucherFileDownload map:{}",busIds);
        String opNo = map.get("opNo") ;
        String batchNo = map.get("batchNo") ;
        JSONObject sendObject = createSendMsg(map);
        try {
            List<String> pactIds = new ArrayList<>() ;
            //流程中的附件
            List<String> chgPactIds = Arrays.stream(busIds.split(",")).collect(Collectors.toList()) ;
            JSONObject parmJson = new JSONObject() ;
            parmJson.put("chgPactIds",chgPactIds) ;
            //获取提前结清信息
            List<LeaseDueChangePactDTO> leasePrepRepayApplyDTOS = leaseFeiginClient.findLeaseDuePactByIds(parmJson);
            Map<String,LeaseDueChangePactDTO> dtoMap = new HashMap<>() ;

            if(CollectionUtils.isNotEmpty(leasePrepRepayApplyDTOS)){
                for(LeaseDueChangePactDTO dto: leasePrepRepayApplyDTOS){
                    if(dtoMap.get(dto.getChgPactId())==null ){
                        dtoMap.put(dto.getChgPactId(),dto) ;
                        pactIds.add(dto.getPactId()) ;
                    }
                }
            }

            Map<String,LeasePactDTO> pactDtoMap = new HashMap<>() ;
            if(CollectionUtils.isNotEmpty(pactIds)){
                JSONObject pactJson = new JSONObject() ;
                pactJson.put("pactIds",pactIds) ;
                List<LeasePactDTO> pactDtos = leaseFeiginClient.getLeasePactByPactIds(pactJson);
                if(CollectionUtils.isNotEmpty(pactDtos)){
                    for(LeasePactDTO dto: pactDtos){
                        if(dtoMap.get(dto.getPactId())==null ){
                            pactDtoMap.put(dto.getPactId(),dto) ;
                        }
                    }
                }
            }

            List<DocFileInfEntity> fileList = null ;
            //合同模板渲染，上传附件下载
            LeaseDueChangePactDTO dto = null ;
            String pactNo = "" ;
            JSONObject parmList = null ;
            //没有上传文件的合同号
            List<String> pactNosNoFiles =  new ArrayList<>() ;
            pactNosNoFiles.add("以下合同流程中未上传附件") ;


            long startTime = System.currentTimeMillis();
            log.info("合同变更凭证附件下载-批号:"+batchNo+"，文件生成开始时间:" + startTime);

            if(null != dtoMap && !dtoMap.isEmpty()){
                JSONObject  parmTempJson = new JSONObject();
                parmTempJson.put("batchNo",batchNo) ;
                parmTempJson.put("opNo",opNo) ;
                DocFileInfEntity docFileInf = null ;

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key) ;
                    pactNo = dto.getPactNo()  ;
                    parmTempJson.put("pactNo",pactNo) ;
                    //还款计划变更
                    if("2".equals(dto.getPactChangType())){
                        //展期
                        if("1".equals(dto.getPactChangeSubType())){
                            parmTempJson.put("templateId","8b56f9b271aa4c2b87353059cb63eb60") ;
                        //付息延期
                        }else if("2".equals(dto.getPactChangeSubType())){
                            parmTempJson.put("templateId","9f5e24978c734659bd06552f17a0e1d1") ;
                        //缩期
                        }else if("6".equals(dto.getPactChangeSubType())){
                            parmTempJson.put("templateId","a11ff685c9be4481a6b70408534677a3") ;
                        //自定义还款
                        }else {
                            parmTempJson.put("templateId","8b2da7ac219f4e78b6dca75a85fc45d6") ;
                        }
                    }

                    //生成合同套打参数
                    parmList = getLeaseDueChangePactParmList(dto, key);
                    parmTempJson.put("parmList",parmList);
                    //生成合同模板
                    docFileBizFiletypeConfigService.renderTemplate(parmTempJson);
                    docFileInf = new DocFileInfEntity() ;
                    docFileInf.setBizNo(key);
                    fileList = docFileInfService.findFileInfList(docFileInf);
                    if(pactDtoMap.get(dto.getPactId()) != null ){
                        //获取租金支付计划表PDF文件
                        docFileInf.setBizNo(dto.getPactId());
                        if("1".equals(pactDtoMap.get(dto.getPactId()).getLeaseType())){
                            docFileInf.setTypeNo("FH-71");
                        }else {
                            docFileInf.setTypeNo("FH-66");
                        }
                        List<DocFileInfEntity> pactFileList = docFileInfService.findFileInfList(docFileInf);
                        fileList.addAll(pactFileList) ;
                    }

                    //如果没有附件，则给出提示
                    if(CollectionUtils.isEmpty(fileList)){
                        pactNosNoFiles.add(pactNo) ;
                        log.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"流程中没有上传附件");
                        continue;
                    }
                    //生成文件到指定位置
                    docFileBizFiletypeConfigService.generateFile(opNo,batchNo, pactNo,fileList) ;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("合同变更凭证附件下载-批号:"+batchNo+"，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime,endTime) ;
            log.info("合同变更凭证附件下载-批号:"+batchNo+"，总耗时:" + duration +"秒");

            //没有上传文件的合同记录到文件
            docFileBizFiletypeConfigService.writInfosToFile(opNo,batchNo,pactNosNoFiles) ;



            long startTime1 = System.currentTimeMillis();
            log.info("合同变更凭证附件下载-批号:"+batchNo+"，打包开始时间:" + startTime1);
            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo,batchNo) ;

            long endTime1 = System.currentTimeMillis();
            log.info("合同变更凭证附件下载-批号:"+batchNo+"，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1,endTime1) ;
            log.info("合同变更凭证附件下载-批号:"+batchNo+"，总耗时:" + duration1 +"秒");

            sendObject.put("statusInfo", "成功。文件名为" + sendObject.getString("busKeyId"));
        } catch (Exception e) {
            log.error("MQCreatVoucherFile-Name(合同变更凭证附件下载)-批号：("+batchNo+")-生成附件失败参数信息：{}-异常信息：{}", sendObject, e);
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }

    /**
     *  合同变更参数处理
     * @param dto
     * @param key
     * @return
     */
    private  JSONObject getLeaseDueChangePactParmList(LeaseDueChangePactDTO dto, String key) {
        JSONObject parmList = new JSONObject();
        parmList.put("chgPactId", key);
        parmList.put("prepayId", key);
        parmList.put("dueId", dto.getDueId());
        parmList.put("pactId", dto.getPactId());
        parmList.put("busId", key);
        parmList.put("applyId", dto.getApplyId());
        return parmList;
    }


    /**
     * 正常结清凭证附件下载
     * @param busIds
     * @param map
     */
    public void voucherFileDown02(String busIds,BaseMap map){
        log.info("正常结清凭证附件下载消费开始voucherFileDownload map:{}",busIds);
        String opNo = map.get("opNo") ;
        String batchNo = map.get("batchNo") ;
        JSONObject sendObject = createSendMsg(map);
        try {
            //流程中的附件
            List<String> liftIds = Arrays.stream(busIds.split(",")).collect(Collectors.toList()) ;
            JSONObject parmJson = new JSONObject() ;
            parmJson.put("liftIds",liftIds) ;
            //获取提前结清信息
            List<LeaseLiftManageDTO> leasePrepRepayApplyDTOS = leaseFeiginClient.getLeaseLiftManageByIds(parmJson);
            Map<String,LeaseLiftManageDTO> dtoMap = new HashMap<>() ;

            if(CollectionUtils.isNotEmpty(leasePrepRepayApplyDTOS)){
                for(LeaseLiftManageDTO dto: leasePrepRepayApplyDTOS){
                    if(dtoMap.get(dto.getLiftId())==null ){
                        dtoMap.put(dto.getLiftId(),dto) ;
                    }
                }
            }

            List<DocFileInfEntity> fileList = null ;
            //合同模板渲染，上传附件下载
            LeaseLiftManageDTO dto = null ;
            String pactNo = "" ;
            JSONObject parmList = new JSONObject() ;
            //没有上传文件的合同号
            List<String> pactNosNoFiles =  new ArrayList<>() ;
            pactNosNoFiles.add("以下合同流程中未上传附件") ;

            long startTime = System.currentTimeMillis();
            log.info("正常结清凭证附件下载-批号:"+batchNo+"，文件生成开始时间:" + startTime);


            if(null != dtoMap && !dtoMap.isEmpty()){
                JSONObject  parmTempJson = new JSONObject();
                parmTempJson.put("batchNo",batchNo) ;
                parmTempJson.put("opNo",opNo) ;
                DocFileInfEntity docFileInf = new DocFileInfEntity() ;

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key) ;
                    pactNo = dto.getPactNo()  ;
                    parmTempJson.put("pactNo",pactNo) ;
                    parmTempJson.put("templateId","3c64b97f00e14c249503d6a31918f666") ;
                    //生成合同套打参数
                    parmList.put("liftId",key);
                    parmList.put("prepayId", key);
                    parmList.put("pactId", dto.getPactId());
                    parmTempJson.put("parmList",parmList);
                    //生成合同模板
                    docFileBizFiletypeConfigService.renderTemplate(parmTempJson);
                    docFileInf.setBizNo(key);
                    fileList = docFileInfService.findFileInfList(docFileInf);

                    //如果没有附件，则给出提示
                    if(CollectionUtils.isEmpty(fileList)){
                        pactNosNoFiles.add(pactNo) ;
                        log.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"流程中没有上传附件");
                        continue;
                    }
                    //生成文件到指定位置
                    docFileBizFiletypeConfigService.generateFile(opNo,batchNo, pactNo,fileList) ;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("正常结清凭证附件下载-批号:"+batchNo+"，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime,endTime) ;
            log.info("正常结清凭证附件下载-批号:"+batchNo+"，总耗时:" + duration +"秒");

            //没有上传文件的合同记录到文件
            docFileBizFiletypeConfigService.writInfosToFile(opNo,batchNo,pactNosNoFiles) ;

            long startTime1 = System.currentTimeMillis();
            log.info("正常结清凭证附件下载-批号:"+batchNo+"，打包开始时间:" + startTime1);

            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo,batchNo) ;

            long endTime1 = System.currentTimeMillis();
            log.info("正常结清凭证附件下载-批号:"+batchNo+"，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1,endTime1) ;
            log.info("正常结清凭证附件下载-批号:"+batchNo+"，总耗时:" + duration1 +"秒");


            sendObject.put("statusInfo", "成功。文件名为" + sendObject.getString("busKeyId"));
        } catch (Exception e) {
            log.error("MQCreatVoucherFile-Name(正常结清凭证附件下载)-批号:("+batchNo+")-生成附件失败参数：{}-异常信息：{}", sendObject, e);
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }


    /**
     * 费用抵扣
     * @param busIds
     * @param map
     */
    public void voucherFileDown05(String busIds,BaseMap map){
        log.info("费用抵扣凭证附件下载消费开始voucherFileDownload map:{}",busIds);
        String opNo = map.get("opNo") ;
        String batchNo = map.get("batchNo") ;
        JSONObject sendObject = createSendMsg(map);
        try {
            //流程中的附件
            List<String> deductAppIds = Arrays.stream(busIds.split(",")).collect(Collectors.toList()) ;
            JSONObject parmJson = new JSONObject() ;
            parmJson.put("deductAppIds",deductAppIds) ;
            //获取提前结清信息
            List<LeaseAssureAmtDeductApplyDTO> applyDTOS = leaseFeiginClient.getLeaseAssureAmtDeductApplyByIds(parmJson);
            Map<String,LeaseAssureAmtDeductApplyDTO> dtoMap = new HashMap<>() ;

            if(CollectionUtils.isNotEmpty(applyDTOS)){
                for(LeaseAssureAmtDeductApplyDTO dto: applyDTOS){
                    if(dtoMap.get(dto.getDeductAppId())==null ){
                        dtoMap.put(dto.getDeductAppId(),dto) ;
                    }
                }
            }

            List<DocFileInfEntity> fileList = null ;
            //合同模板渲染，上传附件下载
            LeaseAssureAmtDeductApplyDTO dto = null ;
            String pactNo = "" ;
            JSONObject parmList = new JSONObject() ;
            //没有上传文件的合同号
            List<String> pactNosNoFiles =  new ArrayList<>() ;
            pactNosNoFiles.add("以下合同流程中未上传附件") ;

            long startTime = System.currentTimeMillis();
            log.info("费用抵扣凭证附件下载-批号:"+batchNo+"，文件生成开始时间:" + startTime);

            if(null != dtoMap && !dtoMap.isEmpty()){
                JSONObject  parmTempJson = new JSONObject();
                parmTempJson.put("batchNo",batchNo) ;
                parmTempJson.put("opNo",opNo) ;
                DocFileInfEntity docFileInf = new DocFileInfEntity() ;

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key) ;
                    pactNo = dto.getPactNo()  ;
                    parmTempJson.put("pactNo",pactNo) ;
                    parmTempJson.put("templateId","c8a140fcf4314fe995c70b6f61fa693c") ;
                    //生成合同套打参数
                    parmList.put("busId",key);
                    parmList.put("prepayId", key);
                    parmList.put("pactId", dto.getPactId());
                    parmTempJson.put("parmList",parmList);
                    //生成合同模板
                    docFileBizFiletypeConfigService.renderTemplate(parmTempJson);
                    docFileInf.setBizNo(key);
                    fileList = docFileInfService.findFileInfList(docFileInf);

                    //如果没有附件，则给出提示
                    if(CollectionUtils.isEmpty(fileList)){
                        pactNosNoFiles.add(pactNo) ;
                        log.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"流程中没有上传附件");
                        continue;
                    }
                    //生成文件到指定位置
                    docFileBizFiletypeConfigService.generateFile(opNo,batchNo, pactNo,fileList) ;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("费用抵扣凭证附件下载-批号:"+batchNo+"，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime,endTime) ;
            log.info("费用抵扣凭证附件下载-批号:"+batchNo+"，总耗时:" + duration +"秒");

            //没有上传文件的合同记录到文件
            docFileBizFiletypeConfigService.writInfosToFile(opNo,batchNo,pactNosNoFiles) ;

            long startTime1 = System.currentTimeMillis();
            log.info("费用抵扣凭证附件下载-批号:"+batchNo+"，打包开始时间:" + startTime1);

            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo,batchNo) ;

            long endTime1 = System.currentTimeMillis();
            log.info("费用抵扣凭证附件下载-批号:"+batchNo+"，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1,endTime1) ;
            log.info("费用抵扣凭证附件下载-批号:"+batchNo+"，总耗时:" + duration1 +"秒");


            sendObject.put("statusInfo", "成功。文件名为" + sendObject.getString("busKeyId"));
        } catch (Exception e) {
            log.error("MQCreatVoucherFile-Name(费用抵扣凭证附件下载)-批号：("+batchNo+")-生成附件失败参数信息：{}-异常信息：{}", sendObject, e);
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }

    /**
     * 付分润费用和SP费用
     * @param busIds
     * @param map
     */
    public void voucherFileDown06(String busIds,BaseMap map){
        log.info("付分润费用和SP费用凭证附件下载消费开始voucherFileDownload map:{}",busIds);
        String opNo = map.get("opNo") ;
        String batchNo = map.get("batchNo") ;
        JSONObject sendObject = createSendMsg(map);
        try {
            //流程中的附件
            List<String> ids = Arrays.stream(busIds.split(",")).collect(Collectors.toList()) ;
            JSONObject parmJson = new JSONObject() ;
            parmJson.put("ids",ids) ;
            //获取提前结清信息
            List<LeasePayApplyDTO> applyDTOS = leaseFeiginClient.getLeasePayApplyByIds(parmJson);
            Map<String,LeasePayApplyDTO> dtoMap = new HashMap<>() ;

            if(CollectionUtils.isNotEmpty(applyDTOS)){
                for(LeasePayApplyDTO dto: applyDTOS){
                    if(dtoMap.get(dto.getId())==null ){
                        dtoMap.put(dto.getId(),dto) ;
                    }
                }
            }

            List<DocFileInfEntity> fileList = null ;
            //合同模板渲染，上传附件下载
            LeasePayApplyDTO dto = null ;
            String pactNo = "" ;
            JSONObject parmList = new JSONObject() ;
            //没有上传文件的合同号
            List<String> pactNosNoFiles =  new ArrayList<>() ;
            pactNosNoFiles.add("以下付款流程中未上传附件") ;
            long startTime = System.currentTimeMillis();
            log.info("付分润费用凭证附件下载-批号:"+batchNo+"，文件生成开始时间:" + startTime);


            if(null != dtoMap && !dtoMap.isEmpty()){
                JSONObject  parmTempJson = new JSONObject();
                parmTempJson.put("batchNo",batchNo) ;
                parmTempJson.put("opNo",opNo) ;
                DocFileInfEntity docFileInf = new DocFileInfEntity() ;

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key) ;
                    pactNo = dto.getPayNo()  ;
                    parmTempJson.put("pactNo",pactNo) ;
                    parmTempJson.put("templateId","449b75181cb84d65b79b5979e6cf7c2e") ;
                    //生成合同套打参数
                    parmList.put("busId",key);
                    parmList.put("prepayId", key);
                    parmTempJson.put("parmList",parmList);
                    //生成合同模板
                    docFileBizFiletypeConfigService.renderTemplate(parmTempJson);
                    docFileInf.setBizNo(key);
                    fileList = docFileInfService.findFileInfList(docFileInf);

                    //如果没有附件，则给出提示
                    if(CollectionUtils.isEmpty(fileList)){
                        pactNosNoFiles.add(pactNo) ;
                        log.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"流程中没有上传附件");
                        continue;
                    }
                    //生成文件到指定位置
                    docFileBizFiletypeConfigService.generateFile(opNo,batchNo, pactNo,fileList) ;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("付分润费用凭证附件下载-批号:"+batchNo+"，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime,endTime) ;
            log.info("付分润费用凭证附件下载-批号:"+batchNo+"，总耗时:" + duration +"秒");

            //没有上传文件的合同记录到文件
            docFileBizFiletypeConfigService.writInfosToFile(opNo,batchNo,pactNosNoFiles) ;

            long startTime1 = System.currentTimeMillis();
            log.info("付分润费用凭证附件下载-批号:"+batchNo+"，打包开始时间:" + startTime1);
            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo,batchNo) ;

            long endTime1 = System.currentTimeMillis();
            log.info("付分润费用凭证附件下载-批号:"+batchNo+"，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1,endTime1) ;
            log.info("付分润费用凭证附件下载-批号:"+batchNo+"，总耗时:" + duration1 +"秒");

            sendObject.put("statusInfo", "成功。文件名为" + sendObject.getString("busKeyId"));
        } catch (Exception e) {
            log.error("MQCreatVoucherFile-Name(付分润费用凭证附件下载)-批号：("+batchNo+")-生成附件失败参数信息：{}-异常信息：{}", sendObject, e);
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }

    /**
     *  退款
     * @param busIds
     * @param map
     */
    public void voucherFileDown08(String busIds,BaseMap map){
        log.info("退款凭证附件下载消费开始voucherFileDownload map:{}",busIds);
        String opNo = map.get("opNo") ;
        String batchNo = map.get("batchNo") ;
        JSONObject sendObject = createSendMsg(map);
        try {
            //流程中的附件
            List<String> ids = Arrays.stream(busIds.split(",")).collect(Collectors.toList()) ;
            JSONObject parmJson = new JSONObject() ;
            parmJson.put("ids",ids) ;
            //获取提前结清信息
            List<LeasePayApplyDTO> applyDTOS = leaseFeiginClient.getLeasePayApplyByIds(parmJson);
            Map<String,LeasePayApplyDTO> dtoMap = new HashMap<>() ;

            if(CollectionUtils.isNotEmpty(applyDTOS)){
                for(LeasePayApplyDTO dto: applyDTOS){
                    if(dtoMap.get(dto.getId())==null ){
                        dtoMap.put(dto.getId(),dto) ;
                    }
                }
            }

            List<DocFileInfEntity> fileList = null ;
            //合同模板渲染，上传附件下载
            LeasePayApplyDTO dto = null ;
            String pactNo = "" ;
            JSONObject parmList = new JSONObject() ;
            //没有上传文件的合同号
            List<String> pactNosNoFiles =  new ArrayList<>() ;
            pactNosNoFiles.add("以下退款流程中未上传附件") ;

            long startTime = System.currentTimeMillis();
            log.info("退款凭证附件下载-批号:"+batchNo+"，文件生成开始时间:" + startTime);

            if(null != dtoMap && !dtoMap.isEmpty()){
                JSONObject  parmTempJson = new JSONObject();
                parmTempJson.put("batchNo",batchNo) ;
                parmTempJson.put("opNo",opNo) ;
                DocFileInfEntity docFileInf = new DocFileInfEntity() ;

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key) ;
                    pactNo = dto.getPayNo()  ;
                    parmTempJson.put("pactNo",pactNo) ;

                    if("RETURN_3".equals(dto.getPayType())){//退款_保证金池
                        parmTempJson.put("templateId","f049abe3a09740c8baf57478a2586d4d") ;
                    }else if("RETURN_1".equals(dto.getPayType())){//退款_暂收款
                        parmTempJson.put("templateId","bbb31ce3a4b640a1b1d2e318b5c304bd") ;
                    }else {//退款_通用,
                        parmTempJson.put("templateId","815250ee63e640f88405beacf5d9c588") ;
                    }

                    //生成合同套打参数
                    parmList.put("busId",key);
                    parmList.put("prepayId", key);
                    parmTempJson.put("parmList",parmList);
                    //生成合同模板
                    docFileBizFiletypeConfigService.renderTemplate(parmTempJson);
                    docFileInf.setBizNo(key);
                    fileList = docFileInfService.findFileInfList(docFileInf);

                    //如果没有附件，则给出提示
                    if(CollectionUtils.isEmpty(fileList)){
                        pactNosNoFiles.add(pactNo) ;
                        log.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"流程中没有上传附件");
                        continue;
                    }
                    //生成文件到指定位置
                    docFileBizFiletypeConfigService.generateFile(opNo,batchNo, pactNo,fileList) ;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("退款凭证附件下载-批号:"+batchNo+"，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime,endTime) ;
            log.info("退款凭证附件下载-批号:"+batchNo+"，总耗时:" + duration +"秒");

            //没有上传文件的合同记录到文件
            docFileBizFiletypeConfigService.writInfosToFile(opNo,batchNo,pactNosNoFiles) ;

            long startTime1 = System.currentTimeMillis();
            log.info("付款投放凭证附件下载-批号:"+batchNo+"，打包开始时间:" + startTime1);
            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo,batchNo) ;
            sendObject.put("statusInfo", "成功。文件名为" + sendObject.getString("busKeyId"));

            long endTime1 = System.currentTimeMillis();
            log.info("退款凭证附件下载-批号:"+batchNo+"，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1,endTime1) ;
            log.info("退款凭证附件下载-批号:"+batchNo+"，总耗时:" + duration1 +"秒");

        } catch (Exception e) {
            log.error("MQCreatVoucherFile-Name(退款凭证附件下载)-批号：("+batchNo+")-生成附件失败参数信息{}-异常信息：{}", sendObject, e);
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }


    /**
     * 合同变更承租人变更
     * @param busIds
     * @param map
     */
    public void voucherFileDown10(String busIds,BaseMap map){
        log.info("合同变更凭证附件下载消费开始voucherFileDownload map:{}",busIds);
        String opNo = map.get("opNo") ;
        String batchNo = map.get("batchNo") ;
        JSONObject sendObject = createSendMsg(map);
        try {
            //流程中的附件
            List<String> pactIds = Arrays.stream(busIds.split(",")).collect(Collectors.toList()) ;
            JSONObject parmJson = new JSONObject() ;
            parmJson.put("pactIds",pactIds) ;
            //获取提前结清信息
            List<LeasePactDTO> leasePactDTOS = leaseFeiginClient.getLeasePacts(parmJson);
            Map<String,LeasePactDTO> dtoMap = new HashMap<>() ;

            if(CollectionUtils.isNotEmpty(leasePactDTOS)){
                for(LeasePactDTO dto: leasePactDTOS){
                    if(dtoMap.get(dto.getPactId())==null ){
                        dtoMap.put(dto.getPactId(),dto) ;
                    }
                }
            }

            List<DocFileInfEntity> fileList = null ;
            //合同模板渲染，上传附件下载
            LeasePactDTO dto = null ;
            String pactNo = "" ;
            JSONObject parmList = null ;
            //没有上传文件的合同号
            List<String> pactNosNoFiles =  new ArrayList<>() ;
            pactNosNoFiles.add("以下合同流程中未上传附件") ;

            long startTime = System.currentTimeMillis();
            log.info("合同变更承租人变更凭证附件下载-批号:"+batchNo+"，文件生成开始时间:" + startTime);

            if(null != dtoMap && !dtoMap.isEmpty()){
                JSONObject  parmTempJson = new JSONObject();
                parmTempJson.put("batchNo",batchNo) ;
                parmTempJson.put("opNo",opNo) ;
                DocFileInfEntity docFileInf = new DocFileInfEntity() ;

                for (String key : dtoMap.keySet()) {
                    dto = dtoMap.get(key) ;
                    pactNo = dto.getPactNo()  ;
                    parmTempJson.put("pactNo",pactNo) ;
                    //合同变更(承租人变更)
                    parmTempJson.put("templateId","8d441255ec42439088367803bd597470") ;
                    //生成合同套打参数
                    parmList = getLeasePactParmList(dto, key);
                    parmTempJson.put("parmList",parmList);
                    //生成合同模板
                    docFileBizFiletypeConfigService.renderTemplate(parmTempJson);
//                    docFileInf.setBizNo(key);
//                    fileList = docFileInfService.findFileInfList(docFileInf);
//                    //如果没有附件，则给出提示
//                    if(CollectionUtils.isEmpty(fileList)){
//                        pactNosNoFiles.add(pactNo) ;
//                        log.info("MQCreatVoucherFile-Name("+batchNo+")-"+pactNo+"流程中没有上传附件");
//                        continue;
//                    }
//                    //生成文件到指定位置
//                    docFileBizFiletypeConfigService.generateFile(opNo,batchNo, pactNo,fileList) ;
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("合同变更承租人变更凭证附件下载-批号:"+batchNo+"，文件生成结束时间:" + endTime);
            long duration = calcTime(startTime,endTime) ;
            log.info("合同变更承租人变更凭证附件下载-批号:"+batchNo+"，总耗时:" + duration +"秒");

            //没有上传文件的合同记录到文件
//            docFileBizFiletypeConfigService.writInfosToFile(opNo,batchNo,pactNosNoFiles) ;

            long startTime1 = System.currentTimeMillis();
            log.info("合同变更承租人变更凭证附件下载-批号:"+batchNo+"，打包开始时间:" + startTime1);
            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo,batchNo) ;

            long endTime1 = System.currentTimeMillis();
            log.info("合同变更承租人变更凭证附件下载-批号:"+batchNo+"，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1,endTime1) ;
            log.info("合同变更承租人变更凭证附件下载-批号:"+batchNo+"，总耗时:" + duration1 +"秒");

            sendObject.put("statusInfo", "成功。文件名为" + sendObject.getString("busKeyId"));
        } catch (Exception e) {
            log.error("MQCreatVoucherFile-Name(合同变更承租人变更凭证附件下载)-批号：("+batchNo+")-生成附件失败参数信息：{}，异常信息：{}", sendObject, e);
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }

    /**
     *  合同变更(承租人变更)参数处理
     * @param dto
     * @param key
     * @return
     */
    private  JSONObject getLeasePactParmList(LeasePactDTO dto, String key) {
        JSONObject parmList = new JSONObject();
        parmList.put("prepayId", dto.getPrepayId());
        parmList.put("pactId", dto.getOriginalPactId());
        parmList.put("chengedCusPactId", dto.getPactId());
        parmList.put("dueId", dto.getDueId());
        parmList.put("busId", dto.getPrepayId());
        parmList.put("applyId", dto.getApplyId());

        parmList.put("occType", dto.getOccType());
        parmList.put("originalPactNo", dto.getOriginalPactNo());
        parmList.put("originalCusName", dto.getOriginalCusName());
        parmList.put("pactNo", dto.getPactNo());
        parmList.put("cusName", dto.getCusName());
        parmList.put("totalLeasePrice", dto.getTotalLeasePrice());
        if(null != dto.getDownPaymentsRate()){
            parmList.put("downPaymentsRate", dto.getDownPaymentsRate().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
        }

        parmList.put("downPaymentsAmt", dto.getDownPaymentsAmt());
        if(null != dto.getPactAmt()){
            parmList.put("applyAmt", dto.getPactAmt().setScale(2, RoundingMode.HALF_UP));
        }
        if(null != dto.getTotalTinterest()){
            parmList.put("totalTinterest", dto.getTotalTinterest().setScale(2, RoundingMode.HALF_UP));
        }

        if(null != dto.getCusTirrShow()){
            parmList.put("cusTirrShow", dto.getCusTirrShow().setScale(4, RoundingMode.HALF_UP));
        }
        if(null != dto.getTotalTreceivables()){
            parmList.put("totalTreceivables", dto.getTotalTreceivables().setScale(2, RoundingMode.HALF_UP));
            parmList.put("totalTreceivables_dx", dto.getTotalTreceivables().setScale(2, RoundingMode.HALF_UP));
        }

        parmList.put("termShow", dto.getTermShow());
        if(null != dto.getRate()){
            parmList.put("rate", dto.getRate().setScale(2, RoundingMode.HALF_UP));
        }
        parmList.put("repayType", dto.getRepayType());
        parmList.put("repayCycle", dto.getRepayCycle());
        parmList.put("graceType", dto.getGraceType());
        parmList.put("graceDays", dto.getGraceDays());
        parmList.put("rateAdjustFlag", dto.getRateAdjustFlag());
        parmList.put("hasMarginDeduction", dto.getHasMarginDeduction());
        parmList.put("beforePay", dto.getBeforePay());
        parmList.put("rebateFlag", dto.getRebateFlag());
        parmList.put("discountFlag", dto.getDiscountFlag());
        parmList.put("discountType", dto.getDiscountType());
        parmList.put("signMethod", dto.getSignMethod());
        parmList.put("pactType", dto.getPactType());
        parmList.put("pledgeInfoFlag", dto.getPledgeInfoFlag());
        parmList.put("repayAccountNo", dto.getRepayAccountNo());
        parmList.put("repayAccountName", dto.getRepayAccountName());
        parmList.put("repayBankName", dto.getRepayBankName());
        parmList.put("invoiceRequire", dto.getInvoiceRequire());
        parmList.put("collateralRequire", dto.getCollateralRequire());
        parmList.put("channelGuaranteeType", dto.getChannelGuaranteeType());
        parmList.put("itemCategory", dto.getItemCategory());
        parmList.put("productName", dto.getProductName());
        parmList.put("channelSource", dto.getChannelSource());
        parmList.put("creditCusName", dto.getCreditCusName());
        parmList.put("limitTypeName", dto.getLimitTypeName());
        parmList.put("leaseType", dto.getLeaseType());
        parmList.put("sellerName", dto.getSellerName());
        parmList.put("hasControlledKangfu", dto.getHasControlledKangfu());
        parmList.put("putoutDate", dto.getPutoutDate());
        parmList.put("insuranceFeeBal", dto.getInsuranceFeeBal());
        parmList.put("loanBalance", dto.getLoanBalance());
        parmList.put("extendBalance", dto.getExtendBalance());
        parmList.put("insuranceDepositBal", dto.getInsuranceDepositBal());
        parmList.put("mortgageNotaryFeeBal", dto.getMortgageNotaryFeeBal());
        parmList.put("managerName", dto.getManagerName());
        parmList.put("pactCreateTime", dto.getPactCreateTime());
        parmList.put("company", dto.getCompany());
        /**
         * 起租日
         */
        parmList.put("dueBeginDate", dto.getDueBeginDate());
        /**
         * 预计结束日
         */
        parmList.put("dueEndDate", dto.getDueEndDate());
        return parmList;
    }



    private long calcTime(long startTime, long endTime) {
        return (endTime - startTime);
    }
}