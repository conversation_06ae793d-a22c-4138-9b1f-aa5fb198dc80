/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.rbmq.listener;

import cn.mftcc.common.rb.BaseMap;
import cn.mftcc.common.rb.RabbitComponent;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.rbmq.constant.CloudConstant;
import cn.mftcc.doc.components.rbmq.service.MsgInvoiceService;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;
import cn.mftcc.rabbitmq.core.BaseRabbiMqCustomHandler;
import cn.mftcc.rabbitmq.listenter.MqListener;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.Header;


/**
 * 定义接收者（可以定义N个接受者，消息会均匀的发送到N个接收者中）
 * <p>
 * RabbitMq接受者1
 * （@RabbitListener声明类上，一个类只能监听一个队列）
 *
 * @author: hh
 * @date: 2022/04/21
 */
@Slf4j
@RabbitListener(queues = CloudConstant.MQ_LEASE_INVOICE_FILE_DOWN)
@RabbitComponent(value = "leaseBatchDownInvoiceFile")
public class LeaseBatchDownInvoiceFile extends BaseRabbiMqCustomHandler<BaseMap> {


    @Autowired
    private LeaseFeiginClient leaseFeiginClient;
    @Autowired
    private MsgInvoiceService msgInvoiceService;

    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Value("${mftcc.fileTmpDown.downloadPath:}")
    private String downloadPath;

    /**
     * 收据批量下载
     * @param baseMap
     * @param channel
     * @param deliveryTag
     */
    @RabbitHandler
    public void onMessage(BaseMap baseMap, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) throws Exception {
        super.onMessage(baseMap, deliveryTag, channel, new MqListener<BaseMap>() {
            @Override
            public void handler(BaseMap map, Channel channel) {
                DownloadInvoiceFileThread voucherFileDown = new DownloadInvoiceFileThread(baseMap, channel,
                        msgInvoiceService, deliveryTag,docFileBizFiletypeConfigService,leaseFeiginClient,downloadPath);
                Thread thread = new Thread(voucherFileDown);
                thread.start();
            }
        });
    }
}