/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.rbmq.listener;

import cn.mftcc.common.rb.BaseMap;
import cn.mftcc.common.rb.RabbitComponent;
import cn.mftcc.common.utils.StringUtil;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.rbmq.constant.CloudConstant;
import cn.mftcc.doc.components.rbmq.service.MsgInvoiceService;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;

import cn.mftcc.rabbitmq.core.BaseRabbiMqHandler;
import cn.mftcc.rabbitmq.listenter.MqListener;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import io.micrometer.core.instrument.util.StringUtils;
import javafx.scene.shape.Path;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.FileWriter;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 定义接收者（可以定义N个接受者，消息会均匀的发送到N个接收者中）
 * <p>
 * RabbitMq接受者1
 * （@RabbitListener声明类上，一个类只能监听一个队列）
 *
 * @author: hh
 * @date: 2022/04/21
 */
@Slf4j
@RabbitListener(queues = CloudConstant.MQ_LEASE_INVOICE_DOWN_ORDER)
@RabbitComponent(value = "leaseBatchDownInvoice")
public class LeaseBatchDownInvoice extends BaseRabbiMqHandler<BaseMap> {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private LeaseFeiginClient leaseFeiginClient;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private MsgInvoiceService msgInvoiceService;


    @Value("${mftcc.fileTmpDown.downloadPath:}")
    private String downloadPath;

    @Value("${mftcc.fileTmpDown.lineStr:}")
    private String lineStr;

    @RabbitHandler
    public void onMessage(BaseMap baseMap, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.onMessage(baseMap, deliveryTag, channel, new MqListener<BaseMap>() {
            @Override
            public void handler(BaseMap map, Channel channel) {
                log.info("发票批量下载消费开始invoiceDownload map:{}",map);
                //业务处理
                JSONObject object = new JSONObject();
                object.put("invoiceAppIds", map.get("invoiceAppIds"));
                String opNo = map.get("opNo");
                String batchNo = map.get("batchNo");
                JSONObject sendObject = new JSONObject();
                sendObject.put("batchNo", batchNo);
                sendObject.put("opNo", map.get("opNo"));
                sendObject.put("opName", map.get("opName"));
                sendObject.put("tmplType", "lease_invoice_down");
                String fileName=opNo.concat("_").concat(batchNo).concat(".zip");
                sendObject.put("busKeyId", fileName);
                boolean flag=false;
                try {
                    JSONArray listFileIds0 = leaseFeiginClient.leaseInvoices(object);
                    if(listFileIds0!=null) {
                        for (int i = 0; i < listFileIds0.size(); i++) {
                            JSONObject ob = listFileIds0.getJSONObject(i);
                            if (StringUtils.isBlank(ob.getString("fileId"))) {
                                try {
                                    leaseFeiginClient.downInvoice(ob.getString("invoiceAppId"));
                                } catch (Exception e) {
                                    log.error("downInvoice异常,invoiceAppNo:{}", ob.getString("invoiceAppNo"), e);
                                }
                            }
                        }
                    }
                    JSONArray jsonArray1 = leaseFeiginClient.leaseInvoices(object);
                    List<String> fileIds = new ArrayList();
                    String msg =new String();
                    if(jsonArray1!=null) {
                        for (int i = 0; i < jsonArray1.size(); i++) {
                            JSONObject ob1 = jsonArray1.getJSONObject(i);
                            if (StringUtils.isBlank(ob1.getString("fileId"))) {
                                msg = msg+ob1.getString("pactNo")+"-"+ob1.getString("invoiceAppNo")+"下载失败;" +lineStr;
                            } else {
                                fileIds.add(ob1.getString("fileId"));
                                msg = msg+ob1.getString("pactNo")+"-"+ob1.getString("invoiceAppNo")+"下载成功;"+lineStr;
                            }
                        }
                    }
                    String resultFileName=fileName.replace(".zip",".txt");
                    try{
                        File file =new File(downloadPath+File.separator+resultFileName);
                        if(!file.exists()){
                            file.createNewFile();
                        }
                        if(StringUtil.isBlank(msg)){
                            msg= msg+map.get("invoiceAppIds").toString()+"下载失败";
                        }
                        FileWriter fileWriter=new FileWriter(file);
                        fileWriter.write(msg);
                        fileWriter.close();
                        flag=true;
                    }catch (Exception e){
                        log.error(fileName+"结果文件信息生成失败:msg{}",msg,e);
                    }

                    if (CollectionUtils.isEmpty(fileIds)) {
                        sendObject.put("statusInfo", "全部失败，请重新操作");
                        sendObject.put("busKeyId", resultFileName);
                    } else {
                        JSONObject ajaxData = new JSONObject();
                        ajaxData.put("fileIds", fileIds);
                        ajaxData.put("fileName",fileName);
                        if(flag){
                            ajaxData.put("resultFileName",resultFileName);
                        }
                        Map<String, Object> map1 = docFileBizFiletypeConfigService.downloadFilesZip(ajaxData);
                        if (map1 != null && "success".equals(map1.get("msg"))) {
                            sendObject.put("statusInfo", "成功。文件名为" + ajaxData.getString("fileName"));
                        } else {
                            sendObject.put("statusInfo", "失败，请重新操作");
                        }
                    }
                } catch (Exception e) {
                    log.error("发票批量下载异常{}", sendObject, e);
                    sendObject.put("statusInfo", "异常失败，请重新操作");
                }
                msgInvoiceService.sendInnerMessage(sendObject);
                return;
            }
        });
    }

}