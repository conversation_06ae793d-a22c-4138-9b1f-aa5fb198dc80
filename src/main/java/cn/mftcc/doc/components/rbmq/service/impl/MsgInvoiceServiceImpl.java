/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.components.rbmq.service.impl;

import cn.mftcc.common.logger.MFLogger;
import cn.mftcc.common.utils.ParmCacheUtil;
import cn.mftcc.doc.components.rbmq.constant.CloudConstant;
import cn.mftcc.doc.components.rbmq.service.MsgInvoiceService;
import cn.mftcc.doc.feign.client.MsgFeignClient;
import cn.mftcc.msg.feign.dto.MsgTriggerDTO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service("msgInvoiceServiceImpl")
public class MsgInvoiceServiceImpl  implements MsgInvoiceService {

    @Autowired
    private MsgFeignClient msgFeignClient;
    @Autowired
    private ParmCacheUtil parmCacheUtil;
    @Override
    public void sendInnerMessage(JSONObject jsonObject) {
        MFLogger.info("mq消息推送："+jsonObject);
        String batchNo = jsonObject.getString("batchNo");// 消息接收人编号
        String opNo = jsonObject.getString("opNo");// 消息接收人编号
        String opName = jsonObject.getString("opName");// 消息接收人名称
        String tmplType =jsonObject.getString("tmplType");
        String statusInfo =jsonObject.getString("statusInfo");
        String busKeyId =jsonObject.getString("busKeyId");

        String optName = parmCacheUtil.getOptName(CloudConstant.MSG_TMPL_TYPE, tmplType);
        MsgTriggerDTO msgTriggerDTO = new MsgTriggerDTO();
        msgTriggerDTO.setSenceNo(tmplType);
        msgTriggerDTO.setBusKeyId(busKeyId);
        Map<String, String> busParams = new HashMap<>();
        busParams.put("optName",optName);
        busParams.put("statusInfo",statusInfo);
        busParams.put("batchNo",batchNo);
        JSONObject examineParms=new JSONObject();
        examineParms.put("caseManNo",opNo);
        examineParms.put("caseManName",opName);
        busParams.put("caseMan",JSONObject.toJSONString(examineParms));
        msgTriggerDTO.setBusParams(busParams);
        try {
            msgFeignClient.triggerBySence(msgTriggerDTO);
        } catch (Exception e) {
            MFLogger.error("调用消息服务，发送消息失败！",e);
        }
    }
}
