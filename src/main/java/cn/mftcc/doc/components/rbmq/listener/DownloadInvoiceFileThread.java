package cn.mftcc.doc.components.rbmq.listener;

import cn.mftcc.common.rb.BaseMap;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.rbmq.service.MsgInvoiceService;
import cn.mftcc.doc.feign.client.LeaseFeiginClient;
import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class DownloadInvoiceFileThread implements Runnable {

    private BaseMap map;
    private Channel channel;
    private MsgInvoiceService msgInvoiceService;
    private Long deliveryTag;
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    private LeaseFeiginClient leaseFeiginClient;
    private String downloadPath;

    public DownloadInvoiceFileThread(BaseMap map, Channel channel,
                                     MsgInvoiceService msgInvoiceService, Long deliveryTag,
                                     DocFileBizFiletypeConfigService docFileBizFiletypeConfigService,
                                     LeaseFeiginClient leaseFeiginClient, String downloadPath) {
        this.map = map;
        this.channel = channel;
        this.msgInvoiceService = msgInvoiceService;
        this.deliveryTag = deliveryTag;
        this.docFileBizFiletypeConfigService = docFileBizFiletypeConfigService;
        this.leaseFeiginClient = leaseFeiginClient;
        this.downloadPath = downloadPath;
    }

    @Override
    public void run() {
        log.info("批量下载消费开始invoiceDownload map:{}",map);
        String opNo = map.get("opNo") ;
        String batchNo = map.get("batchNo") ;
        JSONObject sendObject = createSendMsg(map);
        try {
            long startTime1 = System.currentTimeMillis();
            log.info("收据附件下载-批号:"+batchNo+"，打包开始时间:" + startTime1);
            log.info("开始调用文件初始化生产pdf");
            ArrayList<String> array = map.get("busIds");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("list",array);
            jsonObject.put("opNo",opNo);
            jsonObject.put("batchNo",batchNo);
            String fileName = opNo+"_"+ batchNo;
            String path = downloadPath+ File.separator+fileName;
            jsonObject.put("path",path);
            log.info("文件目录:"+path);
            Map<String, byte[]> stringMap = leaseFeiginClient.batchDownReceipts(jsonObject);
            File folder = new File(path);
            if (!folder.exists()) {
                log.info("生成文件夹:"+fileName);
                folder.mkdirs();
            }
            for (Map.Entry<String, byte[]> entry : stringMap.entrySet()) {
                String fileRename = entry.getKey();
                byte[] data = entry.getValue();
                log.info("文件生成:"+fileRename+":"+data.length);
                String path1 = path + "/" + fileRename;
                //创建文件对象
                File file = new File(path, fileRename);
                //如果文件不存在则新建文件
                if (!file.exists()) {
                    file.createNewFile();
                }
                // 创建文件输出流
                FileOutputStream outputStream = new FileOutputStream(file);
                // 使用文件输出流写入数据
                outputStream.write(data);
                // 关闭文件输出流
                outputStream.close();
            }
            log.info("调用文件初始化生产pdf结束"+folder.length());
            //文件打包
            docFileBizFiletypeConfigService.downloadVoucherFilesZip(opNo,batchNo) ;
            if (folder.exists()) {
                deleteFolder(folder);
            }
            long endTime1 = System.currentTimeMillis();
            log.info("收据附件下载-批号:"+batchNo+"，打包结束时间:" + endTime1);
            long duration1 = calcTime(startTime1,endTime1) ;
            log.info("收据附件下载-批号:"+batchNo+"，总耗时:" + duration1 +"秒");
            sendObject.put("statusInfo", "成功。文件名为" + sendObject.getString("busKeyId"));
        } catch (Exception e) {
            log.error("MQCreatVoucherFile-Name(收据附件下载)-批号:("+batchNo+")-生成附件失败参数：{}-异常信息：{}", sendObject, e.getMessage());
            sendObject.put("statusInfo", "异常失败，请重新操作");
        }
        msgInvoiceService.sendInnerMessage(sendObject);
    }
    /**
     * 删除文件夹及其下的文件
     * @param folder
     */
    public static void deleteFolder(File folder) {
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 递归删除子文件夹及其内容
                    deleteFolder(file);
                } else {
                    // 删除文件
                    file.delete();
                }
            }
        }
        // 删除空文件夹
        folder.delete();
    }

    private JSONObject  createSendMsg(BaseMap map) {
        String opNo = map.get("opNo") ;
        String opName = map.get("opName") ;
        String batchNo = map.get("batchNo") ;

        JSONObject sendObject = new JSONObject();
        sendObject.put("opNo", opNo);
        sendObject.put("opName", opName);
        sendObject.put("batchNo", batchNo);
        sendObject.put("tmplType", "lease_voucher_down");
        String fileName= opNo.concat("_").concat(batchNo).concat(".zip");
        sendObject.put("busKeyId", fileName);
        return sendObject;
    }



    private long calcTime(long startTime, long endTime) {
        return (endTime - startTime);
    }
}