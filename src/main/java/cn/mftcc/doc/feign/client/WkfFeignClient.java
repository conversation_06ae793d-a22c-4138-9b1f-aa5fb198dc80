/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.feign.client;

import cn.mftcc.flowable.feign.api.FlowableInterface;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @Description: 流程接口
 * <AUTHOR>
 * @Date   2021/4/9 18:40
 */
@FeignClient(name="${mftcc.FeignClient.flowable:mftcc-flowable-server}",contextId= "wkfClient")
public interface WkfFeignClient extends FlowableInterface {

}