/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.feign.client;

import cn.mftcc.after.feign.api.AfterTemplateInterface;
import cn.mftcc.elink.feign.api.EsignFeignInterface;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(value = "${mftcc.FeignClient.elink:mftcc-elink-server}",contextId = "EsignFeignInterface")
public interface EsignFeignClient extends EsignFeignInterface {
}
