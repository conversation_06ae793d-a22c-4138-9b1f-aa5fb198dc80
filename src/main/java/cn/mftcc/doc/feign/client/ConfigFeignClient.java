/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.feign.client;

import cn.mftcc.config.feign.api.ConfigInterface;
import cn.mftcc.sys.feign.api.SysInterface;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @author: she<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022/3/15 14:34
 * @Description:
 */
@FeignClient(name = "${mftcc.FeignClient.config:mftcc-config-server}")
public interface ConfigFeignClient extends ConfigInterface {
}
