/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.feign.controller.app;



import cn.mftcc.common.R;
import cn.mftcc.doc.common.utils.FileUtils;
import cn.mftcc.doc.common.utils.OssConfig;
import cn.mftcc.doc.components.file.entity.DocFileInfEntity;
import cn.mftcc.doc.components.file.mapper.DocFileInfMapper;
import cn.mftcc.doc.components.file.service.DocFileBizFiletypeConfigService;
import cn.mftcc.doc.components.file.service.DocFileInfService;
import cn.mftcc.doc.components.mould.entity.DocTemplateEsignerListEntity;
import cn.mftcc.doc.components.mould.mapper.DocTemplateEsignerListMapper;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;


/**
 * 微信端接口
 */
@RequestMapping("/docInterface")
@RestController
public class DocInterfaceController {
    @Autowired
    private DocFileInfMapper docFileInfMapper;
    @Autowired
    private DocTemplateEsignerListMapper docTemplateEsignerListMapper;
    private static final String STR6 = "alert(\"not find the file\")";
    private static final String SCRIPT_HEAD = "<script>";
    private static final String SCRIPT_TAIL = "</script>";
    private static final String CONTENT_TYPE_DEF = "application/x-download";
    private static final String HEADER_VALUE_DEFAULT = "attchement;filename=";
    public static final String HEADER_NAME_DEFAULT = "Content-Disposition";
    private static final String CONTENT_TYPE_DEF1 = "application/octet-stream";
    @Value("${mftcc.template.mould-path:}")
    private String docMouldPath;
    @Value("${mftcc.file.upload-outter-function:}")
    private String uploadOutterFunction;
    @Autowired
    private DocFileInfService docFileInfService;
    @Autowired
    private DocFileBizFiletypeConfigService docFileBizFiletypeConfigService;
    @Autowired
    private OssConfig ossConfig;
    @Value("${mftcc.file.doc-file-upload-path:}")
    private  String docFileUploadPath;




    @RequestMapping("/findFileInfoList")
    @ApiOperation("查询签约合同列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pactId", value = "签约合同id")})
    public R findFileInfoList(@RequestParam("pactId")  String pactId) throws Exception {
        QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(pactId),"biz_no",pactId)
                .orderByDesc("create_time");
        List<DocFileInfEntity> docFileInfEntities = docFileInfMapper.selectList(queryWrapper);
       /* QueryWrapper<DocTemplateEsignerListEntity> qw =new QueryWrapper<>();
        qw.eq("biz_no",pactId);
        qw.eq("if_download","1");
        qw.groupBy("template_no");
        List<DocFileInfEntity> docFileInfEntityList =new ArrayList<>();
        DocFileInfEntity docFileInfEntity;
        List<DocTemplateEsignerListEntity> docTemplateEsignerListEntities = docTemplateEsignerListMapper.selectList(qw);
        for (DocTemplateEsignerListEntity docTemplateEsignerListEntity : docTemplateEsignerListEntities) {
            String bizNo = docTemplateEsignerListEntity.getBizNo();
            String flowNo = docTemplateEsignerListEntity.getFlowNo();
            String nodeNo = docTemplateEsignerListEntity.getNodeNo();
            String typeNo = docTemplateEsignerListEntity.getTypeNo();
            QueryWrapper<DocFileInfEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(StringUtils.isNotEmpty(bizNo),"biz_no",bizNo)
                    .eq(StringUtils.isNotEmpty(flowNo),"flow_no",flowNo)
                    .eq(StringUtils.isNotEmpty(typeNo),"type_no",typeNo)
                    .orderByDesc("create_time");
            List<DocFileInfEntity> docFileInfEntities = docFileInfMapper.selectList(queryWrapper);
            if (docFileInfEntities!=null){
                docFileInfEntity = docFileInfEntities.get(0);
                docFileInfEntityList.add(docFileInfEntity);
            }
        }*/
        return R.ok().put("list",docFileInfEntities);
    }

    @RequestMapping("/downLoadTemplate")
    @ApiOperation("下载合同")
    @ApiImplicitParams({@ApiImplicitParam(name = "ajaxData", value = "下载fileId")})
    public void downLoadTemplate(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
        // 开始下载文件
        InputStream fis =null;
        try {
            // 1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");
            OutputStream toClient =null;
            // 2.设置文件头：最后一个参数是设置下载文件名
            String previewFileName=ajaxData.getString("fileName");//如果是预览，预览文件的名称
            String fileSuffix=new FileUtils().getFileSuffixName(previewFileName);//文件后缀名
            String modelName=ajaxData.getString("modelName");
            modelName=modelName+"."+fileSuffix;
            String fileUrlPath=docMouldPath+"saveMould"+ File.separator+previewFileName;

            File file=new File(fileUrlPath);
            if (file.exists()) {
                fis = new BufferedInputStream(new FileInputStream( file));
                response.reset();
                response.setContentType(CONTENT_TYPE_DEF);
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                response.addHeader("Content-Length", "" + file.length());
                response.addHeader("file-name*", URLEncoder.encode(modelName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();
            } else {
                PrintWriter out = response.getWriter();
                out.print(SCRIPT_HEAD);
                out.print(STR6);
                out.print(SCRIPT_TAIL);
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (null !=fis){
                try {
                    fis.close();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
    }

    @RequestMapping("/downLoadOneFile")
    @ResponseBody
    public void downLoadOneFile(@RequestBody JSONObject ajaxData, HttpServletResponse response) {
        InputStream fis =null;
        try {
            String fileId=ajaxData.getString("fileId");
            DocFileInfEntity tmpObj=this.docFileInfService.findById(fileId);
            // 1.设置文件ContentType类型，这样设置，会自动判断下载文件类型
            response.setContentType("multipart/form-data");

            OutputStream toClient =null;

            if("obs".equals(uploadOutterFunction)){
                // 2.设置文件头：最后一个参数是设置下载文件名
                String obsId = tmpObj.getObsId();
                String suffix=obsId.split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;

                String filePath = docFileBizFiletypeConfigService.getShowUrlByObsid(obsId);
                fis = new URL(filePath).openStream();
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);

                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();

            }else if("oss".equals(uploadOutterFunction)){
                // 2.设置文件头：最后一个参数是设置下载文件名
                String obsId = tmpObj.getFilePath();
                String suffix=obsId.split("\\.")[1];
                String  newFileName=tmpObj.getFileName()+"."+suffix;

//                String filePath = docFileBizFiletypeConfigService.getShowUrlByOssid(obsId);
                fis = ossConfig.readOss(tmpObj.getFilePath());
                response.reset();
                response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);

                response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                toClient = new BufferedOutputStream(response.getOutputStream());
                response.setContentType(CONTENT_TYPE_DEF1);
                byte[] buffer = new byte[1024 * 1024 * 4];
                int i = -1;
                while ((i = fis.read(buffer)) != -1) {
                    toClient.write(buffer, 0, i);
                }
                //fis.close();
                toClient.flush();
                toClient.close();
            } else{
                // 2.设置文件头：最后一个参数是设置下载文件名
                String suffix=new FileUtils().getFileSuffixName(tmpObj.getFilePath());
                String  newFileName=tmpObj.getFileName()+"."+suffix;
                String fileUrlPath=docFileUploadPath+File.separator+tmpObj.getFilePath();
                File file=new File(fileUrlPath);
                if (file.exists()) {
//                    String dfileName = file.getName();
                    fis = new BufferedInputStream(new FileInputStream( file));
                    response.reset();
                    response.setContentType(CONTENT_TYPE_DEF);
                    response.addHeader(HEADER_NAME_DEFAULT, HEADER_VALUE_DEFAULT);
                    response.addHeader("Content-Length", "" + file.length());
                    response.addHeader("file-name*", URLEncoder.encode(newFileName, "UTF-8"));
                    toClient = new BufferedOutputStream(response.getOutputStream());
                    response.setContentType(CONTENT_TYPE_DEF1);
                    byte[] buffer = new byte[1024 * 1024 * 4];
                    int i = -1;
                    while ((i = fis.read(buffer)) != -1) {
                        toClient.write(buffer, 0, i);
                    }
                    //fis.close();
                    toClient.flush();
                    toClient.close();
                } else {
                    PrintWriter out = response.getWriter();
                    out.print(SCRIPT_HEAD);
                    out.print(STR6);
                    out.print(SCRIPT_TAIL);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if (null !=fis){
                try {
                    fis.close();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
    }
}
