/*
 * Copyright © 2020 北京微金时代科技有限公司
 * <EMAIL>
 */
package cn.mftcc.doc.feign.controller;

import cn.mftcc.doc.components.mould.service.DocTemplateTagCheckService;
import cn.mftcc.doc.feign.api.DocTemplateInterface;
import cn.mftcc.doc.feign.dto.DocTemplateCheckDto;
import cn.mftcc.doc.feign.dto.DocTemplateTagDto;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class DocTemplateInterfaceController implements DocTemplateInterface {

    @Autowired
    private DocTemplateTagCheckService docTemplateTagCheckService;


    /**
     * 同步模板标签配置信息至模板标签校验表中
     *
     * @param docTemplateTagDto
     * @throws Exception
     */
    @Override
    public void syncTemplateTagCheck(@RequestBody  DocTemplateTagDto docTemplateTagDto) throws Exception {
        docTemplateTagCheckService.syncTemplateTagCheck(docTemplateTagDto);
    }

    /**
     * 校验模板标签值
     *
     * @param docTemplateCheckDto
     * @throws Exception
     */
    @Override
    public JSONObject checkTemplateTagValue(@RequestBody  DocTemplateCheckDto docTemplateCheckDto) throws Exception {
        return docTemplateTagCheckService.checkTemplateTagValue(docTemplateCheckDto);
    }
}
