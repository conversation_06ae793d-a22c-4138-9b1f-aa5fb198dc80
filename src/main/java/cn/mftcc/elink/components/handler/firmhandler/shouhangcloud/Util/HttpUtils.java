package cn.mftcc.elink.components.handler.firmhandler.shouhangcloud.Util;

import cn.mftcc.elink.common.util.StringUtil;
import cn.mftcc.elink.components.handler.firmhandler.goodwe.Util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName HttpUtils
 * @Description
 * @Version 1.0
 * <AUTHOR>
 */
@Slf4j
public class HttpUtils {


    public static String doPost(String url, Map<String, Object> jsRequestBody, Map<String, String> reqMap) throws Exception {
        try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            HttpPost httppost = new HttpPost(reqMap.get("USE_URL") + url);
            // 设置头
            httppost.addHeader("Content-Type", "application/json;charset=UTF-8");
            String token = (String)jsRequestBody.get("token");
            if(StringUtil.isNotEmpty(token)){
                httppost.addHeader("token", token);
            }
            String authorization = (String)jsRequestBody.get(Constant.AUTHORIZATION);
            if(StringUtil.isNotEmpty(authorization)){
                httppost.addHeader(Constant.AUTHORIZATION, authorization);
            }
            HashMap<String, Object> req = new HashMap<String, Object>();
            // 公共参数
            req.putAll(jsRequestBody);
            StringEntity strEntity = new StringEntity(com.alibaba.fastjson.JSON.toJSONString(req));
            strEntity.setContentType("application/json");
            httppost.setEntity(strEntity);

            try (CloseableHttpResponse response = httpclient.execute(httppost);
                 InputStream inputStream = response.getEntity().getContent();
                 InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
                 BufferedReader reader = new BufferedReader(inputStreamReader)) {

                StringBuilder result = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    result.append(line);
                }
                return result.toString();
            } catch(Exception e) {
                log.error("调用固德威接口", e);
                throw e;
            }
        }
    }
    /**
     * get
     *
     * @param host
     * @param path
     * @param method
     * @param headers
     * @param querys
     * @return
     * @throws Exception
     */
    public static String doGet(String host, String path, String method,
                               Map<String, String> headers,
                               Map<String, Object> querys)
            throws Exception {
        HttpClient httpClient = wrapClient(host);
        HttpGet request = new HttpGet(buildUrl(host, path, querys));
        for (Map.Entry<String, String> e : headers.entrySet()) {
            request.addHeader(e.getKey(), e.getValue());
        }
        String resultString = "";
        HttpResponse response = httpClient.execute(request);
        if (response.getStatusLine().getStatusCode() == 200) {
            resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
        }
        return resultString;
    }
    private static String buildUrl(String host, String path, Map<String, Object> querys) throws UnsupportedEncodingException {
        StringBuilder sbUrl = new StringBuilder();
        sbUrl.append(host);
        if (!StringUtils.isBlank(path)) {
            sbUrl.append(path);
        }
        if (null != querys) {
            StringBuilder sbQuery = new StringBuilder();
            for (Map.Entry<String, Object> query : querys.entrySet()) {
                if (Constant.ZERO < sbQuery.length()) {
                    sbQuery.append("&");
                }
                if (StringUtils.isBlank(query.getKey()) && query.getValue() != null) {
                    sbQuery.append(query.getValue());
                }
                if (!StringUtils.isBlank(query.getKey())) {
                    Object value = query.getValue();
                    if(value == null){
                        sbQuery.append(query.getKey());
                        sbQuery.append("=");
                        continue;
                    }
                    if (value instanceof List) {
                        for (Object o : (List) value) {
                            sbQuery.append(query.getKey());
                            sbQuery.append("=");
                            sbQuery.append(URLEncoder.encode(cn.mftcc.common.utils.StringUtil.valueOf(o), "utf-8"));
                            sbQuery.append("&");
                        }
                        sbQuery.deleteCharAt(sbQuery.length() - 1);
                    } else {
                        sbQuery.append(query.getKey());
                        if (value != null) {
                            sbQuery.append("=");
                            sbQuery.append(URLEncoder.encode(String.valueOf(value), "utf-8"));
                        }
                    }
                }
            }
            if (0 < sbQuery.length()) {
                sbUrl.append("?").append(sbQuery);
            }
        }

        return sbUrl.toString();
    }
    private static HttpClient wrapClient(String host) {
        HttpClient httpClient = new DefaultHttpClient();
        if (host.startsWith("https://")) {
            sslClient(httpClient);
        }

        return httpClient;
    }

    private static void sslClient(HttpClient httpClient) {
        try {
            SSLContext ctx = SSLContext.getInstance("TLS");
            X509TrustManager tm = new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                @Override
                public void checkClientTrusted(X509Certificate[] xcs, String str) {

                }
                @Override
                public void checkServerTrusted(X509Certificate[] xcs, String str) {

                }
            };
            ctx.init(null, new TrustManager[] { tm }, null);
            SSLSocketFactory ssf = new SSLSocketFactory(ctx);
            ssf.setHostnameVerifier(SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
            ClientConnectionManager ccm = httpClient.getConnectionManager();
            SchemeRegistry registry = ccm.getSchemeRegistry();
            registry.register(new Scheme("https", 443, ssf));
        } catch (KeyManagementException ex) {
            throw new RuntimeException(ex);
        } catch (NoSuchAlgorithmException ex) {
            throw new RuntimeException(ex);
        }
    }
}
