<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.mftcc.doc.components.mould.mapper.DocTemplateTagCheckMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="cn.mftcc.doc.components.mould.entity.DocTemplateTagCheckEntity" id="docTemplateTagCheckMap">
        <result property="templateCheckNo" column="template_check_no"/>
        <result property="templateId" column="template_id"/>
        <result property="templateNo" column="template_no"/>
        <result property="versionNo" column="version_no"/>
        <result property="serialId" column="serial_id"/>
        <result property="keyNo" column="key_no"/>
        <result property="tagKeyName" column="tag_key_name"/>
        <result property="groupFlag" column="group_flag"/>
        <result property="useFlag" column="use_flag"/>
        <result property="checkNullFlag" column="check_null_flag"/>
        <result property="checkRule" column="check_rule"/>
        <result property="prompts" column="prompts"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateOpNo" column="update_op_no"/>
        <result property="updateOpName" column="update_op_name"/>
        <result property="showStyle" column="show_style"/>
    </resultMap>

    <sql id="Base_Column_List">
        template_check_no,
        template_id,
        template_no,
        version_no,
        serial_id,
        key_no,
        tag_key_name,
        group_flag,
        use_flag,
        create_time,
        show_style
    </sql>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into doc_template_tag_check
        (
        <include refid="Base_Column_List"></include>
        ) values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (
            #{item.templateCheckNo},
            #{item.templateId},
            #{item.templateNo},
            #{item.versionNo},
            #{item.serialId},
            #{item.keyNo},
            #{item.tagKeyName},
            #{item.groupFlag},
            #{item.useFlag},
            #{item.createTime},
            #{item.showStyle}
            )
        </foreach>
    </insert>

    <update id="updateBatchById"  parameterType="java.util.List">
        <foreach collection="list" item="item" index="index">
            UPDATE doc_template_tag_check SET
            tag_key_name=#{item.tagKeyName},show_style=#{item.showStyle},group_flag=#{item.groupFlag}
            WHERE template_check_no =#{item.templateCheckNo} ;
        </foreach>
    </update>

</mapper>