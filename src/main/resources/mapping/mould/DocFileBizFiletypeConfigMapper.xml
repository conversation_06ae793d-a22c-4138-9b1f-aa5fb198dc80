<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.mftcc.doc.components.file.mapper.DocFileBizFiletypeConfigMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="cn.mftcc.doc.components.file.entity.DocFileBizFiletypeConfigEntity" id="docFileBizFiletypeConfigMap">
        <result property="scId" column="sc_id"/>
        <result property="bizNo" column="biz_no"/>
        <result property="prdUniqueVal" column="prd_unique_val"/>
        <result property="flowNo" column="flow_no"/>
        <result property="nodeNo" column="node_no"/>
        <result property="typeNo" column="type_no"/>
        <result property="typeName" column="type_name"/>
        <result property="parentTypeNo" column="parent_type_no"/>
        <result property="isEncrypt" column="is_encrypt"/>
        <result property="ifMustRead" column="if_must_read"/>
        <result property="ifUpload" column="if_upload"/>
        <result property="uploadFileLimit" column="upload_file_limit"/>
        <result property="prdVerNo" column="prd_ver_no"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="corpId" column="corp_id"/>
        <result property="mainPrdUniqueVal" column="main_prd_unique_val"/>
    </resultMap>
    <sql id="Base_Column_List">
        sc_id,
        biz_no,
        prd_unique_val,
        flow_no,
        node_no,
        type_no,
        type_name,
        parent_type_no,
        is_encrypt,
        if_must_read,
        if_upload,
        upload_file_limit,
        prd_ver_no,
        create_time,
        update_time,
        corp_id,
        main_prd_unique_val
    </sql>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into doc_file_biz_filetype_config
        (
        <include refid="Base_Column_List"></include>
        ) values
        <foreach collection="list" item="item" index="index"
                 separator=",">
            (
            #{item.scId},
            #{item.bizNo},
            #{item.prdUniqueVal},
            #{item.flowNo},
            #{item.nodeNo},
            #{item.typeNo},
            #{item.typeName},
            #{item.parentTypeNo},
            #{item.isEncrypt},
            #{item.ifMustRead},
            #{item.ifUpload},
            #{item.uploadFileLimit},
            #{item.prdVerNo},
            #{item.createTime},
            #{item.updateTime},
            #{item.corpId},
            #{item.mainPrdUniqueVal}
            )
        </foreach>
    </insert>

</mapper>