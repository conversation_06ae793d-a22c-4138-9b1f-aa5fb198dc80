<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.mftcc.doc.components.mould.mapper.DocBizTemplateMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="cn.mftcc.doc.components.mould.entity.DocBizTemplateEntity" id="docBizTemplateMap">
        <result property="btId" column="bt_id"/>
        <result property="bizNo" column="biz_no"/>
        <result property="subBizNo" column="sub_biz_no"/>
        <result property="templateId" column="template_id"/>
        <result property="templateNo" column="template_no"/>
        <result property="versionNo" column="version_no"/>
        <result property="templateName" column="template_name"/>
        <result property="subTemplateName" column="sub_template_name"/>
        <result property="multiFeign" column="multi_feign"/>
        <result property="multiFeignMethod" column="multi_feign_method"/>
        <result property="parmList" column="parm_list"/>
        <result property="prdUniqueVal" column="prd_unique_val"/>
        <result property="flowNo" column="flow_no"/>
        <result property="nodeNo" column="node_no"/>
        <result property="canWrite" column="can_write"/>
        <result property="ifMustRead" column="if_must_read"/>
        <result property="readFlag" column="read_flag"/>
        <result property="ifMustWrite" column="if_must_write"/>
        <result property="writeFlag" column="write_flag"/>
        <result property="savePath" column="save_path"/>
        <result property="saveFileName" column="save_file_name"/>
        <result property="createUserNo" column="create_user_no"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="createOrgNo" column="create_org_no"/>
        <result property="createOrgName" column="create_org_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="elecSealFlag" column="elec_seal_flag"/>
        <result property="elecSealDt" column="elec_seal_dt"/>
        <result property="elecSealPersons" column="elec_seal_persons"/>
        <result property="ifElectricSign" column="if_electric_sign"/>
        <result property="useSealFlag" column="use_seal_flag"/>
        <result property="docObsId" column="doc_obs_id"/>
        <result property="busRelatedId" column="bus_related_id"/>
        <result property="cusRelatedId" column="cus_related_id"/>
        <result property="applyRelatedId" column="apply_related_id"/>
        <result property="constractRelatedId" column="constract_related_id"/>
        <result property="finRelatedId" column="fin_related_id"/>
        <result property="pdfFlag" column="pdf_flag"/>
        <result property="typeNo" column="type_no"/>
        <result property="typeName" column="type_name"/>
        <result property="pdfObsId" column="pdf_obs_id"/>
        <result property="templateSuffix" column="template_suffix"/>
        <result property="esignFinishFlag" column="esign_finish_flag"/>
        <result property="pledgeInfoFlag" column="pledge_info_flag"/>
    </resultMap>

    <select id="findToDoRemoveFileInfo" resultMap="docBizTemplateMap">
        SELECT
          t.bt_id,
          t.save_path,
          t.save_file_name,
		  t.oss_remove_status
        FROM
         doc_biz_template t
        WHERE
         1=1
	     and t.oss_remove_status = '0'
         and t.save_file_name is not null
         and  t.save_file_name  not in (
          SELECT
           DISTINCT(g.save_file_name)
          FROM
           doc_template_esigner_list g
          WHERE
           g.if_esigned != '2'
            and g.save_file_name is not null
         )
    </select>

    <select id="getDocIdFileByContractIdAndTypeNo" resultType="cn.mftcc.doc.components.file.entity.DocFileInfEntity">
        SELECT
            a.file_id,
            a.type_no,
            a.type_name,
            a.file_path
        FROM
            doc_file_inf a
        WHERE
            a.constract_related_id = #{constractRelatedId}
        AND a.type_no IN (
            SELECT
                b.type_no
            FROM
                doc_biz_template b
            WHERE
                b.constract_related_id = #{constractRelatedId}
        )
</select>
</mapper>