<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.mftcc.doc.components.mould.mapper.DocTemplateEsignerListMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="cn.mftcc.doc.components.mould.entity.DocTemplateEsignerListEntity" id="docTemplateEsignerListMap">
        <result property="id" column="id"/>
        <result property="templateBizId" column="template_biz_id"/>
        <result property="templateNo" column="template_no"/>
        <result property="templateName" column="template_name"/>
        <result property="subTemplateName" column="sub_template_name"/>
        <result property="savePath" column="save_path"/>
        <result property="saveFileName" column="save_file_name"/>
        <result property="bizNo" column="biz_no"/>
        <result property="subBizNo" column="sub_biz_no"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="contractId" column="contract_id"/>
        <result property="subContractId" column="sub_contract_id"/>
        <result property="roleName" column="role_name"/>
        <result property="signShortUrl" column="sign_short_url"/>
        <result property="prdUniqueVal" column="prd_unique_val"/>
        <result property="flowNo" column="flow_no"/>
        <result property="nodeNo" column="node_no"/>
        <result property="positionType" column="position_type"/>
        <result property="keyWord" column="key_word"/>
        <result property="coordinatesX" column="coordinates_x"/>
        <result property="coordinatesY" column="coordinates_y"/>
        <result property="esignerType" column="esigner_type"/>
        <result property="esignType" column="esign_type"/>
        <result property="esigner" column="esigner"/>
        <result property="esignSort" column="esign_sort"/>
        <result property="receiverType" column="receiver_type"/>
        <result property="annexFlag" column="annex_flag"/>
        <result property="ifRead" column="if_read"/>
        <result property="ifEsigned" column="if_esigned"/>
        <result property="readTime" column="read_time"/>
        <result property="submitTime" column="submit_time"/>
        <result property="esignTime" column="esign_time"/>
        <result property="mustEsign" column="must_esign"/>
        <result property="cusBaseType" column="cus_base_type"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="receiverPhone" column="receiver_phone"/>
        <result property="esignerNo" column="esigner_no"/>
        <result property="idNo" column="id_no"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="mustFlag" column="must_flag"/>
        <result property="typeNo" column="type_no"/>
        <result property="typeName" column="type_name"/>
        <result property="ifDownload" column="if_download"/>
        <result property="returnUrl" column="return_url"/>
    </resultMap>

</mapper>