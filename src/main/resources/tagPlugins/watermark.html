<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>模板标签</title>
    <!-- <link rel="stylesheet" type="text/css" th:href="@{/scripts/plugins.css}"/> -->

    <link rel="stylesheet" href="../tag/index.css" type="text/css" />

    <script type="text/javascript" src='../tag/vue.min.js'></script>
    <script type="text/javascript" src='../tag/element-ui.min.js'></script>
    <script type="text/javascript" src='../tag/axios.min.js'></script>
    <script type="text/javascript" src="../tag/watermarkPlugins.js"></script>
    <script type="text/javascript" src="../tag/watermark.js"></script>
    <script type="text/javascript" src="../tag/plugins-ui.js"></script>
    <link rel="stylesheet" href="../tag/plugins.css">
    <script>Vue.config.productionTip=false </script>
</head>
<body id="tagsShow-body">
    <div id="app"></div>

</body>


<script type="text/javascript">

    let serverPrefix="[[${serverPath}]][[${serverName}]]";
    let templateId="[[${templateId}]]";
    let waterMarkContent="[[${waterMarkContent}]]";

    var AscThis;
    new Vue({
        el: '#app',
        data: function () {
            return {
                tagShowFlag:false,
                tagList:{}
            }
        },

        created(){
            // var vthis = this;
            // // vthis.test();
            // window.Asc.plugin.init("任俊庆 2012-10-08 12:30:56");

        },
        mounted() {
            var vthis = this;
            // vthis.test();
            window.Asc.plugin.init(waterMarkContent);
        },
        methods: {


            getTags() {
                var that = this;
                let tagUrl=serverPrefix+"/mould/docTemplateModel/getTagsConfig/"+templateId;

                axios.get(tagUrl, {}, {}).then(function (response) {
                    debugger;
                    that.tagList = response.data.data;

                    console.log(that.tagList);
                    that.tagShowFlag=true;

                }).catch(function (error) {
                    console.log(error);
                });

            },
            addTextTag(tag) {
                console.log(tag);

                Asc.scope.text = "{{"+tag.tag+ "}}";
                this.insertTag();
            },
            //插入循环标签
            addLoopTag(tag) {
                console.log(tag)

                Asc.scope.text = "{{?"+tag.tag+ "}}";
                this.insertTag();
                Asc.scope.text = "{{/"+tag.tag+ "}}";
                this.insertTag();
            },
            addImgTag(tag) {
                console.log(tag)
                Asc.scope.text = "{{@"+tag.tag+ "}}";
                this.insertTag();
            },
            addCheckBoxTag(tag) {
                console.log(tag)
                Asc.scope.text = "{{?"+tag.tag+"}} {{@checkBoxKey}}{{checkBoxValue}} {{/"+tag.tag+"}}";
                this.insertTag();
            },
            addTableTag(tag) {
                // console.log(table, tag)

                // Asc.scope.text = "["+tag.tag+ "]";
                if (tag.type=="text"){
                    Asc.scope.text = "["+tag.tag+ "]";
                }else if(tag.type=="img"){
                    Asc.scope.text = "[@"+tag.tag+ "]";
                }else{
                    return false;
                }

                this.insertTag();
            },
            addTable(table) {

                Asc.scope.text = "{{"+table.tag+ "}}";
                this.insertTag();
            },
            insertTag(){
                if (AscThis.info.editorType=="word"){
                    AscThis.callCommand(function () {
                        var oDocument = Api.GetDocument();
                        var oParagraph = Api.CreateParagraph();
                        oParagraph.AddText(Asc.scope.text);
                        oDocument.InsertContent([oParagraph], true);
                    }, false);
                }else if(AscThis.info.editorType=="cell"){
                    AscThis.callCommand(function () {
                        var oWorksheet = Api.GetActiveSheet();
                        Api.GetSelection().SetValue(Asc.scope.text);
                    }, false);
                }

            }
        }
    })
</script>

</html>