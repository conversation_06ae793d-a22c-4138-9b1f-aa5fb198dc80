<!DOCTYPE html>
<html lang="en"  xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8">
        <title>模板标签</title>
        <!-- <link rel="stylesheet" type="text/css" th:href="@{/scripts/plugins.css}"/> -->

        <link rel="stylesheet" href="../tag/index.css" type="text/css" />

        <script type="text/javascript" src='../tag/vue.min.js'></script>
        <script type="text/javascript" src='../tag/element-ui.min.js'></script>
        <script type="text/javascript" src='../tag/axios.min.js'></script>
        <script type="text/javascript" src="../tag/plugins.js"></script>
        <script>Vue.config.productionTip=false </script>
        </head>
        <body >
            <div id="app" style="
    max-height: 500px;
    overflow: auto;
" >
                <div class="box-card" v-for="(tagType,i) in tagList" >
                    <div class="tagTitle">{{tagType.desc}}</div>
                    <div style="margin: top 4px; "  >
                         <span  v-for="tag in tagType.tags">
                                <!-- 加载文字标签-->
                                <el-button  icon="el-icon-warning-outline" class="el-button-margin-top"   size="mini"  v-if="'text'==tag.type"   :key="tag.key" @click="addTextTag(tag)">{{tag.tag}}</el-button>
                             <!-- 加载图片标签-->
                                <el-button  icon="el-icon-picture-outline" class="el-button-margin-top"   size="mini"   v-if="'img'==tag.type"   :key="tag.key" @click="addImgTag(tag)">{{tag.tag}}</el-button>

                                   <el-dropdown  v-if="'table'==tag.type"  @click="addTextTag(tag)"  @command="addTableTag" split-button   size="mini"  >
                                    {{tag.tag}}
                                    <el-dropdown-menu slot="dropdown" >
                                      <el-dropdown-item v-for="(subtag,index) in tag.value"   :command="subtag"  @click="addTableTag(tag,subtag)" :key="index"  >{{subtag.tag}}</el-dropdown-item>
                                    </el-dropdown-menu>
                                  </el-dropdown>
                             <!-- 加载循环标签-->

                                <el-dropdown  v-if="'loop'==tag.type"  @click="addLoopTag(tag)" @command="addTextTag"  split-button   size="mini" >
                                    {{tag.tag}}
                                    <el-dropdown-menu slot="dropdown" >
                                      <el-dropdown-item v-for="(subtag,index) in tag.value"   :command="subtag"   >{{subtag.tag}}</el-dropdown-item>
                                    </el-dropdown-menu>
                                  </el-dropdown>


                             <!-- 加载勾选项标签-->

                             <el-button   icon="el-icon-circle-check"  size="mini"   v-if="'checkbox'==tag.type"   :key="tag.key" @click="addCheckBoxTag(tag)">{{tag.tag}}</el-button>



                         </span>
                    </div>
                </div>
            </div>


        </body>

    <style>

        .box-card {
            margin-bottom: 10px;
        }
        .box-card .tagTitle{
            font-family: "PingFang SC", "Hiragino Sans GB","Helvetica Neue",Helvetica,"Microsoft YaHei","微软雅黑",Arial,sans-serif;
            font-size:16px;
            height:24px;
            font-weight: 800;
            line-height: 24px;
        }
        .box-card .el-button-margin-top{
            margin-top:12px;
        }
        .box-card .el-dropdown__caret-button{
            margin-top:12px;
        }

    </style>
    <script type="text/javascript">

        let serverPrefix="[[${serverPath}]][[${serverName}]]";
        let templateId="[[${templateId}]]";

        var AscThis;
        new Vue({
            el: '#app',
            data: function () {
                return {
                    tagShowFlag:false,
                    tagList:{}
                }
            },

            created(){

                var vthis = this;
                // vthis.test();
                window.Asc.plugin.init = function () {
                    AscThis = this;
                    vthis.getTags();
                };
                window.Asc.plugin.init();
                window.Asc.plugin.button = function (id) {
                    if (-1 === id) {
                        this.executeCommand("close", "");
                    }
                };
            },
            mounted() {

            },
            methods: {


                getTags() {
                    var that = this;
                    let tagUrl=serverPrefix+"/mould/docTemplateModel/getTagsConfig/"+templateId;

                    axios.get(tagUrl, {}, {}).then(function (response) {
                        debugger;
                        that.tagList = response.data.data;

                        console.log(that.tagList);
                        that.tagShowFlag=true;

                    }).catch(function (error) {
                        console.log(error);
                    });

                },
                addTextTag(tag) {
                    console.log(tag);

                    Asc.scope.text = "{{"+tag.tag+ "}}";
                    this.insertTag();
                },
                //插入循环标签
                addLoopTag(tag) {
                    console.log(tag)

                    Asc.scope.text = "{{?"+tag.tag+ "}}";
                    this.insertTag();
                    Asc.scope.text = "{{/"+tag.tag+ "}}";
                    this.insertTag();
                },
                addImgTag(tag) {
                    console.log(tag)
                    Asc.scope.text = "{{@"+tag.tag+ "}}";
                    this.insertTag();
                },
                addCheckBoxTag(tag) {
                    console.log(tag)
                    Asc.scope.text = "{{?"+tag.tag+"}} {{@checkBoxKey}}{{checkBoxValue}} {{/"+tag.tag+"}}";
                    this.insertTag();
                },
                addTableTag(tag) {
                    // console.log(table, tag)

                    // Asc.scope.text = "["+tag.tag+ "]";
                    if (tag.type=="text"){
                        Asc.scope.text = "["+tag.tag+ "]";
                    }else if(tag.type=="img"){
                        Asc.scope.text = "[@"+tag.tag+ "]";
                    }else{
                        return false;
                    }

                    this.insertTag();
                },
                addTable(table) {

                    Asc.scope.text = "{{"+table.tag+ "}}";
                    this.insertTag();
                },
                insertTag(){
                    if (AscThis.info.editorType=="word"){
                        AscThis.callCommand(function () {
                            var oDocument = Api.GetDocument();
                            var oParagraph = Api.CreateParagraph();
                            oParagraph.AddText(Asc.scope.text);
                            oDocument.InsertContent([oParagraph], true);
                        }, false);
                    }else if(AscThis.info.editorType=="cell"){
                        AscThis.callCommand(function () {
                            var oWorksheet = Api.GetActiveSheet();
                            Api.GetSelection().SetValue(Asc.scope.text);
                        }, false);
                    }

                }
            }
        })
    </script>

</html>