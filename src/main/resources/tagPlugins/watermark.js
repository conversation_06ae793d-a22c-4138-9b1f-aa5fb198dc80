// Example set settings to editors
(function(window, undefined){

    window.Asc.plugin.init = function(markContent)
    {


        if(window.Asc.plugin.markContent==undefined || ""==window.Asc.plugin.markContent || window.Asc.plugin.markContent==null){

            window.Asc.plugin.markContent=markContent;
        }

        var initSettings = {
            copyoutenabled : false,
            watermark_on_draw : JSON.stringify({
                "transparent" : 0.1,
                "type" : "rect",
                "width" : 300,
                "height" : 400,
                "rotate" : -45,
                "margins" : [ 10, 10, 10, 10 ],
                // "fill" : [255, 0, 0],
                // "stroke-width" : 1,
                // "stroke" : [0, 0, 255],
                "align" : 1,

                "paragraphs" : [ {
                    "align" : 2,
                    // "fill" : [255, 0, 0],
                    "linespacing" : 1,

                    "runs" : [
                        {
                            // "text" : "oijhkkll",
                             "text" : window.Asc.plugin.markContent,
                            "fill" : [169,169,169],
                            // "font-family" : "Arial",
                            "font-size" : 50,
                            "bold" : true,
                            "italic" : false,
                            "strikeout" : false,
                            "underline" : false
                        },


                        {
                            "text" : "<%br%>"
                        }
                    ]
                }
                ]
            })
        };


        //
        this.executeMethod("SetProperties", [initSettings], function(markContent) {
            // window.Asc.plugin.executeCommand("close", "");
        });
    };

    window.Asc.plugin.button = function(id)
    {
        // this.executeCommand("close", "");
    };
     // window.Asc.plugin.init();

})(window, undefined);