server:
  port: 8060
spring:
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      maxFileSize: 150MB #单个数据大小
      maxRequestSize: 150MB # 是总数据大小
  thymeleaf:
    prefix: classpath:/tagPlugins/
  mvc:
    static-path-pattern: /tag/**
  resources:
    static-locations: classpath:/tagPlugins/
  application:
    name: mftcc-doc-server
  cloud:
    nacos:
      config:
        server-addr: ${NACOS_ADDRESS:*************:8848}
        namespace: 30a7f7f1-f414-4acc-8a4b-bbb5808727d5
        group: example
        file-extension: yml
        prefix: mftcc-doc-server
        extension-configs[0]:
          data-id: mftcc.yml
          group: example
          refresh: true
        extension-configs[1]:
          data-id: mftcc-feign.yml
          refresh: true
          group: example
        refresh-enabled: true
seata:
  application-id: ${spring.application.name}
  enable-auto-data-source-proxy: true  # 使用多数据源设为false ,普通的情况下true
  tx-service-group: mftcc_tx_group #和seataServer.properties中service.vgroupMapping.mftcc_tx_group 对应
  config:
    type: nacos # nacos 应该是目前比较常用的,以nacos为例子
    nacos:
      server-addr: ${NACOS_ADDRESS:127.0.0.1:8848}
      username:
      password:
      namespace: 30a7f7f1-f414-4acc-8a4b-bbb5808727d5
      # 微服务必须在一个组，否则服务发现不了，但Seata-server 可以在不同的组中
      group: SEATA_GROUP
      dataId: "seataServer.properties"
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: ${NACOS_ADDRESS:127.0.0.1:8848}
      group: SEATA_GROUP
      namespace: 30a7f7f1-f414-4acc-8a4b-bbb5808727d5
      username:
      password: