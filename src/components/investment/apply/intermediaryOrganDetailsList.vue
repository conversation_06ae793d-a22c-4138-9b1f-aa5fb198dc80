<template>
<el-collapse v-model="activeNames" @change="handleChange">
  <el-collapse-item  name="1">
      <template style="margin-top: 20px;" slot="title">
            <div class="mftcc-collapse-title scroll">
              <svg class="icon" aria-hidden="true"  style="">
                <use xlink:href="#icon-neirongyetongyitubiao"></use>
              </svg>
                <span class="titleNameSpan">{{titleName}}</span>
            </div>
      </template>
         <div class="text item" style="width:105%;">
              <mftcc-table :tableId="tableId" :initSearchData="tableInitSearchData" :parentVm="this"
                   ref="intermediaryOrganDetailsList"
                   class="user-table" style="width:100%"></mftcc-table>
         </div>
    </el-collapse-item>
</el-collapse>
</template>

<script>

export default {
  name: 'intermediaryOrganDetailsList',
  title: '中介机构',
  props: {
    estabProjectId:'', //立项项目编码
    cusId: "",
    cusType:''
  },
  data() {
    return {
        tableId:"investment/intermediaryOrganDetailsTable",
         tableInitSearchData:{
           estabProjectId:this.estabProjectId,
           busType:this.cusType
        },
        activeNames: ['1'],
        titleName:"中介机构",
    };
  },
  watch: {
    cusType: {
      deep: true,
      handler(val) {
        this.tableInitSearchData.estabProjectId = this.estabProjectId
        this.tableInitSearchData.busType = this.cusType
        this.tableRefresh()
      },
    },
  },
  computed: {},
  methods: {
    tableRefresh(){
      this.$nextTick(()=>{
        this.$refs.intermediaryOrganDetailsList.refresh();
      })
    }
  },
  created() {},
  mounted() {}
};
</script>
<style scoped src="@/assets/investment/css/publicbusinessstyle.css"/>
