<template>
 <el-card class="box-card">
  <div class="">
    <el-row style="margin-bottom: 15px">
      <el-link
        :underline="false"
        class="afterlabel"

        >尽职调查</el-link>
        <el-link
        :underline="false"
        style="position:absolute; right:25px; font-size: 16px;"
        :class="[clickFlag&&clickShow?'':'disable-click']" @click="clickFlag&&clickShow?toInvestigation():''"
        >
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-tiaozhuan"></use>
        </svg>
        </el-link>
    </el-row>
    <el-row class="setBottom">
      <div class="pClass">
        <div  class="tag" :class="[clickFlag&&clickShow?'':'disable-click']" @click="clickFlag&&clickShow?toInvestigation('项目背景'):''">
          <i class="el-icon-star-off"></i>
          <span> 项目背景</span>
        </div>
        <div  class="tag" :class="[clickFlag&&clickShow?'':'disable-click']" @click="clickFlag&&clickShow?toInvestigation('公司概况'):''">
          <i class="el-icon-star-off"></i>
          <span> 公司概况</span>
        </div>
        <div  class="tag" :class="[clickFlag&&clickShow?'':'disable-click']" @click="clickFlag&&clickShow?toInvestigation('经营情况分析'):''">
          <i class="el-icon-star-off"></i>
          <span> 经营情况分析</span>
        </div>
        <div  class="tag" :class="[clickFlag&&clickShow?'':'disable-click']" @click="clickFlag&&clickShow?toInvestigation('行业分析'):''">
          <i class="el-icon-star-off"></i>
          <span> 行业分析</span>
        </div>
        <div  class="tag" :class="[clickFlag&&clickShow?'':'disable-click']" @click="clickFlag&&clickShow?toInvestigation('财务分析'):''">
          <i class="el-icon-star-off"></i>
          <span> 财务分析</span>
        </div>
        <div  class="tag" :class="[clickFlag&&clickShow?'':'disable-click']" @click="clickFlag&&clickShow?toInvestigation('盈利预测'):''">
          <i class="el-icon-star-off"></i>
          <span> 盈利预测</span>
        </div>
        <div  class="tag" :class="[clickFlag&&clickShow?'':'disable-click']" @click="clickFlag&&clickShow?toInvestigation('调查结论及意见'):''">
          <i class="el-icon-star-off"></i>
          <span> 调查结论及意见</span>
        </div>
      </div>
    </el-row>
  </div>
  </el-card>
</template>

<script>
import investmentApply from "@/api/investment/report/busReportInvestigation";
export default {
  name:'investigationRight',
  data() {
    return {
      wkfAppId:'',
      cusId:'',
      projectState:'',
      clickFlag:false,
    };
  },
  props:{
    estabProjectId:'',
    clickShow: {
      type:Boolean,
      default:false
    },
  },
  methods: {
    getEstabProjectInfo(){
      investmentApply.findByEstabProjectId(this.estabProjectId,res=>{
        if (res.code == 0) {
          let data = res.data;
          this.wkfAppId = data.projectEntity.wkfAppId;
          this.cusId = data.projectEntity.cusId;
          if(data.busReportInvestigationEntity){
            this.clickFlag = true;
          }
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      });
    },
    toInvestigation(obj){
      if(obj){
        this.$router.push({path: '/report/projectReportInsert',
          query: {
            estabProjectId: this.estabProjectId,
            wkfAppId: this.wkfAppId,
            cusId: this.cusId,
            itemName: obj
          }
        });
      }else{
        this.$router.push({path: '/report/projectReportInsert',
          query: {
            estabProjectId: this.estabProjectId,
            wkfAppId: this.wkfAppId,
            cusId: this.cusId
          }
        });
      }

    }
  },
  mounted() {
    this.getEstabProjectInfo()
  },
};
</script>
<style scoped>
  .setBottom {
    margin-top: 35px;
    margin-left: 10px;
  }
  .afterlabel{
    color: #4A7EC3;
    font-weight: 600;
    font-size: 16px;
    margin-left: 5px;
    position: absolute;
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  }
  .icon{
    font-size: 25px;
  }
  .jindiao {
    width: 100%;
    height: 100%;
    position:relative;
  }
  .pClass{
    display: flex;
    flex-wrap: wrap;
  }
  .pClass .el-icon-star-off{
    line-height:normal;
  }
  .tag{
    background-color: #ecf5ff;
    border-color: #d9ecff;
    margin-right: 10px;
    margin-bottom: 10px;
    /*display: inline-block;*/
    /*height: 20px;*/
    padding: 2px 10px;
    /*line-height: 20px;*/
    font-size: 12px;
    color: #409eff;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    box-sizing: border-box;
    white-space: nowrap;
    cursor: pointer;
  }
  .disable-click{
    color: #8cc5ff;
    cursor: not-allowed;
  }
</style>
