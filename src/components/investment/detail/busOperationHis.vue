<template>
  <el-card class="box-card" ref="busOperationHis">
    <div>
      <el-row style="margin-bottom: 15px">
        <el-link
          :underline="false"
          style="
            color: #4a7ec3;
            font-weight: bold;
            font-size: 16px;
            margin-left: 10px;
            margin-top: 10px;
          "
          >信息变更记录</el-link
        >
        <!-- <i class="el-icon-rank" @click="showAllBusOperationHis"></i> -->
        <svg class="icon" aria-hidden="true" @click="showAllBusOperationHis">
          <use xlink:href="#icon-xinxibiangeng"></use>
        </svg>
      </el-row>
      <el-row class="setBottom">
        <el-col
          :span="24"
          style="color: #141414; font-size: 14px; margin-left: 10px"
        >
          管理员采集了标的企业的上游企业信息
          <el-row class="setTop">
            <span style="font-size: 12px; color: #3d3d3d"
              >管理员 2021-09-14 16:05:30</span
            >
          </el-row>
        </el-col>

        <el-col
          :span="24"
          style="color: #141414; font-size: 14px; margin-left: 10px"
        >
          管理员采集了标的企业的财务信息
          <el-row class="setTop">
            <span style="font-size: 12px; color: #3d3d3d"
              >管理员 2021-09-13 16:05:30</span
            >
          </el-row>
        </el-col>
      </el-row>
      <!-- <el-row class="setBottom" v-for="item in listTemp" :key="item.id">
      <el-col :span="24" style="color: #141414; font-size: 14px; margin-left: 10px">
        {{ item.title }}
        <el-row class="setTop">
          <span style="font-size: 12px; color: #3d3d3d"
            >{{ item.opName }} {{ item.date }}</span
          >
        </el-row>
      </el-col>
    </el-row> -->
    </div>
    <!-- 添加弹出框展示操作信息 -->
    <!-- <bus-change-log-details-dialog   ref="busChangeLogDetailsDialog"
                                    :estabProjectId="estabProjectId"></bus-change-log-details-dialog> -->
    <el-dialog
      title="信息变更记录"
      :visible.sync="centerDialogVisible"
      width="80%"
      center
    >
      <ul class="timeline-wrapper">
        <li class="timeline-item" v-for="t in timelineList" :key="t.id">
          <div v-if="t.showFlag == '0'">
            <div>
              <span class="spanTitle">{{ t.month }}</span>
            </div>
            <div class="timeline-box">
              <div class="out-circle">
                <div class="in-circle"></div>
              </div>
              <div class="long-line"></div>
            </div>
            <div class="timeline-content">
              <i
                class="el-icon-arrow-down"
                @click="handleClick(t.clickShowEvent)"
              ></i>
            </div>
          </div>
          <div v-else>
            <div v-if="t.monthShowFlag == 'nineShowFalg'">
              <div>
                <span class="spanTitle">{{ t.time }}</span>
              </div>
              <div class="timeline-box" v-if="nineShowFalg">
                <div class="out-circle">
                  <div class="in-circle"></div>
                </div>
                <div class="long-line"></div>
              </div>
              <div class="timeline-content" v-if="nineShowFalg">
                 <div class="content">
                    <div class="timeline-date"> {{t.date}}</div>
                    <div class="timeline-title">{{ t.title }}</div>
                    <div class="timeline-desc">{{ t.content }}</div>
                 </div>
                 <div  class="divBorder">
                    <div class="timeline-title">{{ t.title }}</div>
                    <div class="timeline-desc">{{ t.content }}</div>
                 </div>
              </div>
            </div>
            <div v-if="t.monthShowFlag == 'eightShowFalg'">
              <div>
                <span class="spanTitle">{{ t.time }}</span>
              </div>
              <div class="timeline-box" v-if="eightShowFalg">
                <div class="out-circle">
                  <div class="in-circle"></div>
                </div>
                <div class="long-line"></div>
              </div>
              <div class="timeline-content" v-if="eightShowFalg">
                 <div class="content">
                    <div class="timeline-date"> {{t.date}}</div>
                    <div class="timeline-title">{{ t.title }}</div>
                    <div class="timeline-desc">{{ t.content }}</div>
                 </div>
                 <div  class="divBorder">
                    <div class="timeline-title">{{ t.title }}</div>
                    <div class="timeline-desc">{{ t.content }}</div>
                 </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </el-dialog>
  </el-card>
</template>

<script>
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    // 这里存放数据
    return {
      estabProjectId: this.estabProjectId,
      centerDialogVisible: false,
      nineShowFalg: true,
      eightShowFalg: true,
      timelineList: [
        {
          date: "20211009 17:30:30",
          title: "立项",
          content: "立项",
          month: "09月",
          showFlag: "0",
          clickShowEvent: "nineShowFalgEvent",
        },
        {
          date: "20211009 17:30:30",
          title: "管理员",
          content: "新增立项业务",
          month: "09月",
          monthShowFlag: "nineShowFalg",
          time:"0909",
        },
        {
          date: "20211009 17:30:30",
          title: "立项",
          content: "立项",
          month: "09月",
          monthShowFlag: "nineShowFalg",
          time:"0908",
        },
        {
          date: "20211009 17:30:30",
          title: "立项",
          content: "立项",
          month: "09月",
          monthShowFlag: "nineShowFalg",
          time:"0908",
        },
        {
          date: "20211009 17:30:30",
          title: "立项",
          content: "立项",
          month: "09月",
          monthShowFlag: "nineShowFalg",
          time:"0907",
        },
        {
          date: "20211009 17:30:30",
          title: "立项",
          content: "立项",
          month: "09月",
          monthShowFlag: "nineShowFalg",
          time:"0907",
        },
        {
          date: "20211009 17:30:30",
          title: "立项",
          content: "立项",
          month: "09月",
          monthShowFlag: "nineShowFalg",
          time:"0907",
        },
        {
          date: "20211009 17:30:30",
          title: "立项",
          content: "立项",
          month: "09月",
          monthShowFlag: "nineShowFalg",
          time:"0907",
        },
        {
          date: "20211009 17:30:30",
          title: "立项",
          content: "立项",
          month: "09月",
          monthShowFlag: "nineShowFalg",
          time:"0905",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          showFlag: "0",
          clickShowEvent: "eightShowFalgEvent",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
        {
          date: "20211008 17:30:30",
          title: "立项",
          content: "立项",
          month: "08月",
          monthShowFlag: "eightShowFalg",
        },
      ],
    };
  },
  props: ["estabProjectId"],
  methods: {
    handleClick(i) {
      let event = i;
      if (event == "nineShowFalgEvent") {
        this.nineShowFalgEvent();
      } else if (event == "eightShowFalgEvent") {
        this.eightShowFalgEvent();
      }
    },

    //获取展示内容
    findCusChangeInfo() {
      api.findCusChangeInfo(
        this.cusId,
        (res) => {
          if (res.code === 0) {
            this.listTemp = res.list;
          } else {
            this.$alert(res.msg, this.$alertTitle, {
              type: res.msgType,
              dangerouslyUseHTMLString: true,
              callback: (action) => {},
            });
          }
        },
        (error) => {}
      );
    },
    //展示所有的业务操作历史
    showAllBusOperationHis() {
      this.centerDialogVisible = true;
    },
    //9月
    nineShowFalgEvent() {
      if (this.nineShowFalg == false) {
        this.nineShowFalg = true;
      } else {
        this.nineShowFalg = false;
      }
    },
    //8月
    eightShowFalgEvent() {
      if (this.eightShowFalg == false) {
        this.eightShowFalg = true;
      } else {
        this.eightShowFalg = false;
      }
    },
  },
  mounted() {
    // this.findCusChangeInfo();
    // $bus.$on("refreshChangeInfo", () => {
    //     this.findCusChangeInfo();
    //   });
  },
};
</script>
<style scoped src="@/assets/investment/css/publicbusinessstyle.css"/>
<style scoped>
  .icon{
    font-size: 21px;
    position: absolute;
    right: 30px;
  }
</style>
