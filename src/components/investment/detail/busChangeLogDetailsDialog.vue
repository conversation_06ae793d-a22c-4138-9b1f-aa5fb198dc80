<template>
  <div class="mftcc-container">
    <el-dialog
    title="提示"
    :visible.sync="centerDialogVisible"
    width="60%"
    center>
    <el-steps direction="vertical" :active="1">
       <el-step title="步骤 1"></el-step>
       <el-step title="步骤 2"></el-step>
       <el-step title="步骤 3" description="这是一段很长很长很长的描述性文字"></el-step>
    </el-steps> 
   </el-dialog>
  </div>
</template>
<script>

export default {
  components: {
    
  },
  name: "busChangeLogDetailsDialog",
  provide(){
    
  },
  data() {
    return {
       centerDialogVisible: false,
    };
  },
  methods: {
    showBusDialog(){
      this.centerDialogVisible=true;
    },
   
  },
  mounted() {
    
  },
};
</script>
