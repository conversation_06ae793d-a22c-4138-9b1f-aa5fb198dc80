<template>
 <el-card class="box-card">
  <div class="touho">
    <el-row style="margin-bottom: 15px">
      <el-link
        :underline="false"
        class="afterlabel"
      
        >投后管理</el-link>
        <!-- font-weight: bold; -->
        <el-link
        :underline="false"
        style="position:absolute; right:25px; font-size: 16px;"
        >
        <svg class="icon" aria-hidden="true">
          <use xlink:href="#icon-tiaozhuan"></use>
        </svg>
        </el-link>
    </el-row>
    <el-row class="setBottom">
      <el-row style="font-size: 25px">
      <el-col :span="6" style="margin-left: 20px;"><span class="mainInfo_amt_span">0.00</span></el-col>
      <el-col :span="6" style="margin-left: 20px;"><span class="mainInfo_amt_span">12</span></el-col>
      <el-col :span="6" style="margin-left: 20px;"><span class="mainInfo_amt_span">0.00</span></el-col>
      </el-row>
      <el-row style="font-size: 10px;">
        <el-col :span="6" style="margin-left: 20px">投资金额(万)</el-col>
        <el-col :span="6" style="margin-left: 20px;">投资期限(月)</el-col>
        <el-col :span="6" style="margin-left: 20px;">已投金额(万)</el-col>
      </el-row>
    </el-row>
  </div>
  </el-card>
</template>

<script>
export default {
  data() {
    return {
      listTemp:{}
    };
  },
  props:["cusId"],
  methods: {
    findCusChangeInfo(){
        api.findCusChangeInfo(this.cusId,res => {
            if(res.code === 0){
                this.listTemp = res.list;
            }else{
                this.$alert(res.msg, this.$alertTitle, {
                    type: res.msgType,
                    dangerouslyUseHTMLString: true,
                    callback: action => {}
                });
            }
        },error => {});
    }
  },
  mounted() {

  },
};
</script>
<style scoped>
  .setBottom {
    margin-top: 35px;
    margin-left: 20px;
    /* position:absolute; */
  }
  .afterlabel{
    color: #4A7EC3; font-weight: 600; font-size: 16px; margin-left: 5px;position:absolute;
    font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
  }
  .icon{
    font-size: 25px;
  }
  .touho {
    width: 100%;
    height: 100%;
    position:relative;
  }

</style>
