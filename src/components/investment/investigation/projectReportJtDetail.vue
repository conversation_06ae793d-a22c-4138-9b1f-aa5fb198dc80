<template>
    <div>
        <!-- 项目成员信息 -->
        <project-team-detail-list v-if="tableShow" :estabProjectId="estabProjectId" :tableId="tableId" :busType="busType" titleName="尽调成员"></project-team-detail-list>
        <!--尽调任务分配-->
        <task-due-detail-list v-if="taskShow" :estabProjectId="estabProjectId"></task-due-detail-list>
        <!-- 尽调审核历史信息 -->
        <busdiligence-approve-his :wkfAppId="wkfAppId" :estabProjectId="estabProjectId"></busdiligence-approve-his>
        <!-- 尽调问题清单信息 -->
        <el-card class="box-card">
            <due-template-show-compontent :estabProjectId="estabProjectId" editFlag="0"></due-template-show-compontent>
        </el-card>
        <!--尽调资料-->
        <el-card class="box-card">
            <due-file-show-compontent :estabProjectId="estabProjectId" :cusId="cusId" editFlag="0" flowCode='responsibleInvestigationView'></due-file-show-compontent>
        </el-card>
        <!--尽调审批资料-->
        <el-card class="box-card">
            <project-report-jt-approve-file :estabProjectId="estabProjectId" :cusId="cusId" editFlag="0"></project-report-jt-approve-file>
        </el-card>
        <!--风控部风控岗-->
        <el-card class="box-card">
          <due-file-show-compontent :estabProjectId="estabProjectId" :cusId="cusId" editFlag="0" flowCode='risk_control_flow_node'></due-file-show-compontent>
        </el-card>
    </div>
</template>
<script>
import busProjectReportJt from "@/api/investment/investigation/busProjectReportJt";
import ProjectTeamDetailList from '@/components/investment/apply/projectTeamDetailList';
import InvestmentEstablishmentHis from '@/components/investment/flow/investmentEstablishmentHis';
import InvestmentDoc from '@/components/investment/apply/investmentDoc';
import TaskDueDetailList from "@/components/investment/investigation/taskDueDetailList";
import BusdiligenceApproveHis from "@/components/investment/flow/busdiligenceApproveHis";
import DueTemplateShowCompontent from "@/views/investment/investigation/dueTemplateShowCompontent";
import DueFileShowCompontent from "@/views/investment/investigation/dueFileShowCompontent";
import ProjectReportJtApproveFile from "@/views/investment/investigation/projectReportJtApproveFile";

export default {
    props: {
        estabProjectId: '',
        wkfAppId: '',
        cusId: '',
        taskShow: {
          type:Boolean,
          default:false
        },
    },
    components: {
        ProjectReportJtApproveFile,
        DueFileShowCompontent,
        DueTemplateShowCompontent,
        BusdiligenceApproveHis,
        TaskDueDetailList,
        ProjectTeamDetailList,
        InvestmentEstablishmentHis,
        InvestmentDoc,
    },
    data() {
        // 这里存放数据
        return {
            busType: '',
            tableShow:false,
            tableId: 'investment/busInvestmentProjectTeamDetailTable'
        };
    },
    created() {
        this.getBusType()
    },
    methods: {
        // 获取尽调项目小组小组类型、中介机构的业务类型（使用的是业务主流程节点编号）
        getBusType() {
            busProjectReportJt.getDetailNodeNo(null, res => {
                if (res.code == 0) {
                    this.busType = res.data
                    this.tableShow = true
                } else {
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true
                    });
                }
            })
        },
    },
};
</script>
<style scoped>
.box-card  >>> .el-card__header {
    padding: 0px !important;
}
.box-card  >>> .el-card__body {
    padding: 0px !important;
}
</style>
