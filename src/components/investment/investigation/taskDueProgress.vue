<template>
  <el-progress :percentage="row[fieldName]"  text-inside="false"
                    stroke-width="14" :color="customColorMethod"></el-progress>
</template>
<script>
export default {
  data() {
    return {};
  },
  props: {
    fieldName: {
      type: String,
      required: true
    },
    row: {
      type: Object,
      required: true
    }
  },
  methods: {
    //改变进度条的颜色  
    customColorMethod(percentage) {
      if (percentage < 30) {
        return "#909399";
      } else if (percentage < 70) {
        return "#e6a23c";
      } else {
        return "#67c23a";
      }
    },
} 
};
</script>