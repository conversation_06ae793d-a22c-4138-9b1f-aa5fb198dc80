<template>
    <el-collapse v-model="activeNames" @change="handleChange">
        <el-collapse-item name="1">
            <template style="margin-top: 20px;" slot="title">
                <div class="mftcc-collapse-title scroll">
                    <svg class="icon" aria-hidden="true"  style="">
                        <use xlink:href="#icon-neirongyetongyitubiao"></use>
                    </svg>
                    <span class="titleNameSpan">{{ titleName }}</span>
                </div>
            </template>
            <div class="text item">
              <mftcc-table :tableId="tableId" :initSearchData="tableInitSearchData" :parentVm="this"
                   ref="busDueTaskDetailList"
                   class="user-table"></mftcc-table>
            </div>
            <task-due-record-dialog ref="taskDueRecordDialog" :estab-project-id="estabProjectId"></task-due-record-dialog>
        </el-collapse-item>
    </el-collapse>
</template>
<script>
import TaskDueRecordDialog from "@/views/investment/investigation/taskDueRecordDialog";
import establishmentProjectApi from '@/api/investment/apply/investmentApply';
export default {
    name: 'projectTeamDetailList',
    components: {
        TaskDueRecordDialog
    },
    props: {
        estabProjectId: '',
        cusId: '',
    },
    data() {
        return {
            tableId: "investment/busDueTaskDetailTable",
            activeNames: ['1'],
            titleName: "尽调任务",
            tableInitSearchData: {
                estabProjectId: this.estabProjectId,
            },
            tableOption:{
                pageNo:1,
                pageSize:10,
                total:0
            },
            dicArray:[],
            tableData: []
        }
    },
    created() {

    },
    methods: {
     //跳转尽职调查页面
     seeTaskDueTuneOut(){
        establishmentProjectApi.findById(this.estabProjectId,res=>{
          if(res.code === 0){
            //获取立项对象
            let  busEstablishmentProject = res.data;
            this.$router.push({path: '/report/projectReportInsert',
             query: {
               estabProjectId: busEstablishmentProject.estabProjectId,
               wkfAppId: busEstablishmentProject.wkfAppId,
               cusId: busEstablishmentProject.cusId
             }
            });
          }else{
            this.$alert(res.msg, this.$alertTitle,{
              type: res.msgType,
              dangerouslyUseHTMLString: true
            });
          }
        })
    },
      getTable(){
        //获取立项主信息
          establishmentProjectApi.findById(this.estabProjectId,res => {
                if(res.code === 0){
                    var data = res.data;
                    if(data.bizTaskDefId == "setup_team"){
                        this.tableId = "investment/busDueTaskDetailTable1"
                    }else{
                        this.tableId = "investment/busDueTaskDetailTable"
                    }
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            //this.$router.back(-1);
                        }
                    });
                }
            },error => {
               // this.$router.back(-1);
            });

      },
    },
    mounted() {
        this.getTable()
    },
}
</script>
<style scoped src="@/assets/investment/css/publicbusinessstyle.css"/>
<style scoped>
.myTable >>> .el-table__body{
  padding-bottom: 20px;
}
</style>
