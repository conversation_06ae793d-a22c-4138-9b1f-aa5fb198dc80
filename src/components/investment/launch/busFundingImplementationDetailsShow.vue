<template>
    <div class="mftcc-container">
        <el-header><div class="mftcc-form-header">放款前提条件落实</div></el-header>
        <el-container>
            <el-row>
                <mftcc-form :formId="formId" :parentVm="this" ref="busFundingImplementationDetailsForm"></mftcc-form>\
                <!-- 要件 -->
                <div class="mftcc-web">
                    <fileMultiShowCompnoent v-if="docShowFlag" :paramList="paramList"></fileMultiShowCompnoent>
                </div>
            </el-row>
        </el-container>
    </div>
</template>
<script>
import api from '@/api/investment/launch/busFundingImplementation'
import fileMultiShowCompnoent from '@/components/doc/file/fileMultiShowCompnoent';
export default {
    name: 'busFundingImplementationDetailsShow',
    title: '项目资金投放_划款前提条件落实情况详情',
    props: ['estabProjectId','wkfAppId','implementationId','busExceptionsId'],
    components: {
        fileMultiShowCompnoent: fileMultiShowCompnoent,
    },
    data() {
        return {
            docShowFlag: false,
            formId: 'investment/busFundingImplementationDetailsForm'
        }
    },
    methods: {
        findById(){
            let implementationId = this.implementationId;
            api.findImplementationById(implementationId,res => {
                if(res.code === 0){
                    var data = res.data;
                    this.formId = data.formId;
                     this.$nextTick(() => {
                        this.$refs.busFundingImplementationDetailsForm.setFormValue(data.implementationEntity);
                        this.$refs.busFundingImplementationDetailsForm.setFormValue("statementContent", "郑重声明：审核人员只依据申报部门提供的资料进行形式要件审核，不对其真实准确性和合法有效性负责。");
                    });
                  this.getFileParamList(data.implementationEntity);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            },error => {
                this.$router.back(-1);
            });
        },
        getFileParamList(reqData) {
                api.getFileParamList(reqData, res => {
                    if (res.code === 0) {
                        this.paramList = res.list;
                        this.docShowFlag = true;
                    } else {
                        this.$alert(res.msg, this.$alertTitle, {
                            type: res.msgType,
                            dangerouslyUseHTMLString: true,
                            callback: action => {
                            }
                        });
                    }
                }, error => {
                });
        },
        //回调函数
        approvalCallback(callback) {
            let data = this.$refs.busFundingImplementationDetailsForm.getFormValue();
            return callback(data);
        },
        back(){
            this.$router.back(-1);
        }
    },
    mounted() {
        this.findById();
    }
}
</script>
