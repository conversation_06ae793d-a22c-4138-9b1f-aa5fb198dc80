<template>
<el-collapse v-model="activeNames" @change="handleChange">
  <el-collapse-item  name="1">
    <template style="margin-top: 20px;" slot="title">
            <div class="mftcc-collapse-title scroll">
                <svg class="icon" aria-hidden="true"  style="">
                    <use xlink:href="#icon-neirongyetongyitubiao"></use>
                </svg>
                <span class="titleNameSpan">内部审核详情</span>
            </div>
    </template>
     <el-row>
        <mftcc-form v-if="firstTableFlag" :formId="projectFormId" :parentVm="this" ref="busFundingProjectApproveDetailsForm"></mftcc-form>
        <mftcc-form v-if="showFormFlag" :formId="formId" :parentVm="this" ref="busFundingFinrstOrRoutineShowForm"></mftcc-form>
    </el-row>
    </el-collapse-item>
</el-collapse>

</template>
<script>
import api from '@/api/investment/launch/busFundingApply';
import approveApi from '@/api/investment/launch/busFundingFirstApprove';
import routineApi from '@/api/investment/launch/busFundingRoutineApprove';
export default {
    name: 'busFundingApplyShowDetail',
    title: '内部审核详情',
    props: ["exceptionsId","estabProjectId","fundingFlag","exceptionApproveId","fundFlag"],
    components: {},
    data() {
        return {
            estabProjectId: this.estabProjectId,
            display: false,
            formId:"investment/busFundingFirstDetailsForm",
            fundingFlag:this.fundingFlag,
            exceptionApproveId:this.exceptionApproveId,
            busExceptionsId:this.exceptionsId,
            fundFlag:this.fundFlag,
            showFormFlag:false,
            projectFormId:"investment/busFundingProjectDetailsForm",
            activeNames:['1']
        }
    },

    methods: {
         //转账申请信息返显
    getInitData() {
      debugger
      // 根据id返显详情
      if(this.fundFlag == '0'){
        //首次
          this.showFormFlag = true;
          this.firstTableFlag = true;
          this.formId="investment/busFundingFirstDetailsForm";
          if(this.busExceptionsId == undefined){
            return false;
          }else{
            this.getprojectReportApproveDetail();
          }
      }else{
        //常规
          this.showFormFlag = true;
          this.firstTableFlag = true;
          this.formId="investment/busFundingRoutineApproveDetailsForm";
           if(this.busExceptionsId == undefined){
              return false;
           }else{
               this.getfundRoutineApproveDetail();
           }
      }
    },
        //获取放款首次出资事项信息
        getprojectReportApproveDetail(){
            let  data = {};
            data.busExceptionsId = this.busExceptionsId;
            data.estabProjectId = this.estabProjectId;
            approveApi.getFundingFirstShowDetail(data,res => {
                if(res != null){
                    var data = res.data;
                    this.showFormFlag = true;
                    this.$nextTick(()=>{
                        this.$refs.busFundingProjectApproveDetailsForm.setFormValue(data);
                        this.$refs.busFundingProjectApproveDetailsForm.setFormValue(res.busContractData);
                        this.$refs.busFundingFinrstOrRoutineShowForm.setFormValue(data);
                        this.$refs.busFundingFinrstOrRoutineShowForm.setFormValue("amountContract",res.busContractData.amountContract);
                        this.$refs.busFundingFinrstOrRoutineShowForm.setFormValue("surplusAmt",res.busContractData.amountUseable);
                    });
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            },error => {
                this.$router.back(-1);
            });
        },

        //获取常规出资事项信息
        getfundRoutineApproveDetail(){
            let  data = {};
            data.busExceptionsId = this.busExceptionsId;
            data.estabProjectId = this.estabProjectId;
            routineApi.getFundRoutineShowDetail(data,res => {
                if(res != null){
                    var data = res.data;
                    this.showFormFlag = true;
                    this.$nextTick(()=>{
                        this.$refs.busFundingFinrstOrRoutineShowForm.setFormValue(data);
                        this.$refs.busFundingProjectApproveDetailsForm.setFormValue(data);
                        this.$refs.busFundingProjectApproveDetailsForm.setFormValue(res.busContractData);
                    });
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            },error => {
                this.$router.back(-1);
            });
        },



        back(){
            this.$router.back(-1);
        }
    },


    mounted(){
        this.getInitData();
    }
}
</script>
<style scoped src="@/assets/investment/css/projectBusDetailstyle.css"/>
