<template>
    <el-card class="box-card">
    <div class="mftcc-web" >
        <!-- 要件配置 -->
        <!-- <fileMultiShowCompnoent :paramList="paramList"></fileMultiShowCompnoent> -->
        <mftcc-all-file v-if="docShowFlag" :docParameter="docParameter"></mftcc-all-file>
    </div>
    </el-card>
</template>
<script>
    import api from '@/api/investment/launch/busFundingApply';
    import fileMultiShowCompnoent from '@/components/doc/file/fileMultiShowCompnoent';
import mftccAllFile from '@/components/doc/file/mftccAllFile';
    export default {
        // import引入的组件需要注入到对象中才能使用
        components: {
            fileMultiShowCompnoent: fileMultiShowCompnoent,
            mftccAllFile:mftccAllFile,
        },
        data() {
            // 这里存放数据
            return {
                paramList: [],
                showFlag: false,
                docShowFlag:false,
                docParameter:{}
            };
        },
        props: [ "estabProjectId","exceptionsId" ],
        methods: {
            getFileParamList() {
                let reqData = {
                    estabProjectId: this.estabProjectId,
                    exceptionsId:this.exceptionsId
                };
                api.getFundingApplyFileParamList(reqData, res => {
                    if (res.code === 0) {
                        this.docParameter = res.list;
                        this.docShowFlag = true;
                    } else {
                        this.$alert(res.msg, this.$alertTitle, {
                            type: res.msgType,
                            dangerouslyUseHTMLString: true,
                            callback: action => {
                            }
                        });
                    }
                }, error => {
                });
            }
        },
        mounted() {
            this.getFileParamList();
        }
    }
</script>
<style scoped>
   .box-card  >>> .el-card__header {
     padding: 0px !important;
   }
   .box-card  >>> .el-card__body {
     padding: 0px !important;
   }
</style>

