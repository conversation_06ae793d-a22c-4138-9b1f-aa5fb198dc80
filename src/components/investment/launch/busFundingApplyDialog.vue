<template>
  <div>
    <mftcc-dialog-form
        :formId="formId"
        title="转账申请信息"
        :show.sync="formShow"
        :center="true"
        :parentVm="this"
        class="parm-mole-dialog"
        ref="busFundingApplyDetailForm"

    ></mftcc-dialog-form>
  </div>
</template>
<script>
import api from '@/api/investment/launch/busFundingApply'

export default {
  components: {},
  props: {estabProjectId: "", transferId: ''},
  data() {
    return {
      formShow: false,
      applyId: "",
      agencyId: "",
      busType: '',
      formId:'investment/busFundingApplyDetailForm'
    };
  },
  created() {
  },
  watch: {},
  computed: {},
  methods: {

    //转账申请信息返显
    showDialog(id) {
      // 根据id返显详情
      api.findById(id, res => {
        if (res.code === 0) {
          this.formShow = true;
          this.$nextTick(() => {
            this.$refs.busFundingApplyDetailForm.getFormRef((form) => {
              form.resetForm(() => {
                // 表单赋值
                form.setFormValue(res.data);
              });
            })
          });
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      })
    },

  },
  mounted() {
  }
};
</script>
<style lang="scss" scoped>

</style>
