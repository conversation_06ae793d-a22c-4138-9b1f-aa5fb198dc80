<template>
    <div class="mftcc-container">
        <el-header><div class="mftcc-form-header">资金拨付申请详情</div></el-header>
        <div class="mftcc-form-tips"><i class="el-icon-warning-outline"></i>说明：带*号的为必须项信息，请填写完整</div>
        <el-container>
            <el-row>
                <mftcc-form :formId="formId" :parentVm="this" ref="busFundingRoutineDetailForm"></mftcc-form>
                <!-- 要件 -->
                <div class="mftcc-web">
                    <fileMultiShowCompnoent v-if="docShowFlag" :paramList="paramList"></fileMultiShowCompnoent>
                </div>
            </el-row>
        </el-container>
    </div>
</template>
<script>
import api from '@/api/investment/launch/busFundingRoutine'
import fileMultiShowCompnoent from '@/components/doc/file/fileMultiShowCompnoent';
export default {
    name: 'busFundingRoutineDetailShow',
    title: '资金拨付申请详情',
    props: ["exceptionsId","estabProjectId","wkfAppId","busExceptionsId"],
    components: {
        fileMultiShowCompnoent: fileMultiShowCompnoent,
    },
    data() {
        return {
            formId: "investment/busFundingRoutineInsertForm",
            docShowFlag: false,
        }
    },
    methods: {
        getEstablishmentDetail(){
            let  data = {};
            // TODO待修改
            data.exceptionsId = this.busExceptionsId;
            data.estabProjectId = this.estabProjectId;
            api.getRountineDetail(data,res => {
                if(res.code === 0){
                    let result = res.data;
                    this.formId = result.formId;
                    this.$nextTick(() => {
                        this.$refs.busFundingRoutineDetailForm.setFormValue(result.routine);
                        this.$refs.busFundingRoutineDetailForm.setFormValue(result.contract);
                        this.$refs.busFundingRoutineDetailForm.setFormValue("depositBankNos", result.contract.depositBankNo);
                        this.$refs.busFundingRoutineDetailForm.setFormValue("surplusAmt", result.contract.amountUseable);
                    });
                    this.getFileParamList(data);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true
                    });
                }
            })
        },
        getFileParamList(reqData) {
                api.getFileParamList(reqData, res => {
                    if (res.code === 0) {
                        this.paramList = res.list;
                        this.docShowFlag = true;
                    } else {
                        this.$alert(res.msg, this.$alertTitle, {
                            type: res.msgType,
                            dangerouslyUseHTMLString: true,
                            callback: action => {
                            }
                        });
                    }
                }, error => {
                });
        },
        //回调函数
        approvalCallback(callback) {
            let data = this.$refs.busFundingRoutineDetailForm.getFormValue();
            return callback(data);
        },
        back(){
            this.$router.back(-1);
        }
    },
    mounted() {
        this.getEstablishmentDetail();
    }
}
</script>
