<template>
<el-collapse v-model="activeNames" @change="handleChange">
  <el-collapse-item  name="1">
    <template style="margin-top: 20px;" slot="title">
            <div class="mftcc-collapse-title scroll">
                <svg class="icon" aria-hidden="true"  style="">
                  <use xlink:href="#icon-neirongyetongyitubiao"></use>
                </svg>
                <span class="titleNameSpan">转账申请详情</span>
            </div>
    </template>
     <el-row>
        <mftcc-form v-if="showFormFlag"  formId="investment/busFundingApplyShowDetailForm" :parentVm="this" ref="busFundingApplyDetailForm"></mftcc-form>
    </el-row>
    </el-collapse-item>
</el-collapse>

</template>
<script>
import api from '@/api/investment/launch/busFundingApply'
export default {
    name: 'busFundingApplyShowDetail',
    title: '转账申请详情',
    props: ["legalTrialId","exceptionsId","estabProjectId"],
    components: {},
    data() {
        return {
            estabProjectId: "",
            display: false,
            showFormFlag:false
        }
    },

    mounted() {

    },
    methods: {
         //转账申请信息返显
    showDialog() {
      // 根据id返显详情
     let obj ={estabProjectId:this.estabProjectId,exceptionsId:this.exceptionsId}
      api.getFundingApplyDetail(obj, res => {
        if (res.code === 0) {
          if(res.data.allFlag == 'N'){
             this.showFormFlag = false;
          }else{
            this.showFormFlag = true;
            this.$nextTick(() => {
            this.$refs.busFundingApplyDetailForm.setFormValue(res.data.entityData);
          });
          }

        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      })
    },
        back(){
            this.$router.back(-1);
        }
    },


    mounted(){
        this.showDialog()
    }
}
</script>
