<template>
  <el-collapse  v-model="activeNames"   @change="handleChange">
    <div v-for = '(item,index) in msgLists' :key='item'>
      <el-collapse-item  :name="index">
        <template style="margin-top: 20px;" slot="title">
            <div class="mftcc-collapse-title scroll">
                <svg class="icon" aria-hidden="true"  style="">
                    <use xlink:href="#icon-neirongyetongyitubiao"></use>
                </svg>
                <span class="titleNameSpan">资金投放({{item.exceptionNo}})</span>
            </div>
        </template>
      <el-row>
        <div class="wrapper">
           <!--内部审核信息 -->
          <bus-funding-show-detail   :estabProjectId="estabProjectId"  :exceptionsId="item.approveId" :fundingFlag="item.fundingFlag" :scheduleId="scheduleId" :exceptionApproveId="item.exceptionApproveId" :fundFlag="item.fundFlag"></bus-funding-show-detail>
           <!--转账申请信息 -->
           <!-- <bus-funding-apply-show-detail   :estabProjectId="estabProjectId"  :exceptionsId="item.approveId" :scheduleId="scheduleId"></bus-funding-apply-show-detail> -->
           <!--内部审核流程 -->
           <bus-funding-approve-his   :wkfAppId="wkfAppId" :approveId ="item.excepApproveId" :fundingFlag="item.fundingFlag" :exceptionApproveId="item.exceptionApproveId"></bus-funding-approve-his>
           <!--转账审批历史信息 -->
           <!-- <transfer-approve-his   :wkfAppId="wkfAppId" :estabProjectId="estabProjectId"  :exceptionsId="item.approveId"></transfer-approve-his> -->
           <!--集团流程信息-->
           <bus-group-process-list   :estabProjectId="estabProjectId" flowName="internal_audit"   :scheduleId="scheduleId"  :wkfAppId="wkfAppId" :showStyleFlag="true" :exceptionsId="item.approveId"></bus-group-process-list>
           <!-- 要件信息-内部审核 -->
           <bus-funding-doc :estabProjectId="estabProjectId" :exceptionsId="item.approveId" ></bus-funding-doc>
           <!-- 要件信息-转账 -->
          </div>
       </el-row>
    </el-collapse-item>
  </div>
</el-collapse>
</template>
<script>
import BusFundingApplyShowDetail from '@/components/investment/launch/busFundingApplyShowDetail';
import busFundingDoc from '@/components/investment/launch/busFundingDoc';
import TransferApproveHis from '@/components/investment/flow/transferApproveHis';
import BusGroupProcessList from '@/components/investment/group/busGroupProcessLossList';
import BusFundingApproveHis from '@/components/investment/flow/busFundingFirstOrRouteApproveHis';
import api from '@/api/investment/launch/busFundingRoutineApprove';
import BusFundingShowDetail from '@/components/investment/launch/busFundingFinrstOrRoutineShowDetail';



export default {
  components: {
      busFundingDoc,
      TransferApproveHis,
      BusGroupProcessList,
      BusFundingApproveHis,
      BusFundingApplyShowDetail,
      BusFundingShowDetail
  },
     //父页面向子页面传参
     props:["estabProjectId","checkApproveId","layoutShowProjectFlag","wkfAppId","cusId"],
  data() {
    return {
        traceNo:'',
        layoutShowProjectFlag:'',
        wkfAppId:this.wkfAppId,
        scheduleId:'-1',
        msgLists:[1,2],
        activeNames:['1']
    };
  },
  watch: {},
  computed: {},
  methods: {
     newId(){
        this.scheduleId = '...';
      },
      //获取初始化数据
      getParamList(){
          let param ={
            wkfAppId:this.estabProjectId
          }
          api.getBusFundApplyDetail(param,res => {
                if(res.code === 0){
                    let data = res.data;
                    if(data.length == 0){
                       var tempParam = {};
                       var array= [];
                       tempParam.estabProjectId = this.estabProjectId;
                       tempParam.approveId = "";
                       array.push(tempParam) ;
                       this.msgLists = array;
                    }else{
                       this.msgLists = data;
                    }
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {

                        }
                    });
                }
            },error => {
               // this.$router.back(-1);
            });
      }
  },
  created() {},
  mounted() {
      this.newId();
      this.getParamList();
  }
};
</script>

<style scoped src="@/assets/investment/css/publicbusinessstyle.css"/>
