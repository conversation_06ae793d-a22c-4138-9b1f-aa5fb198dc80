<template>
<el-collapse v-model="activeNames" @change="handleChange">
  <el-collapse-item  name="1">
    <template style="margin-top: 20px;" slot="title">
            <div class="mftcc-collapse-title scroll">
                <i class="el-icon-star-on"></i>
                <span class="titleNameSpan">{{titleName}}</span>
            </div>
    </template>
    <el-row>
        <mftcc-table tableId="investment/busFundingApplyDetailTable" :parentVm="this" ref="busFundingApplyDetailTable" class="user-table" :initSearchData="tableInitSearchData"></mftcc-table>
        <bus-funding-apply-dialog @callback="teamInsertCallBackFunc" ref="busFundingApplyDialog" :wkfAppId="transferId"></bus-funding-apply-dialog>
    </el-row>
    </el-collapse-item>
</el-collapse>
</template>

<script>
import api from '@/api/investment/launch/busFundingApplyApprove';
import applyApi from '@/api/investment/launch/busFundingApply';
import busFundingApplyDialog from '@/components/investment/launch/busFundingApplyDialog';

export default {
  name: 'busFundingDetailList',
  title: '放款-转账申请列表',
  props:["estabProjectId","scheduleId"],
  components: {
      busFundingApplyDialog
  },

  data() {
    return {
      activeNames: ['1'],
      titleName:"转账申请信息",
      tableInitSearchData:{

        },
    }
  },
   watch:{
        scheduleId:{
            handler(val){

                this.tableInitSearchData.estabProjectId=this.estabProjectId
                this.tableRefresh()
            },
        }
    },
  methods: {
    tableRefresh(data) {
        this.$refs.busFundingApplyDetailTable.refresh()
    },
    query(index, row){
      this.$router
                .push({path: '/launch/busFundingApplyApproveDetails', query: {applyHisId:row.applyHisId}});
    },
    handleChange(val) {

        },
     // 选择列表信息
    selectbusFundingData(index,row){
        this.$refs.busFundingApplyDialog.showDialog(row.transferId);
    },
  },
  mounted() {

  }
}



</script>
