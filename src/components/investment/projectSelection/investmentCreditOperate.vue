<template>
 <el-card class="box-card">
  <div slot="header" class="clearfix">
   <!-- 左侧页签 -->
 <el-row>
  <el-col :span="24">
  <div class="grid-content bg-purple">
  <div class="box" :style="dynamicPosition">
    <div class="mft_list_tabs">
      <keep-alive>
        <el-menu
          :default-active="activeIndex"
          mode="horizontal"
          @select="handleSelect"
        >
          <el-menu-item index="1">
            <div class="detrans">企业信息</div>
          </el-menu-item>
          <el-menu-item index="2">
            <div class="detrans">企业走访</div>
          </el-menu-item>
<!--          <el-menu-item index="3">
            <div class="detrans">董监高管理</div>
          </el-menu-item>
          <el-menu-item index="4">
            <div class="detrans">股权分红</div>
          </el-menu-item>-->
          <el-menu-item index="5">
            <div class="detrans">报告管理</div>
          </el-menu-item>
          <el-menu-item index="6">
            <div class="detrans">日常管理</div>
          </el-menu-item>
        </el-menu>
      </keep-alive>
    </div>
  </div>
  </div>
  </el-col>
 </el-row>
 </div>
</el-card>
</template>
<script>
import uuid from 'uuid';
export default {
    name: 'investmentCreditOperate',
    title: '操作按钮页面',
    data() {
        return {
            activeIndex: "1",
            stopDisabled:false,
            fileDisabled:false,
            flowId:uuid.v4().replace(/-/g,''),
            projectState:this.projectState,
            corpFlagShow:true,
            busFlagShow:false,
            reportFlagShow:false,
            projectFlagShow: false,
            pactFlagShow: false,
            pundFlagShow :false,

        }
    },
    props: ["estabProjectId",,"cusId","projectState"],
    inject:['changeViewShowFlag'],
    methods: {
        handleSelect(key, keyPath) {
        this.activeIndex = key;
        let viewData={};
        viewData.layoutShowCorpFlag=false;
        viewData.companyShow=false;
        if (key == "1") {
          viewData.companyShow=false;
          viewData.layoutShowCorpFlag=true;
        } else if (key == "2") {
          viewData.layoutShowCorpFlag=false;
          viewData.companyShow=true;
        } else if (key == "5") {
          viewData.reportShow=true;
        }else if (key == "6") {
          viewData.everydayShow=true;
        }

        //提交至父级页面
         this.changeViewShowFlag(viewData);
       },
       //根据项目状态控制标签显示隐藏
      updateLabelShow(){
        //通过权限控制按钮下信息的默认展示
        if($hasPerm('target_enterprise_info')){
            this.activeIndex= "1";
            this.handleSelect(this.activeIndex,'');
        }else if($hasPerm('project_application_info')){
            this.activeIndex= "2";
            this.handleSelect(this.activeIndex,'');
        }
        if(this.projectState == '1' || this.projectState == '2'){
          //立项录入、立项审核中
          this.busFlagShow = true;
          this.reportFlagShow = false;
          this.projectFlagShow = false;
          this.pactFlagShow = false;
          this.pundFlagShow = false;
        }else if(this.projectState == '3'){
          //尽职调查中
          this.busFlagShow = true;
          this.reportFlagShow = true;
          this.projectFlagShow = false;
          this.pactFlagShow = false;
          this.pundFlagShow = false;

        }else if(this.projectState == '4'){
          //项目审批中
          this.busFlagShow = true;
          this.reportFlagShow = true;
          this.projectFlagShow = true;
          this.pactFlagShow = false;
          this.pundFlagShow = false;

        }else if(this.projectState == '5'){
          //合同、审批否决
          this.busFlagShow = true;
          this.reportFlagShow = true;
          this.projectFlagShow = true;
          this.pactFlagShow = true;
          this.pundFlagShow = false;

        }else if(this.projectState == '7'){
          //资金投放
           this.busFlagShow = true;
          this.reportFlagShow = true;
          this.projectFlagShow = true;
          this.pactFlagShow = true;
          this.pundFlagShow = true;
        }

      },


       stopInfo () {
          this.stopDisabled = true;
       },
       filePrint(){
         this.fileDisabled = true;
       }
    },
    mounted() {
        // this.updateLabelShow();
    },
    created(){
      //this.getRecordTableData();
    },
}
</script>
<style scoped src="@/assets/investment/css/appDetailMainFram.css"/>
