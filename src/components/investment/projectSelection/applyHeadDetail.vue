<template>
     <div>
       <!-- 项目主信息 -->
        <bus-apply-main-info-show :estabProjectId="estabProjectId"></bus-apply-main-info-show>
       <!-- 操作按钮区 -->
       <investment-operate :estabProjectId="estabProjectId" :projectState="projectState"></investment-operate>
    </div>
</template>
<script>
import BusApplyMainInfoShow from '@/components/investment/apply/busApplyMainInfoShow';
import InvestmentOperate from '@/components/investment/projectSelection/investmentOperate';
export default {
    //由父级页面向子级页面传参
    props:["estabProjectId","bizMark","wkfAppId","cusId","projectState"],
    // import引入的组件需要注入到对象中才能使用
    components: {
      BusApplyMainInfoShow,
      InvestmentOperate
    },
    data () {
      // 这里存放数据
      return {
        traceNo:this.wkfAppId,
        projectState:this.projectState
      };
    },
    //用于子页面调用父页面方法
    provide () {
      return {

      }
    },
    // 监听属性 类似于data概念
    computed: {

    },
    //初始化数据
    initData(){
       //获取数据并重新赋值

    },
    // 监控data中的数据变化
    watch: {

    },
    // 方法集合
    methods: {

    },
    // 生命周期 - 创建之前
    mounted() {
      //声明全局的修改方法
      // $bus.$on("updateLayoutShowBusFlag", (layoutShowBusFlag) => {
      //     this.layoutShowBusFlag=layoutShowBusFlag;
      // });
    },
    beforeMount () {
    }, // 生命周期 - 挂载之前
    beforeUpdate () {
    }, // 生命周期 - 更新之前
    updated () {
    }, // 生命周期 - 更新之后
    beforeDestroy () {
    }, // 生命周期 - 销毁之前
    destroyed () {
    }, // 生命周期 - 销毁完成
    activated () {
    } // 如果页面有keep-alive缓存功能，这个函数会触发
  };
</script>
<style scoped src="@/assets/investment/css/publicbusinessstyle.css"/>
