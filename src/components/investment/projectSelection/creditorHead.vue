<template>
  <el-card class="box-card template-color">
    <div slot="header" class="clearfix template-color">
      <el-row>
        <el-col :span="24">
          <div class="grid-content bg-purple template-color">
            <div class="box" :style="dynamicPosition">
              <div class="mft_head_tabs">
                <keep-alive>
                  <el-menu   :default-active="activeIndex" mode="horizontal" @select="handleSelect">
                    <el-menu-item index="1">
                      <div class="detrans">债权投资台账</div>
<!--                      <div class="detrans" style="border-radius: 5px">债权投资台账</div>-->
                    </el-menu-item>
<!--                    <el-menu-item index="2">
                      <div class="detrans">债权</div>
                    </el-menu-item>-->
                    <!-- <el-menu-item index="3">
                      <div class="detrans">企业走访</div>
                    </el-menu-item>
                    <el-menu-item index="4">
                      <div class="detrans">董监高管理</div>
                    </el-menu-item> -->
                  </el-menu>
                </keep-alive>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
<script>
import uuid from "uuid";
export default {
  name: "creditorHead",
  title: "债权头部按钮页面",
  data() {
    return {
      activeIndex: "1",
      stopDisabled: false,
      fileDisabled: false,
      flowId: uuid.v4().replace(/-/g, ""),
      firstFlagShow: true,
      secondFlagShow: true,
      thirdFlagShow: true,
      fourthFlagShow: true
    };
  },
  methods: {
    handActive(val){
      alert("xuanzhong+"+val);
      this.activeIndex = val;
       this.$emit("getFormData",data);
    },
    handleSelect(key, keyPath) {
      this.activeIndex = key;
      this.$emit("isShow",key);
      // if (key == "1") {
      //   this.$router.push({
      //     path: "/projectSelection/projectManagementEquityList"
      //   });
      // } else if (key == "2") {
      //   // viewData.layoutShowBusFlag = true;
      //   this.$router.push({
      //     path: "/projectSelection/projectManagementEquityDetails"
      //   });
      // } else if (key == "3") {
      //   viewData.layoutShowReportFlag = true;
      // } else if (key == "4") {
      //   viewData.layoutShowProjectFlag = true;
      // }
      // //提交至父级页面
      // this.changeViewShowFlag(viewData);
    },
    //根据项目状态控制标签显示隐藏
    updateLabelShow() {
      //通过权限控制按钮下信息的默认展示
      if ($hasPerm("target_enterprise_info")) {
        this.activeIndex = "1";
        this.handleSelect(this.activeIndex, "");
      } else if ($hasPerm("project_application_info")) {
        this.activeIndex = "2";
        this.handleSelect(this.activeIndex, "");
      }
      if (this.projectState == "1" || this.projectState == "2") {
        //立项录入、立项审核中
        this.secondFlagShow = true;
        this.thirdFlagShow = false;
        this.fourthFlagShow = false;
        this.pactFlagShow = false;
        this.pundFlagShow = false;
      } else if (this.projectState == "3") {
        //尽职调查中
        this.secondFlagShow = true;
        this.thirdFlagShow = true;
        this.fourthFlagShow = false;
        this.pactFlagShow = false;
        this.pundFlagShow = false;
      } else if (this.projectState == "4") {
        //项目审批中
        this.secondFlagShow = true;
        this.thirdFlagShow = true;
        this.fourthFlagShow = true;
        this.pactFlagShow = false;
        this.pundFlagShow = false;
      }
    },
    stopInfo() {
      this.stopDisabled = true;
    },
    filePrint() {
      this.fileDisabled = true;
    }
    //初始化按钮是够可操作
    //打开完善资料选择弹框
  },
  mounted() {
    // this.updateLabelShow();
  },
  created() {
    //this.getRecordTableData();
  }
};
</script>
<style scoped src="@/assets/investment/css/appDetailMainFram.css"/>
<style scoped>
.p-10 {
  padding: 10px;
  margin-left: 10px;
  margin-right: 15px;
}

.mft_head_tabs .el-menu {
  border-bottom: none;
  margin-left: 2px;
  /* pointer-events: all; */
}

.mft_head_tabs .el-menu-item {
  color: #FFFFFF;
  /* 最小宽度不够字体放大会导致长度变化，或者使用定宽 */
  /* min-width: 200px; */
  text-align:center;
  width: 140px;
  height: 50px;
  line-height: 50px;
  background-color: #399CFF;
  border: 1px solid #399CFF;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

</style>
