<template>
  <div>
    <div style="padding-top: 20px;font-size: large;font-weight: 600;text-align: center;">已投企业架构树</div>
      <el-input
            placeholder="输入关键字进行过滤"
            v-model="filterText"
            suffix-icon="el-icon-search"
            style="margin: 10px;width: -webkit-fill-available;background-color: #e9ece2;"
          ></el-input>
          <el-tree
            class="filter-tree borderbt"
            :data="cusList"
            :props="defaultProps"
            default-expand-all
            :filter-node-method="filterNode"
            :highlight-current="true"
            :check-on-click-node="true"
            :expand-on-click-node="false"
            @node-click="testTree"
            style="font-size: 12px;font-weight:400;"
            ref="tree"
          ></el-tree>
  </div>
</template>
<script>
import uuid from "uuid";
import api from "@/api/investment/projectSelection/busInventoryProjectEquity";
export default {
  name: "cusTree",
  title: "股权头部按钮页面",
  data() {
    return {
      filterText: "",
      defaultProps: {
        children: "children",
        label: "cusName"
      },
      cusList : [],
      // cusList: [
      //   {
      //     id: 1,
      //     name: "集团1",
      //     sonList: [
      //       {
      //         id: 101,
      //         name: "子公司101",
      //         sonList: [
      //           { id: 10101, name: "子公司10101" },
      //           { id: 10202, name: "子公司10202" }
      //         ]
      //       },
      //       { id: 101, name: "子公司102" }
      //     ]
      //   },
      //   { id: 2, name: "集团2" },
      //   { id: 3, name: "集团3" }
      // ]
    };
  },
  props: ["estabProjectId", , "cusId", "projectState"],
   watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    testTree(data) {
      this.$emit("getTree",data);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    findCus() {
      api.findCus(
        "",
        res => {
          if (res.code === 0) {
            var data = res.data;
            if(data.length>0){
              this.$emit("getCus",data[0].cusId);
            }
            this.cusList = data;
          } else {
            this.$alert(res.msg, this.$alertTitle, {
              type: res.msgType,
              dangerouslyUseHTMLString: true,
              callback: action => {
                this.$router.back(-1);
              }
            });
          }
        },
        error => {
          this.$router.back(-1);
        }
      );
    },
  },
  mounted() {
     this.findCus();
  },
  created() {
  }
};
</script>
<style scoped src="@/assets/investment/css/appDetailMainFram.css"/>
<style scoped>
.borderbt {
  border-bottom: 1px solid #e9e9e9;
}
</style>
