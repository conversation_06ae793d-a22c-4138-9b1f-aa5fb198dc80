<template>
 <el-card class="box-card">
  <div slot="header" class="clearfix">
   <!-- 左侧页签 -->
 <el-row>
  <el-col :span="24">
  <div class="grid-content bg-purple">
  <div class="box" :style="dynamicPosition">
    <div class="mft_list_tabs" style="border-bottom:gainsboro 1px solid;width: 100%" >
      <keep-alive>
        <el-menu
          :default-active="activeIndex"
          mode="horizontal"
          @select="handleSelect"
        >
          <el-menu-item index="1" id="change-menu" >
            <div :class="detrans" ref="line" @click="toggleTab('baseInfo')"><a  class="span-line">基本信息管理</a></div>
          </el-menu-item>
          <el-menu-item index="2" id="change-menu">
            <div :class="detrans" ref="line" @click="toggleTab('compInfo')"><a class="span-line">企业走访</a></div>
          </el-menu-item>
          <el-menu-item index="3" id="change-menu">
            <div class="detrans" ref="line" @click="toggleTab('djgManage')"><a  class="span-line">董监高管理</a></div>
          </el-menu-item>
          <el-menu-item index="4" id="change-menu">
            <div class="detrans" ref="line" @click="toggleTab('bonusShare')"><a  class="span-line">股权分红</a></div>
          </el-menu-item>
          <el-menu-item index="5" id="change-menu">
            <div class="detrans" ref="line" @click="toggleTab('reportManage')"><a  class="span-line">报告管理</a></div>
          </el-menu-item>
        </el-menu>
      </keep-alive>
    </div>
  </div>
  </div>
  </el-col>
 </el-row>
 </div>
</el-card>
</template>
<script>
import uuid from 'uuid';
export default {
    name: 'investmentOperate',
    title: '操作按钮页面',
    data() {
        return {
            activeIndex: "1",
            stopDisabled:false,
            fileDisabled:false,
            flowId:uuid.v4().replace(/-/g,''),
            projectState:this.projectState,
            corpFlagShow:true,
            busFlagShow:false,
            reportFlagShow:false,
            projectFlagShow: false,
            pactFlagShow: false,
            pundFlagShow :false,

        }
    },
    props: ["estabProjectId","cusId","projectState"],
    inject:['changeViewShowFlag'],
    methods: {
      toggleTab: function(tab) {
        this.currentTab = tab; // tab 为当前触发标签页的组件名
        //标签页切换时变色及下划线
        if(this.currentTab =="baseInfo"){
          this.detrans1 = "tabline";
          this.detrans2 = "tab";
        }
        if(this.currentTab =="compInfo"){
          this.detrans1 = "tab";
          this.detrans2 = "tabline";
        }
      },
        handleSelect(key, keyPath) {
        this.activeIndex = key;
        let viewData={};
        viewData.layoutShowCorpFlag=false;
        viewData.companyShow=false;
        viewData.djgShow=false;
        viewData.bonusShow = false;
        viewData.reportShow = false;
        if (key == "1") {
          viewData.layoutShowCorpFlag=true;
        } else if (key == "2") {
          viewData.companyShow=true;
        } else if (key == "3") {
          viewData.djgShow=true;
        } else if (key == "4") {
          viewData.bonusShow=true;
        } else if (key == "5") {
          viewData.reportShow=true;
        }
        //提交至父级页面
         this.changeViewShowFlag(viewData);
       },
       //根据项目状态控制标签显示隐藏
      updateLabelShow(){
        //通过权限控制按钮下信息的默认展示
        if($hasPerm('target_enterprise_info')){
            this.activeIndex= "1";
            this.handleSelect(this.activeIndex,'');
        }else if($hasPerm('project_application_info')){
            this.activeIndex= "2";
            this.handleSelect(this.activeIndex,'');
        }
        if(this.projectState == '1' || this.projectState == '2'){
          //立项录入、立项审核中
          this.busFlagShow = true;
          this.reportFlagShow = false;
          this.projectFlagShow = false;
          this.pactFlagShow = false;
          this.pundFlagShow = false;
        }else if(this.projectState == '3'){
          //尽职调查中
          this.busFlagShow = true;
          this.reportFlagShow = true;
          this.projectFlagShow = false;
          this.pactFlagShow = false;
          this.pundFlagShow = false;

        }else if(this.projectState == '4'){
          //项目审批中
          this.busFlagShow = true;
          this.reportFlagShow = true;
          this.projectFlagShow = true;
          this.pactFlagShow = false;
          this.pundFlagShow = false;

        }else if(this.projectState == '5'){
          //合同、审批否决
          this.busFlagShow = true;
          this.reportFlagShow = true;
          this.projectFlagShow = true;
          this.pactFlagShow = true;
          this.pundFlagShow = false;

        }else if(this.projectState == '7'){
          //资金投放
           this.busFlagShow = true;
          this.reportFlagShow = true;
          this.projectFlagShow = true;
          this.pactFlagShow = true;
          this.pundFlagShow = true;
        }

      },


       stopInfo () {
          this.stopDisabled = true;
       },
       filePrint(){
         this.fileDisabled = true;
       }
    },
    mounted() {
        // this.updateLabelShow();
    },
    created(){
      //this.getRecordTableData();
    },
}
</script>
<style scoped src="@/assets/investment/css/appDetailMainFram.css"/>
<style scoped>
 .span-line:hover{
   text-decoration: underline;
   color: #1c02ff;
 }
 a {
   color:#1a1a1a;
   text-decoration: none;
 }

 .el-menu-item.is-active >>> .span-line{
    color: blue;
 }

 #active{
  color: blue;
 }
.span-line:link{
  color: #1a1a1a;
}
.span-line:visited{
  color: blue;
  text-decoration: underline;
}
#change-menu{
  background-color: white;
  border: none;
}
 /**tab不带下划线 */
 .tab{
   float: left;
   margin-left: 25px;
 }
 /**tab带下划线 */
 .tabline{
   float: left;
   margin-left: 25px;
   /* text-decoration: underline; */
   color: #409eff;
   border-bottom: 2px solid;
 }
</style>
