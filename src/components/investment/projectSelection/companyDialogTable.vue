<template>
  <el-dialog
      :visible.sync="visible"
      @close="$emit('update:show', false)"
      @open="opened"
      :width="width"
      :fullscreen="fullscreen"
      :center="center"
      append-to-body
      ref="dialog"
      class="mftcc-table-dialog"
      title="客户列表">
    <div class="dialog-content">
      <div class="mftcc-dialog-content">
        <mftcc-table
            v-if="tableShow"
            tableId="investment/projectCusTable"
            :parentVm="this"
            ref="dialogList">
        </mftcc-table>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submit">确 定</el-button>
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>

export default {
  name:'companyDialogTable',
  data() {
    return {
      initData: {},
      visible: this.show,
      tableShow: false,
    }
  },
  components: {
  },
  props: {
    show: {
      type: Boolean,
      default() {
        return false;
      }
    },
    width: {
      type: String,
      default() {
        return "1000px";
      }
    },
    height: {
      type: String,
      default() {
        return "600px";
      }
    },
    fullscreen: {
      type: Boolean,
      default() {
        return false;
      }
    },
    center: {
      type: Boolean,
      default() {
        return false;
      }
    },
  },
  watch: {
    show () {
      let _this = this
      _this.visible = _this.show;
      if(_this.visible === true){
        _this.$set(_this.initData,"isSelect",false)
        _this.$set(_this.initData,"tableData",[])
        _this.$set(_this.initData,"isPagination",true)
        _this.$set(_this.initData,"url",$servers.investment+"/projectSelection/busInventoryProjectEquity/findProjectCusByPage")
        _this.tableShow = true;
        _this.$nextTick(() => {
          _this.$refs.dialogList.initTable()
        })
      }
    }
  },
  methods: {
    submit() {
      let value = this.$refs.dialogList.getCurrentValue()
      this.$emit('callback', value);
      this.visible = false;
    },
    opened() {
      this.tableShow = true
      this.$refs.dialog.$el.children[0].style.height = this.height;
    },
    closed() {
      this.tableShow = false
      this.visible = false;
      this.show = false
    },
  }
}
</script>
<style scoped>
.mftcc-table-dialog .mftcc-form-search {
  padding: 0 20px;
}

.mftcc-table-dialog .search_form_div {
  display: flex;
  align-items: flex-start;
}

.mftcc-table-dialog .dialog-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.mftcc-table-dialog .mftcc-search-form .search-button button {
  float: right;
}

.mftcc-table-dialog .mftcc-dialog-content {
  flex-grow: 1;
  display: flex;
  padding-top: 0px !important;
}

.mftcc-table-dialog .el-dialog {
  display: flex;
  flex-direction: column;
}

.mftcc-table-dialog .el-dialog__body {
  flex-grow: 1;
  height: 100%;
}

</style>
