<template>
    <el-card class="box-card">
        <div slot="header" class="clearfix">
            <el-row class="main_info_header_container">
                <!-- 项目主信息---头部信息 -->
                <div class="mainInfoTopTip">

                </div>
                <!-- 项目主信息---主要内容部分 -->
                <div>
                    <table>
                        <tr>
                            <td>
                                <div :style="{ backgroundImage:'url('+require('@/assets/investment/detail/'+Img)+')'}"
                                     class="mainInfoTopTip_leftImg" id="amgDiv">
                                    <span>项目信息</span>
                                </div>
                            </td>
                            <td>
                                <div class="mainInfoTopTip_div">
                                    <div class="mainInfoTopTip_itemName">
                                        <span class="mainInfoTopTip_itemName">项目名称：</span>
                                        <span class="mainInfoTopTip_itemName_prt">{{this.loanApplyDetail.projectName }}</span>
                                    </div>
                                    <td class="mainInfo_amt">
                                    <el-row>
                                        <el-col :span="8"><span
                                        class="mainInfo_amt_span">{{ this.loanApplyDetail.investmentAmount }}</span> 万
                                        </el-col>
                                        <el-col :span="8"><span
                                        class="mainInfo_amt_span">{{ this.loanApplyDetail.termShow }}</span></el-col>
                                        <el-col :span="8"><span class="mainInfo_amt_span">{{ investmentedAmount }}</span>万</el-col>
                                    </el-row>
                                    <el-row>
                                        <el-col :span="8">投资金额</el-col>
                                        <el-col :span="8">投资期限</el-col>
                                        <el-col :span="8">已投金额</el-col>
                                    </el-row>
                                    </td>
                                    <el-row class="mainInfo_bottom">
                                        <el-col :span="6">
                                            <span class="mainInfo_bottom_itemName">
                                                <img
                                                class="dateinfo_icon"
                                                :src="'/fh-web/static/images/layout/info/dateinfo-1.png'"
                                                />
                                                立项日期：{{ this.loanApplyDetail.createDate.substring(0,10) }}</span>
                                        </el-col>
                                        <el-col :span="6">
                                            <span class="mainInfo_bottom_itemName">
                                                <img
                                                class="personinfo_icon"
                                                :src="'/fh-web/static/images/layout/info/personinfo-1.png'"
                                                />
                                                项目经办人：{{ projectCreateName }}</span>
                                        </el-col>
                                        <el-col :span="6">
                                            <span class="mainInfo_bottom_itemName">
                                                <img
                                                class="coordinationinfo_icon"
                                                :src="'/fh-web/static/images/layout/info/personinfo-1.png'"
                                                />
                                                协同项目：{{ this.loanApplyDetail.collaborative }}</span>
                                        </el-col>
                                        <el-col :span="6">
                                            <span class="mainInfo_bottom_itemName">
                                                <img
                                                class="discussioninfo_icon"
                                                :src="'/fh-web/static/images/layout/info/personinfo-1.png'"
                                                />
                                                重点客户：{{ this.loanApplyDetail.priority }}</span>
                                        </el-col>
                                        </el-row>
                                </div>
                            </td>

                    </tr>
            </table>
        </div>
        </el-row>
        </div>
    </el-card>
</template>
<script>
import api from '@/api/investment/apply/investmentApply';
import calcUtil from '@/utils/calcUtil';

export default {
    data() {
        return {
            loanApplyDetail: {},
            Img: "applyBgImg.png",
            applyMainInfo: {
                applyCnt: "0",
                contractIngCnt: "0",
                contractFinishCnt: "0"
            },
            cusNo: "",
            applyCntVisible: false,
            contractIngCntVisible: false,
            contractFinishCntVisible: false,
            cntTableData: {cusId: this.cusId},
            investmentedAmount:0,
            projectCreateName: ""
        }
    },
    props: ["estabProjectId","investmentMode"],
    methods: {
        // applyCntOpen () {
        //   this.applyCntVisible = true;
        // },
        // contractIngCntOpen () {
        //   this.contractIngCntVisible = true;
        // },
        // contractFinishCntOpen () {
        //   this.contractFinishCntVisible = true;
        // },
        findById() {
            let estabProjectId = this.estabProjectId;
            api.findById(estabProjectId, res => {
                if (res.code === 0) {
                    var data = res.data;
                    data.investmentAmount = calcUtil.divide(data.investmentAmount, 10000);
                    data.investmentAmount = calcUtil.formatMoney(data.investmentAmount, 2);
                    if(data.isCollaborative=='0'){
                        data.collaborative = '否';
                    }else{
                        data.collaborative = '是';
                    }
                    if(data.isPriority=='0'){
                        data.priority = '否';
                    }else{
                        data.priority = '是';
                    }
                    this.loanApplyDetail = data;
                } else {
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            //this.$router.back(-1);
                        }
                    });
                }
            }, error => {
                //this.$router.back(-1);
            });
        },
        findInvestmentedAmount(){
            let data = {investmentMode:this.$route.query.investmentMode,estabProjectId:this.estabProjectId};
            api.findInvestmentedAmount(data,res=>{
                if (res.code === 0) {
                    var data = res.data;
                    data.investmentedAmount = calcUtil.divide(data.investmentedAmount, 10000);
                    data.investmentedAmount = calcUtil.formatMoney(data.investmentedAmount, 2);
                    this.investmentedAmount = data.investmentedAmount;
                    this.projectCreateName = data.projectCreateName;
                } else {
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                        }
                    });
                }
            })
        }
    },
    mounted() {
        this.findById();
        this.findInvestmentedAmount();
    }
}
</script>
<style scoped src="@/assets/investment/css/appDetailMainFram.css"/>
<style scoped>
.box-card >>> .el-card__header {
    padding: 0px !important;
}

.box-card >>> .el-card__body {
    padding: 0px !important;
}
</style>

