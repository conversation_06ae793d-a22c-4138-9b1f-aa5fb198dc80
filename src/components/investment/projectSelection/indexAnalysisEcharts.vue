<template>
  <div class="mftcc_home_box">
    <el-row :gutter="12" style="margin-top: 12px">
      <el-col :span="8">
        <div class="list_box_item">
          <div class="list_title" style="">投资规模总览</div>
          <div class="list_date">统计截止：{{ newDate }}</div>
          <div class="line_bar_box">
            <div id="amtEchart" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="list_box_item">
          <div class="list_title" style="">投资类型占比</div>
          <div class="line_bar_box">
            <div id="e2Echart" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="list_box_item">
          <div class="list_title" style="">投资行业分布</div>
          <div class="line_bar_box">
            <div id="e3Echart" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="12" style="margin-top: 12px">
      <el-col :span="8">
        <div class="list_box_item">
          <div class="list_title" style="">投资企业性质占比</div>
          <div class="line_bar_box">
            <div id="e4Echart" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="list_box_item">
          <div class="list_title" style="">项目类型占比</div>
          <div class="line_bar_box">
            <div id="e5Echart" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="list_box_item">
          <div class="list_title" style="">投资区域占比</div>
          <div class="line_bar_box">
            <div id="e6Echart" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import api from "@/api/investment/projectSelection/busInventoryProjectEquity";
import * as echarts from "echarts";
import dateUtilsApi from "@/utils/dateUtils";
import calcUtils from '@/utils/calcUtil';
export default {
  name: "indexAnalysisEcharts",
  title: "股权头部按钮页面",
  props: ["param"],
  data() {
    return {
      activeNameFlg: "0",
      activeName: "first",
      activeIndex: "1",
      param: {},
      newDate: "",
    };
  },
  watch: {
    param: {
      handler(val) {
        this.getIndexAnalysis();
      },
    },
  },
  mounted() {
    this.getIndexAnalysis();
    this.getNewDate();
  },
  methods: {
    getNewDate() {
      this.newDate = dateUtilsApi.getNewDate();
    },
    getIndexAnalysis() {
      let param = this.param;
      api.getIndexAnalysis(
        param,
        (res) => {
          if (res.code === 0) {
            var data = res.data;
            // document.getElementById("amtEchart").innerHTML = "";
            // document.getElementById("e2Echart").innerHTML = "";
            // document.getElementById("e3Echart").innerHTML = "";
            // document.getElementById("e4Echart").innerHTML = "";
            // document.getElementById("e5Echart").innerHTML = "";
            // document.getElementById("e6Echart").innerHTML = "";
            if (data.e1 != undefined) {
              this.amtEchart(data.e1);
            }
            if (data.e2 != undefined) {
              this.e2Echart(data.e2);
            }
            if (data.e3 != undefined) {
              this.e3Echart(data.e3);
            }
            if (data.e4 != undefined) {
              this.e4Echart(data.e4);
            }
            if (data.e5 != undefined) {
              this.e5Echart(data.e5);
            }
            if (data.e6 != undefined) {
              if(param.investmentMode=="0101"&&param.i=="0"){
                this.e6Echart(data.e6);
              }else{
                this.e6Echart(data.e6);
              }
            }
          } else {
            this.$alert(res.msg, this.$alertTitle, {
              type: res.msgType,
              dangerouslyUseHTMLString: true,
              callback: (action) => {
                this.$router.back(-1);
              },
            });
          }
        },
        (error) => {
          this.$router.back(-1);
        }
      );
    },
    // 投资规模总览
    amtEchart(data) {
      var dateSub = [];
      var amt = [];
      var count = [];
      for (var i = 0; i < data.length; i++) {
        dateSub.push(data[i].dateSub);
        let bal = calcUtils.divide(data[i].amt,1000);
        amt.push(bal);
        count.push(data[i].count);
      }
      var option;
      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
        },
        legend: {
          data: ["投资金额", "项目总数"],
          bottom: "5%",
        },
        xAxis: [
          {
            type: "category",
            data: dateSub,
            axisPointer: {
              type: "shadow",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "单位：万元",
            splitNumber: 5,
            minInterval: 1,
            scale: true,
            axisLabel: {
              formatter: "{value}",
            },
          },
          {
            type: "value",
            name: "单位：个",
            splitNumber: 5,
            minInterval: 1,
            scale: true,
            axisLabel: {
              formatter: "{value}",
            },
          },
        ],
        series: [
          {
            name: "投资金额",
            type: "bar",
            data: amt,
          },
          {
            name: "项目总数",
            type: "line",
            yAxisIndex: 1,
            data: count,
          },
        ],
      };
      let lineDom = document.getElementById("amtEchart");
      var lineChart = echarts.init(lineDom);
      lineChart.setOption(option);
    },
    e2Echart(data) {
      var option;
      option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          type: "scroll",
          bottom: "5%",
        },
        series: [
          {
            name: "投资类型",
            type: "pie",
            radius: "50%",
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      let lineDom = document.getElementById("e2Echart");
      var lineChart = echarts.init(lineDom);
      lineChart.setOption(option);
    },
    e3Echart(data) {
      var option;
      option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          type: "scroll",
          bottom: "5%",
        },
        series: [
          {
            name: "行业分布",
            type: "pie",
            radius: [20, 100],
            center: ["50%", "50%"],
            roseType: "area",
            itemStyle: {
              borderRadius: 10,
            },
            data: data,
          },
        ],
      };
      let lineDom = document.getElementById("e3Echart");
      var lineChart = echarts.init(lineDom);
      lineChart.setOption(option);
    },
    e4Echart(data) {
      var option;
      option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          type: "scroll",
          bottom: "5%",
        },
        series: [
          {
            name: "企业性质",
            type: "pie",
            radius: "50%",
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      let lineDom = document.getElementById("e4Echart");
      var lineChart = echarts.init(lineDom);
      lineChart.setOption(option);
    },
    e5Echart(data) {
      var option;
      option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          type: "scroll",
          bottom: "5%",
        },
        series: [
          {
            name: "项目类型",
            type: "pie",
            radius: "50%",
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      let lineDom = document.getElementById("e5Echart");
      var lineChart = echarts.init(lineDom);
      lineChart.setOption(option);
    },
    e6Echart(data) {
      var option;
      option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          type: "scroll",
          bottom: "5%",
        },
        series: [
          {
            name: "区域",
            type: "pie",
            radius: ["30%", "50%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: "40",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: data,
          },
        ],
      };
      let lineDom = document.getElementById("e6Echart");
      var lineChart = echarts.init(lineDom);
      lineChart.setOption(option);
    },
  },
};
</script>
<style scoped>
a:hover {
  text-decoration: underline;
}
.mftcc_home_box {
  background-color: #eef3f7;
  min-width: 1100px;
  min-height: 100%;
  overflow: auto;
}
.mftcc_home_header {
  position: relative;
  width: auto;
  height: 200px;
  margin-top: 12px;
  margin-left: 12px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(207, 222, 244, 0.1);
  border-radius: 8px;
}

.heder_title {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 194px;
  height: 42px;
  padding-left: 16px;
  background: #6d6bdb;
  border-radius: 8px 0px 100px 0px;
  font-size: 18px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  line-height: 42px;
}

.heder_item {
  position: absolute;
  top: 42px;
  height: 158px;
  width: 100%;
}

.item_box {
  padding-top: 31px;
  display: flex;
  justify-content: space-around;
}

.bus_item {
  position: relative;
  width: 13%;
  height: 102px;
  background: #ffffff;
  box-shadow: 1px 3px 6px 0px rgba(64, 158, 255, 0.2);
  border-radius: 8px;
}

.bus_item_icon {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 62px;
  height: 57px;
}

.bus_item_data {
  position: absolute;
  top: 15px;
  left: 71px;
  width: 58px;
  height: 45px;
  font-size: 32px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #141414;
  line-height: 45px;
}

.bus_item_desc {
  position: absolute;
  top: 66px;
  left: 78px;
  width: 66px;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  white-space: nowrap;
  line-height: 20px;
}

.list_box_item {
  position: relative;
  width: 100%;
  height: 400px;
  margin-left: 13px;
  background: #ffffff;
  box-shadow: 0px 0px 10px 0px rgba(207, 222, 244, 0.1);
  border-radius: 8px;
}

.list_title {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 158px;
  height: 42px;
  border-radius: 8px 0px 100px 0px;
  padding-left: 16px;
  font-size: 18px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: bold;
  color: black;
  line-height: 42px;
}

.list_date {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 158px;
  height: 42px;
  border-radius: 8px 0px 100px 0px;
  padding-left: 16px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #00ced1;
  line-height: 42px;
}

.list_link_more {
  position: absolute;
  top: 62px;
  right: 36px;
}

.link_more_name {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #409eff;
  line-height: 22px;
}

.list_link_box {
  position: absolute;
  top: 62px;
}

.link_item {
  margin-left: 17px;
}

.link_name_select {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #409eff;
  line-height: 22px;
}

.link_name {
  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #141414;
  line-height: 22px;
}

.link_item .link_data {
  color: #409eff;
}

.tble_box {
  position: absolute;
  top: 96px;
  left: 0px;
  width: 100%;
  height: 100%;
}

.list_data_box {
  position: absolute;
  width: 100%;
  top: 104px;
}

.data_item {
  position: relative;
  width: 100%;
  min-height: 46px;
  height: auto;
}

.dataIcon {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0px;
  left: 16px;
  background-size: cover;
  background-image: url("/fh-web/static/images/layout/home/<USER>");
}

.data_icon_1 {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0px;
  left: 16px;
  background-size: cover;
  background-image: url("/fh-web/static/images/layout/home/<USER>");
}

.data_icon_2 {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0px;
  left: 16px;
  background-size: cover;
  background-image: url("/fh-web/static/images/layout/home/<USER>");
}

.data_icon_3 {
  position: absolute;
  width: 36px;
  height: 36px;
  top: 0px;
  left: 16px;
  background-size: cover;

  background-image: url("/fh-web/static/images/layout/home/<USER>");
}

.data_title {
  position: absolute;
  top: 0px;
  left: 69px;
  width: 180px;
  height: 22px;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 22px;
}

.data_des {
  position: absolute;
  top: 24px;
  left: 69px;
  width: 55%;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

.data_date {
  position: absolute;
  top: 0px;
  right: 16px;
  width: 20%;
  height: 20px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 20px;
  right: 60px;
  top: 20px;
}

.line {
  background-color: #ececec;
  height: 1px;
  width: 100%;
  margin-left: 16px;
  margin-right: 16px;
  margin-bottom: 11px;
  margin-top: 11px;
}

.line_bar_box {
  position: absolute;
  top: 50px;
  left: 10%;
  width: 80%;
  /* width: 308px; */
  height: 80%;
}

.pie_bar_box {
  position: absolute;
  top: 30%;
  left: 5%;
  width: 56%;
  height: 70%;
}
.pie_invest_amt_title {
  width: 56px;
  height: 20px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #adadad;
  line-height: 20px;
  font-size: 13px;
}
.pie_invest_amt {
  width: 70px;
  height: 20px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #3d3d3d;
  line-height: 20px;
  margin-left: 10px;
  margin-right: 10px;
  font-size: 13px;
}
.pie_invest_amt_rate {
  width: 56px;
  height: 22px;
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #e11a1a;
  line-height: 22px;
  margin-left: 6px;
  font-size: 13px;
}
.pie_invest_amt_rate2 {
  width: 59px;
  height: 22px;
  font-size: 16px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #47b239;
  line-height: 22px;
  margin-left: 6px;
  font-size: 13px;
}
</style>
