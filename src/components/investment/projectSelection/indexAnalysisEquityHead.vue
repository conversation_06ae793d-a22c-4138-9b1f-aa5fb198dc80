<template>
  <div class="monitorTop">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="储备" name="first"></el-tab-pane>
      <el-tab-pane label="进行" name="second"></el-tab-pane>
      <el-tab-pane label="存量" name="third"></el-tab-pane>
      <el-tab-pane label="退出" name="fourth"></el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import uuid from "uuid";
export default {
  name: "indexAnalysisEquityHead",
  title: "股权头部按钮页面",
  data() {
    return {
      activeNameFlg: '0',
      activeName: 'first',

      activeIndex: "1",
      stopDisabled: false,
      fileDisabled: false,
      flowId: uuid.v4().replace(/-/g, ""),
      firstFlagShow: true,
      secondFlagShow: true,
      thirdFlagShow: true,
      fourthFlagShow: true
    };
  },
  computed:{
    // activeName(){
    //   return this.activeNameFlg==='0'?'first': activeName;
    // }
  },
  methods: {
    handActive(val) {
      this.activeIndex = val;
      this.$emit("getFormData", data);
    },
    handleClick(tab, event) {
      this.$emit("isShow", tab.index);
    },
    //根据项目状态控制标签显示隐藏
    updateLabelShow() {
      //通过权限控制按钮下信息的默认展示
      if ($hasPerm("target_enterprise_info")) {
        this.activeIndex = "1";
        this.handleSelect(this.activeIndex, "");
      } else if ($hasPerm("project_application_info")) {
        this.activeIndex = "2";
        this.handleSelect(this.activeIndex, "");
      }
      if (this.projectState == "1" || this.projectState == "2") {
        //立项录入、立项审核中
        this.secondFlagShow = true;
        this.thirdFlagShow = false;
        this.fourthFlagShow = false;
        this.pactFlagShow = false;
        this.pundFlagShow = false;
      } else if (this.projectState == "3") {
        //尽职调查中
        this.secondFlagShow = true;
        this.thirdFlagShow = true;
        this.fourthFlagShow = false;
        this.pactFlagShow = false;
        this.pundFlagShow = false;
      } else if (this.projectState == "4") {
        //项目审批中
        this.secondFlagShow = true;
        this.thirdFlagShow = true;
        this.fourthFlagShow = true;
        this.pactFlagShow = false;
        this.pundFlagShow = false;
      }
    },
    stopInfo() {
      this.stopDisabled = true;
    },
    filePrint() {
      this.fileDisabled = true;
    }
    //初始化按钮是够可操作
    //打开完善资料选择弹框
  },
  mounted() {
    // this.updateLabelShow();
  },
  created() {
    //this.getRecordTableData();
  }
};
</script>
<style scoped src="@/assets/investment/css/appDetailMainFram.css"/>
<style scoped>

  .monitorTop{
    width: 100%;
    /* height: 100%;     */
  }

  .monitorTop >>>  .el-tabs__item {
    font-size: 17px !important;
    margin-left: 10px;
    margin-top: 6px;
    border:0.5px solid;
    border-bottom:0;
    border-top-left-radius: 11px;
    border-top-right-radius: 11px;
    color: white;
    background-color: #409eff;
  }

  .monitorTop >>>  .el-tabs__header{
    background-color: #F0F2F5;
  }

  .monitorTop >>>  .el-tabs__item.is-active {
    color: #409eff;
    background-color: white;
  }

  /* 内部行业报告 */
  .monitorTop >>> #tab-tab_1638442568542{
    /* color: #3d3d3d;
    background-color: white; */
    border: 0px none;
    margin-left: 0px;
    margin-top: 0px;
  }

  /* 外部行业报告 */
  .monitorTop >>> #tab-tab_1638442592574{
    /* color: #3d3d3d;
    background-color: white; */
    border: 0px none;
    margin-left: 0px;
    margin-top: 0px;
  }


  :target{
    color: #3d3d3d;
    background-color: white;
  }

</style>
