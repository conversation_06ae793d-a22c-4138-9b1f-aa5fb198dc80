<template>
    <div class="mftcc-web">
        <!-- 模板配置 -->
        <templateRenderComponent
                v-if="templateShowFlag"
                :prdUniqueVal="prdUniqueVal"
                :flowNo="flowNo"
                :bizNo="bizNo"
                :nodeNo="nodeNo"
                :parmList="parmList"
                :editFlag="templateEditFlag">
        </templateRenderComponent>
        <!-- 要件配置 -->
        <mftcc-all-file  :docParameter="docParameter" v-if="showFlag" ></mftcc-all-file>
    </div>
</template>
<script>
import api from '@/api/investment/group/busCliquesProjectCheck'
import applyApi from '@/api/investment/apply/investmentApply'
import templateRenderComponent from '@/components/doc/template/templateRenderComponent';
import mftccAllFile from '@/components/doc/file/mftccAllFile';
export default {
    name: 'busCliquesProjectCheckDoc',
    title: '超权限项目申报资料要件页面',
    props: ["estabProjectId","projectId","projectCheckApproveId","checkId"],
    components: {
        templateRenderComponent:templateRenderComponent,
        mftccAllFile:mftccAllFile
    },
    data() {
        return {
            //模板参数---开始
            templateShowFlag:false,//是否展示模板
            prdUniqueVal:"",//产品的唯一标识：格式“产品号_版本号”，如果没有可以为空。
            flowNo:"",//流程标识
            bizNo:"",//相应的业务流水编号
            nodeNo:"",//流程中的节点标识
            parmList: {},//书签取值需要的业务参数
            templateEditFlag:"",//模板是否允许编辑  1-允许，0-不允许
            //模板参数---结束

            //要件参数---开始
            paramList: [],
            showFlag: false,
            docParameter:{},
            //要件参数---结束
        }
    },
    mounted() {
        if (this.checkId){
            this.getTemplateParams()
            this.getFileParamList()
        }
    },
    methods: {
        // 初始化模板信息
        getTemplateParams(){
            const data = {
                estabProjectId: this.estabProjectId,
                projectId: this.projectId,
                approveId: this.projectCheckApproveId,
                checkId: this.checkId
            }
            api.getDocParams(data,res => {
                if(res.code === 0){
                    const data = res.data
                    this.templateShowFlag = true
                    this.prdUniqueVal = data.prdUniqueVal
                    this.flowNo = data.flowNo
                    this.bizNo = data.bizNo
                    this.nodeNo = data.nodeNo
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true
                    });
                }
            });
        },
        // 初始化要件信息
        getFileParamList() {
            let reqData = {
                    estabProjectId: this.estabProjectId,
                    flowCode:"accept_approve",
                };
                applyApi.getFileParamListView(reqData, res => {
                    if (res.code === 0) {
                        this.docParameter = res.docParameter;
                        this.showFlag = true;
                    } else {
                        this.$alert(res.msg, this.$alertTitle, {
                            type: res.msgType,
                            dangerouslyUseHTMLString: true,
                            callback: action => {
                            }
                        });
                    }
                }, error => {
                });
        }
    },
}
</script>
<style>
</style>
