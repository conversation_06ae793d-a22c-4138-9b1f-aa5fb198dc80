<template>
  <div >

    <el-dialog title="详情信息" :visible.sync="formShow" :close-on-click-modal="false" :center="true" width="70%" :modal="false" :modal-append-to-body="false" class="parm-mole-dialog">
          <div class="mftcc-container" style="width:100%">
            <el-row>
              <el-col :span="24">
                <el-card class="box-card" style="margin-top: 5px;width:100%;height:190px" :body-style="{ padding: '0px' }">
                    <div slot="header" class="clearfix">
                        <span style="line-height: 15px; font-size: 16px; font-weight: 700;">流程信息</span>
                    </div>
                      <div style="background: #fff;margin-left:20px">
                          <biz-time-line v-if="isShowTimeLine" @getHisTaskList="getHisTaskList" :traceNo="approveNo" :bizMark="bizMark" :estabProjectId="estabProjectId" ref="bizTimeLine"></biz-time-line>
                      </div>
                </el-card>
                 <el-card v-if="isShowTable" class="box-card" style="margin-top: 5px;" :body-style="{ padding: '0px' }">
                    <div slot="header" class="clearfix">
                        <span style="line-height: 20px; font-size: 16px; font-weight: 700;">{{formTitle}}</span>
                    </div>
                   <mftcc-form   :formId="formId"  :ref="formRef"></mftcc-form>
                </el-card>
                    <bus-equity-apply-report-details v-if="isShowEquity" :equity="equityId">  </bus-equity-apply-report-details>
                <!-- 要件配置 -->
                  <bus-group-flow-doc v-if="docShowFlag"  :estabProjectId="estabProjectId" :flowCode="busMark" :exceptionsId="exceptionsId" :keyId="keyId"></bus-group-flow-doc>
                </el-col>
            </el-row>
    </div>
     </el-dialog>
  </div>

</template>
<script>
import api from '@/api/investment/launch/busFundingApply';
import cliquesApi from '@/api/investment/group/busCliquesTeamworkDeclare';
import BusFlowPoint from '@/components/investment/flow/busFlowPoint';
import backwithApi from '@/api/investment/group/busCliquesPowerWithinBack';
import loanPremiseApi from '@/api/investment/group/busCliquesLoanPremise';
import projectApi from '@/api/investment/group/busCliquesProjectDeclare';
import cheakApi from '@/api/investment/group/busCliquesProjectCheck';
import busEquityApplyReportDetails from '@/components/investment/flow/busEquityApplyReportDetails';
import applyApi from '@/api/investment/apply/investmentApply';
import InvestmentFlowDoc from '@/components/investment/flow/investmentFlowDoc.vue';
import fileShowCompnoents from '@/components/doc/file/fileShowCompnoents';
import BusGroupFlowDoc from '@/components/investment/group/busGroupFlowDoc.vue';
export default {
  components: {
    BusFlowPoint,
    fileShowCompnoents:fileShowCompnoents,
    busEquityApplyReportDetails,
    InvestmentFlowDoc,
    BusGroupFlowDoc
  },
  props: {estabProjectId: "", transferId: '',wkfAppId:'',exceptionsId:''},
  data() {
    return {
      formShow: false,
      applyId: "",
      agencyId: "",
      busType: '',
      formId:'',
      formRef:'',
      bizMark:"",
      traceNo:this.wkfAppId,
      formTitle:"",
      timeKey:new Date().getTime(),
      docShowFlag:false, //要件显示配置
      prdUniqueVal:'',
      flowNo:'',
      nodeNo:'',
      appName:'',
      bizNo:'',
      titleName:'',
      approveNo:'',
      isShowTimeLine:false,
      isShowTable:false,
      isShowEquity:false,
      equityId:'',
      busMark:'',
      estabProjectId:'',
      exceptionsId:'',
      hisTask:[],
      keyId: ''
    };
  },
  created() {
  },
  watch: {
    approveNo(newVal,oldVal){

    },
    formShow(val){
      this.isShowTimeLine = val;
    }
  },
  computed: {},
  methods: {

    //详情信息返显
    showDialog(nodeNo, estabProjectId, approveId, isQcyProject, equityId, bizMark, exceptionsId,hisTask) {
      this.docShowFlag = false;
      this.hisTask = hisTask;
      //判断回显的表单信息
      if (nodeNo == "project_reporting") {
        //协同项目申报
        this.isShowTable = true;
        this.isShowEquity = false;
        this.formId = 'investment/busCliquesTeamworkDeclareDetailsForm';
        this.formRef = 'busCliquesTeamworkDeclareDetailsForm';
        this.getprojectReportDetail(estabProjectId);
        this.approveNo = approveId;
        this.bizMark = bizMark;
        this.estabProjectId = estabProjectId;
        // this.busMark ="investmentProject"
      } else if (nodeNo == "ultra_permission_access") {
        //超权限项目准入
        this.isShowTable = false;
        this.isShowEquity = true;
        this.docShowFlag = false;
        this.equityId = equityId;
        this.formId = 'investment/investmentApplyUpdateForm';
        this.formRef = 'investmentApplyUpdateForm';
        this.getDetail(estabProjectId);
        this.approveNo = approveId;
        this.bizMark = bizMark;
        this.estabProjectId = estabProjectId;
      } else if (nodeNo == "project_filing") {
        //权限内项目备案
        this.isShowTable = true;
        this.isShowEquity = false;
        this.formId = 'investment/busCliquesPowerWithinBackDetailsForm';
        this.formRef = 'busCliquesPowerWithinBackDetailsForm';
        this.getCliquesPowerWithDetail(estabProjectId);
        this.approveNo = approveId;
        this.bizMark = bizMark;
        this.estabProjectId = estabProjectId;
      } else if (nodeNo == "project_declare_approve") {
        //项目申报
        this.isShowTable = true;
        this.isShowEquity = false;
        if (isQcyProject == '1') {
          this.formId = 'investment/busCliquesProjectDeclareDetailsFormQcyNoButton';
        } else {
          this.formId = 'investment/busCliquesProjectDeclareDetailsFormNoButton';
        }
        this.formRef = 'busCliquesProjectDeclareDetailsForm';
        this.getCliquesProjectDetail(estabProjectId);
        this.approveNo = approveId;
        this.bizMark = bizMark;
        this.estabProjectId = estabProjectId;
      } else if (nodeNo == "loan_premise_approve") {
        //放款前提条件审核
        this.isShowTable = true;
        this.isShowEquity = false;
        this.formId = 'investment/busCliquesLoanPremiseDetailsFormNoButton';
        this.formRef = 'busCliquesLoanPremiseDetails';
        this.exceptionsId = exceptionsId;
        this.getCliquesLoanPremiseDetail(estabProjectId);
        this.approveNo = approveId;
        this.bizMark = bizMark;
        this.estabProjectId = estabProjectId;

      } else if (nodeNo == "super_project_approve") {
        //超权限项目审批
        this.isShowTable = true;
        this.isShowEquity = false;
        if (isQcyProject == '1') {
          this.formId = "investment/busCliquesProjectCheckDetailsFormQcyNoButton";
        } else {
          this.formId = "investment/busCliquesProjectCheckDetailsFormNoButton";
        }
        this.approveNo = approveId;
        this.bizMark = bizMark;
        this.formRef = "busCliquesProjectCheckDetailsForm";
        this.getCliquesProjectCheckDetail(estabProjectId);
        this.estabProjectId = estabProjectId;
      }

    },
    //协同项目申报详情获取
    getprojectReportDetail(estabProjectId) {
      cliquesApi.getBusCliquesTeamworkDeclare(estabProjectId, res => {
        if (res.code === 0) {
          this.formShow = true;
          this.$nextTick(() => {
            this.$refs.busCliquesTeamworkDeclareDetailsForm.setFormValue(res.data.busCliquestData);
            this.formTitle = "协同申报信息";
            //要件配置信息
            this.busMark = "investmentProject";
            this.docShowFlag = true;
            // this.prdUniqueVal = res.data.docParam.prdUniqueVal;
            // this.flowNo = res.data.docParam.flowNo;
            // this.nodeNo = "UserTask_0untqqe";
            // this.appName = res.data.docParam.appName;
            // this.bizNo = res.data.docParam.bizNo;
            // this.timeKey = new Date().getTime();
            this.keyId = res.data.busCliquestData.teamworkId;
            this.titleName = "项目协同申报资料";
          });
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      })
    },

    //超权限项目准入详情获取
    //放款前提条件审核
    getCliquesLoanPremiseDetail(estabProjectId) {
      let obj = {};
      obj.estabProjectId = estabProjectId;
      obj.exceptionsId = this.exceptionsId;
      loanPremiseApi.getCliquesLoanPremiseDetail(obj, res => {
        if (res.code === 0) {
          this.formShow = true;
          this.$nextTick(() => {
            this.$refs.busCliquesLoanPremiseDetails.setFormValue(res.data.busCliquestData);
            this.formTitle = "落实前提条件审核信息";
            //要件配置信息
            this.busMark = "investmentPreLoan";
            this.docShowFlag = true;
            // this.prdUniqueVal = res.data.docParam.prdUniqueVal;
            // this.flowNo = res.data.docParam.flowNo;
            // this.nodeNo = res.data.docParam.nodeNo;
            // this.appName = res.data.docParam.appName;
            // this.bizNo = res.data.docParam.bizNo;
            // this.timeKey = new Date().getTime();
            // this.titleName = "集团放款前提条件审核资料";
          });
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      })
    },

    //权限内项目备案
    getCliquesPowerWithDetail(estabProjectId) {
      backwithApi.getBusCliquesPowerWithinBack(estabProjectId, res => {
        if (res.code === 0) {
          this.formShow = true;
          this.$nextTick(() => {
            this.$refs.busCliquesPowerWithinBackDetailsForm.setFormValue(res.data.busCliquestData);
            this.formTitle = "项目备案信息";
            //要件配置信息
            this.busMark = "investmentSuperProject"
            this.docShowFlag = true;
            // this.prdUniqueVal = res.data.docParam.prdUniqueVal;
            // this.flowNo = res.data.docParam.flowNo;
            // this.nodeNo = res.data.docParam.nodeNo;
            // this.appName = res.data.docParam.appName;
            // this.bizNo = res.data.docParam.bizNo;
            // this.timeKey = new Date().getTime();
            // this.titleName = "权限内项目备案审核资料";
          });
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      })
    },
    //超权限项目申报
    getCliquesProjectDetail(estabProjectId) {
      projectApi.getBusCliquesProjectDeclareData(estabProjectId, res => {
        if (res.code === 0) {
          this.formShow = true;
          this.$nextTick(() => {
            this.$refs.busCliquesProjectDeclareDetailsForm.setFormValue(res.data.busCliquestData);
            this.formTitle = "项目申报信息";
            //要件配置信息
            this.busMark = "investmentSuperAuthorityProject"
            this.docShowFlag = true;
            // this.prdUniqueVal = res.data.docParam.prdUniqueVal;
            // this.flowNo = res.data.docParam.flowNo;
            // this.nodeNo = res.data.docParam.nodeNo;
            // this.appName = res.data.docParam.appName;
            // this.bizNo = res.data.docParam.bizNo;
            // this.timeKey = new Date().getTime();
            // this.titleName = "超权限项目申报资料";
          });
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      })
    },
    //超权限项目审批
    getCliquesProjectCheckDetail(estabProjectId) {
      cheakApi.getCliquesProjectCheckDetail(estabProjectId, res => {
        if (res.code === 0) {
          this.formShow = true;
          this.$nextTick(() => {
            this.$refs.busCliquesProjectCheckDetailsForm.setFormValue(res.data.busCliquestData);
            this.formTitle = "项目审批信息";
            //要件配置信息
            this.busMark = "investmentSuperAuthorityApprove";
            this.docShowFlag = true;
            // this.prdUniqueVal = res.data.docParam.prdUniqueVal;
            // this.flowNo = res.data.docParam.flowNo;
            // this.nodeNo = res.data.docParam.nodeNo;
            // this.appName = res.data.docParam.appName;
            // this.bizNo = res.data.docParam.bizNo;
            // this.timeKey = new Date().getTime();
            // this.titleName = "超权限项目审批资料";
          });
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      })
    },
    getDetail(estabProjectId) {
      applyApi.findById(estabProjectId, res => {
        if (res.code === 0) {
          this.formShow = true;
          var data = res.data;
          this.$refs.investmentApplyUpdateForm.setFormValue(data);
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true,
            callback: action => {
              this.$router.back(-1);
            }
          });
        }
      }, error => {
        this.$router.back(-1);
      });
    },
    getHisTaskList(){
      $bus.$emit('getHisTaskListResult',this.hisTask);
    }

  },
  mounted() {
  }
};
</script>
<style lang="scss" scoped>

</style>
