<template>
    <el-row>
        <mftcc-form :formId="formId" :parentVm="this" ref="busCliquesProjectCheckDetailsForm"></mftcc-form>
        <bus-cliques-project-details  v-if="display"  ref="openDialog" ></bus-cliques-project-details>
    </el-row>
</template>
<script>
import api from '@/api/investment/group/busCliquesProjectCheck'
import busCliquesProjectDetails from '@/components/investment/group/busCliquesProjectDetails'
export default {
    name: 'busCliquesProjectCheckDetails',
    title: '超权限项目审批详情',
    props: ["checkId", "isQcyProject"],
    components: {
        busCliquesProjectDetails
    },
    data() {
        return {
            formId: '',
            estabProjectId: "",
            display: false
        }
    },
    mounted() {
        if (this.isQcyProject === '1'){
            this.formId = 'investment/busCliquesProjectCheckDetailsFormQcy'
        } else {
            this.formId = 'investment/busCliquesProjectCheckDetailsForm'
        }
        if(this.checkId){
            this.findById();
        }
    },
    methods: {
        // 获取项目详情信息
        getProjectDetail(){
            this.$refs.openDialog.openDialog(this.estabProjectId);
        },
        findById(){
            api.findById(this.checkId,res => {
                this.estabProjectId = res.data.estabProjectId;
                if(res.code === 0){
                    var data = res.data;
                    this.$refs.busCliquesProjectCheckDetailsForm.setFormValue(data);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
                this.display = true;
            },error => {
                this.$router.back(-1);
            });
        },
        back(){
            this.$router.back(-1);
        }
    },
}
</script>
