<template>
    <el-row>
        <mftcc-form :formId="formId" :parentVm="this" ref="busCliquesReviewReportDetailsForm"></mftcc-form>
    </el-row>
</template>
<script>
import equityApi from '@/api/investment/group/busCliquesReviewReportEquity'
import creditorApi from '@/api/investment/group/busCliquesReviewReportCreditor'
export default {
    name: 'busCliquesReviewReportDetails',
    title: '超权限项目审批-审查报告详情',
    props: ["reportId", "declareProjectType"],
    components: {},
    data() {
        return {
            formId: 'investment/busCliquesReviewReportEquityDetailsForm',
            estabProjectId: "",
            display: false
        }
    },
    created () {
        if (this.declareProjectType === '0101'){
            this.formId = 'investment/busCliquesReviewReportEquityDetailsForm'
        } else if (this.declareProjectType === '0102'){
            this.formId = 'investment/busCliquesReviewReportCreditorDetailsForm'
        }
        if(this.reportId){
            this.findById();
        }
    },
    methods: {
        findById(){
            if (this.declareProjectType === '0101'){
                equityApi.findById(this.reportId,res => {
                    this.estabProjectId = res.data.estabProjectId;
                    if(res.code === 0){
                        var data = res.data;
                        this.$nextTick(() => {
                            this.$refs.busCliquesReviewReportDetailsForm.setFormValue(data);
                        });

                    }else{
                        this.$alert(res.msg, this.$alertTitle, {
                            type: res.msgType,
                            dangerouslyUseHTMLString: true,
                            callback: action => {
                                this.$router.back(-1);
                            }
                        });
                    }
                    this.display = true;
                },error => {
                    this.$router.back(-1);
                });
            } else if (this.declareProjectType === '0102'){
                creditorApi.findById(this.reportId,res => {
                    this.estabProjectId = res.data.estabProjectId;
                    if(res.code === 0){
                        var data = res.data;
                        this.$nextTick(() => {
                            this.$refs.busCliquesReviewReportDetailsForm.setFormValue(data);
                        });

                    }else{
                        this.$alert(res.msg, this.$alertTitle, {
                            type: res.msgType,
                            dangerouslyUseHTMLString: true,
                            callback: action => {
                                this.$router.back(-1);
                            }
                        });
                    }
                    this.display = true;
                },error => {
                    this.$router.back(-1);
                });
            }
        },
        back(){
            this.$router.back(-1);
        }
    },
}
</script>
