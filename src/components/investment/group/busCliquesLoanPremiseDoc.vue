<template>
    <div class="mftcc-web">
        <!-- 要件配置 -->
        <!-- <fileMultiShowCompnoent v-if="showFlag" :paramList="paramList"/> -->
        <mftcc-all-file v-if="showFlag" :docParameter="docParameter"></mftcc-all-file>
    </div>
</template>
<script>
import api from '@/api/investment/group/busCliquesLoanPremise'
import mftccAllFile from '@/components/doc/file/mftccAllFile';
export default {
    name: 'busCliquesLoanPremiseDoc',
    title: '重大部放款前提要件页面',
    props: ["loanPremiseApproveId","estabProjectId","projectId","loanPremiseId"],
    components: {

        mftccAllFile:mftccAllFile
    },
    data() {
        return {
            //要件参数---开始
            paramList: [],
            showFlag: false,
            docParameter:{}
            //要件参数---结束
        }
    },
    mounted() {
        if (this.loanPremiseId){
            this.getFileParamList()
        }
    },
    methods: {
        // 初始化要件信息
        getFileParamList() {
            let reqData = {
                loanPremiseId: this.loanPremiseId
            };
            api.getFileParamList(reqData, res => {
                if (res.code === 0) {
                    this.docParameter = res.list;
                    this.showFlag = true
                } else {
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                        }
                    });
                }
            }, error => {
            });
        }
    },
}
</script>
<style>
</style>
