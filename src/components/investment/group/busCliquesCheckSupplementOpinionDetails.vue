<template>
    <el-row>
        <mftcc-form formId="investment/busCliquesCheckSupplementOpinionDetailsForm" :parentVm="this" ref="busCliquesCheckSupplementOpinionDetailsForm"></mftcc-form>
        <div class="reportContent" v-html="textValue"></div>
    </el-row>
</template>
<script>
import api from '@/api/investment/group/busCliquesCheckSupplementOpinion'
export default {
    name: 'busCliquesCheckSupplementOpinionDetails',
    title: '超权限项目审批-补充资料详情',
    props: ["optionId"],
    components: {
    },
    data() {
        return {
            formId: '',
            estabProjectId: "",
            textValue: '',
            isClear: false,
            display: false
        }
    },
    mounted() {
        if(this.optionId){
            this.findById();
        }
    },
    methods: {
        findById(){
            api.findById(this.optionId,res => {
                this.estabProjectId = res.data.estabProjectId;
                if(res.code === 0){
                    var data = res.data;
                    this.textValue = data.supplementOpinion;
                    this.$refs.busCliquesCheckSupplementOpinionDetailsForm.setFormValue(data);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
                this.display = true;
            },error => {
                this.$router.back(-1);
            });
        },
        back(){
            this.$router.back(-1);
        },
    },
}
</script>
<style scoped>
.reportContent {
  font-weight: 400;
  /*padding: 9px;*/
  padding-left: 33px;
  /*min-height: 50px;*/
}
</style>
