<template>
    <div style="background: #fff;margin-left:20px">
        <time-line
                ref="establishmentHis"
                :timeLineShow="isShowTimeLine"
                :traceNo="approveId"
                :bizMark="bizMark"
                :isShowSign="true"/>

    </div>
</template>
<script>
import api from '@/api/investment/group/busCliquesProjectCheck'
export default {
    name: 'examineTimeLineComponent',
    title: '超权限项目审批-审批详情-审批历史',
    props: ['checkId'],
    data() {
        return {
            bizMark: 'investmentSuperAuthorityApprove',
            approveId: '',
            estabProjectId: '',
            isShowTimeLine: false,
        }
    },
    mounted() {
        if(this.checkId){
            this.findById()
        }
    },
    methods: {
        findById(){
            this.isShowTimeLine = false
            api.findById(this.checkId,res => {
                if(res.code === 0){
                    this.estabProjectId = res.data.estabProjectId
                    this.approveId = res.data.approveId
                    this.isShowTimeLine = true
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            },error => {
                this.$router.back(-1);
            });
        },
        back(){
            this.$router.back(-1);
        }
    },
}
</script>
