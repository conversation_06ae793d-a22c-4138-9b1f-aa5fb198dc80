<template>
<el-collapse v-model="activeNames" @change="handleChange">
  <el-collapse-item  name="1">
    <template slot="title">
            <div class="mftcc-collapse-title scroll">
                <svg class="icon" aria-hidden="true"  style="">
                  <use xlink:href="#icon-neirongyetongyitubiao"></use>
                </svg>
                <span v-if="showStyleFlag" >{{titleName}}</span>
                 <span v-else class="titleNameSpan">{{titleName}}</span>
            </div>
    </template>
    <el-row>
        <mftcc-table tableId="investment/investmentGroupProcssTable" :parentVm="this" ref="investmentGroupProcssTable" :initSearchData="tableInitSearchData" class="user-table"></mftcc-table>
        <bus-group-process-detail-dialog @callback="teamInsertCallBackFunc" ref="busGroupProcessDetailDialog" :wkfAppId="wkfAppId"></bus-group-process-detail-dialog>
    </el-row>
    </el-collapse-item>
</el-collapse>
</template>
<script>
import api from '@/api/investment/group/busGroupProcess';
import applyApi from '@/api/investment/launch/busFundingApply';
import busGroupProcessDetailDialog from '@/components/investment/group/busGroupProcessDetailDialog';

export default {
  name: 'busGroupProcessList',
  title: '流程列表信息',
  components: {
      busGroupProcessDetailDialog
  },
  props:{scheduleId : "",estabProjectId : "",flowName:"",wkfAppId:"",showStyleFlag:"",exceptionsId:""},
  data() {
    return {
      activeNames: ['1'],
      titleName:"集团流程信息",
      tableInitSearchData:{
            "estabProjectId":'',
            "flowName":[]
        },
      scheduleId:'',
      wkfAppId:this.wkfAppId,
      showStyleFlag:this.showStyleFlag,
      sourceUrl:"@/assets/investment/css/publicbusinessstyle.css",
      exceptionsId:this.exceptionsId
    }
  },
   watch:{
        scheduleId:{
            handler(val){
            },
        }
    },
  methods: {
    query(index, row){
      this.$router
                .push({path: '/launch/busFundingApplyApproveDetails', query: {applyHisId:row.applyHisId}});
    },
    tableRefresh(data) {
            this.$nextTick(() => {
                this.$refs.investmentGroupProcssTable.refresh()
            })
        },
    handleChange(val) {
          console.log(val);
        },
     // 选择列表信息
    selectbusFundingData(index,row){
        this.$refs.busGroupProcessDetailDialog.showDialog(row.nodeNo,this.estabProjectId,row.approveId,row.isQcyProject,row.equityId,row.bizMark,'',row.hisTask);
    },
    //列表数据查询
      getBusGroupList(data){
        api.getBusGroupProcessList(data, res => {
          if (res.code === 0) {
              setTimeout(() => {
                  this.$refs.investmentGroupProcssTable.option.tableData = res.data.records
              }, 300);
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      })
      },
      initData(){
             var flowName = [];
             if(this.flowName == 'project_review'){
                  //立项
                flowName = ["project_reporting","ultra_permission_access"];
              }else if(this.flowName == "project_approve"){
                  //项目审批
                   flowName = ["project_filing","project_declare_approve","super_project_approve"];
              }else if(this.flowName == "internal_audit"){
                  //内部审核
                   flowName = ["loan_premise_approve"];
                   this.tableInitSearchData.exceptionsId = this.exceptionsId;
              }
                this.tableInitSearchData.estabProjectId=this.estabProjectId;
                this.tableInitSearchData.flowName = flowName;
                this.getBusGroupList(this.tableInitSearchData);
      }
  },
   mounted() {
         this.initData();

  }
}
</script>
<style scoped  src="@/assets/investment/css/publicbusinessstyle.css"/>
