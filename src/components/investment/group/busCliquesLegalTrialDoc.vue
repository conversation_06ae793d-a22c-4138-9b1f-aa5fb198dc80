<template>
    <div class="mftcc-web">
        <!-- 要件配置 -->
        <mftcc-all-file  :docParameter="docParameter" v-if="showFlag"></mftcc-all-file>
    </div>
</template>
<script>
import api from '@/api/investment/group/busCliquesLegalTrial'
import applyApi from '@/api/investment/apply/investmentApply'
import mftccAllFile from '@/components/doc/file/mftccAllFile';
export default {
    name: 'busCliquesLegalTrialDoc',
    title: '集团法审要件页面',
    props: ["estabProjectId"],
    components: {
        mftccAllFile:mftccAllFile
    },
    data() {
        return {
            //要件参数---开始
            paramList: [],
            showFlag: false,
            docParameter: {}
            //要件参数---结束
        }
    },
    mounted() {
        this.getFileParamList()
    },
    methods: {
        // 初始化要件信息
        getFileParamList() {
            let reqData = {
                estabProjectId: this.estabProjectId,
                flowCode:'legal_trial_update'
            };
            applyApi.getFileParamListView(reqData, res => {
                    if (res.code === 0) {
                        this.docParameter = res.docParameter;
                        this.showFlag = true;
                    } else {
                        this.$alert(res.msg, this.$alertTitle, {
                            type: res.msgType,
                            dangerouslyUseHTMLString: true,
                            callback: action => {
                            }
                        });
                    }
            }, error => {});
        }
    },
}
</script>
<style>
</style>
