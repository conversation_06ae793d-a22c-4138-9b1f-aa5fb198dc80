<template>
    <div>
        <i class="el-icon-s-order" style="color: #409eff;" @click="showDialog">查看</i>

        <mftcc-dialog-form
            formId="investment/busCliquesProjectCheckOnlyDCDetailsForm"
            title="主要内容及结构"
            :show.sync="isShowDialog"
            :center="true"
            height="200px"
            :footer="false"
            :parentVm="this"
            ref="busCliquesProjectCheckOnlyDCDetailsForm"
        ></mftcc-dialog-form>
    </div>
</template>

<script>
export default {
    name: 'declareContentComponent',
    // 一票否决、下发批复列表使用
    title: 'table自定义组件-主要内容及结构（点击弹框展示主要内容及结构信息）',
    props: ['fieldName', 'row'],
    data() {
        return {
            isShowDialog: false
        }
    },
    methods:{
        showDialog(){
            this.isShowDialog = true
            this.$refs.busCliquesProjectCheckOnlyDCDetailsForm.getFormRef((editForm) => {
                editForm.setFormValue(this.row)
            });
        }
    }
}
</script>
