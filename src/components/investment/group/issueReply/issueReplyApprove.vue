<template>
    <div class="mftcc-container">
        <!-- 下发批复审批视图 -->
        <mftcc-layout layoutId="investment/busCliquesIssueReplyView"
                      v-if="isShowLayout"
                      :parentVm="this"
                      :paramData="paramData"
                      ref="busCliquesIssueReplyView">
        </mftcc-layout>
    </div>
</template>
<script>
import api from '@/api/investment/group/busCliquesIssueReply'
export default {
    name: 'issueReplyApprove',
    title: '下发批复审批视图',
    props: ['replyId'],
    data() {
        return {
            isShowLayout: false,
            paramData: {}
        }
    },
    mounted() {
        this.findById();
    },
    methods: {
        // 获取下发批复信息
        findById(){
            api.findById(this.replyId,res => {
                if(res.code === 0){
                    var data = res.data
                    this.paramData.issueReplyInfo = data
                    this.isShowLayout = true
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            },error => {
                this.$router.back(-1);
            });
        },
        //提交下发批复审核
        doCommit(params, callback){
            let data = {};
            data.params = params;
            api.doCommit(data,res =>{
                if(res.code===0){
                    res.msg = res.data.msg;
                    callback(res);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            })
        },
    }
}
</script>
