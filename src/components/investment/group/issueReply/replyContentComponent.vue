<template>
    <div>
        <i class="el-icon-s-order" style="color: #409eff;" @click="showDialog">查看</i>

        <mftcc-dialog-form
            formId="investment/busCliquesIssueReplyOnlyRCDetailsForm"
            title="批复内容"
            :show.sync="isShowDialog"
            :center="true"
            height="200px"
            :footer="false"
            :parentVm="this"
            ref="busCliquesIssueReplyOnlyRCDetailsForm"
        ></mftcc-dialog-form>
    </div>
</template>

<script>
export default {
    name: 'replyContentComponent',
    title: '下发批复-批复内容组件（点击弹框展示批复内容）',
    props: ['fieldName', 'row'],
    data() {
        return {
            isShowDialog: false
        }
    },
    methods:{
        showDialog(){
            this.isShowDialog = true
            this.$refs.busCliquesIssueReplyOnlyRCDetailsForm.getFormRef((editForm) => {
                editForm.setFormValue(this.row)
            });
        }
    }
}
</script>
