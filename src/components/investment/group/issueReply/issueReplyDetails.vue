<template>
    <div class="mftcc-container">
        <el-container>
            <el-row>
                <mftcc-form formId="investment/busCliquesIssueReplyDetailsForm" :parentVm="this" ref="busCliquesIssueReplyDetailsForm"/>
            </el-row>
        </el-container>
    </div>
</template>
<script>
export default {
    name: 'issueReplyDetails',
    title: '下发批复视图-下发批复详情组件',
    props: ['issueReplyInfo'],
    data() {
        return {
        }
    },
    mounted () {
        this.$refs.busCliquesIssueReplyDetailsForm.setFormValue(this.issueReplyInfo);
    },
    methods: {
        // 查看项目详情（链接项目详情视图）
        getProjectDetail(){
            console.log("查看项目详情，立项项目id", this.issueReplyInfo.estabProjectId);
        },
    },
}
</script>