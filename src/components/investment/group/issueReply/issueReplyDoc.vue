<template>
    <div class="mftcc-container">
        <!-- 要件配置 -->
        <div class="mftcc-web">
            <mftcc-all-file :docParameter="docParameter" v-if="docShowFlag"/>
        </div>
    </div>
</template>
<script>
import api from '@/api/investment/group/busCliquesIssueReply'
import mftccAllFile from '@/components/doc/file/mftccAllFile';
export default {
    name: 'issueReplyDoc',
    title: '下发批复视图-要件组件',
    props: ['issueReplyInfo'],
    components: {
        mftccAllFile:mftccAllFile,
    },
    data() {
        return {
            //文档要件参数---开始
            docShowFlag: false,
            docParameter: {},
            //文档要件参数---结束
        }
    },
    mounted(){
        this.getDocParam()
    },
    methods: {
        // 获取“一票否决”要件参数
        getDocParam(){
            this.docShowFlag = false
            const param = {
                replyId: this.issueReplyInfo.replyId
            }
            api.getDocParam(param,res => {
                if(res.code === 0){
                    const data = res.data
                    this.docShowFlag = true
                    this.docParameter = data
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true
                    });
                }
            })
        },
    },
}
</script>
