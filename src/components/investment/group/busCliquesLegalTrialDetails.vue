<template>
    <el-row>
        <mftcc-form formId="investment/busCliquesLegalTrialDetailsForm" :parentVm="this" ref="busCliquesLegalTrialDetailsForm"></mftcc-form>
    </el-row>
</template>
<script>
import api from '@/api/investment/group/busCliquesLegalTrial'
export default {
    name: 'busCliquesLegalTrialDetails',
    title: '集团法审详情',
    props: ["legalTrialId",],
    components: {},
    data() {
        return {
            estabProjectId: "",
            display: false
        }
    },

    mounted() {
        if(this.legalTrialId){
            this.findById()
        }
    },
    methods: {
        findById(){
            api.findById(this.legalTrialId,res => {
                this.estabProjectId = res.data.estabProjectId;
                if(res.code === 0){
                    var data = res.data;
                    this.$refs.busCliquesLegalTrialDetailsForm.setFormValue(data);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
                this.display = true;
            },error => {
                this.$router.back(-1);
            });
        },
        back(){
            this.$router.back(-1);
        }
    },
}
</script>
