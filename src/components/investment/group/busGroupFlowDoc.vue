<template>
    <div class="mftcc-web">
        <!-- 要件配置 -->
        <!-- <fileMultiShowCompnoent :paramList="paramList"></fileMultiShowCompnoent> -->
        <mftcc-all-file :docParameter="docParameter" v-if="docShowFlag"/>
    </div>
</template>
<script>
    import api from '@/api/investment/group/busGroupProcess'
    import fileMultiShowCompnoent from '@/components/doc/file/fileMultiShowCompnoent';
import mftccAllFile from '@/components/doc/file/mftccAllFile';
    import applyApi from '@/api/investment/apply/investmentApply'

    export default {
        // import引入的组件需要注入到对象中才能使用
        components: {
            fileMultiShowCompnoent: fileMultiShowCompnoent,
             mftccAllFile:mftccAllFile
        },
        data() {
            // 这里存放数据
            return {
                docParameter: {},
                docShowFlag: false,
                paramList: [],
                showFlag: false,
            };
        },
        props: ["estabProjectId","flowCode","exceptionsId","keyId"],
        methods: {
            getGroupFileParamList() {
                let reqData = {
                estabProjectId: this.estabProjectId,
                flowCode:this.flowCode,
                keyId: this.keyId,
                exceptionsId:this.exceptionsId
            };
            applyApi.getFileParamListView(reqData, res => {
                    if (res.code === 0) {
                        debugger;
                        this.docShowFlag = true;
                        this.docParameter = res.docParameter;

                    } else {
                        this.$alert(res.msg, this.$alertTitle, {
                            type: res.msgType,
                            dangerouslyUseHTMLString: true,
                            callback: action => {
                            }
                        });
                    }
            }, error => {});

                // let reqData = {
                //     estabProjectId: this.estabProjectId,
                //     flowCode:this.flowCode,
                //     exceptionsId:this.exceptionsId

                // };
                // api.getGroupFileParamList(reqData, res => {
                //     if (res.code === 0) {
                //         this.paramList = res.list;
                //         this.showFlag = true;
                //     } else {
                //         this.$alert(res.msg, this.$alertTitle, {
                //             type: res.msgType,
                //             dangerouslyUseHTMLString: true,
                //             callback: action => {
                //             }
                //         });
                //     }
                // }, error => {
                // });
            }
        },
        mounted() {
            this.getGroupFileParamList();
        }
    }
</script>
<style scoped>
   .box-card  >>> .el-card__header {
     padding: 0px !important;
   }
   .box-card  >>> .el-card__body {
     padding: 0px !important;
   }
</style>

