<template>
    <div class="mftcc-container">
        <!-- 一票否决审批视图 -->
        <mftcc-layout layoutId="investment/busCliquesOneVetoView"
                      v-if="isShowLayout"
                      :parentVm="this"
                      :paramData="paramData"
                      ref="busCliquesOneVetoView">
        </mftcc-layout>
    </div>
</template>
<script>
import api from '@/api/investment/group/busCliquesOneVeto'
export default {
    name: 'oneVetoApprove',
    title: '一票否决审批视图',
    props: ['vetoId'],
    data() {
        return {
            isShowLayout: false,
            paramData: {}
        }
    },
    mounted() {
        this.findById()
    },
    methods: {
        // 获取“一票否决”信息
        findById(){
            api.findById(this.vetoId,res => {
                if(res.code === 0){
                    var data = res.data
                    this.paramData.oneVetoInfo = data
                    this.isShowLayout = true
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            },error => {
                this.$router.back(-1);
            });
        },
        //提交“一票否决”审核
        doCommit(params, callback){
            let data = {};
            data.params = params;
            api.doCommit(data,res =>{
                if(res.code===0){
                    res.msg = res.data.msg;
                    callback(res);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            })
        },
    },
}
</script>
