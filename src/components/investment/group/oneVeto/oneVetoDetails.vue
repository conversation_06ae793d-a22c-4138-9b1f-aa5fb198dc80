<template>
    <div class="mftcc-container">
        <el-container>
            <el-row>
                <mftcc-form formId="investment/busCliquesOneVetoDetailsForm" :parentVm="this" ref="busCliquesOneVetoDetailsForm"/>
            </el-row>
        </el-container>
    </div>
</template>
<script>
export default {
    name: 'oneVetoDetails',
    title: '一票否决视图-一票否决详情组件',
    props: ['oneVetoInfo'],
    data() {
        return {
            // ${meetInfo}
        }
    },
    mounted () {
        this.$refs.busCliquesOneVetoDetailsForm.setFormValue(this.oneVetoInfo);
    },
    methods: {
        // 查看项目详情（链接项目详情视图）
        getProjectDetail(){
            console.log("查看项目详情，立项项目id", this.issueReplyInfo.estabProjectId);
        },
    },
}
</script>