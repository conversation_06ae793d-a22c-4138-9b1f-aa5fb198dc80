<template>
    <div>
        <el-tag v-if="row[fieldName] === '01'" size="small" type="warning" effect="dark" style="background-color: #ffc554;border-color: #ffc554">待处理</el-tag>
        <el-tag v-if="row[fieldName] === '02'" size="small" type="warning" effect="dark" style="background-color: #ffc554;border-color: #ffc554">审批中</el-tag>
        <el-tag v-if="row[fieldName] === '03'" size="small" type="success" effect="dark">未行使</el-tag>
        <el-tag v-if="row[fieldName] === '04'" size="small" type="danger" effect="dark" style="background-color: #f11616;border-color: #f11616">已行使</el-tag>
    </div>
</template>

<script>
export default {
    name: 'oneVetoStsComponent',
    title: '一票否决列表-否决状态组件',
    props: ['fieldName', 'row'],
    data() {
        return {
        }
    },
    methods:{
    }
}
</script>
<style scoped>
    .el-tag {
        width: 80px;
        text-align:center;
        border-radius: 14px;
    }

</style>
