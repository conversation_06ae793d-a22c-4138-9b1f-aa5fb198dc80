<template>
  <div>
    <!-- <mftcc-dialog-table @callback="getSelectOption" :show.sync="tableShow" :option="tableOption"></mftcc-dialog-table> -->
    <el-dialog
      title="部门树"
      :visible.sync="tableShow"
      top="15vh"
      :modal-append-to-body="true"
      append-to-body
      class="mole-dialog"
      width="1000px"
    >
      <el-input
        placeholder="输入关键字进行过滤"
        v-model="filterText"
        suffix-icon="el-icon-search"
        style="margin: 10px;width: -webkit-fill-available;background-color: #e9ece2;"
      ></el-input>
      <el-tree
        class="filter-tree"
        :data="orgList"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        :highlight-current="true"
        :check-on-click-node="true"
        :expand-on-click-node="false"
        @node-click="testTree"
        style="font-size: 14px;font-weight:600;"
        ref="tree"
      ></el-tree>
      <div class="mftcc-container-button" style="text-align:center">
      <el-button type="primary" v-if="$hasPerm('')" @click="getSelectOption()">保 存</el-button>
      <el-button v-if="$hasPerm('')" @click="dialogFormVisible">取 消</el-button>
    </div>
    </el-dialog>
  </div>
</template>

<script>
import api from "@/api/investment/group/orgOrgs";
export default {
  name: "orgOrgsTree",
  title: "部门树",
  props: ["pkOrg"],
  data() {
    return {
      filterText: "",
      defaultProps: {
        children: "children",
        label: "name"
      },
      orgList: [],
      tableShow: false,
      pkOrg: "",
      treeData : {},
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    //显示弹框
    showDialog() {
      this.tableShow = true;
      this.getOrgTree(this.pkOrg);
    },
    //显示弹框
    dialogFormVisible() {
      this.tableShow = false;
    },
    getSelectOption() {
      this.$emit("callback", this.treeData);
      this.dialogFormVisible();
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    testTree(data) {
      this.treeData = data;
    },
    getOrgTree(pkOrg) {
      if (pkOrg == "") {
        pkOrg = "-";
      }
      api.getOrgTree(
        pkOrg,
        reponse => {
          if (reponse.code === 0) {
            var data = reponse.data;
            this.orgList = data;
          } else {
            this.$alert(reponse.msg, this.$alertTitle, {
              type: reponse.msgType,
              callback: action => {
                this.$router.back(-1);
              }
            });
          }
        },
        error => {
          this.$router.back(-1);
        }
      );
    }
  }
};
</script>
