
<template>
    <el-dialog
    title = "项目详情"
    width="100%"
    :modal="false"
    :visible.sync="isShowData"
    :before-close="handleClose"
    >
        <project-check-approve-detail-view
        v-if="display"
        :cusId="cusId"
        :estabProjectId="estabProjectId"
        :establishmentApproveId="establishmentApproveId"
        :wkfAppId="wkfAppId"
        ></project-check-approve-detail-view>
   </el-dialog>
</template>

<script>
import projectCheckApproveDetailView from "@/components/investment/flow/projectCheckApproveDetailView"
import api from '@/api/investment/apply/investmentApply'
export default {
    name:"busCliquesProjectDetails",
    data(){
        return{
            // props: ["approveId","estabProjectId","cusId","wkfAppId","investPalnId","checkApproveId","establishmentApproveId"],
            display: false,
            isShowData:false,
            estabProjectId: "",
            cusId : "",
            establishmentApproveId: "",
            wkfAppId:""
        };
    },
    components:{projectCheckApproveDetailView},
    methods: {
     openDialog(estabProjectId){
         console.log(estabProjectId)
         this.estabProjectId =  estabProjectId;
         this.isShowData = true;
         api.findById(this.estabProjectId, res =>{
             this.cusId = res.data.cusId;
             this.establishmentApproveId = res.data.establishmentApproveId;
             this.wkfAppId = res.data.wkfAppId;
             this.display = true;
         });

     },
        handleClose(done) {
            this.display = false;
            done();
        }
 }
}
</script>

<style>

</style>
