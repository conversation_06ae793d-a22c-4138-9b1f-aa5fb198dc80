<template>
    <div>
        <i class="el-icon-s-order" style="color: #409eff;" @click="showDialog">查看</i>

        <el-dialog  title="补充材料意见" :visible.sync="isShowDialog" width="60%">
            <div class="reportContent" v-html="row[fieldName]"></div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'supplementOpinionComponent',
    title: '补充材料意见-补充意见组件（点击弹框展示补充意见）',
    props: ['fieldName', 'row'],
    data() {
        return {
            isShowDialog: false
        }
    },
    methods:{
        showDialog(){
            this.isShowDialog = true
        }
    }
}
</script>
<style scoped>
    .reportContent {
        font-weight: 400;
        /*padding: 9px;*/
        padding: 30px;
        /*min-height: 50px;*/
    }
</style>
