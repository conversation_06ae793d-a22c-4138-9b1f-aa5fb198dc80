<template>
  <div class="pt mftcc-list">
    <mftcc-dialog-table @callback="getSelectOption" :show.sync="tableShow" :option="tableOption"></mftcc-dialog-table>
  </div>
</template>

<script>
import api from "@/api/investment/group/bdPsndoc";
export default {
  name: "bdPsndocList",
  title: "人员基本信息列表",
  data() {
    return {
      tableShow: false,
      tableOption: {
        title: "用户选择",
        tableId: "investment/bdPsndocTable",
        search: true,
        type: "radio", //checkbox,radio
        data: [],
        value: [],
        initSearchData: {enablestate:2},
        isPagination: true,
        url: "/mftcc-investment-server/sys/bdPsndoc/findByPage"
      }
    };
  },
  methods: {
    //显示弹框
    showDialog(){
        this.tableShow = true;
    },
    //显示弹框
    dialogFormVisible(){
        this.tableShow = false;
    },
    getSelectOption(data){
         this.$emit('callback', data);
    },
    query(index, row) {
      this.$router.push({
        path: "/group/bdPsndocDetails",
        query: { addr: row.addr }
      });
    },
    insert() {
      this.$router.push({ path: "/group/bdPsndocInsert" });
    },
    onSearch() {
      let formData = this.$refs.bdPsndocSearchForm.getFormValue();
      this.$refs.bdPsndocList.search(formData);
    },
    edit(index, row) {
      this.$router.push({
        path: "/group/bdPsndocUpdate/",
        query: { addr: row.addr }
      });
    },
    delete(index, row) {
      this.$confirm("此操作将永久删除该记录, 是否继续?", this.$alertTitle, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        api.deleteById(row.addr, res => {
          if (res.code === 0) {
            this.$message({
              type: res.msgType,
              dangerouslyUseHTMLString: true,
              message: res.msg
            });
            this.$refs.bdPsndocList.refresh();
          } else {
            this.$alert(res.msg, this.$alertTitle, {
              type: res.msgType,
              dangerouslyUseHTMLString: true
            });
          }
        });
      });
    },
    back(){

    }
  }
};
</script>
