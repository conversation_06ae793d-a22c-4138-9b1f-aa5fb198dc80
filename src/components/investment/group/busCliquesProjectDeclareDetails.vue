<template>
    <el-row>
        <mftcc-form :formId="formId" :parentVm="this" ref="busCliquesProjectDeclareDetailsForm"></mftcc-form>
        <bus-cliques-project-details v-if="display" ref="openDialog"></bus-cliques-project-details>
    </el-row>
</template>
<script>
import busCliquesProjectDetails from '@/components/investment/group/busCliquesProjectDetails'
import api from '@/api/investment/group/busCliquesProjectDeclare'
export default {
    name: 'busCliquesProjectDeclareDetails',
    title: '超权限项目申报表详情',
    props: ["declareId", "isQcyProject"],
    components: {
      busCliquesProjectDetails
    },
    data() {
        return {
            formId: '',
            estabProjectId: "",
            display: false
        }
    },
    mounted() {
        if (this.isQcyProject === '1'){
            this.formId = 'investment/busCliquesProjectDeclareDetailsFormQcy'
        } else {
            this.formId = 'investment/busCliquesProjectDeclareDetailsForm'
        }
        if(this.declareId){
            this.findById();
        }
    },
    methods: {
        // 获取项目详情信息
        getProjectDetail(){
            this.$refs.openDialog.openDialog(this.estabProjectId)
        },
        findById(){
            api.findById(this.declareId,res => {
                this.estabProjectId = res.data.estabProjectId;
                if(res.code === 0){
                    var data = res.data;
                    this.$refs.busCliquesProjectDeclareDetailsForm.setFormValue(data);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
                this.display = true;
            },error => {
                this.$router.back(-1);
            });
        },
        back(){
            this.$router.back(-1);
        }
    },
}
</script>
