<template>
    <el-row>
<!--        <div class="mftcc-web">-->
<!--            <open-one-file-compont-->
<!--                    v-if="templateShowFlag"-->
<!--                    :fileId="fileId"-->
<!--                    :docType="docType"-->
<!--                    :downloadFlag="downloadFlag"-->
<!--                    :reLoadFlag="reLoadFlag"-->
<!--                    :parmList="parmList"/>-->
<!--        </div>-->
        <mftcc-form formId="investment/busCliquesLoanPremiseDetailsForm" :parentVm="this" ref="busCliquesLoanPremiseDetailsForm"></mftcc-form>
        <bus-cliques-project-details ref="openDialog"></bus-cliques-project-details>
    </el-row>
</template>
<script>
import api from '@/api/investment/group/busCliquesLoanPremise'
import busCliquesProjectDetails from '@/components/investment/group/busCliquesProjectDetails'
import openOneFileCompont from '@/components/doc/file/openOneFileCompont';
export default {
    name: 'busCliquesLoanPremiseDetails',
    title: '重大部放款前提详情',
    props: ["loanPremiseApproveId","estabProjectId","projectId","loanPremiseId"],
    components: {
        busCliquesProjectDetails,
        openOneFileCompont:openOneFileCompont
    },
    data() {
        return {
            // 模板展示组件参数---开始
            templateShowFlag: false,     //是否展示模板展示组件
            fileId: "",                  //文件唯一编号或者模板唯一编号
            docType: "",                 //文件类型 1-附件 2-模板
            downloadFlag: "",           //是否提供下载功能（如果是模板设置0）
            reLoadFlag: "",             //是否需要重新加载模板 0-否1-是
            parmList: {},                //重新加载模板需要的参数（如果不需要重新加载可以不传）
            // 模板展示组件参数---结束
        }
    },
    mounted() {
        if(this.loanPremiseId){
            this.findById()
            // this.getTemplate()
        }
    },

    methods: {
        // 获取模板展示组件参数信息
        getTemplate(){
            this.templateShowFlag = false
            api.getOpenOneFileCompontParms(this.loanPremiseId,res => {
                if(res.code === 0){
                    const data = res.data
                    this.fileId = data.fileId
                    this.docType = data.docType
                    this.downloadFlag = data.downloadFlag
                    this.reLoadFlag = data.reLoadFlag
                    this.parmList = data.parmList
                    this.templateShowFlag = true
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                    })
                }
            },error => {
            });
        },
        // 获取项目详情信息
        getProjectDetail(){
        this.$refs.openDialog.openDialog(this.estabProjectId);
        },
        findById(){
            api.findById(this.loanPremiseId,res => {
                if(res.code === 0){
                    var data = res.data;
                    this.$refs.busCliquesLoanPremiseDetailsForm.setFormValue(data);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            },error => {
                this.$router.back(-1);
            });
        },
        back(){
            this.$router.back(-1);
        }
    },
}
</script>
