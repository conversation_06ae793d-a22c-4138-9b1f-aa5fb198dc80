<template>
<div>
  <div class="header">
    <!--年份选择-->
    <el-select class="yearsSelect" v-model="yearvalue" @change="yearsChange($event,yearvalue)" filterable placeholder="请选择">
      <el-option
        v-for="item in yearOptions"
        :key="item.FORECASTMONTH"
        :value="item.FORECASTMONTH">
      </el-option>
    </el-select>
    <!--指标选择-->
    <el-select v-model="indexvalue" @change="indexChange($event,indexvalue)" filterable placeholder="请选择">
      <el-option
        v-for="item in indexOptions"
        :key="item.INDICATORNAME"
        :value="item.INDICATORNAME">
      </el-option>
    </el-select>
  </div>
    <!--echart堆叠柱状图-->
    <div id="main">

    </div>
</div>
</template>

<script>
import api from '@/api/investment/apply/busMacroe';
import * as echarts from 'echarts';
//formatDate日期格式化js
import {formatDate} from '@/api/investment/apply/date.js';




export default {
  name: 'investmentMacroeChart',
  title: '宏观经济指标Echart列表',
  props: ["macrclick","charclick"],
  data() {
    return {
      //获取指标年份
      yearOptions:[{

      }],
      //获取指标名称
      indexOptions: [{

        }],

      yearvalue: '', //年份value
      indexvalue: '', //指标名称value
      summary: [],    //指标年份和名称
      summaryList: [{}],    //宏观预测综合和宏观预测明细
      macrclick: this.macrclick,  //指标Echart图
      charclick: this.charclick,  //指标table列表
    }
  },
  components: {


  },


  // 生命周期 - 创建完成（可以访问当前this实例）
  created () {

  },

  methods: {

    // 初始化宏观指标日期，名称数据
    initConomicValue(dataobj){
      api.initConomicValue(dataobj, res => {
        if (res.code === 0) {
          //获取宏观指标年份
          this.yearOptions = res.data.allMonth;
          //把后台数据列如202010格式的年份格式为2020年10月
          this.yearOptions.forEach((item, index) => {
            let newdate = item.FORECASTMONTH.replace(/(\d{4})(?=\d)/g, "$1 ");
            var date = new Date(newdate);
            item.FORECASTMONTH = formatDate(date, 'yyyy年MM月');
          });
          //获取第一条年份日期value
          this.yearOptions.every((item, index) => {
            this.yearvalue = item.FORECASTMONTH;
            return false;
          });

          //获取宏观指标名称
          this.indexOptions = res.data.allName;
          //获取第一条指标名称value
          this.indexOptions.every((item, index) => {
            this.indexvalue = item.INDICATORNAME;
            return false;
          });

          //获取选择框里已选中的值
          this.yearsChange(this.yearvalue);
          this.indexChange(this.indexvalue);

        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true,
            callback: action => {
              // this.$router.back(-1);
            }
          });
        }
      }, error => {
        // this.$router.back(-1);
      });
    },

    //获取年份选中改变事件
    yearsChange(yearvalue){
      //获取指标年份列如202010
      let yearsitem = yearvalue.substring(0,4)+yearvalue.substring(5,7);
      this.summary.yearsitem = yearsitem;
      if(this.summary.indexvalue == null){
        console.log("只获取到指标年份"+this.summary.yearsitem);
      }else{
        //查询宏观预测综合指标年份
        this.yearsIndexSummaryList();
      }
    },

     //获取指标名称选中改变事件
    indexChange(indexvalue){
      this.summary.indexvalue = indexvalue;
      if(this.summary.yearsitem == null){
        console.log("只获取到指标名称"+this.summary.indexvalue);
      }else{
        //查询宏观预测综合指标年份
        this.yearsIndexSummaryList();
      }
    },

    //查询宏观预测综合指标年份ecart数据
    yearsIndexSummaryList() {
      let data = {};
      data.yearsitem = this.summary.yearsitem;
      data.indexvalue = this.summary.indexvalue;
      api.getSummaryList(data, res => {
      if (res.code === 0) {
          //获取后台是否有宏观预测综合时
          if(res.data.summaryList != null){
            this.summaryList.summaryList = res.data.summaryList;
          }else{
            //选择框选择时无数据时清空
            this.summaryList.summaryList = null;
          }
          //获取后台是否有宏观预测明细时
          if(res.data.croforeCastList!=null){
            this.summaryList.croforeCastList = res.data.croforeCastList;
          }else{
            //选择框选择时无数据时清空
            this.summaryList.croforeCastList = null;
          }
          //调用Echarts显示
          this.myEcharts();

            } else {
            this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
            });
          }
        })
    },

    //Echarts柱状图，折线图
    myEcharts() {
      var chartDom = document.getElementById('main');
      var myChart = echarts.init(chartDom);
      var option;

      let xAxisData = [];
      let data1 = [];
      let data2 = [];
      let data3 = [];
      let data4 = [];
      let data5 = [];


      //获取X轴12个月
      for (let i = 1; i < 13; i++) {
        xAxisData.push(i + '月');
        //获取宏观预测明细和宏观预测综合12个月的list
        let estid = null;
        let estMax = null;
        let estMin = null;
        let summaryid = null;
        let forecastMax = null;
        let forecastMin = null;
        let forecastMid = null;
        //Echart的series的data定义对象
        let data1jsons = {};
        let data2jsons = {};
        let data3jsons = {};
        let data4jsons = {};
        let data5jsons = {};
        //宏观预测明细有数据时
        if(this.summaryList.croforeCastList!=null){
          let month = this.summaryList.croforeCastList.RPT_DATE.substring(4,6);
            if(i == month){
              //宏观预测明细id
              estid = this.summaryList.croforeCastList.OBJECT_ID;
              //宏观预测明细最大预测值
              estMax = this.summaryList.croforeCastList.EST_MAX;
              //宏观预测明细最小预测值
              estMin = this.summaryList.croforeCastList.EST_MIN ==null ? 0 : this.summaryList.croforeCastList.EST_MIN;
            }
            //如宏观预测明细当前月有值则赋值，无则给0
            let foreMax = estMax == null ? 0 : estMax;
            let foreMin = estMin == null ? 0 : estMin;
            let croforeCastid = estid == null ? 0 : estid;
            data1jsons.id = croforeCastid;
            data1jsons.value = foreMin;
            data1.push(data1jsons);
            data2jsons.id = croforeCastid;
            data2jsons.value = foreMax;
            data2.push(data2jsons);

          }else{
            //如宏观预测明细当前月有值则赋值，无则给0
            let foreMax = estMax == null ? 0 : estMax;
            let foreMin = estMin == null ? 0 : estMin;
            let croforeCastid = estid == null ? 0 : estid;
            data1jsons.id = croforeCastid;
            data1jsons.value = foreMin;
            data1.push(data1jsons);
            data2jsons.id = croforeCastid;
            data2jsons.value = foreMax;
            data2.push(data2jsons);
          }

        if(this.summaryList.summaryList!=null){
          let summarymonth = this.summaryList.summaryList.FORECAST_MONTH.substring(4,6);
          if(i == summarymonth){
            //宏观预测综合id
            summaryid = this.summaryList.summaryList.OBJECT_ID;
            //宏观预测综合指标最大值
            forecastMax = this.summaryList.summaryList.FORECAST_MAX;
            //宏观预测综合指标最小值
            forecastMin = this.summaryList.summaryList.FORECAST_MIN;
            //宏观预测综合指标中位值
            forecastMid = this.summaryList.summaryList.FORECAST_MID;

          }
          //如宏观预测综合当前月有值则赋值，无则给0
          let castMax = forecastMax == null ? 0 : forecastMax;
          let castMin = forecastMin == null ? 0 : forecastMin;
          let castMid = forecastMid == null ? 0 : forecastMid;
          let castid = summaryid == null ? 0 : summaryid;
          data3jsons.id = castid;
          data3jsons.value = castMax;
          data3.push(data3jsons);
          data4jsons.id = castid;
          data4jsons.value = castMin;
          data4.push(data4jsons);
          data5jsons.id = castid;
          data5jsons.value = castMid;
          data5.push(data5jsons);
        }else{
          //如宏观预测综合当前月有值则赋值，无则给0
          let castMax = forecastMax == null ? 0 : forecastMax;
          let castMin = forecastMin == null ? 0 : forecastMin;
          let castMid = forecastMid == null ? 0 : forecastMid;
          let castid = summaryid == null ? 0 : summaryid;
          data3jsons.id = castid;
          data3jsons.value = castMax;
          data3.push(data3jsons);
          data4jsons.id = castid;
          data4jsons.value = castMin;
          data4.push(data4jsons);
          data5jsons.id = castid;
          data5jsons.value = castMid;
          data5.push(data5jsons);
        }

      }

      var emphasisStyle = {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0,0,0,0.3)'
        }
      };
      option = {
        legend: {
          data: ['预测明细最小值', '预测明细最大值', '预测综合最小值', '预测综合中位值', '预测综合最大值'],
          left: '10%',
          top: "90%",
        },
        brush: {
          toolbox: ['rect', 'polygon'],
          xAxisIndex: 0
        },
        toolbox: {
          feature: {
            magicType: {
              type: ['stack']
            },
            dataView: {},
            right: "102"
          }
        },
        tooltip: {},
        xAxis: {
          data: xAxisData,
          name: '(月)',
          axisLine: { onZero: true },
          splitLine: { show: false },
          splitArea: { show: false }
        },
        yAxis: {
          name: '(%)'
        },
        grid: {
          height:250,
          bottom: 100
        },
        series: [
          {
            name: '预测明细最小值',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            data: data1,
            itemStyle: {
                normal: {
                color: '#409EFF',
                //这里修改了柱子的颜色以及透明度，注意如果要修改透明度，一定要配置在itemStyle里面，直接写在外面不起作用。
                }
              },
          },
          {
            name: '预测明细最大值',
            type: 'bar',
            stack: 'one',
            emphasis: emphasisStyle,
            data: data2,
            itemStyle: {
                normal: {
                color: '#40DBFF',
                //这里修改了柱子的颜色以及透明度，注意如果要修改透明度，一定要配置在itemStyle里面，直接写在外面不起作用。
                }
              },
          },
          {
            name: '预测综合最小值',
            type: 'line',
            stack: 'two',
            emphasis: emphasisStyle,
            data: data3,
            itemStyle: {
                normal: {
                color: '#77FF44',
                //这里修改了柱子的颜色以及透明度，注意如果要修改透明度，一定要配置在itemStyle里面，直接写在外面不起作用。
                }
              },
          },
          {
            name: '预测综合中位值',
            type: 'line',
            stack: 'two',
            emphasis: emphasisStyle,
            data: data4,
            itemStyle: {
                normal: {
                color: '#FF9AE0',
                //这里修改了柱子的颜色以及透明度，注意如果要修改透明度，一定要配置在itemStyle里面，直接写在外面不起作用。
                }
              }
          },
          {
            name: '预测综合最大值',
            type: 'line',
            stack: 'two',
            emphasis: emphasisStyle,
            data: data5,
            itemStyle: {
                normal: {
                color: '#FF3838',
                //这里修改了柱子的颜色以及透明度，注意如果要修改透明度，一定要配置在itemStyle里面，直接写在外面不起作用。
                }
              }
          }
        ]
      };
      myChart.on('brushSelected', function (params) {
        var brushed = [];
        var brushComponent = params.batch[0];
        for (var sIdx = 0; sIdx < brushComponent.selected.length; sIdx++) {
          var rawIndices = brushComponent.selected[sIdx].dataIndex;
          brushed.push('[Series ' + sIdx + '] ' + rawIndices.join(', '));
        }
        myChart.setOption({
          // title: {
          //   backgroundColor: '#333',
          //   text: 'SELECTED DATA INDICES: \n' + brushed.join('\n'),
          //   bottom: 0,
          //   right: '10%',
          //   width: 100,
          //   textStyle: {
          //     fontSize: 12,
          //     color: '#fff'
          //   }
          // }
        });
      });

      //当点击Echart柱状图，折线图时
      myChart.on('click',(params)=>{
          let returnParentData = {};
          this.macrclick = false;
          this.charclick = true;
          returnParentData.macrclick = this.macrclick;
          returnParentData.charclick = this.charclick;
          returnParentData.dataid = params.data.id;
          //向父组件传值
          this.$emit("listenTochildEvent",returnParentData);

      });

      option && myChart.setOption(option);
      //找到canvas设置宽高
      var $canvas = $("#main").find("canvas");
      $canvas.width(430);
      $canvas.heghit(430);

    },

  },
  mounted() {

      this.initConomicValue();
      // this.myEcharts();
    }
}
</script>
<style scoped>
  #main {
    width: 1200px;
    margin:0 auto;
    height: 500px;
    padding-top: 100px;
    min-height: 350px;
    background-color: #fff;
  }
  .header{
    margin-top: 20px;
  }
  .header >>> .el-input__inner{
    background-color:rgba(64, 158, 255, 0.1);
  }
  .yearsSelect{
    width: 120px;
    margin: 0 10px;
  }

</style>
