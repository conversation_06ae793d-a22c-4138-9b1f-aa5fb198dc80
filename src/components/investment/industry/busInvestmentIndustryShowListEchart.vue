<template>
  <div class="monitorTop">
        <mftcc-table :tableId="tableId" :initSearchData="tableInitSearchData"
            :parentVm="this" v-if="tableFlag" ref="investmentIndustryList" class="user-table"></mftcc-table>

          <div class="icon_header" v-if="iconFlag">
            <svg class="icon" aria-hidden="true" @click="tableSwitch('chart')">
              <use xlink:href="#icon-shujufenxi"></use>
            </svg>
            <svg class="icon" aria-hidden="true" @click="tableSwitch('table')">
              <use xlink:href="#icon-liebiaoxingshi"></use>
            </svg>
          </div>

          <!--行业报告类型显示块-->
          <investmentIndustryShowQueryList v-if="chartFlag"></investmentIndustryShowQueryList>

            <!--弹出对话框-->
            <el-dialog
              :title="industryTitle"
              :visible.sync="formVisible"
              :center="true"
              top="25vh"
              :modal-append-to-body="false"
              class="mole-dialog"
              width="100%"
              :fullscreen="fullscreen"
            >
            <div class="mftcc-container" style="max-height: 700px;" v-if="viewFlag" :key="time">
              <div class="mftcc-web">
              <openOneFileCompont
                :parmList="parmList"
                :reLoadFlag="reLoadFlag"
                :fileId="fileId"
                :docType="docType"
                :downloadFlag="downloadFlag">
              </openOneFileCompont>
              </div>
            </div>
          </el-dialog>
    </div>
</template>

<script>
import api from '@/api/investment/apply/busIndustry';
import openOneFileCompont from '@/components/doc/file/openOneFileCompont';
import investmentIndustryShowQueryList from "@/components/investment/industry/investmentIndustryShowQueryList"


export default {
  name: 'busInvestmentIndustryShowEchartList',
  title: '行业报告显示表单',
  data() {
    return {
      tableInitSearchData: {
        industryType: this.$route.query.industryType,
      },

      industryTitle : "",
      fileId: "",           //文件唯一编号或者模板唯一编号
      docType: "1",          //文件类型 1-附件 2-模板
      downloadFlag: "1",     //是否提供下载功能 1下載，0不下載
      reLoadFlag: "1",       //是否需要重新加载模板 0-否1-是
      parmList: {},          //重新加载模板需要的参数（如果不需要重新加载可以不传）
      viewFlag:false,
      formVisible:false,      //是否显示 Dialog
      fullscreen:true,        //是否为全屏 Dialog
      //industryType: this.$route.query.industryType,       //行业报告类型
      pageNo:1,
      pageSize:10,
      tableId: "investment/busInvestmentIndustryShowEchartTable",
      industryReportType: "",
      activeNames:'1',
      tableFlag:true,
      chartFlag:false,
      iconFlag: true,   //图标标识

    }
  },
  components: {
    //要件预览
    openOneFileCompont:openOneFileCompont,
    //行业报告类型显示块
    investmentIndustryShowQueryList,
  },

  methods: {

    //切换Echart或表单
    tableSwitch(name){
        this.activeNames = ['0'];
        if(name == "table"){
          this.tableFlag = true;
          this.chartFlag = false;

        }else if(name == "chart"){
          this.time = new Date().getTime();
          this.tableFlag = false;
          this.chartFlag = true;
          this.iconFlag = false;

        }
    },

    //弹框行业资讯要件详情
    query(index, row) {
      this.formVisible = true;
      this.industryTitle = row.industryTitle;
      this.docType = "1";
      this.fileId = row.fileId;
      this.viewFlag = true;
      this.time = new Date().getTime();
    },

    //行业报告列表查询
    findIndustryByPage(){
      let data={};
      data.industryType =this.industryType;
      data.pageNo=this.pageNo;
      data.pageSize=this.pageSize;
      data.tableId=this.tableId;
      api.findIndustryByPage(data,res => {
        if(res.code === 0){
          this.$refs.investmentIndustryList.option.tableData=res.list.records;

        }else{
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true
          });
        }
      });
    },

    callBack () {
      this.$router.back(-1);
      this.$router.push({
        path: "/industry/investmentIndustryShowList",
        query: { industryId: this.industryId }
      });
    },

    handleClick(tab, event) {
        console.log(tab, event);
      },

    //跳转行业报告新增界面
    insert() {
      this.$router
          .push({path: '/apply/investmentIndustryInsert', query: {industryId: this.industryId}});
    },

    //跳转行业报告修改界面
    edit(index, row){
        this.$router
                .push({path: '/apply/investmentIndustryDetailsInfo', query: {industryId: row.industryId}});
    },

    //行业报告删除操作
    delete(index, row){
        this.$confirm('此操作将永久删除该记录, 是否继续?', this.$alertTitle, {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            let data = {};
            data.industrydata = row;
            api.deleteData(data,res => {
                if(res.code === 0){
                    res.msg = res.data.msg;
                    this.$message({
                        type: res.msgType,
                        dangerouslyUseHTMLString:true,
                        message: res.msg
                    })
                    this.$refs.investmentIndustryList.refresh();
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true
                    });
                }
            });
        });
    },

    //行业报告列表搜索
    onSearch() {
      let formData = this.$refs.investmentIndustryList.getFormValue();
      this.$refs.investmentIndustryList.search(formData);
    },
    mounted () {
    // this.findIndustryByPage();
  }

  }
}
</script>
<style scoped>

  .icon_header{
    width: 50px;
    right: 420px;
    top: 80px;
    position: absolute;
  }
  .icon{
    margin: 0 2px 0 2px;
  }


</style>


