<template>
  <div class="monitorTop">
            <el-tree
              :data="userTreeData"
              show-checkbox
              default-expand-all
              node-key="brNo"
              ref="tree"
              highlight-current
              :props="defaultProps">
            </el-tree>
    </div>
</template>

<script>
import api from '@/api/investment/apply/busMacroe';


export default {
  name: 'investmentMacroeTreeList',
  title: '宏观经济可见范围树',
  data() {
    return {
      tableInitSearchData: {
        caseId: this.$route.query.caseId,
      },
      formVisible:false,      //是否显示 Dialog
      fullscreen:true,        //是否为全屏 Dialog
      userTreeData: [],

      defaultProps: {
        children: "children",
        label: "brName",
        isLeaf: "isTure",
      },

    }
  },
  components: {

  },
  mounted() {
    console.log(this.userTreeData)

    },
  methods: {



  }
}
</script>
