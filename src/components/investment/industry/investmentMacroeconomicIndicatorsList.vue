<template>
  <div >
        <!--宏观经济指标Echart图-->
        <investmentMacroeChart v-if="macroeclick" :macrclick="macroeclick" :charclick="chartclick" @listenTochildEvent="showMacroeChart"></investmentMacroeChart>
        <!--宏观经济指标管理列表-->
        <investmentMacroeEchartList v-if="chartclick" :echartid="echartid"></investmentMacroeEchartList>

    </div>
</template>

<script>
import api from '@/api/investment/apply/busMacroe';
import investmentMacroeChart from "@/components/investment/industry/investmentMacroeChart"
import investmentMacroeEchartList from "@/components/investment/industry/investmentMacroeEchartList"



export default {
  name: 'investmentMacroeconomicIndicatorsList',
  title: '宏观经济指标管理列表',
  data() {
    return {
      tableInitSearchData: {
        macroscopicId: this.$route.query.macroscopicId,
      },
    macroeclick: true,  //指标Echart图
    chartclick: false,  //指标table列表
    echartid: '',       //统计图id

    }
  },
  components: {
    //echart指标
    investmentMacroeChart,
    investmentMacroeEchartList,

  },

  // 生命周期 - 创建完成（可以访问当前this实例）
  created () {

  },

  methods: {

    //监听子组件传入值
    showMacroeChart(data){
      this.macroeclick = data.macrclick;
      this.chartclick = data.charclick;
      this.echartid = data.dataid;
    }


  },

  mounted() {

  },
}
</script>
