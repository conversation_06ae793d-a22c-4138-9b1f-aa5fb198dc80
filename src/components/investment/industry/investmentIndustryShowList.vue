<template>
  <div class="mftcc_home_box">
    <div class="mftcc_home_header">
      <div class="heder_item">
        <div class="icon_header">
                <a href="javascript:;" @click="tableSwitch('chart')">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-shujufenxi"></use>
                  </svg>
                </a>
                <a href="javascript:;" @click="tableSwitch('table')">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-liebiaoxingshi"></use>
                  </svg>
                </a>
          </div>

        <!--行业报告类型显示块-->
        <investmentIndustryShowQueryList v-if="chartFlag"></investmentIndustryShowQueryList>
        <!--行业报告显示表单列表Echart-->
        <busInvestmentIndustryShowListEchart v-if="tableFlag"></busInvestmentIndustryShowListEchart>
    </div>
    </div>
  </div>
</template>

<script>
import investmentIndustryShowQueryList from "@/components/investment/industry/investmentIndustryShowQueryList"
import busInvestmentIndustryShowListEchart from "@/components/investment/industry/busInvestmentIndustryShowListEchart"



export default {
  name: 'investmentIndustryShowList',
  title: '行业报告类型显示管理列表',
  data() {
    return {
      activeNames:'1',
      tableFlag:false,   //行业表单标识
      chartFlag:true,   //行业块标识

    }
  },
  components: {
    //行业报告类型显示块
    investmentIndustryShowQueryList,
    //行业报告显示表单列表Echart
    busInvestmentIndustryShowListEchart
  },

  methods: {

    //切换Echart或表单
    tableSwitch(name){
        this.activeNames = ['0'];
        if(name == "table"){
          this.tableFlag = true;
          this.chartFlag = false;

        }else if(name == "chart"){
          this.time = new Date().getTime();
          this.tableFlag = false;
          this.chartFlag = true;

        }
    },

  },
  mounted () {

  }
}
</script>
<style scoped src="@/assets/investment/css/busIndustryStyle.css"/>


