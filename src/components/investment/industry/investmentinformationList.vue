<template>
  <div class="monitorTop">
        <mftcc-table tableId="investment/busInvestmentResearchTable" :initSearchData="tableInitSearchData"
            :parentVm="this" ref="investmentResearchList" class="user-table"></mftcc-table>

            <!--弹出对话框-->
            <el-dialog
              :title="researchTitle"
              :visible.sync="formVisible"
              :center="true"
              top="25vh"
              :modal-append-to-body="false"
              class="mole-dialog"
              width="100%"
              :fullscreen="fullscreen"
            >
            <div class="mftcc-container" style="max-height: 700px;" v-if="viewFlag" :key="time">
              <div class="mftcc-web">
              <openOneFileCompont
                :parmList="parmList"
                :reLoadFlag="reLoadFlag"
                :fileId="fileId"
                :docType="docType"
                :downloadFlag="downloadFlag">
              </openOneFileCompont>
              </div>
            </div>
          </el-dialog>
    </div>
</template>

<script>
import api from '@/api/investment/apply/busResearch'
import openOneFileCompont from '@/components/doc/file/openOneFileCompont';


export default {
  name: 'investmentinformationList',
  title: '投资案例列表',
  data() {
    return {
      tableInitSearchData: {
        researchId: this.$route.query.researchId,
      },
      researchTitle : "",
      fileId: "",           //文件唯一编号或者模板唯一编号
      docType: "1",          //文件类型 1-附件 2-模板
      downloadFlag: "1",     //是否提供下载功能 1下載，0不下載
      reLoadFlag: "1",       //是否需要重新加载模板 0-否1-是
      parmList: {},          //重新加载模板需要的参数（如果不需要重新加载可以不传）
      viewFlag:false,
      formVisible:false,      //是否显示 Dialog
      fullscreen:true,        //是否为全屏 Dialog

    }
  },
  components: {
    //要件预览
    openOneFileCompont:openOneFileCompont
  },

  methods: {
    //弹框投资资讯要件详情
    query(index, row) {
      this.formVisible = true;
      this.researchTitle = row.researchTitle;
      this.docType = "1";
      this.fileId = row.fileId;
      this.viewFlag = true;
      this.time = new Date().getTime();

      //加载详情页面
        // this.$router.push({
        //   path: '/apply/investmentResearchDetails', query: {
        //     reloadFlag:true,
        //     researchId: row.researchId
        //   }
        // });

    },

    handleClick(tab, event) {
        console.log(tab, event);
      },

    //跳转资讯新增界面
    insert() {
      this.$router
          .push({path: '/apply/investmentResearchInsert', query: {researchId: this.researchId}});
    },

    //跳转资讯修改界面
    edit(index, row){
        this.$router
                .push({path: '/apply/investmentResearchDetailsInfo', query: {researchId: row.researchId}});
    },

    //资讯删除操作
    delete(index, row){
        this.$confirm('此操作将永久删除该记录, 是否继续?', this.$alertTitle, {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            let data = {};
            data.researchdata = row;
            api.deleteData(data,res => {
                if(res.code === 0){
                    res.msg = res.data.msg;
                    this.$message({
                        type: res.msgType,
                        dangerouslyUseHTMLString:true,
                        message: res.msg
                    })
                    this.$refs.investmentResearchList.refresh();
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true
                    });
                }
            });
        });
    },

    //投资资讯列表搜索
    onSearch() {
      let formData = this.$refs.investmentResearchList.getFormValue();
      this.$refs.investmentResearchList.search(formData);
    },
  }
}
</script>
