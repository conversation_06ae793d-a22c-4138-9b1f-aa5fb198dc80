<template>
  <!-- <div class="mftcc_home_box">
    <div class="mftcc_home_header">
      <div class="heder_item"> -->

        <div class="item_box" v-if="chartFlag">
          <div class="bus_item"
            v-for="(item, index) in busItemList"
            :key="index"
            >
              <div class="bus_item_desc" @click="goListPage(item)">
                <a style="cursor: pointer">
                  {{ item.wayName }}
                </a>
              </div>
              <div class="bus_item_data" :style="getWayColor(item.wayColor)" @click="goListPage(item)">
                <a style="cursor: pointer">
                  {{ item.industryNum }}
                </a>
                <a class="fen">
                  份
                </a>
              </div>
              <img
                class="bus_item_icon"
                :src="item.wayIcon"
              />
          </div>

        </div>
      <!-- </div>
    </div>
  </div> -->
</template>

<script>
import api from '@/api/investment/apply/busIndustry';


export default {
  name: 'investmentIndustryQueryList',
  title: '行业报告类型显示管理块',
  data() {
    return {
      tableInitSearchData: {
        industryId: this.$route.query.industryId,
      },
      busItemList: [],        //行业报告信息
      getWayColor: function(val){
        if(val == '1'){
          return {'color':'#61CCEB'}
        }else if(val == '2'){
          return {'color':'#8E5FEC'}
        }else if(val == '3'){
          return {'color':'#EF4F4F'}
        }else if(val == '4'){
          return {'color':'#F8BE34'}
        }else {
          return {'color':'#7CD023'}
        }
      },
      activeNames:'1',
      tableFlag:false,
      chartFlag:true,

    }
  },
  components: {

  },

  methods: {

    //获取行业报告统计信息
    getProjectDataStatistics () {
      api.getProjectDataStatistics("", res => {
        if (res.code === 0) {
          this.busItemList = res.data.list;
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true,
            callback: action => {
              this.$router.back(-1);
            }
          });
        }
      });
    },


    //获取行业报告统计颜色
    // getWayColor(item, index){
    //   if(item.wayColor=="1"){
    //     this.bus_item_data="bus_item_data";
    //   }else if(item.wayColor=="2"){

    //   }
    // },

    //点击进入对应的页面
    goListPage (item) {
      this.$router.push({ path: '/apply/busInvestmentIndustryList',query:{industryType: item.wayNo}});
    },

  },
  mounted () {
    this.getProjectDataStatistics();
  }
}
</script>
<style scoped src="@/assets/investment/css/busIndustryStyle.css"/>


