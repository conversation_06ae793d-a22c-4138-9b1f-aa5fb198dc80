<template>
  <div >
      <!--自定义tab标签-->
      <div class="tabs">
        <div :class="tabA" ref="line" @click="toggleTab('investmentMacroeconomicIndicatorsShowList')"><a>
          宏观经济指标
        </a></div>
        <div :class="tabB" ref="line" @click="toggleTab('investmentMacroeconomicReportShowList')"><a>
          宏观经济报告
        </a></div>
      </div>
      <!--宏观经济指标显示,报告显示组件-->
      <investment-Macroeconomic-Indicators-ReportList :is="currentTab" keep-alive></investment-Macroeconomic-Indicators-ReportList>
    </div>
</template>

<script>
import api from '@/api/investment/apply/busMacroe';
import investmentMacroeconomicIndicatorsShowList from "@/components/investment/industry/investmentMacroeconomicIndicatorsShowList"
import investmentMacroeconomicReportShowList from "@/components/investment/industry/investmentMacroeconomicReportShowList"



export default {
  name: 'investmentMacroeConomicShowList',
  title: '宏观经济显示列表',
  data() {
    return {
      currentTab: 'investmentMacroeconomicIndicatorsShowList', // currentTab 用于标识当前触发的子组件
      tabA:"tabline",   //切换tab样式
      tabB:"tab",
      tableInitSearchData: {
        macroscopicId: this.$route.query.macroscopicId,
      },

      macroscopicTitle: '',        //标题
      fileId: "",           //文件唯一编号或者模板唯一编号
      docType: "1",          //文件类型 1-附件 2-模板
      downloadFlag: "1",     //是否提供下载功能 1下載，0不下載
      reLoadFlag: "1",       //是否需要重新加载模板 0-否1-是
      parmList: {},          //重新加载模板需要的参数（如果不需要重新加载可以不传）
      viewFlag:false,
      formVisible:false,      //是否显示 Dialog
      fullscreen:true,        //是否为全屏 Dialog

    }
  },
  components: {
    //宏观经济指标显示
    investmentMacroeconomicIndicatorsShowList,
    //宏观经济报告显示
    investmentMacroeconomicReportShowList,

  },

  methods: {

    toggleTab: function(tab) {
      this.currentTab = tab; // tab 为当前触发标签页的组件名
      //标签页切换时变色及下划线
      if(this.currentTab =="investmentMacroeconomicIndicatorsShowList"){
        this.tabA = "tabline";
        this.tabB = "tab";
      }
      if(this.currentTab =="investmentMacroeconomicReportShowList"){
        this.tabA = "tab";
        this.tabB = "tabline";
      }
    },

    handleClick(tab, event) {
        console.log(tab, event);
      },


  }
}
</script>
<style scoped>
  .tabs{
    width: 100%;
    height: 20px;
    /*margin-top: 10px;*/
  }
  .tab{
    float: left;
    margin-left: 25px;
  }
  .tabline{
    float: left;
    margin-left: 25px;
    text-decoration: underline;
    color: #409eff;
  }

</style>
