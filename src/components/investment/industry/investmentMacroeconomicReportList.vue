<template>
  <div style="width: 100%;height: 585px;">
        <mftcc-table tableId="investment/busInvestmentMacroeTable" :initSearchData="tableInitSearchData"
            :parentVm="this" ref="investmentMacroeConomicList" class="user-table" style="height:100%"></mftcc-table>

            <!--弹出对话框-->
            <el-dialog
              :title="macroscopicTitle"
              :visible.sync="formVisible"
              :center="true"
              top="25vh"
              :modal-append-to-body="false"
              class="mole-dialog"
              width="100%"
              :fullscreen="fullscreen"
            >

            <div class="mftcc-container" style="max-height: 700px;" v-if="viewFlag" :key="time">
              <div class="mftcc-web">
              <openOneFileCompont
                :parmList="parmList"
                :reLoadFlag="reLoadFlag"
                :fileId="fileId"
                :docType="docType"
                :downloadFlag="downloadFlag">
              </openOneFileCompont>
              </div>
            </div>
          </el-dialog>
    </div>
</template>

<script>
import api from '@/api/investment/apply/busMacroe';
import openOneFileCompont from '@/components/doc/file/openOneFileCompont';



export default {
  name: 'investmentMacroeconomicReportList',
  title: '宏观经济报告管理列表',
  data() {
    return {
      tableInitSearchData: {
        macroscopicId: this.$route.query.macroscopicId,
      },

      macroscopicTitle: '',        //标题
      fileId: "",           //文件唯一编号或者模板唯一编号
      docType: "1",          //文件类型 1-附件 2-模板
      downloadFlag: "1",     //是否提供下载功能 1下載，0不下載
      reLoadFlag: "1",       //是否需要重新加载模板 0-否1-是
      parmList: {},          //重新加载模板需要的参数（如果不需要重新加载可以不传）
      viewFlag:false,
      formVisible:false,      //是否显示 Dialog
      fullscreen:true,        //是否为全屏 Dialog

    }
  },
  components: {
    //要件预览
    openOneFileCompont:openOneFileCompont,

  },

  methods: {
    //弹框宏观经济要件详情
    query(index, row) {
      this.formVisible = true;
      this.macroscopicTitle = row.macroscopicTitle;
      this.docType = "1";
      this.fileId = row.fileId;
      this.viewFlag = true;
      this.time = new Date().getTime();
    },



    handleClick(tab, event) {
        console.log(tab, event);
      },

    //跳转宏观经济新增界面
    insert() {
      this.$router
          .push({path: '/apply/investmentConomicInsert', query: {macroscopicId: this.macroscopicId}});
    },


    //宏观经济列表搜索
    onSearch() {
      let formData = this.$refs.investmentMacroeConomicList.getFormValue();
      this.$refs.investmentMacroeConomicList.search(formData);
    },

    //跳转宏观经济修改界面
    edit(index, row){
        this.$router
                .push({path: '/apply/investmentConomicDetailsInfo', query: {macroscopicId: row.macroscopicId}});
    },

    //宏观经济删除操作
    delete(index, row){
        this.$confirm('此操作将永久删除该记录, 是否继续?', this.$alertTitle, {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            let data = {};
            data.macroeData = row;
            api.deleteData(data,res => {
                if(res.code === 0){
                    res.msg = res.data.msg;
                    this.$message({
                        type: res.msgType,
                        dangerouslyUseHTMLString:true,
                        message: res.msg
                    })
                    this.$refs.investmentMacroeConomicList.refresh();
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true
                    });
                }
            });
        });
    },

  }
}
</script>
