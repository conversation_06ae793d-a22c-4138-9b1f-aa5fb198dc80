<template>
  <div style="width: 100%;height: 585px;">
        <mftcc-Table :tableId="tableId" :initSearchData="tableInitSearchData"
            :parentVm="this" ref="investmentMacroeEchartList" class="user-table" style="height:100%"></mftcc-Table>


    </div>
</template>

<script>
import api from '@/api/investment/apply/busMacroe';
import openOneFileCompont from '@/components/doc/file/openOneFileCompont';





export default {
  name: 'investmentMacroeEchartList',
  title: '宏观经济指标管理列表',
  props: ["echartid"],
  data() {
    return {
      tableInitSearchData: {
        // objectId: this.echartid,
      },

      tableId: "investment/chinamaCroforeCastSummaryTable",
      summaryCastdata:[],
    }
  },
  components: {
    //要件预览
    openOneFileCompont:openOneFileCompont,

  },

  mounted() {
    this.initFormValue();
  },

  methods: {
    //弹框宏观经济要件详情
    query(index, row) {
      this.formVisible = true;
      this.macroscopicTitle = row.macroscopicTitle;
      this.docType = "1";
      this.fileId = row.fileId;
      this.viewFlag = true;
      this.time = new Date().getTime();
    },

    // 初始化宏观预测综合,宏观预测明细表单数据
    initFormValue(){
      let data = {};
      data.echartid = this.echartid;
      api.getConomicEchartForm(data, res => {
        if (res.code === 0) {
          let summarydata = res.data;
          this.summaryCastdata = summarydata;
          //宏观指标表单标识
          if(summarydata.summaryTable == 0){
            //宏观预测综合表单
            this.tableId="investment/chinamaCroforeCastSummaryTable";
          }else{
            //宏观经济明细表单
            this.tableId="investment/chinamaCroforeCastTable";
          }
        } else {
          this.$alert(res.msg, this.$alertTitle, {
            type: res.msgType,
            dangerouslyUseHTMLString: true,
            callback: action => {
              // this.$router.back(-1);
            }
          });
        }
      }, error => {
        // this.$router.back(-1);
      });
    },


    //宏观指标点击柱状图，折线图，调用列表默认选中点击行数据
    summaryStyle(row){
      let objectId = row.row.objectId;
      //获取列表选中时row数据
      let rowdata = this.$refs.investmentMacroeEchartList.getCurrentValue();
      if(objectId == this.echartid){
        if(rowdata!=null){
          //不选中行样式
          return ("tr","el-table__row");
        }else{
          //选中行样式
          return ("tr","el-table__row current-row");
        }
      }
    },

    handleClick(tab, event) {
        console.log(tab, event);
      },

    //跳转宏观经济新增界面
    insert() {
      this.$router
          .push({path: '/apply/investmentConomicInsert', query: {macroscopicId: this.macroscopicId}});
    },


    //宏观经济列表搜索
    onSearch() {
      let formData = this.$refs.investmentMacroeEchartList.getFormValue();
      this.$refs.investmentMacroeEchartList.search(formData);
    },

    //跳转宏观经济修改界面
    edit(index, row){
        this.$router
                .push({path: '/apply/investmentConomicDetailsInfo', query: {macroscopicId: row.macroscopicId}});
    },

    //宏观经济删除操作
    delete(index, row){
        this.$confirm('此操作将永久删除该记录, 是否继续?', this.$alertTitle, {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }).then(() => {
            let data = {};
            data.macroeData = row;
            api.deleteData(data,res => {
                if(res.code === 0){
                    res.msg = res.data.msg;
                    this.$message({
                        type: res.msgType,
                        dangerouslyUseHTMLString:true,
                        message: res.msg
                    })
                    this.$refs.investmentMacroeEchartList.refresh();
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true
                    });
                }
            });
        });
    },

  }
}
</script>
<style scoped>
  /* .el-table__row{
    background-color: burlywood;
  } */
</style>
