<template>
  <div class="mftcc_home_box">
    <div class="mftcc_home_header">
      <div class="heder_item">
        <div class="icon_header">
                <a href="javascript:;" @click="tableSwitch('chart')">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-shujufenxi"></use>
                  </svg>
                </a>
                <a href="javascript:;" @click="tableSwitch('table')">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-liebiaoxingshi"></use>
                  </svg>
                </a>
          </div>

        <!--行业报告类型显示管理块-->
        <investmentIndustryQueryList v-if="chartFlag"></investmentIndustryQueryList>
        <!--行业报告表单列表Echart-->
        <busInvestmentIndustryListEchart v-if="tableFlag"></busInvestmentIndustryListEchart>
    </div>
    </div>
  </div>
</template>

<script>
import investmentIndustryQueryList from "@/components/investment/industry/investmentIndustryQueryList"
import busInvestmentIndustryListEchart from "@/components/investment/industry/busInvestmentIndustryListEchart"


export default {
  name: 'investmentIndustryListblock',
  title: '行业报告类型显示管理块列表',
  data() {
    return {
      activeNames:'1',
      tableFlag:false,   //行业表单标识
      chartFlag:true,   //行业块标识

    }
  },
  components: {
    //行业报告表单列表Echart
    busInvestmentIndustryListEchart,
    //行业报告类型显示管理块
    investmentIndustryQueryList,
  },

  methods: {


    //切换Echart或表单
    tableSwitch(name){
        this.activeNames = ['0'];
        if(name == "table"){
          this.tableFlag = true;
          this.chartFlag = false;

        }else if(name == "chart"){
          this.time = new Date().getTime();
          this.tableFlag = false;
          this.chartFlag = true;

        }
    },


  },
  mounted () {

  }
}
</script>
<style scoped src="@/assets/investment/css/busIndustryStyle.css"/>


