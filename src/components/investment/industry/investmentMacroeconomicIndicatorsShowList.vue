<template>
  <div>
    <!--宏观经济指标Echart图-->
    <investment-macroe-chart v-if="macroeclick" :macrclick="macroeclick" :charclick="chartclick" @listenTochildEvent="showMacroeChart"></investment-macroe-chart>
    <!--宏观经济指标管理列表-->
    <investmentMacroeEchartList v-if="chartclick" :echartid="echartid" @listenchildEvent="showMacroeList"></investmentMacroeEchartList>
  </div>
</template>

<script>
import api from '@/api/investment/apply/busMacroe';
import InvestmentMacroeChart from "@/components/investment/industry/investmentMacroeChart";
import investmentMacroeEchartList from "@/components/investment/industry/investmentMacroeEchartList";



export default {
  name: 'investmentMacroeconomicIndicatorsShowList',
  title: '宏观经济指标显示列表',
  data() {
    return {
      tableInitSearchData: {
        macroscopicId: this.$route.query.macroscopicId,
      },

      macroeclick: true,  //指标Echart图
      chartclick: false,  //指标table列表
      echartid: '',       //统计图id
      rptDate:'',
      forecastMonth:'',
      indicatorName:'',
    }
  },
  components: {
    InvestmentMacroeChart,
    investmentMacroeEchartList,

  },

  methods: {

    //监听子组件传入值
    showMacroeChart(data){
      this.macroeclick = data.macrclick;
      this.chartclick = data.charclick;
      this.echartid = data.dataid;
    },

    //监听列表过来的指标日期和名称
    showMacroeList(data){
      this.rptDate = data.rptDate;
      this.forecastMonth = data.forecastMonth;
      this.indicatorName = data.indicatorName;
    }


  },
}
</script>
