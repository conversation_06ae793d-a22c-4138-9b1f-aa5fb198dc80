<template>
    <div class="mftcc-container">
        <el-header><div class="mftcc-form-header">详情页面</div></el-header>
        <el-container>
            <el-row>
                <mftcc-form formId="investment/busInvestmentPoliciesInsertNewForm" :parentVm="this" ref="busInvestmentPoliciesInsertNewForm" style="margin-bottom: 20px;"></mftcc-form>
                <!-- 要件配置 -->
                <div class="mftcc-web" style="height:300px">
                    <mftcc-file :docParameter="docParameter" v-if="docShowFlag" @fileUploadCallback="fileUploadCallback($event)" @fileDeleteCallback="fileDeleteCallback($event)"></mftcc-file>
                </div>
            </el-row>
        </el-container>
    </div>
</template>
<script>
import api from '@/api/investment/apply/busPolicies'
import mftccFile from '@/components/doc/file/mftccFile'
export default {
    name: 'busInvestmentPoliciesRegulationsShow',
    title: '政策法规审批详情',
    components:{
        mftccFile:mftccFile
    },
    props: ["wkfAppId","approveId","policiesId"],
    data() {
        return {
            //模板参数---开始(一些要素与要件参数共用)
            templateShowFlag:false,//是否展示模板
            parmList: {},//书签取值需要的业务参数
            templateEditFlag:"0",//模板是否允许编辑  1-允许，0-不允许
            //模板参数---结束

            fileId: "",           //新增附件id
            delfileFalg: "0",     //要件回调删除标识0，存入，1删除

            //文档要件参数---开始
            docParameter: {},
            docShowFlag: false,
            // appName: "investmentResearch",//流程配置标识
            // fileShowFlag: false,//是否显示要件组件
            // prdUniqueVal: "",//产品的唯一标识：格式“产品号_版本号”，如果没有可以为空。
            // flowNo: "",//流程标识
            // nodeNo: "",//流程中的节点标识
            // bizNo: "",// 项目申请号
            // displayByConfig: "0",//是否根据配置与否来决定是否展示要件1-是 0-否
            //editFlag: "1",//要件是否允许编辑  1-允许，0-不允许
            //文档要件参数---结束
        }
    },
    methods: {

        //审批提交函数
        doCommit(params, callback){
            let data = {};
            data.params = params;
            api.policiesAppCommit(data,res =>{
                if(res.code===0){
                    res.msg = res.data.msg;
                    callback(res);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            })
        },

        //回显审批数据
        findById(){
            let policiesId = this.policiesId;
            api.findById(policiesId,res => {
                if(res.code === 0){
                    var data = res.data;
                    this.$refs.busInvestmentPoliciesInsertNewForm.setFormValue(data);
                    this.getDocParam(policiesId);
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
            },error => {
                this.$router.back(-1);
            });
        },

        //要件
        getDocParam(policiesId){
            api.findDocNodeInfo(policiesId,res => {
            if(res.code === 0){
                    var data = res.data;
                    this.docParameter ={
                        prdUniqueVal: '99993',             //产品的唯一标识：格式“产品号_版本号”必填  
                        flowNo: data.flowNo,                  //流程标识必填  
                        bizNo: data.bizNo,                   //项目申请号必填  
                        nodeNo: data.nodeNo,                  //流程中的节点标识必填  
                        addFloderFlag: true,              // 是否显示打包下载、不填默认false  
                        appName: 'investment',          //微服务名称 investment-投資//必填  
                        isMultiple: false,               //控制上传是否允许选择多个，true允许/false不允许，不传默认为true
                    }
                    this.docShowFlag = true;
                }else{
                    this.$alert(res.msg, this.$alertTitle, {
                        type: res.msgType,
                        dangerouslyUseHTMLString: true,
                        callback: action => {
                            this.$router.back(-1);
                        }
                    });
                }
        },error => {
                this.$router.back(-1);
            });
        },
        callBackFun(callback){
            return callback();
        },
        back(){
            this.$router.back(-1);
        }
    },
    mounted() {
        this.findById();
    }
}
</script>
