#
# Copyright © 2020 北京微金时代科技有限公司
# <EMAIL>
#
FROM anapsix/alpine-java:8_jdk_unlimited

MAINTAINER <EMAIL>

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN mkdir -p /mftcc

WORKDIR /mftcc

EXPOSE 9999

ADD ./ /mftcc/

CMD java -Dfile.encoding=utf-8 -Djava.security.egd=file:/dev/./urandom -jar /mftcc/RuleISSUE.jar --spring.config.location=classpath:/,classpath:/config/,file:./,file:./config/,file:/mftcc/bootstrap-nacos.yml || sleep 60