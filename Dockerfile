FROM anapsix/alpine-java:8_server-jre_unlimited

MAINTAINER <EMAIL>

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN mkdir -p /mftcc

WORKDIR /mftcc

EXPOSE 8039

ADD target/mftcc-flowable-server.jar /mftcc/
ADD target/mftcc-flowable-server /mftcc/
RUN chmod +x /mftcc/mftcc-flowable-server

ADD conf/application.yml /mftcc/application.yml
ADD license/ /mftcc/

CMD /mftcc/mftcc-flowable-server java -Djava.security.egd=file:/dev/./urandom -Dfile.encoding=utf-8 -jar /mftcc/mftcc-flowable-server.jar --spring.config.location=classpath:/,classpath:/config/,file:./,file:./config/,file:/mftcc/application.yml || sleep 120
