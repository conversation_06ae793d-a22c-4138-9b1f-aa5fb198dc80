#
# Copyright © 2020 北京微金时代科技有限公司
# <EMAIL>
#
FROM anapsix/alpine-java:8_server-jre_unlimited

MAINTAINER <EMAIL>

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN mkdir -p /mftcc

WORKDIR /mftcc

EXPOSE 18080

ADD 启动项目/ /mftcc

CMD java -Djava.security.egd=file:/dev/./urandom -Dfile.encoding=utf-8 -jar /mftcc/RDP-SERVER.jar --spring.config.location=classpath:/,classpath:/config/,file:./,file:./config/,file:/mftcc/application.yml || sleep 60