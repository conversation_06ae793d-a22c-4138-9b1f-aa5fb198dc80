#
# Copyright © 2020 北京微金时代科技有限公司
# <EMAIL>
#

FROM anapsix/alpine-java:8_server-jre_unlimited

MAINTAINER <EMAIL>

#定义时区参数
ENV TZ=Asia/Shanghai
#安装时区数据包
RUN apk add tzdata
#设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo '$TZ' > /etc/timezone

RUN mkdir -p /mftcc

WORKDIR /mftcc

EXPOSE 8060

ADD target/mftcc-doc-server.jar /mftcc
ADD target/lib/ /mftcc/lib/

CMD java -Dio.netty.leakDetectionLevel=DISABLED -Dloader.path=./BOOT-INF/lib,/mftcc/lib -Djava.security.egd=file:/dev/./urandom -jar $JAVA_OPT /mftcc/mftcc-doc-server.jar