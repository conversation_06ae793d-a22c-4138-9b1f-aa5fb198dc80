FROM anapsix/alpine-java:8_jdk_nashorn

MAINTAINER <EMAIL>

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN mkdir -p /mftcc

WORKDIR /mftcc

EXPOSE 28080

ADD target/sentinel-dashboard-1.8.5.jar /mftcc
ADD conf/application.properties /mftcc/
#ADD target/lib/ /mftcc

CMD  java -Djava.security.egd=file:/dev/./urandom -Dfile.encoding=utf-8 -jar /mftcc/sentinel-dashboard-1.8.5.jar --spring.config.location=file:/mftcc/application.properties || sleep 360