@echo off
title mftcc-flowable-server-%date%-%time%-%cd%
chcp 65001
if not exist "%JAVA_HOME%\bin\java.exe" echo Please set the JAVA_HOME variable in your environment, We need java(x64)! jdk8 or later is better! & EXIT /B 1
set "JAVA=%JAVA_HOME%\bin\java.exe"

setlocal enabledelayedexpansion

set BASE_DIR=%~dp0

set BASE_DIR="%BASE_DIR:~0,-5%"

set DEFAULT_SEARCH_LOCATIONS="classpath:/,classpath:/config/,file:./,file:./config/"
set CUSTOM_SEARCH_LOCATIONS=%DEFAULT_SEARCH_LOCATIONS%,file:%BASE_DIR%/conf/application.yml

set SERVER=mftcc-flowable-server

set "JAVA_OPT=%JAVA_OPT% -server -Xms2g -Xmx2g -Xmn1g -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m"

set "JAVA_OPT=%JAVA_OPT% -D%SERVER%.home=%BASE_DIR%"
set "JAVA_OPT=%JAVA_OPT% -Dfile.encoding=utf-8"
set "JAVA_OPT=%JAVA_OPT% -jar %BASE_DIR%\target\%SERVER%.jar"
set "JAVA_OPT=%JAVA_OPT% --spring.config.location=%CUSTOM_SEARCH_LOCATIONS%"

call %BASE_DIR%\target\%SERVER% "%JAVA%" %JAVA_OPT% %SERVER%.%SERVER% %*

@if errorlevel 1 pause
