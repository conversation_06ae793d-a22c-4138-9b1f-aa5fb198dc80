SERVER=sentinel-dashboard-1.8.5

BASE_DIR=$(cd "$(dirname "$0")/..";pwd)

CUSTOM_SEARCH_LOCATIONS='file:'$BASE_DIR'/conf/application.properties'


JAVA_OPT=' -server -Xms2g -Xmx2g -Xmn1g -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m'
JAVA_OPT=$JAVA_OPT' -D'$SERVER'.home='$BASE_DIR
JAVA_OPT=$JAVA_OPT' -Dfile.encoding=utf-8'
JAVA_OPT=$JAVA_OPT' -jar '$BASE_DIR'/target/'$SERVER'.jar'
JAVA_OPT=$JAVA_OPT' --spring.config.location='$CUSTOM_SEARCH_LOCATIONS
#echo $JAVA_OPT


if [ ! -d "$BASE_DIR/logs" ]; then
  mkdir $BASE_DIR/logs
fi

nohup java $JAVA_OPT >> $BASE_DIR/logs/${SERVER}.out 2>&1 &