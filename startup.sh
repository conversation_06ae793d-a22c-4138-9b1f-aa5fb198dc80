SERVER=mftcc-rule-service

BASE_DIR=$(cd "$(dirname "$0")/..";pwd)

DEFAULT_SEARCH_LOCATIONS="classpath:/,classpath:/config/,file:./,file:./config/"
CUSTOM_SEARCH_LOCATIONS=$DEFAULT_SEARCH_LOCATIONS',file:'$BASE_DIR'/bootstrap-nacos.yml'


JAVA_OPT=' -server -Xms2g -Xmx2g -Xmn1g -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m'
JAVA_OPT=$JAVA_OPT' -D'$SERVER'.home='$BASE_DIR
JAVA_OPT=$JAVA_OPT' -Dfile.encoding=utf-8'
JAVA_OPT=$JAVA_OPT' -jar '$BASE_DIR'/RuleISSUE.jar'
JAVA_OPT=$JAVA_OPT' --spring.config.location='$CUSTOM_SEARCH_LOCATIONS
#echo $JAVA_OPT


if [ ! -d "$BASE_DIR/logs" ]; then
  mkdir $BASE_DIR/logs
fi

nohup java $JAVA_OPT >> $BASE_DIR/logs/${SERVER}.out 2>&1 &